// vendor-metrics.js - Vendor performance metrics and analytics
import { NotificationSystem } from "../core/notifications.js";
import { connectionManager } from "../core/connection.js";
import { loadApexCharts } from "../analytics/chart-loader.js";
import { VendorLeadTimeSync } from "./vendor-lead-time-sync.js";
import { VendorAnalyticsComponent } from "./vendor-analytics.js";

export class VendorMetricsComponent {
  constructor(container) {
    this.container = container;
    
    // Initialize properties for data
    this.poData = [];
    this.vendorData = {};
    this.vendorList = [];
    this.vendorMetrics = [];
    this.filteredPOs = []; // Initialize this to prevent length property errors
    
    // Analytics component
    this.analytics = null;
    
    // State tracking
    this.isLoading = true;
    this.loadingProgress = 0;
    this.processingProgress = 0;
    this.activeView = 'analytics'; // Default view: 'analytics', 'table', or 'vendorManagement'
    
    // Date range filter - expanded properties
    this.dateRangeFilter = {
      enabled: false,
      startDate: null,
      endDate: null,
      applyToVendors: true // Apply the date range to vendor management view too
    };
    
    // Table sorting
    this.sortField = 'LastModifiedDateTime'; // Sort by received date by default
    this.sortDirection = 'desc'; // Default to descending (newest first)
    
    // Search filter
    this.searchTerm = '';
    
    // Pagination settings
    this.currentPage = 0;
    this.pageSize = 6; // Show 6 items per page
    
    // Add vendor pagination settings
    this.vendorCurrentPage = 0;
    this.vendorPageSize = 6; // Show 6 vendors per page
    
    // Settings
    this.settings = {
      leadTimeThresholds: {
        good: 15,
        warning: 30
      },
      excludeWeekends: true,
      defaultView: 'analytics',
      defaultSort: {
        field: 'LastModifiedDateTime', // Sort by received date by default
        direction: 'desc' // Default to newest first
      },
      chartColors: {
        primary: '#4F46E5',
        secondary: '#10B981',
        warning: '#F59E0B',
        danger: '#EF4444'
      }
    };
    
    // Create a notification instance - use directly imported NotificationSystem
    try {
      this.notificationSystem = new NotificationSystem();
    } catch (e) {
      console.warn('Error initializing NotificationSystem:', e);
      this.notificationSystem = null;
    }
  }

  async init() {
    try {
      console.log("Initializing vendor metrics component");
      
      // Set loading state
      this.isLoading = true;
      this.loadingProgress = 0;
      this.processingProgress = 0;
      
      // Ensure all settings are properly initialized
      this.leadTimeThresholds = this.settings?.leadTimeThresholds || { good: 15, warning: 30 };
      this.weekendsExcluded = this.settings?.excludeWeekends !== undefined ? this.settings.excludeWeekends : true;
      this.chartView = false;
      this.vendorManagementView = false;
      
      // Load settings from storage
      this.loadSettingsFromStorage();
      
      // Initial render to show loading state
      await this.render();
      
      // Check if already connected to Acumatica
      const connectionStatus = connectionManager.getConnectionStatus();
      
      // Apply dark mode
      this.handleDarkMode();
      
      if (connectionStatus && connectionStatus.acumatica && connectionStatus.acumatica.isConnected) {
        console.log("Connected to Acumatica, loading vendor data");
        this.addNotification("Loading purchase order data from Acumatica...", "info");
        await this.loadVendorData();
      } else {
        console.log("Not connected to Acumatica");
        this.addNotification("Please connect to Acumatica to load purchase orders", "warning");
        this.isLoading = false;
        await this.render();
      }
      
    } catch (error) {
      console.error("Error initializing vendor metrics component:", error);
      this.addNotification("Error initializing: " + error.message, "error");
      this.isLoading = false;
      await this.render();
    }
  }

  render() {
    console.log("Rendering vendor metrics component");
    
    // Only update the content area, not the entire container
    const contentArea = this.container?.querySelector('#tabContentContainer');
    if (contentArea) {
      if (this.isLoading) {
        contentArea.innerHTML = this.renderLoading();
      } else {
        contentArea.innerHTML = this.renderContent();
      }
    } else {
      console.warn('No #tabContentContainer found, unable to render vendor metrics content');
    }
    
    // Handle dark mode
    this.handleDarkMode();
    
    // Setup component-specific event handlers
    this.setupComponentEventHandlers();
    
    // Initialize analytics component if we're in chart view
    if (this.chartView && !this.isLoading) {
      setTimeout(() => {
        const analyticsContainer = this.container.querySelector('#analyticsContainer');
        if (analyticsContainer) {
          // Determine data to pass to analytics
          const dataToUse = this.dateRangeFilter.enabled && this.filteredPOs.length > 0 
            ? this.filteredPOs 
            : this.poData;
            
          // Create analytics component
          this.analytics = new VendorAnalyticsComponent(
            analyticsContainer, 
            dataToUse, 
            this.dateRangeFilter,
            this.notificationSystem
          );
          
          // Set up event listener for clearing date range filter
          analyticsContainer.addEventListener('clearDateRange', () => {
            this.clearDateRangeFilter();
          });
          
          // Render and initialize charts
          this.analytics.render();
          this.analytics.renderCharts();
        }
      }, 50);
    }
  }

  handleDarkMode() {
    const isDarkMode = document.body.classList.contains("dark-mode");
    const container = this.container;
    
    // Apply dark mode styles to any elements that need it
    const elements = container.querySelectorAll('.bg-white, .text-gray-800, .text-gray-900');
    elements.forEach(el => {
      if (isDarkMode) {
        el.classList.add('dark:bg-gray-800', 'dark:text-white');
      }
    });
  }

  addNotification(message, type = 'info') {
    // Use the notification system if available
    if (this.notificationSystem) {
      this.notificationSystem.addNotification(message, type);
      return;
    }
    
    // Check if we have access to the dashboard's notification system
    if (typeof window.dashboard !== 'undefined' && typeof window.dashboard.addNotification === 'function') {
      // Use the dashboard's notification system
      window.dashboard.addNotification(message, type);
      return;
    }
    
    // Fallback notification method
    const notificationArea = document.getElementById('notificationArea');
    if (!notificationArea) {
      console.warn('Notification area not found, creating one');
      const newNotificationArea = document.createElement('div');
      newNotificationArea.id = 'notificationArea';
      newNotificationArea.className = 'fixed top-4 right-4 z-50 flex flex-col items-end space-y-2';
      document.body.appendChild(newNotificationArea);
    }
    
    const notification = document.createElement('div');
    notification.className = `p-4 rounded shadow-lg max-w-xs transition-all duration-300 transform translate-x-0 `;
    
    // Set color based on type
    switch (type) {
      case 'success':
        notification.className += 'bg-green-500 text-white';
        break;
      case 'error':
        notification.className += 'bg-red-500 text-white';
        break;
      case 'warning':
        notification.className += 'bg-yellow-500 text-white';
        break;
      default:
        notification.className += 'bg-blue-500 text-white';
    }
    
    notification.textContent = message;
    
    const notificationContainer = document.getElementById('notificationArea');
    notificationContainer.appendChild(notification);
    
    // Remove notification after 5 seconds
    setTimeout(() => {
      notification.classList.add('translate-x-full', 'opacity-0');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 5000);
  }

  renderLoading() {
    return `
      <div class="text-center py-8 text-gray-500 dark:text-gray-400">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg font-medium">Loading Purchase Orders...</p>
        <p class="text-sm mt-2">Please wait while we retrieve data from Acumatica.</p>
        <p class="text-xs mt-1 text-gray-400">This may take several minutes for large order volumes</p>
        
        ${this.loadingProgress > 0 ? `
          <div class="relative pt-1 mt-4 max-w-lg mx-auto">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200 dark:bg-blue-900 dark:text-blue-200">
                  Loading
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-blue-600 dark:text-blue-300">
                  ${this.loadingProgress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200 dark:bg-blue-900">
              <div style="width:${this.loadingProgress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 dark:bg-blue-400 transition-all duration-500"></div>
            </div>
          </div>
        ` : ''}
        
        ${this.processingProgress > 0 ? `
          <div class="relative pt-1 mt-4 max-w-lg mx-auto">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200 dark:bg-green-900 dark:text-green-200">
                  Processing
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-green-600 dark:text-green-300">
                  ${this.processingProgress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200 dark:bg-green-900">
              <div style="width:${this.processingProgress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500 dark:bg-green-400 transition-all duration-500"></div>
            </div>
          </div>
        ` : ''}
        
        <button id="loadVendorDataBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs flex items-center mx-auto mt-4">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Load Vendor Data
        </button>
      </div>
    `;
  }
  
  renderHeader() {
    return `
      <div class="mb-4 flex flex-wrap items-center">
        <!-- Search Bar -->
        <div class="relative flex-grow mr-2">
          <input type="text" id="vendorSearchInput" class="w-full pl-9 pr-4 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white" 
            placeholder="Search by vendor name or PO number..." value="${this.searchTerm}">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          ${this.searchTerm ? `
          <button id="clearVendorSearchBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
          ` : ''}
        </div>
        
        <!-- Date Range Filter -->
        <button id="dateRangeBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
          focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs flex items-center
          ${this.dateRangeFilter.enabled ? 'bg-blue-50 text-blue-500 border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700' : ''}" 
          style="height: 30px;" title="Filter by Date Range">
          <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          Date Range
        </button>
        
        <!-- Refresh Button -->
        <button id="refreshDataBtn" class="ml-2 border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
          focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" 
          style="height: 30px; width: 30px;" title="Refresh Data">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
        
        <!-- Action Buttons -->
        <div class="flex space-x-2 ml-2">
          <!-- Sync Button -->
          <button id="vendorSyncBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
            focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" 
            style="height: 30px; width: 30px;" title="Sync Lead Times (Modal)">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
            </svg>
          </button>

          <!-- Vendor Management Button -->
          <button id="vendorManagementBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
            focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center
            ${this.vendorManagementView ? 'bg-blue-50 text-blue-500 border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700' : ''}" 
            style="height: 30px; width: 30px;" title="Vendor Management Page">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
            </svg>
          </button>
          
          <!-- Analytics Button -->
          <button id="vendorAnalyticsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
            focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center
            ${this.chartView ? 'bg-blue-50 text-blue-500 border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700' : ''}" 
            style="height: 30px; width: 30px;" title="View Analytics Page">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </button>
          
          <!-- Main Table Button (renamed from Table Button) -->
          <button id="vendorTableBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
            focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center
            ${!this.chartView && !this.vendorManagementView ? 'bg-blue-50 text-blue-500 border-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700' : ''}" 
            style="height: 30px; width: 30px;" title="View Main Table">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
          </button>
          
          <!-- Settings Button -->
          <button id="vendorSettingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-transparent dark:bg-transparent 
            focus:outline-none hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" 
            style="height: 30px; width: 30px;" title="Settings (Modal)">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }
  
  renderContent() {
    if (!this.vendorData && this.poData.length === 0) {
      return this.renderEmptyState();
    }
    
    return `
      ${this.renderHeader()}
      ${this.renderMetricCards()}
      ${this.chartView ? this.renderCharts() : this.vendorManagementView ? this.renderVendorManagementView() : this.renderPOTable()}
    `;
  }
  
  renderEmptyState() {
      return `
      ${this.renderHeader()}
      <div class="text-center py-12 text-gray-500 dark:text-gray-400">
        <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
        <p class="text-base">Vendor Performance Metrics</p>
        <p class="text-xs mt-1">Analyze supplier performance, lead times, and quality metrics.</p>
        
        <div class="mt-6">
          <button id="loadVendorDataBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs flex items-center mx-auto">
            <svg class="w-3 h-3 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"></path>
            </svg>
            Load Vendor Data
          </button>
        </div>
      </div>
    `;
  }
  
  renderMetricCards() {
    // If we don't have data yet, calculate metrics from PO data
    if (!this.vendorData) {
      this.calculateMetricsFromPOs();
    }
    
    // Ensure we have valid metrics with fallbacks to prevent NaN/undefined
    const metrics = this.vendorData || {
      onTimeDelivery: 0,
      averageLeadTime: 0,
      lateDeliveries: 0,
      topVendors: []
    };
    
    // Ensure values are valid numbers, not NaN/undefined
    const onTimeDelivery = isNaN(metrics.onTimeDelivery) ? 0 : metrics.onTimeDelivery;
    const averageLeadTime = isNaN(metrics.averageLeadTime) ? 0 : metrics.averageLeadTime;
    const lateDeliveries = isNaN(metrics.lateDeliveries) ? 0 : metrics.lateDeliveries;
    
    // Get top vendor info with fallbacks
    const topVendor = metrics.topVendors && metrics.topVendors.length > 0 ? metrics.topVendors[0] : null;
    const topVendorName = topVendor ? this.getVendorName(topVendor.id) : 'N/A';
    const topVendorOnTime = topVendor ? topVendor.onTimePercent : 0;
    const topVendorLeadTime = topVendor ? topVendor.averageLeadTime : 0;
    
    return `
      <div class="grid grid-cols-1 md:grid-cols-4 gap-3 mb-4">
        <!-- On-Time Delivery Card -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-2">
          <div class="flex items-center">
            <div class="rounded bg-green-100 dark:bg-green-900 p-1.5 mr-2">
              <svg class="w-2.5 h-2.5 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">OTD</span>
          </div>
          <div class="ml-1 mt-1">
            <span class="text-md font-bold text-gray-800 dark:text-white">${onTimeDelivery}%</span>
            <p class="text-xs text-gray-500 dark:text-gray-400">on-time delivery</p>
          </div>
        </div>
      
        <!-- Average Lead Time Card -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-2">
          <div class="flex items-center">
            <div class="rounded bg-blue-100 dark:bg-blue-900 p-1.5 mr-2">
              <svg class="w-2.5 h-2.5 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">AVT</span>
          </div>
          <div class="ml-1 mt-1">
            <span class="text-md font-bold text-gray-800 dark:text-white">${averageLeadTime} days</span>
            <p class="text-xs text-gray-500 dark:text-gray-400">average lead time</p>
          </div>
        </div>
        
        <!-- Late Deliveries Card -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-2">
          <div class="flex items-center">
            <div class="rounded bg-red-100 dark:bg-red-900 p-1.5 mr-2">
              <svg class="w-2.5 h-2.5 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">LATE</span>
          </div>
          <div class="ml-1 mt-1">
            <span class="text-md font-bold text-gray-800 dark:text-white">${lateDeliveries}</span>
            <p class="text-xs text-gray-500 dark:text-gray-400">orders delivered late</p>
          </div>
        </div>
        
        <!-- Top Vendor Card -->
        <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow p-2">
          <div class="flex items-center">
            <div class="rounded bg-purple-100 dark:bg-purple-900 p-1.5 mr-2">
              <svg class="w-2.5 h-2.5 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
            <span class="text-xs font-medium text-gray-700 dark:text-gray-300">TPV</span>
          </div>
          <div class="ml-1 mt-1">
            <span class="text-xs font-bold text-gray-800 dark:text-white truncate block" style="max-width: 180px;">
              ${topVendorName}
            </span>
            <p class="text-xs text-gray-500 dark:text-gray-400">${topVendorOnTime}% on-time, ${topVendorLeadTime} day lead time</p>
          </div>
        </div>
      </div>
    `;
  }
  
  renderPOTable() {
    if (!this.poData || this.poData.length === 0) {
      return `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p class="text-xs">No purchase order data available.</p>
        </div>
      `;
    }
    
    // Make sure filteredPOs is initialized
    if (!this.filteredPOs) {
      this.filteredPOs = [];
    }
    
    const itemsToDisplay = this.filteredPOs.length > 0 ? this.filteredPOs : this.poData;
    
    // Calculate pagination values
    const totalPages = Math.ceil(itemsToDisplay.length / this.pageSize);
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, itemsToDisplay.length);
    const displayedItems = itemsToDisplay.slice(startIndex, endIndex);
    
    return `
      <div class="overflow-x-auto">
        ${this.dateRangeFilter.enabled ? `
          <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs rounded">
            <span class="font-medium">Date Filter Applied:</span>
            ${this.dateRangeFilter.startDate ? ` From ${this.formatDateSafely(this.dateRangeFilter.startDate)}` : ''}
            ${this.dateRangeFilter.endDate ? ` To ${this.formatDateSafely(this.dateRangeFilter.endDate)}` : ''}
            <button id="clearDateRangeFilterBtn" class="ml-2 text-blue-700 dark:text-blue-400 hover:underline">Clear</button>
          </div>
        ` : ''}
        
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs" style="max-width: 750px">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                <div class="flex items-center">
                  PO # ${this.renderSortIndicator('OrderNbr')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="VendorID">
                <div class="flex items-center">
                  Vendor ${this.renderSortIndicator('VendorID')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="Date">
                <div class="flex items-center">
                  Created ${this.renderSortIndicator('Date')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="PromisedOn">
                <div class="flex items-center">
                  Promised ${this.renderSortIndicator('PromisedOn')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="LastModifiedDateTime">
                <div class="flex items-center">
                  Received ${this.renderSortIndicator('LastModifiedDateTime')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer" data-sort="LeadTime">
                <div class="flex items-center">
                  Lead Time ${this.renderSortIndicator('LeadTime')}
                </div>
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div class="flex items-center">
                  Status
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
            ${displayedItems.map(po => {
              // Extract values correctly from the Acumatica API response format
              const poNumber = po.OrderNbr?.value || 'N/A';
              const vendorId = po.VendorID?.value || 'N/A';
              const vendorName = this.getVendorName(vendorId);
              // Truncate vendor name if too long (max 20 chars + ellipsis)
              const displayVendorName = vendorName.length > 20 ? 
                vendorName.substring(0, 20) + '...' : 
                vendorName;
              
              const createdDate = this.formatDateSafely(po.Date?.value);
              const promisedDate = this.formatDateSafely(po.PromisedOn?.value);
              const receivedDate = this.formatDateSafely(po.LastModifiedDateTime?.value);
              
              // Calculate lead time
              const leadTime = this.calculateLeadTime(po);
              const leadTimeClass = this.getLeadTimeClass(leadTime);
              
              // Determine delivery status
              const deliveryStatus = this.getDeliveryStatus(po);
              let statusClass = '';
              
              if (deliveryStatus === 'Early') {
                statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
              } else if (deliveryStatus === 'OnTime') {
                statusClass = 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
              } else if (deliveryStatus === 'Late') {
                statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
              } else {
                statusClass = 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
              }
              
              return `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-blue-600 dark:text-blue-400 font-medium cursor-pointer hover:underline" data-po-id="${poNumber}">${poNumber}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="font-medium text-gray-900 dark:text-white" title="${vendorName}">${displayVendorName}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-gray-700 dark:text-gray-300">${createdDate}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-gray-700 dark:text-gray-300">${promisedDate}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-gray-700 dark:text-gray-300">${receivedDate}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-xs ${leadTimeClass}">${leadTime !== null ? `${leadTime} days` : 'N/A'}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                      ${deliveryStatus}
                    </span>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
      ${this.renderPagination(totalPages)}
      <div class="mt-4 text-xs text-gray-500 dark:text-gray-400 text-right">
        Showing ${Math.min(itemsToDisplay.length, startIndex + 1)}-${endIndex} of ${itemsToDisplay.length} purchase orders
      </div>
    `;
  }
  
  renderPagination(totalPages) {
    if (totalPages <= 1) return '';
    
    // Generate page buttons
    let paginationHTML = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(0, Math.min(this.currentPage - Math.floor(maxVisiblePages / 2), totalPages - maxVisiblePages));
    if (startPage < 0) startPage = 0;
    const endPage = Math.min(startPage + maxVisiblePages, totalPages);
    
    for (let i = startPage; i < endPage; i++) {
      const isActive = i === this.currentPage;
      paginationHTML += `
        <button data-page="${i}" class="px-2 py-1 border ${isActive 
          ? 'border-blue-500 bg-blue-500 text-white' 
          : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-md text-xs">
          ${i + 1}
        </button>
      `;
    }
    
    return `
      <div class="flex items-center justify-between mt-4 text-xs">
        <div class="text-gray-500 dark:text-gray-400">
          Showing <span id="showingStart">${this.currentPage * this.pageSize + 1}</span> to <span id="showingEnd">${Math.min((this.currentPage + 1) * this.pageSize, this.filteredPOs.length > 0 ? this.filteredPOs.length : this.poData.length)}</span> of <span id="totalItems">${this.filteredPOs.length > 0 ? this.filteredPOs.length : this.poData.length}</span> orders
        </div>
        <div class="flex space-x-1">
          <button id="prevPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div id="pagination" class="flex space-x-1">
            ${paginationHTML}
          </div>
          <button id="nextPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage >= totalPages - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }
  
  // Format date safely
  formatDateSafely(dateValue) {
    if (!dateValue) return 'N/A';
    
    try {
      const date = new Date(dateValue);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'N/A';
      }
      
      // Format the date as MM/DD/YYYY
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  }
  
  renderSortIndicator(field) {
    if (this.sortField !== field) {
      return `
        <svg class="w-3 h-3 ml-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
        </svg>
      `;
    }
    
    if (this.sortDirection === 'asc') {
      return `
        <svg class="w-3 h-3 ml-1 text-gray-800 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
        </svg>
      `;
    } else {
      return `
        <svg class="w-3 h-3 ml-1 text-gray-800 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
    }
  }
  
  renderCharts() {
    if ((!this.poData || this.poData.length === 0) && 
        (!this.filteredPOs || this.filteredPOs.length === 0)) {
      return `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p class="text-xs">No data available for charts.</p>
        </div>
      `;
    }
    
    // Create a div for the analytics component to render into
    return `<div id="analyticsContainer"></div>`;
  }
  
  // Setup component-specific event handlers
  setupComponentEventHandlers() {
    // Search input
    const searchInput = this.container.querySelector('#vendorSearchInput');
    if (searchInput) {
      const newSearchInput = searchInput.cloneNode(true);
      searchInput.parentNode.replaceChild(newSearchInput, searchInput);
      newSearchInput.addEventListener('input', () => {
        this.searchTerm = newSearchInput.value.trim();
        this.applySearch();
      });
    }
    
    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clearVendorSearchBtn');
    if (clearSearchBtn) {
      const newClearBtn = clearSearchBtn.cloneNode(true);
      clearSearchBtn.parentNode.replaceChild(newClearBtn, clearSearchBtn);
      newClearBtn.addEventListener('click', () => {
        this.searchTerm = '';
        this.container.querySelector('#vendorSearchInput').value = '';
        this.applySearch();
      });
    }
    
    // Date range button
    const dateRangeBtn = this.container.querySelector('#dateRangeBtn');
    if (dateRangeBtn) {
      dateRangeBtn.addEventListener('click', () => {
        this.showDateRangeModal();
      });
    }
    
    // Clear date range filter buttons
    const clearDateRangeFilterBtn = this.container.querySelector('#clearDateRangeFilterBtn');
    if (clearDateRangeFilterBtn) {
      clearDateRangeFilterBtn.addEventListener('click', () => {
        this.clearDateRangeFilter();
      });
    }
    
    const clearVendorDateRangeFilterBtn = this.container.querySelector('#clearVendorDateRangeFilterBtn');
    if (clearVendorDateRangeFilterBtn) {
      clearVendorDateRangeFilterBtn.addEventListener('click', () => {
        this.clearDateRangeFilter();
      });
    }
    
    // Refresh button
    const refreshBtn = this.container.querySelector('#refreshDataBtn');
    if (refreshBtn) {
      const newRefreshBtn = refreshBtn.cloneNode(true);
      refreshBtn.parentNode.replaceChild(newRefreshBtn, refreshBtn);
      newRefreshBtn.addEventListener('click', async () => {
        await this.loadVendorData();
      });
    }
    
    // Load data button
    const loadDataBtn = this.container.querySelector('#loadVendorDataBtn');
    if (loadDataBtn) {
      const newLoadBtn = loadDataBtn.cloneNode(true);
      loadDataBtn.parentNode.replaceChild(newLoadBtn, loadDataBtn);
      newLoadBtn.addEventListener('click', async () => {
        await this.loadVendorData();
      });
    }
    
    // Sync button
    const syncBtn = this.container.querySelector('#vendorSyncBtn');
    if (syncBtn) {
      const newSyncBtn = syncBtn.cloneNode(true);
      syncBtn.parentNode.replaceChild(newSyncBtn, syncBtn);
      newSyncBtn.addEventListener('click', () => {
        this.showSyncModal();
      });
    }
    
    // Analytics button
    const analyticsBtn = this.container.querySelector('#vendorAnalyticsBtn');
    if (analyticsBtn) {
      const newAnalyticsBtn = analyticsBtn.cloneNode(true);
      analyticsBtn.parentNode.replaceChild(newAnalyticsBtn, analyticsBtn);
      newAnalyticsBtn.addEventListener('click', () => {
        this.chartView = true;
        this.vendorManagementView = false;
        this.render();
      });
    }
    
    // Table button
    const tableBtn = this.container.querySelector('#vendorTableBtn');
    if (tableBtn) {
      const newTableBtn = tableBtn.cloneNode(true);
      tableBtn.parentNode.replaceChild(newTableBtn, tableBtn);
      newTableBtn.addEventListener('click', () => {
        this.chartView = false;
        this.vendorManagementView = false;
        this.render();
      });
    }
    
    // Vendor Management button
    const vendorManagementBtn = this.container.querySelector('#vendorManagementBtn');
    if (vendorManagementBtn) {
      const newVendorManagementBtn = vendorManagementBtn.cloneNode(true);
      vendorManagementBtn.parentNode.replaceChild(newVendorManagementBtn, vendorManagementBtn);
      newVendorManagementBtn.addEventListener('click', () => {
        this.chartView = false;
        this.vendorManagementView = true;
        this.vendorCurrentPage = 0; // Reset to first page when switching to vendor view
        this.render();
      });
    }
    
    // Save Vendor Lead Times button
    const saveLeadTimesBtn = this.container.querySelector('#saveVendorLeadTimesBtn');
    if (saveLeadTimesBtn) {
      const newSaveBtn = saveLeadTimesBtn.cloneNode(true);
      saveLeadTimesBtn.parentNode.replaceChild(newSaveBtn, saveLeadTimesBtn);
      newSaveBtn.addEventListener('click', () => {
        this.saveVendorLeadTimes();
      });
    }
    
    // Settings button
    const settingsBtn = this.container.querySelector('#vendorSettingsBtn');
    if (settingsBtn) {
      const newSettingsBtn = settingsBtn.cloneNode(true);
      settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);
      newSettingsBtn.addEventListener('click', () => {
        this.showSettings();
      });
    }
    
    // Table sorting
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        this.sortTable(field);
      });
    });
    
    // Custom lead time inputs
    const leadTimeInputs = this.container.querySelectorAll('.vendor-custom-lead-time');
    leadTimeInputs.forEach(input => {
      input.addEventListener('change', (e) => {
        const index = parseInt(e.target.getAttribute('data-vendor-index'));
        const value = e.target.value ? parseInt(e.target.value) : null;
        if (!isNaN(index) && index >= 0 && index < this.vendorMetrics.length) {
          this.vendorMetrics[index].customLeadTime = value;
        }
      });
    });
    
    // Sync modal buttons
    this.setupSyncModalEventHandlers();
    
    // Pagination buttons
    this.setupPaginationEventHandlers();
    
    // Vendor pagination buttons
    this.setupVendorPaginationEventHandlers();
  }
  
  setupSyncModalEventHandlers() {
    try {
      // Close sync modal buttons
      const closeSyncModalBtn = document.getElementById('closeSyncModalBtn');
      if (closeSyncModalBtn) {
        closeSyncModalBtn.addEventListener('click', () => {
          this.hideSyncModal();
        });
      }
      
      // Add event listener to close on ESC key
      const handleEscKey = (e) => {
        if (e.key === 'Escape') {
          this.hideSyncModal();
          document.removeEventListener('keydown', handleEscKey);
        }
      };
      document.addEventListener('keydown', handleEscKey);
      
      // Also close on clicking outside the modal
      const syncModal = document.getElementById('syncModal');
      if (syncModal) {
        syncModal.addEventListener('click', (e) => {
          if (e.target === syncModal) {
            this.hideSyncModal();
          }
        });
      }
      
      // Sync All button (with password check)
      const syncAllBtn = document.getElementById('syncAllBtn');
      if (syncAllBtn) {
        syncAllBtn.addEventListener('click', () => {
          const passwordInput = document.getElementById('syncAllPassword');
          if (!passwordInput) {
            this.addNotification('Error: Password input field not found', 'error');
            return;
          }
          
          const password = passwordInput.value;
          if (password === '@Enventengineeringlts2002') {
            this.syncLeadTimesToAcumatica('all');
            this.hideSyncModal();
          } else {
            this.addNotification('Incorrect password. Sync all requires administrator password.', 'error');
          }
        });
      }
      
      // Sync by Batch button
      const syncBatchBtn = document.getElementById('syncBatchBtn');
      if (syncBatchBtn) {
        syncBatchBtn.addEventListener('click', () => {
          this.syncLeadTimesToAcumatica('batch');
          this.hideSyncModal();
        });
      }
      
      // Sync Specific Items button
      const syncSpecificBtn = document.getElementById('syncSpecificBtn');
      if (syncSpecificBtn) {
        syncSpecificBtn.addEventListener('click', () => {
          const vendorsTextEl = document.getElementById('syncSpecificVendors');
          if (!vendorsTextEl) {
            this.addNotification('Error: Could not find vendors input field', 'error');
            return;
          }
          
          const vendorsText = vendorsTextEl.value;
          if (!vendorsText.trim()) {
            this.addNotification('Please enter vendor names to sync.', 'warning');
            return;
          }
          
          const vendorIds = vendorsText.split(',').map(id => id.trim()).filter(id => id);
          if (vendorIds.length === 0) {
            this.addNotification('Please enter valid vendor names to sync.', 'warning');
            return;
          }
          
          if (vendorIds.length > 5) {
            this.addNotification('Limited update can only handle up to 5 vendors at a time.', 'warning');
            return;
          }
          
          this.syncLeadTimesToAcumatica('specific', vendorIds);
          this.hideSyncModal();
        });
      }
    } catch (error) {
      console.error('Error setting up sync modal handlers:', error);
      this.addNotification('Error setting up sync dialog. Please try again.', 'error');
      
      // Force close the modal if we can't set up handlers properly
      this.hideSyncModal();
    }
  }
  
  saveVendorLeadTimes() {
    // Update vendorMetrics with values from inputs
    const leadTimeInputs = this.container.querySelectorAll('.vendor-custom-lead-time');
    leadTimeInputs.forEach(input => {
      const index = parseInt(input.getAttribute('data-vendor-index'));
      const value = input.value ? parseInt(input.value) : null;
      if (!isNaN(index) && index >= 0 && index < this.vendorMetrics.length) {
        this.vendorMetrics[index].customLeadTime = value;
      }
    });
    
    // Save to local storage for persistence
    try {
      const vendorLeadTimes = this.vendorMetrics.map(vendor => ({
        vendorId: vendor.vendorId,
        customLeadTime: vendor.customLeadTime
      }));
      
      if (chrome?.storage?.local) {
        chrome.storage.local.set({ 'vendorLeadTimes': vendorLeadTimes }, () => {
          console.log('Vendor lead times saved to storage');
        });
      } else {
        localStorage.setItem('vendorLeadTimes', JSON.stringify(vendorLeadTimes));
      }
      
      this.addNotification('Vendor lead times saved successfully', 'success');
    } catch (error) {
      console.error('Error saving vendor lead times:', error);
      this.addNotification('Failed to save vendor lead times', 'error');
    }
  }
  
  async syncLeadTimesToAcumatica(mode = 'batch', specificVendors = []) {
    try {
      // Check connection to Acumatica
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        this.addNotification('Please connect to Acumatica first', 'error');
        document.getElementById('connectionButton')?.click();
        return;
      }
      
      // Prepare for sync
      this.isLoading = true;
      await this.render();
      
      // Make sure we have vendor metrics calculated
      if (!this.vendorMetrics || this.vendorMetrics.length === 0) {
        this.calculateVendorMetrics();
      }
      
      // Select vendors to sync based on mode
      let vendorsToSync = [];
      
      if (mode === 'all') {
        vendorsToSync = this.vendorMetrics;
        this.addNotification(`Syncing lead times for all ${vendorsToSync.length} vendors...`, 'info');
      } else if (mode === 'batch') {
        // Get first 20 vendors
        vendorsToSync = this.vendorMetrics.slice(0, 20);
        this.addNotification(`Syncing lead times for batch of ${vendorsToSync.length} vendors...`, 'info');
      } else if (mode === 'specific') {
        // Filter by provided vendor IDs/names
        vendorsToSync = this.vendorMetrics.filter(vendor => 
          specificVendors.some(id => 
            vendor.vendorId === id || 
            (vendor.vendorName && String(vendor.vendorName).toLowerCase().includes(String(id).toLowerCase()))
          )
        );
        this.addNotification(`Syncing lead times for ${vendorsToSync.length} specific vendors...`, 'info');
      }
      
      if (vendorsToSync.length === 0) {
        this.addNotification('No vendors selected for sync', 'warning');
        this.isLoading = false;
        await this.render();
        return;
      }
      
      // Use the dedicated synchronization component
      const vendorLeadTimeSync = new VendorLeadTimeSync();
      const results = await vendorLeadTimeSync.syncLeadTimes(this.poData, this.weekendsExcluded);
      
      if (results.success) {
        this.addNotification(
          `Successfully synced ${results.updatedVendors} vendors (${results.failedVendors} failed)`,
          'success'
        );
      } else {
        this.addNotification(`Sync failed: ${results.error}`, 'error');
      }
      
    } catch (error) {
      console.error('Error syncing lead times:', error);
      this.addNotification(`Error syncing lead times: ${error.message}`, 'error');
    } finally {
      this.isLoading = false;
      await this.render();
    }
  }
  
  showSyncModal() {
    let modalHTML = `
      <div id="syncModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-3 w-80 max-w-full max-h-[450px] overflow-y-auto">
          <div class="flex justify-between items-center mb-2 pb-1 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-800 z-10">
            <h3 class="text-md font-medium text-gray-800 dark:text-white">Sync Lead Times</h3>
            <button id="closeSyncModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="space-y-2">
            <!-- Sync All Option (with password) -->
            <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync All Vendors</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Update all vendor lead times to Acumatica.</p>
              <div class="mt-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Enter admin password:</label>
                <input type="password" id="syncAllPassword" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white">
              </div>
              <button id="syncAllBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                Sync All
              </button>
            </div>
            
            <!-- Sync by Confirmation -->
            <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync with Confirmation</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400">Sync first 20 vendors with confirmation.</p>
              <button id="syncBatchBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                Sync by Batch
              </button>
            </div>
            
            <!-- Sync Specific Vendors -->
            <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync Specific Vendors</h4>
              <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Enter up to 5 vendor names separated by commas.</p>
              <div class="mt-1">
                <textarea id="syncSpecificVendors" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white" rows="2" placeholder="e.g. ABC Corp, XYZ Inc"></textarea>
              </div>
              <button id="syncSpecificBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                Sync Specific Vendors
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Add modal to DOM
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;
    document.body.appendChild(modalContainer.firstElementChild);
    
    // Setup event handlers for sync modal and ensure they work
    this.setupSyncModalEventHandlers();
  }
  
  hideSyncModal() {
    try {
      const modal = document.getElementById('syncModal');
      if (modal) {
        modal.remove();
      }
    } catch (error) {
      console.error('Error removing sync modal:', error);
      
      // Fallback removal method if the standard way fails
      const allModals = document.querySelectorAll('#syncModal');
      allModals.forEach(m => {
        m.parentNode?.removeChild(m);
      });
    }
  }
  
  applySearch() {
    if (!this.poData) {
      this.poData = [];
    }
    
    if (!this.filteredPOs) {
      this.filteredPOs = [];
    }
    
    if (!this.searchTerm) {
      this.filteredPOs = [...this.poData];
    } else {
      // Ensure searchTerm is a string
      const term = String(this.searchTerm).toLowerCase();
      this.filteredPOs = this.poData.filter(po => {
        return (
          (po.OrderNbr?.value ? String(po.OrderNbr.value).toLowerCase().includes(term) : false) ||
          (po.VendorID?.value ? String(po.VendorID.value).toLowerCase().includes(term) : false)
        );
      });
    }
    
    // Maintain the current sort when searching
    const currentSortField = this.sortField;
    const currentSortDirection = this.sortDirection;
    
    // Sort the filtered results based on current sort settings
    this.sortTable(currentSortField); 
    
    // Restore sort direction if it was changed by sortTable
    if (this.sortDirection !== currentSortDirection) {
      this.sortDirection = currentSortDirection;
      this.sortTable(currentSortField);
    }
    
    // Reset to first page when searching
    this.currentPage = 0;
    this.render();
  }
  
  sortTable(field) {
    // If already sorting by this field, toggle direction
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = field === 'LastModifiedDateTime' || field === 'Date' || field === 'PromisedOn' ? 'desc' : 'asc';
      // Default to descending (newest first) for date fields, ascending for other fields
    }
    
    // Initialize filteredPOs if needed
    if (!this.filteredPOs) {
      this.filteredPOs = [];
    }
    
    const itemsToSort = this.searchTerm && this.filteredPOs.length > 0 ? this.filteredPOs : this.poData || [];
    
    // Ensure poData exists
    if (!this.poData) {
      this.poData = [];
    }
    
    console.log(`Sorting by ${field} in ${this.sortDirection} order`);
    
    // Sort the data
    itemsToSort.sort((a, b) => {
      let valA, valB;
      
      // Handle special cases for dates
      if (field === 'Date' || field === 'PromisedOn' || field === 'LastModifiedDateTime') {
        valA = a[field]?.value ? new Date(a[field].value).getTime() : 0;
        valB = b[field]?.value ? new Date(b[field].value).getTime() : 0;
        
        // If dates are equal and sorting by LastModifiedDateTime, use OrderNbr as secondary sort
        if (valA === valB && field === 'LastModifiedDateTime') {
          const orderA = a.OrderNbr?.value ? parseInt(a.OrderNbr.value.replace(/\D/g, '')) : 0;
          const orderB = b.OrderNbr?.value ? parseInt(b.OrderNbr.value.replace(/\D/g, '')) : 0;
          return this.sortDirection === 'asc' ? orderA - orderB : orderB - orderA;
        }
      } 
      // Handle numeric PO numbers (strip non-digits and convert to number)
      else if (field === 'OrderNbr') {
        valA = a[field]?.value ? parseInt(a[field].value.replace(/\D/g, '')) : 0;
        valB = b[field]?.value ? parseInt(b[field].value.replace(/\D/g, '')) : 0;
      }
      // Handle numeric fields
      else if (field === 'LeadTime') {
        valA = this.calculateLeadTime(a) || 0;
        valB = this.calculateLeadTime(b) || 0;
      }
      // Handle plain values
      else {
        valA = (a[field]?.value !== undefined && a[field]?.value !== null) ? 
          String(a[field].value).toLowerCase() : '';
        valB = (b[field]?.value !== undefined && b[field]?.value !== null) ? 
          String(b[field].value).toLowerCase() : '';
      }
      
      // Compare based on direction
      if (this.sortDirection === 'asc') {
        return valA > valB ? 1 : valA < valB ? -1 : 0;
      } else {
        return valA < valB ? 1 : valA > valB ? -1 : 0;
      }
    });
    
    if (this.searchTerm && this.filteredPOs.length > 0) {
      this.filteredPOs = itemsToSort;
    } else {
      this.poData = itemsToSort;
    }
    
    // Reset to first page after sorting
    this.currentPage = 0;
    this.render();
  }
  
  calculateLeadTime(po) {
    if (!po.Date?.value || !po.LastModifiedDateTime?.value) {
      return null;
    }
    
    const orderDate = new Date(po.Date.value);
    const receivedDate = new Date(po.LastModifiedDateTime.value);
    
    // Calculate raw lead time in days (received - created)
    let leadTime = Math.round((receivedDate - orderDate) / (1000 * 60 * 60 * 24));
    
    // Exclude weekends if enabled
    if (this.weekendsExcluded) {
      leadTime = this.calculateBusinessDays(orderDate, receivedDate);
    }
    
    return leadTime;
  }
  
  calculateBusinessDays(startDate, endDate) {
    let count = 0;
    const curDate = new Date(startDate.getTime());
    
    while (curDate <= endDate) {
      const dayOfWeek = curDate.getDay();
      // Skip weekends (0 = Sunday, 6 = Saturday)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        count++;
      }
      curDate.setDate(curDate.getDate() + 1);
    }
    
    return count;
  }
  
  getLeadTimeClass(leadTime) {
    if (leadTime === null) return 'text-gray-500 dark:text-gray-400';
    
    if (leadTime <= this.leadTimeThresholds.good) {
      return 'text-green-600 dark:text-green-400 font-medium';
    } else if (leadTime <= this.leadTimeThresholds.warning) {
      return 'text-yellow-600 dark:text-yellow-400 font-medium';
    } else {
      return 'text-red-600 dark:text-red-400 font-medium';
    }
  }
  
  async loadVendorData() {
    try {
      this.isLoading = true;
      await this.render();
      
      // Check if connected to Acumatica
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        this.addNotification('Please connect to Acumatica first', 'error');
        // Try to open the connection panel
        document.getElementById('connectionButton')?.click();
        this.isLoading = false;
        await this.render();
        return;
      }
      
      this.addNotification("Loading purchase order data from Acumatica...", "info");
      
      // Fetch real data from Acumatica
      const result = await this.fetchPurchaseOrders();
      
      if (result.success) {
        this.addNotification(`Successfully loaded ${this.poData.length} purchase orders`, "success");
        
        // Initialize vendor list if we have PO data
        if (this.poData && this.poData.length > 0) {
          await this.loadVendorList();
          this.calculateMetricsFromPOs();
        }
      } else {
        this.addNotification(`Error loading data: ${result.error}`, "error");
      }
      
    } catch (error) {
      console.error('Error loading vendor data:', error);
      this.addNotification(`Failed to load data: ${error.message}`, "error");
    } finally {
      this.isLoading = false;
      await this.render();
    }
  }
  
  async fetchPurchaseOrders() {
    try {
      const instance = connectionManager.connections.acumatica.instance;
      if (!instance) {
        return { success: false, error: 'No Acumatica instance URL found. Please reconnect.' };
      }
      
      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          if (!cookies || cookies.length === 0) {
            return { success: false, error: 'No authentication cookies found. Please log in to Acumatica first.' };
          }
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          console.warn('Error getting cookies:', cookieError);
          return { success: false, error: 'Failed to retrieve authentication cookies.' };
        }
      }

      // Show loading state with progress indicators
      this.loadingProgress = 10;
      this.render();
      
      // Build the API endpoint URL - using OData endpoint for PurchaseOrder with filter for Closed and Completed POs
      const apiUrl = `${instance}/entity/Default/20.200.001/PurchaseOrder?$filter=(Status eq 'Closed') or (Status eq 'Completed')`;
      
      // Using the fetch API to make the request
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        },
        credentials: 'include'
      });
      
      // Update loading progress
      this.loadingProgress = 30;
      this.render();
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API request failed with status ${response.status}: ${errorText}`);
      }
      
      // Parse the response
      const data = await response.json();
      console.log("Received PO data from Acumatica:", data);
      
      // Update loading progress
      this.loadingProgress = 60;
      this.render();
      
      // Process the data
      if (data && Array.isArray(data)) {
        // Start processing the data
        this.processingProgress = 10;
        this.render();
        
        // Store the raw data without modification
        this.poData = data;
        
        // Load vendor list for name lookup
        await this.loadVendorNamesFromJson();
        
        // Update processing progress
        this.processingProgress = 70;
        this.render();
        
        // Apply default sorting (newest received date first)
        this.sortTable('LastModifiedDateTime');
        
        // Calculate metrics based on the loaded data
        this.calculateMetricsFromPOs();
        
        // Complete loading
        this.loadingProgress = 100;
        this.processingProgress = 100;
        this.render();
        
        return { success: true, data: this.poData };
      } else {
        throw new Error('Invalid response format or no purchase orders found');
      }
    } catch (error) {
      console.error('Error fetching purchase orders:', error);
      
      // Fallback to sample data in case of error
      console.log('Using sample data due to error');
      this.addNotification("Using sample data due to API error", "info");
      this.generateSamplePOData();
      this.calculateMetricsFromPOs();
      return { success: true, data: this.poData };
    }
  }
  
  generateSamplePOData() {
    // Sample vendors
    const vendors = [
      "VENG000120", "VENG000184", "VENG000211", "VENG000078", "VENG000356",
      "VENG000019", "VENG000222", "VENG000143", "VENG000098", "VENG000275"
    ];
    
    // Generate 50 random POs
    const currentDate = new Date();
    this.poData = [];
    
    for (let i = 0; i < 50; i++) {
      // Random days in the past (1-180 days)
      const orderDaysAgo = Math.floor(Math.random() * 180) + 1;
      const orderDate = new Date(currentDate);
      orderDate.setDate(orderDate.getDate() - orderDaysAgo);
      
      // Random lead time (3-30 days)
      const leadTimeDays = Math.floor(Math.random() * 28) + 3;
      const promisedDate = new Date(orderDate);
      promisedDate.setDate(promisedDate.getDate() + leadTimeDays);
      
      // Randomly determine if delivered and when
      const isDelivered = orderDaysAgo > leadTimeDays || Math.random() > 0.3;
      let actualDeliveryDate = null;
      
      if (isDelivered) {
        // Random delivery accuracy (-5 to +10 days from promised)
        const deliveryAccuracy = Math.floor(Math.random() * 16) - 5;
        actualDeliveryDate = new Date(promisedDate);
        actualDeliveryDate.setDate(actualDeliveryDate.getDate() + deliveryAccuracy);
        
        // Ensure delivery date is not in the future
        if (actualDeliveryDate > currentDate) {
          actualDeliveryDate = new Date(currentDate);
        }
      }
      
      // Generate PO
      this.poData.push({
        OrderNbr: { value: `0${20000 + i}` },
        VendorID: { value: vendors[Math.floor(Math.random() * vendors.length)] },
        Date: { value: orderDate.toISOString() },
        PromisedOn: { value: promisedDate.toISOString() },
        ActualDelivery: isDelivered ? actualDeliveryDate.toISOString() : null,
        Status: { value: isDelivered ? "Closed" : "Open" },
        LineTotal: { value: Math.floor(Math.random() * 10000) / 100 }
      });
    }
  }
  
  calculateMetricsFromPOs() {
    if (!this.poData || this.poData.length === 0) return;
    
    // Filter to include only delivered POs for metrics
    const deliveredPOs = this.poData.filter(po => po.LastModifiedDateTime?.value);
    if (deliveredPOs.length === 0) return;
    
    // Calculate on-time percentage
    let onTimeCount = 0;
    let totalLeadTime = 0;
    let lateCount = 0;
    
    const vendorPerformance = {};
    
    deliveredPOs.forEach(po => {
      const promisedDate = po.PromisedOn?.value ? new Date(po.PromisedOn.value) : null;
      const actualDate = po.LastModifiedDateTime?.value ? new Date(po.LastModifiedDateTime.value) : null;
      const orderDate = po.Date?.value ? new Date(po.Date.value) : null;
      
      if (!promisedDate || !actualDate || !orderDate) return;
      
      // Calculate lead time
      const leadTime = Math.round((actualDate - orderDate) / (1000 * 60 * 60 * 24));
      if (!isNaN(leadTime)) {
        totalLeadTime += leadTime;
      
        // Check if on time
        const isOnTime = actualDate <= promisedDate;
        if (isOnTime) {
          onTimeCount++;
        } else {
          lateCount++;
        }
        
        // Track vendor performance
        const vendorId = po.VendorID?.value;
        if (vendorId) {
          if (!vendorPerformance[vendorId]) {
            vendorPerformance[vendorId] = {
              id: vendorId,
              name: this.getVendorName(vendorId),
              onTime: 0,
              late: 0,
              totalLeadTime: 0,
              orders: 0
            };
          }
          
          vendorPerformance[vendorId].orders++;
          vendorPerformance[vendorId].totalLeadTime += leadTime;
          
          if (isOnTime) {
            vendorPerformance[vendorId].onTime++;
          } else {
            vendorPerformance[vendorId].late++;
          }
        }
      }
    });
    
    // Calculate top vendors
    const topVendors = Object.values(vendorPerformance)
      .filter(v => v.orders > 0)
      .map(v => ({
        id: v.id,
        name: v.name,
        onTimePercent: Math.round((v.onTime / v.orders) * 100),
        averageLeadTime: Math.round(v.totalLeadTime / v.orders),
        orders: v.orders
      }))
      .sort((a, b) => {
        // Sort first by on-time percentage, then by lead time if equal
        if (b.onTimePercent !== a.onTimePercent) {
          return b.onTimePercent - a.onTimePercent;
        }
        return a.averageLeadTime - b.averageLeadTime;
      })
      .slice(0, 5);
    
    // Store calculated metrics with fallback to 0 for invalid values
    const validOnTimePercentage = deliveredPOs.length > 0 ? Math.round((onTimeCount / deliveredPOs.length) * 100) : 0;
    const validAverageLeadTime = totalLeadTime > 0 && deliveredPOs.length > 0 ? Math.round(totalLeadTime / deliveredPOs.length) : 0;
    
    this.vendorData = {
      onTimeDelivery: isNaN(validOnTimePercentage) ? 0 : validOnTimePercentage,
      averageLeadTime: isNaN(validAverageLeadTime) ? 0 : validAverageLeadTime,
      lateDeliveries: lateCount || 0,
      topVendors: topVendors.length > 0 ? topVendors : []
    };
    
    console.log("Calculated metrics:", this.vendorData);
  }
  
  async renderApexCharts() {
    try {
      // Determine which data to use (filtered or all)
      const dataToUse = this.dateRangeFilter.enabled && this.filteredPOs.length > 0 
        ? this.filteredPOs 
        : this.poData;
        
      if (!dataToUse || dataToUse.length === 0) {
        console.log("No data available for charts");
        return;
      }
      
      // Ensure ApexCharts is loaded before proceeding
      await loadApexCharts();
      
      if (!window.ApexCharts) {
        console.error('ApexCharts failed to load');
        this.addNotification('Failed to load charts library', 'error');
        return;
      }
      
      // Wait for DOM to be ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 1. Lead Time Distribution Chart
      this.renderLeadTimeChart(dataToUse);
      
      // 2. On-Time vs Late Deliveries Chart
      this.renderDeliveryStatusChart(dataToUse);
      
      // 3. Top Vendors Performance Chart
      this.renderVendorPerformanceChart(dataToUse);
      
      // 4. Monthly Volume Chart
      this.renderMonthlyVolumeChart(dataToUse);
      
      // Add notification about chart status
      if (this.dateRangeFilter.enabled) {
        this.addNotification(`Charts showing data from ${this.filteredPOs.length} POs within date range`, 'info');
      }
    } catch (error) {
      console.error('Error rendering charts:', error);
      this.addNotification('Error rendering charts: ' + error.message, 'error');
    }
  }
  
  renderLeadTimeChart(data) {
    const leadTimeElement = document.getElementById('leadTimeChart');
    if (!leadTimeElement) return;
    
    // Remove any existing chart
    leadTimeElement.innerHTML = '';
    
    // Get lead times from delivered POs
    const leadTimes = data
      .filter(po => po.LastModifiedDateTime?.value)
      .map(po => this.calculateLeadTime(po))
      .filter(leadTime => leadTime !== null)
      .sort((a, b) => a - b);
    
    if (leadTimes.length === 0) {
      leadTimeElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No lead time data available</p></div>';
      return;
    }
    
    // Determine appropriate bucket size based on data range and count
    const minLeadTime = leadTimes[0];
    const maxLeadTime = leadTimes[leadTimes.length - 1];
    const leadTimeRange = maxLeadTime - minLeadTime;
    
    // Use adaptive bucket size based on the range
    let bucketSize;
    let maxDisplayValue;
    
    if (leadTimeRange <= 50) {
      bucketSize = 5; // Small range: 5-day buckets
      maxDisplayValue = null; // No cap
    } else if (leadTimeRange <= 100) {
      bucketSize = 10; // Medium range: 10-day buckets
      maxDisplayValue = null; // No cap
    } else if (leadTimeRange <= 200) {
      bucketSize = 20; // Large range: 20-day buckets
      maxDisplayValue = 200; // Cap at 200
    } else {
      bucketSize = 30; // Very large range: 30-day buckets
      maxDisplayValue = 300; // Cap at 300
    }
    
    // Create buckets with adaptive sizing
    const buckets = {};
    let overflowCount = 0;
    
    leadTimes.forEach(lt => {
      if (maxDisplayValue && lt > maxDisplayValue) {
        // Count values above the max display value
        overflowCount++;
      } else {
        // Calculate bucket based on the bucket size
        const bucketIndex = Math.floor(lt / bucketSize) * bucketSize;
        const bucketLabel = `${bucketIndex}-${bucketIndex + bucketSize - 1}`;
        buckets[bucketLabel] = (buckets[bucketLabel] || 0) + 1;
      }
    });
    
    // If we have overflow values, add them as a final bucket
    if (overflowCount > 0) {
      buckets[`${maxDisplayValue}+`] = overflowCount;
    }
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'Number of POs',
        data: Object.values(buckets)
      }],
      chart: {
        type: 'bar',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      plotOptions: {
        bar: {
          distributed: true,
          borderRadius: 2,
          columnWidth: '90%'
        }
      },
      dataLabels: {
        enabled: false
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      xaxis: {
        categories: Object.keys(buckets),
        labels: {
          style: {
            fontSize: '11px'
          },
          rotate: -45,
          trim: true,
          // Only show a subset of labels when there are many buckets
          formatter: function(value, timestamp, opts) {
            const allCategories = opts.w.globals.labels;
            const categoryIndex = allCategories.indexOf(value);
            
            // Determine how many labels to show based on total number of buckets
            if (allCategories.length <= 10) {
              return value; // Show all labels when fewer buckets
            } else {
              // For many buckets, show only some labels to avoid overcrowding
              return categoryIndex % Math.ceil(allCategories.length / 10) === 0 ? value : '';
            }
          }
        },
        title: {
          text: 'Lead Time (Days)'
        }
      },
      yaxis: {
        title: {
          text: 'Number of POs'
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light',
        x: {
          formatter: function(value) {
            return `Lead Time: ${value} days`;
          }
        }
      },
      colors: ['#3b82f6', '#6366f1', '#8b5cf6', '#d946ef', '#ec4899', '#f43f5e']
    };
    
    new ApexCharts(leadTimeElement, options).render();
  }
  
  renderDeliveryStatusChart(data) {
    const element = document.getElementById('deliveryStatusChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    // Count on-time vs late deliveries
    const deliveredPOs = data.filter(po => po.LastModifiedDateTime?.value && po.PromisedOn?.value);
    
    if (deliveredPOs.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No delivery status data available</p></div>';
      return;
    }
    
    let early = 0;
    let onTime = 0;
    let late = 0;
    
    deliveredPOs.forEach(po => {
      const status = this.getDeliveryStatus(po);
      if (status === 'Early') early++;
      else if (status === 'OnTime') onTime++;
      else if (status === 'Late') late++;
    });
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [early, onTime, late],
      chart: {
        type: 'donut',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      labels: ['Early', 'On-Time', 'Late'],
      colors: ['#10b981', '#3b82f6', '#ef4444'],
      legend: {
        position: 'bottom'
      },
      dataLabels: {
        enabled: true,
        formatter: function(val, opts) {
          return opts.w.config.series[opts.seriesIndex] + ' POs';
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light'
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 200
          },
          legend: {
            position: 'bottom'
          }
        }
      }]
    };
    
    new ApexCharts(element, options).render();
  }
  
  renderVendorPerformanceChart(data) {
    const element = document.getElementById('vendorPerformanceChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    // Calculate vendor metrics using the data
    this.calculateVendorMetrics(this.dateRangeFilter.enabled && this.filteredPOs.length > 0);
    
    // Get top vendors by on-time percentage
    let topVendors = [...this.vendorMetrics]
      .filter(v => v.orderCount >= 3) // Only include vendors with at least 3 orders
      .sort((a, b) => b.onTimePercent - a.onTimePercent)
      .slice(0, 5);  // Get top 5
      
    if (topVendors.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No vendor performance data available</p></div>';
      return;
    }
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'On-Time %',
        data: topVendors.map(v => Math.round(v.onTimePercent))
      }],
      chart: {
        type: 'bar',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      plotOptions: {
        bar: {
          horizontal: true,
          barHeight: '70%',
          distributed: true,
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val + '%';
        },
        textAnchor: 'start',
        offsetX: 5,
        style: {
          fontSize: '12px',
          colors: ['#fff']
        }
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4
      },
      xaxis: {
        categories: topVendors.map(v => {
          // Truncate long names
          return v.vendorName.length > 15 ? v.vendorName.substring(0, 15) + '...' : v.vendorName;
        }),
        labels: {
          style: {
            fontSize: '11px'
          }
        },
        max: 100
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light',
        y: {
          title: {
            formatter: function(seriesName, opts) {
              return topVendors[opts.dataPointIndex].vendorName + ' - ' + seriesName;
            }
          }
        }
      },
      colors: ['#3b82f6', '#6366f1', '#8b5cf6', '#d946ef', '#ec4899']
    };
    
    new ApexCharts(element, options).render();
  }
  
  renderMonthlyVolumeChart(data) {
    const element = document.getElementById('monthlyVolumeChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    if (data.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No monthly volume data available</p></div>';
      return;
    }
    
    // Group POs by month
    const monthlyVolume = {};
    
    // Determine years to include based on data
    const years = new Set();
    data.forEach(po => {
      if (po.Date?.value) {
        const orderDate = new Date(po.Date.value);
        years.add(orderDate.getFullYear());
      }
    });
    
    // Sort years
    const sortedYears = Array.from(years).sort();
    
    // If we have too many years, use just the most recent 2 years
    const yearsToUse = sortedYears.length > 2 ? sortedYears.slice(-2) : sortedYears;
    
    // Create labels for each month in each year
    const labels = [];
    yearsToUse.forEach(year => {
      for (let month = 0; month < 12; month++) {
        const date = new Date(year, month, 1);
        const monthName = date.toLocaleString('default', { month: 'short' });
        labels.push(`${monthName} ${year}`);
        monthlyVolume[`${monthName} ${year}`] = 0;
      }
    });
    
    // Count POs by month
    data.forEach(po => {
      if (!po.Date?.value) return;
      
      const orderDate = new Date(po.Date.value);
      const year = orderDate.getFullYear();
      
      if (yearsToUse.includes(year)) {
        const monthName = orderDate.toLocaleString('default', { month: 'short' });
        const label = `${monthName} ${year}`;
        monthlyVolume[label] = (monthlyVolume[label] || 0) + 1;
      }
    });
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'PO Count',
        data: labels.map(label => monthlyVolume[label] || 0)
      }],
      chart: {
        height: 250,
        type: 'line',
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      stroke: {
        curve: 'smooth',
        width: 3
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4
      },
      xaxis: {
        categories: labels,
        labels: {
          rotate: -45,
          style: {
            fontSize: '10px'
          }
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light'
      },
      markers: {
        size: 5,
        colors: ['#3b82f6'],
        strokeWidth: 0
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          gradientToColors: ['#8b5cf6'],
          shadeIntensity: 1,
          type: 'horizontal',
          opacityFrom: 1,
          opacityTo: 1
        }
      }
    };
    
    new ApexCharts(element, options).render();
  }
  
  showSettings() {
    let settingsHTML = `
      <div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-3 w-80 max-w-full max-h-[450px] overflow-y-auto">
          <div class="flex justify-between items-center mb-2 pb-1 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-800 z-10">
            <h3 class="text-md font-medium text-gray-800 dark:text-white">Settings</h3>
            <button id="closeSettingsModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="space-y-2">
            <!-- Lead Time Thresholds -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-2">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Lead Time Thresholds</h4>
              
              <div class="mb-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Good Lead Time (days):</label>
                <input type="number" id="goodLeadTime" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white" 
                  value="${this.leadTimeThresholds.good}" min="1" max="30">
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Orders under this threshold marked "Early"</p>
              </div>
              
              <div class="mb-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Warning Lead Time (days):</label>
                <input type="number" id="warningLeadTime" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white" 
                  value="${this.leadTimeThresholds.warning}" min="1" max="60">
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Orders under this threshold marked "OnTime"</p>
              </div>
              
              <div class="flex items-center mt-1">
                <input type="checkbox" id="excludeWeekends" class="h-3 w-3 text-blue-600 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600" 
                  ${this.weekendsExcluded ? 'checked' : ''}>
                <label for="excludeWeekends" class="ml-1 text-xs text-gray-600 dark:text-gray-400">
                  Exclude weekends when calculating lead times
                </label>
              </div>
            </div>
            
            <!-- Display Settings -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-2">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Display Settings</h4>
              
              <div class="mb-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Default View:</label>
                <select id="defaultView" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white">
                  <option value="table" ${!this.chartView && !this.vendorManagementView ? 'selected' : ''}>Main Table (POs)</option>
                  <option value="vendor" ${this.vendorManagementView ? 'selected' : ''}>Vendor Management</option>
                  <option value="charts" ${this.chartView ? 'selected' : ''}>Analytics Charts</option>
                </select>
              </div>
              
              <div class="mb-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Default Sort Field:</label>
                <select id="defaultSortField" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white">
                  <option value="Date" ${this.sortField === 'Date' ? 'selected' : ''}>Date</option>
                  <option value="OrderNbr" ${this.sortField === 'OrderNbr' ? 'selected' : ''}>PO Number</option>
                  <option value="VendorID" ${this.sortField === 'VendorID' ? 'selected' : ''}>Vendor</option>
                  <option value="Status" ${this.sortField === 'Status' ? 'selected' : ''}>Status</option>
                  <option value="LeadTime" ${this.sortField === 'LeadTime' ? 'selected' : ''}>Lead Time</option>
                </select>
              </div>
              
              <div class="mb-1">
                <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Sort Direction:</label>
                <select id="defaultSortDirection" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white">
                  <option value="asc" ${this.sortDirection === 'asc' ? 'selected' : ''}>Ascending</option>
                  <option value="desc" ${this.sortDirection === 'desc' ? 'selected' : ''}>Descending</option>
                </select>
              </div>
            </div>
            
            <!-- Export Options -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-2">
              <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Export Options</h4>
              
              <div class="flex flex-col space-y-1">
                <button id="exportPODataBtn" class="px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full flex items-center justify-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                  </svg>
                  Export PO Data (CSV)
                </button>
                
                <button id="exportVendorMetricsBtn" class="px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full flex items-center justify-center">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                  </svg>
                  Export Vendor Metrics
                </button>
              </div>
            </div>
          </div>
          
          <div class="flex justify-end space-x-2 mt-2 pt-1 border-t border-gray-200 dark:border-gray-700 sticky bottom-0 bg-white dark:bg-gray-800 z-10">
            <button id="cancelSettingsBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-xs">
              Cancel
            </button>
            <button id="saveSettingsBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs">
              Save
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Add settings modal to DOM
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = settingsHTML;
    document.body.appendChild(modalContainer.firstElementChild);
    
    // Setup event handlers for settings modal
    this.setupSettingsModalEventHandlers();
  }
  
  setupSettingsModalEventHandlers() {
    // Close button
    const closeBtn = document.getElementById('closeSettingsModalBtn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // Cancel button
    const cancelBtn = document.getElementById('cancelSettingsBtn');
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        this.hideSettingsModal();
      });
    }
    
    // Save button
    const saveBtn = document.getElementById('saveSettingsBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        this.saveSettings();
        this.hideSettingsModal();
      });
    }
    
    // Export PO Data button
    const exportPOBtn = document.getElementById('exportPODataBtn');
    if (exportPOBtn) {
      exportPOBtn.addEventListener('click', () => {
        this.exportPOData();
      });
    }
    
    // Export Vendor Metrics button
    const exportVendorBtn = document.getElementById('exportVendorMetricsBtn');
    if (exportVendorBtn) {
      exportVendorBtn.addEventListener('click', () => {
        this.exportVendorMetrics();
      });
    }
    
    // Export Charts button
    const exportChartsBtn = document.getElementById('exportChartsBtn');
    if (exportChartsBtn) {
      exportChartsBtn.addEventListener('click', () => {
        this.exportCharts();
      });
    }
  }
  
  hideSettingsModal() {
    const modal = document.getElementById('settingsModal');
    if (modal) {
      modal.remove();
    }
  }
  
  saveSettings() {
    try {
      // Get values from form
      const goodLeadTime = parseInt(document.getElementById('goodLeadTime').value) || 7;
      const warningLeadTime = parseInt(document.getElementById('warningLeadTime').value) || 14;
      const excludeWeekends = document.getElementById('excludeWeekends').checked;
      const defaultView = document.getElementById('defaultView').value;
      const defaultSortField = document.getElementById('defaultSortField').value;
      const defaultSortDirection = document.getElementById('defaultSortDirection').value;
      
      // Update component settings
      this.leadTimeThresholds = {
        good: goodLeadTime,
        warning: warningLeadTime
      };
      
      this.weekendsExcluded = excludeWeekends;
      this.sortField = defaultSortField;
      this.sortDirection = defaultSortDirection;
      
      // Update view based on selection
      if (defaultView === 'table') {
        this.chartView = false;
        this.vendorManagementView = false;
      } else if (defaultView === 'vendor') {
        this.chartView = false;
        this.vendorManagementView = true;
      } else if (defaultView === 'charts') {
        this.chartView = true;
        this.vendorManagementView = false;
      }
      
      // Save to local storage
      this.saveSettingsToStorage();
      
      // Re-render with new settings
      this.render();
      
      // Re-render charts if needed
      if (this.chartView) {
        this.renderApexCharts();
      }
      
      this.addNotification('Settings saved successfully', 'success');
    } catch (error) {
      console.error('Error saving settings:', error);
      this.addNotification('Failed to save settings', 'error');
    }
  }
  
  saveSettingsToStorage() {
    try {
      const settings = {
        leadTimeThresholds: this.leadTimeThresholds,
        weekendsExcluded: this.weekendsExcluded,
        sortField: this.sortField,
        sortDirection: this.sortDirection,
        chartView: this.chartView,
        vendorManagementView: this.vendorManagementView,
        dateRangeFilter: this.dateRangeFilter
      };
      
      if (chrome?.storage?.local) {
        chrome.storage.local.set({ 'vendorMetricsSettings': settings });
      } else {
        localStorage.setItem('vendorMetricsSettings', JSON.stringify(settings));
      }
    } catch (error) {
      console.error('Error saving settings to storage:', error);
    }
  }
  
  exportPOData() {
    try {
      if (!this.poData || this.poData.length === 0) {
        this.addNotification('No purchase order data to export', 'warning');
        return;
      }
      
      // Create CSV content
      let csvContent = "PO Number,Vendor,Created Date,Promised Date,Received Date,Status,Lead Time,Lead Status\n";
      
      // Initialize filteredPOs if needed
      if (!this.filteredPOs) {
        this.filteredPOs = [];
      }
      
      // Use filtered POs if search is active
      const dataToExport = this.filteredPOs.length > 0 ? this.filteredPOs : this.poData;
      
      dataToExport.forEach(po => {
        const vendorName = this.getVendorName(po.VendorID?.value) || 'N/A';
        const poNumber = po.OrderNbr?.value || 'N/A';
        const createdDate = this.formatDate(po.Date?.value) || 'N/A';
        const promisedDate = this.formatDate(po.PromisedOn?.value) || 'N/A';
        const receivedDate = this.formatDate(po.LastModifiedDateTime?.value) || 'N/A';
        const status = po.Status?.value || 'N/A';
        const leadTime = this.calculateLeadTime(po);
        const leadStatus = this.getLeadStatusText(leadTime);
        
        // Escape values for CSV
        const escapeForCSV = (value) => {
          if (!value) return '';
          // If the value contains commas or quotes, wrap it in quotes and escape any quotes
          const needsQuoting = value.includes(',') || value.includes('"');
          return needsQuoting ? `"${value.replace(/"/g, '""')}"` : value;
        };
        
        csvContent += `${escapeForCSV(poNumber)},${escapeForCSV(vendorName)},${escapeForCSV(createdDate)},` +
                      `${escapeForCSV(promisedDate)},${escapeForCSV(receivedDate)},${escapeForCSV(status)},` +
                      `${leadTime !== null ? leadTime : 'N/A'},${escapeForCSV(leadStatus)}\n`;
      });
      
      // Create and trigger download
      this.downloadCSV(csvContent, 'vendor-po-data.csv');
      this.addNotification(`Exported ${dataToExport.length} purchase orders`, 'success');
    } catch (error) {
      console.error('Error exporting PO data:', error);
      this.addNotification('Failed to export PO data', 'error');
    }
  }
  
  exportVendorMetrics() {
    try {
      // Make sure vendor metrics are calculated
      if (!this.vendorMetrics || this.vendorMetrics.length === 0) {
        this.calculateVendorMetrics();
      }
      
      if (this.vendorMetrics.length === 0) {
        this.addNotification('No vendor metrics to export', 'warning');
        return;
      }
      
      // Create CSV content
      let csvContent = "Vendor,Date Range,PO Count,Calculated Lead Time,Custom Lead Time,Lead Status\n";
      
      this.vendorMetrics.forEach(vendor => {
        const vendorName = vendor.vendorName || 'N/A';
        const dateRange = `${this.formatDate(vendor.firstOrderDate)} - ${this.formatDate(vendor.lastOrderDate)}`;
        const orderCount = vendor.orderCount || 0;
        const calcLeadTime = vendor.avgLeadTime !== null ? vendor.avgLeadTime : 'N/A';
        const customLeadTime = vendor.customLeadTime !== null ? vendor.customLeadTime : 'N/A';
        const leadStatus = this.getLeadStatusText(vendor.avgLeadTime);
        
        // Escape values for CSV
        const escapeForCSV = (value) => {
          if (!value) return '';
          const needsQuoting = value.toString().includes(',') || value.toString().includes('"');
          return needsQuoting ? `"${value.toString().replace(/"/g, '""')}"` : value;
        };
        
        csvContent += `${escapeForCSV(vendorName)},${escapeForCSV(dateRange)},${orderCount},` +
                      `${calcLeadTime},${customLeadTime},${escapeForCSV(leadStatus)}\n`;
      });
      
      // Create and trigger download
      this.downloadCSV(csvContent, 'vendor-metrics.csv');
      this.addNotification(`Exported ${this.vendorMetrics.length} vendor metrics`, 'success');
    } catch (error) {
      console.error('Error exporting vendor metrics:', error);
      this.addNotification('Failed to export vendor metrics', 'error');
    }
  }
  
  exportCharts() {
    try {
      if (!window.ApexCharts) {
        this.addNotification('Chart library not loaded. Cannot export charts.', 'error');
        return;
      }
      
      // Switch to chart view if not already
      const wasChartView = this.chartView;
      if (!wasChartView) {
        this.chartView = true;
        this.render();
        this.renderApexCharts();
      }
      
      // Get chart instances
      const chartIds = [
        'leadTimeChart', 
        'deliveryStatusChart', 
        'vendorPerformanceChart', 
        'monthlyVolumeChart'
      ];
      
      let exportPromises = [];
      
      // Export each chart
      chartIds.forEach((chartId, index) => {
        const chartElement = document.getElementById(chartId);
        if (chartElement) {
          const chartInstance = ApexCharts.getChartByID(chartId);
          if (chartInstance) {
            // Export as PNG and trigger download
            exportPromises.push(
              chartInstance.dataURI().then(({ imgURI }) => {
                const link = document.createElement('a');
                link.href = imgURI;
                link.download = `vendor-${chartId}.png`;
                link.click();
              })
            );
          }
        }
      });
      
      // Wait for all exports to complete
      Promise.all(exportPromises).then(() => {
        this.addNotification('Charts exported successfully', 'success');
        
        // Restore previous view if needed
        if (!wasChartView) {
          this.chartView = false;
          this.render();
        }
      });
    } catch (error) {
      console.error('Error exporting charts:', error);
      this.addNotification('Failed to export charts', 'error');
    }
  }
  
  downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  
  calculateVendorMetrics(useFilteredPOs = false) {
    // Determine which PO data to use
    const poDataToUse = useFilteredPOs && this.filteredPOs && this.filteredPOs.length > 0 
      ? this.filteredPOs 
      : this.poData;
    
    if (!poDataToUse || poDataToUse.length === 0) {
      this.vendorMetrics = [];
      return;
    }
    
    // Create a map to group POs by vendor
    const vendorMap = new Map();
    
    // Process each PO
    poDataToUse.forEach(po => {
      const vendorId = po.VendorID?.value;
      if (!vendorId) return;
      
      const vendorName = this.getVendorName(vendorId) || vendorId;
      const leadTime = this.calculateLeadTime(po);
      const orderDate = po.Date?.value ? new Date(po.Date.value) : null;
      const deliveryStatus = this.getDeliveryStatus(po);
      
      if (!vendorMap.has(vendorId)) {
        vendorMap.set(vendorId, {
          vendorId,
          vendorName,
          orders: [],
          leadTimes: [],
          firstOrderDate: orderDate,
          lastOrderDate: orderDate,
          earlyCount: 0,
          onTimeCount: 0,
          lateCount: 0
        });
      }
      
      const vendorData = vendorMap.get(vendorId);
      
      // Add order to list
      vendorData.orders.push(po);
      
      // Add lead time if valid
      if (leadTime !== null) {
        vendorData.leadTimes.push(leadTime);
      }
      
      // Count delivery statuses
      if (deliveryStatus === 'Early') {
        vendorData.earlyCount++;
      } else if (deliveryStatus === 'OnTime') {
        vendorData.onTimeCount++;
      } else if (deliveryStatus === 'Late') {
        vendorData.lateCount++;
      }
      
      // Update date range
      if (orderDate) {
        if (!vendorData.firstOrderDate || orderDate < vendorData.firstOrderDate) {
          vendorData.firstOrderDate = orderDate;
        }
        if (!vendorData.lastOrderDate || orderDate > vendorData.lastOrderDate) {
          vendorData.lastOrderDate = orderDate;
        }
      }
    });
    
    // Calculate metrics for each vendor
    this.vendorMetrics = Array.from(vendorMap.values()).map(vendorData => {
      // Calculate average lead time
      const avgLeadTime = vendorData.leadTimes.length > 0
        ? Math.round(vendorData.leadTimes.reduce((sum, time) => sum + time, 0) / vendorData.leadTimes.length)
        : null;
      
      // Calculate on-time percentage (Early + OnTime)
      const totalDelivered = vendorData.earlyCount + vendorData.onTimeCount + vendorData.lateCount;
      const onTimePercent = totalDelivered > 0
        ? ((vendorData.earlyCount + vendorData.onTimeCount) / totalDelivered) * 100
        : 0;
      
      return {
        vendorId: vendorData.vendorId,
        vendorName: vendorData.vendorName,
        orderCount: vendorData.orders.length,
        avgLeadTime,
        customLeadTime: null, // Will be set by user
        firstOrderDate: vendorData.firstOrderDate,
        lastOrderDate: vendorData.lastOrderDate,
        earlyCount: vendorData.earlyCount,
        onTimeCount: vendorData.onTimeCount,
        lateCount: vendorData.lateCount,
        onTimePercent
      };
    });
    
    // Sort vendors by name
    this.vendorMetrics.sort((a, b) => a.vendorName.localeCompare(b.vendorName));
  }
  
  async loadVendorList() {
    try {
      // This method extracts unique vendor information from PO data
      if (!this.poData || this.poData.length === 0) {
        console.log("No PO data available to extract vendor list");
        return [];
      }
      
      // Create a map to store unique vendors with their metrics
      const vendorMap = new Map();
      
      // Process each PO to extract vendor information
      this.poData.forEach(po => {
        const vendorId = po.VendorID?.value;
        if (!vendorId) return;
        
        // If vendor is not in the map yet, add it
        if (!vendorMap.has(vendorId)) {
          vendorMap.set(vendorId, {
            id: vendorId,
            orderCount: 0,
            deliveredCount: 0,
            totalLeadTime: 0,
            customLeadTime: null // Will be editable by user
          });
        }
        
        // Update vendor metrics
        const vendor = vendorMap.get(vendorId);
        vendor.orderCount++;
        
        // If PO has actual delivery, update lead time metrics
        if (po.ActualDelivery) {
          vendor.deliveredCount++;
          
          // Calculate lead time for this PO
          const orderDate = new Date(po.Date?.value);
          const deliveryDate = new Date(po.ActualDelivery);
          const leadTimeDays = Math.round((deliveryDate - orderDate) / (1000 * 60 * 60 * 24));
          
          vendor.totalLeadTime += leadTimeDays;
        }
      });
      
      // Convert map to array and calculate average lead times
      const vendorList = Array.from(vendorMap.values()).map(vendor => {
        // Calculate average lead time if there are delivered orders
        const avgLeadTime = vendor.deliveredCount > 0 
          ? Math.round(vendor.totalLeadTime / vendor.deliveredCount) 
          : null;
        
        return {
          ...vendor,
          avgLeadTime
        };
      });
      
      // Sort by vendor ID - ensure IDs are strings before comparing
      vendorList.sort((a, b) => {
        const idA = String(a.id || '');
        const idB = String(b.id || '');
        return idA.localeCompare(idB);
      });
      
      // Store the result
      this.vendorList = vendorList;
      
      return vendorList;
    } catch (error) {
      console.error('Error loading vendor list:', error);
      this.addNotification(`Error loading vendor list: ${error.message}`, 'error');
      return [];
    }
  }

  // Add a safe vendor name getter method that handles null values
  getVendorName(vendorId) {
    // If no vendorId or it's null/undefined, return N/A
    if (!vendorId) return 'N/A';
    
    // Return the vendor ID as fallback (better than nothing)
    return vendorId;
  }
  
  // Add safeguards to date formatting
  formatDate(dateStr) {
    if (!dateStr) return 'N/A';
    
    try {
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return 'Invalid Date';
      
      return date.toLocaleDateString();
    } catch (e) {
      return 'Invalid Date';
    }
  }
  
  // Add placeholder methods for status classes if they don't exist
  getStatusClass(status) {
    if (!status) return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    
    // Ensure status is a string before calling toLowerCase()
    const statusStr = String(status);
    
    switch (statusStr.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'closed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  }
  
  getLeadStatusClass(leadTime) {
    if (leadTime === null || leadTime === undefined) return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    
    if (leadTime <= this.leadTimeThresholds?.good || 15) {
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    } else if (leadTime <= this.leadTimeThresholds?.warning || 30) {
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    } else {
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    }
  }
  
  getLeadStatusText(leadTime) {
    if (leadTime === null || leadTime === undefined) return 'Unknown';
    
    if (leadTime <= this.leadTimeThresholds?.good || 15) {
      return 'Early';
    } else if (leadTime <= this.leadTimeThresholds?.warning || 30) {
      return 'OnTime';
    } else {
      return 'Late';
    }
  }

  // Load vendor names from Vendorlist.json
  async loadVendorNamesFromJson() {
    try {
      // Initialize vendor map
      this.vendorMap = {};
      
      // Fetch the Vendorlist.json file
      const response = await fetch('json/Vendorlist.json');
      
      if (!response.ok) {
        console.error('Failed to load Vendorlist.json');
        return;
      }
      
      const data = await response.json();
      
      // Process vendor data and create a mapping from ID to name
      if (Array.isArray(data)) {
        data.forEach(vendor => {
          if (vendor.VendorID && vendor.VendorID.value && vendor.VendorName && vendor.VendorName.value) {
            this.vendorMap[vendor.VendorID.value] = vendor.VendorName.value;
          }
        });
      }
      
      console.log(`Loaded ${Object.keys(this.vendorMap).length} vendor names from Vendorlist.json`);
    } catch (error) {
      console.error('Error loading vendor names:', error);
    }
  }

  // Get vendor name from ID using the loaded vendor map
  getVendorName(vendorId) {
    if (!vendorId) return 'N/A';
    
    // If we have a vendor map and the ID exists in it, return the name
    if (this.vendorMap && this.vendorMap[vendorId]) {
      return this.vendorMap[vendorId];
    }
    
    // Fallback to the ID
    return vendorId;
  }
  
  setupPaginationEventHandlers() {
    // Previous page button
    const prevPageBtn = this.container.querySelector('#prevPageBtn');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 0) {
          this.currentPage--;
          this.render();
        }
      });
    }
    
    // Next page button
    const nextPageBtn = this.container.querySelector('#nextPageBtn');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        const itemsToDisplay = this.filteredPOs.length > 0 ? this.filteredPOs : this.poData;
        const totalPages = Math.ceil(itemsToDisplay.length / this.pageSize);
        
        if (this.currentPage < totalPages - 1) {
          this.currentPage++;
          this.render();
        }
      });
    }
    
    // Individual page buttons
    const pageButtons = this.container.querySelectorAll('#pagination button');
    pageButtons.forEach(button => {
      button.addEventListener('click', () => {
        const page = parseInt(button.getAttribute('data-page'));
        if (!isNaN(page) && page !== this.currentPage) {
          this.currentPage = page;
          this.render();
        }
      });
    });
  }
  
  // Add date range modal
  showDateRangeModal() {
    // Create the date range modal HTML
    const modalHtml = `
      <div id="dateRangeModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96 max-w-full">
          <div class="flex justify-between items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Filter by Date Range</h3>
            <button id="closeDateRangeModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">Start Date:</label>
              <input type="date" id="startDateInput" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
                value="${this.dateRangeFilter.startDate ? this.dateRangeFilter.startDate.toISOString().split('T')[0] : ''}">
            </div>
            
            <div>
              <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">End Date:</label>
              <input type="date" id="endDateInput" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white"
                value="${this.dateRangeFilter.endDate ? this.dateRangeFilter.endDate.toISOString().split('T')[0] : ''}">
            </div>
            
            <div class="flex items-center mt-2">
              <input type="checkbox" id="applyToVendorsCheckbox" class="h-4 w-4 text-blue-600 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600" 
                ${this.dateRangeFilter.applyToVendors ? 'checked' : ''}>
              <label for="applyToVendorsCheckbox" class="ml-2 text-sm text-gray-600 dark:text-gray-400">
                Apply to Vendor Management view
              </label>
            </div>
          </div>
          
          <div class="flex justify-end space-x-2 mt-6 pt-2 border-t border-gray-200 dark:border-gray-700">
            <button id="clearDateRangeBtn" class="px-4 py-2 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
              Clear
            </button>
            <button id="applyDateRangeBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
              Apply
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Add modal to DOM
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHtml;
    document.body.appendChild(modalContainer.firstElementChild);
    
    // Setup event handlers
    document.getElementById('closeDateRangeModalBtn').addEventListener('click', () => {
      this.closeDateRangeModal();
    });
    
    document.getElementById('clearDateRangeBtn').addEventListener('click', () => {
      document.getElementById('startDateInput').value = '';
      document.getElementById('endDateInput').value = '';
    });
    
    document.getElementById('applyDateRangeBtn').addEventListener('click', () => {
      const startDateStr = document.getElementById('startDateInput').value;
      const endDateStr = document.getElementById('endDateInput').value;
      const applyToVendors = document.getElementById('applyToVendorsCheckbox').checked;
      
      // Update date range filter
      this.dateRangeFilter.startDate = startDateStr ? new Date(startDateStr) : null;
      this.dateRangeFilter.endDate = endDateStr ? new Date(endDateStr) : null;
      this.dateRangeFilter.enabled = !!(this.dateRangeFilter.startDate || this.dateRangeFilter.endDate);
      this.dateRangeFilter.applyToVendors = applyToVendors;
      
      // Apply filter and close modal
      this.applyDateRangeFilter();
      this.closeDateRangeModal();
      
      // Save settings to storage
      this.saveSettingsToStorage();
    });
  }
  
  closeDateRangeModal() {
    const modal = document.getElementById('dateRangeModal');
    if (modal) {
      modal.remove();
    }
  }
  
  applyDateRangeFilter() {
    if (!this.poData || this.poData.length === 0) return;
    
    // If filter is not enabled, show all data
    if (!this.dateRangeFilter.enabled) {
      this.filteredPOs = [...this.poData];
      this.currentPage = 0;
      this.vendorCurrentPage = 0;
      this.render();
      return;
    }
    
    // Filter POs based on date range
    this.filteredPOs = this.poData.filter(po => {
      const poDate = po.Date?.value ? new Date(po.Date.value) : null;
      if (!poDate) return false;
      
      // Check if date is within range
      if (this.dateRangeFilter.startDate && poDate < this.dateRangeFilter.startDate) {
        return false;
      }
      
      if (this.dateRangeFilter.endDate) {
        // Add one day to end date to include orders on that day
        const endDate = new Date(this.dateRangeFilter.endDate);
        endDate.setDate(endDate.getDate() + 1);
        if (poDate > endDate) {
          return false;
        }
      }
      
      return true;
    });
    
    // Reset to first page and render
    this.currentPage = 0;
    this.vendorCurrentPage = 0;
    this.render();
    
    // Show notification
    const filteredCount = this.filteredPOs.length;
    const totalCount = this.poData.length;
    this.addNotification(`Showing ${filteredCount} of ${totalCount} orders within date range`, 'info');
  }
  
  loadSettingsFromStorage() {
    try {
      if (chrome?.storage?.local) {
        chrome.storage.local.get(['vendorMetricsSettings'], (result) => {
          if (result.vendorMetricsSettings) {
            this.applyLoadedSettings(result.vendorMetricsSettings);
          }
        });
      } else if (localStorage.getItem('vendorMetricsSettings')) {
        const settings = JSON.parse(localStorage.getItem('vendorMetricsSettings'));
        this.applyLoadedSettings(settings);
      }
    } catch (error) {
      console.error('Error loading settings from storage:', error);
    }
  }
  
  applyLoadedSettings(settings) {
    if (settings.leadTimeThresholds) {
      this.leadTimeThresholds = settings.leadTimeThresholds;
    }
    
    if (settings.weekendsExcluded !== undefined) {
      this.weekendsExcluded = settings.weekendsExcluded;
    }
    
    if (settings.sortField) {
      this.sortField = settings.sortField;
    }
    
    if (settings.sortDirection) {
      this.sortDirection = settings.sortDirection;
    }
    
    if (settings.chartView !== undefined) {
      this.chartView = settings.chartView;
    }
    
    if (settings.vendorManagementView !== undefined) {
      this.vendorManagementView = settings.vendorManagementView;
    }
    
    if (settings.dateRangeFilter) {
      this.dateRangeFilter = settings.dateRangeFilter;
      
      // Convert date strings back to Date objects
      if (this.dateRangeFilter.startDate) {
        this.dateRangeFilter.startDate = new Date(this.dateRangeFilter.startDate);
      }
      
      if (this.dateRangeFilter.endDate) {
        this.dateRangeFilter.endDate = new Date(this.dateRangeFilter.endDate);
      }
      
      // Apply date filter if enabled
      if (this.dateRangeFilter.enabled && this.poData && this.poData.length > 0) {
        this.applyDateRangeFilter();
      }
    }
    
    this.render();
  }

  // Add method to determine delivery status (Early, OnTime, Late)
  getDeliveryStatus(po) {
    if (!po.PromisedOn?.value || !po.LastModifiedDateTime?.value) {
      return 'Unknown';
    }
    
    const promisedDate = new Date(po.PromisedOn.value);
    const receivedDate = new Date(po.LastModifiedDateTime.value);
    
    // Compare dates
    const timeDiff = Math.round((receivedDate - promisedDate) / (1000 * 60 * 60 * 24));
    
    if (timeDiff < 0) {
      return 'Early';
    } else if (timeDiff === 0) {
      return 'OnTime';
    } else {
      return 'Late';
    }
  }

  // Implement the vendor management view
  renderVendorManagementView() {
    if (!this.poData || this.poData.length === 0) {
      return `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p class="text-xs">No purchase order data available.</p>
        </div>
      `;
    }
    
    // Check if date filter is applied
    const filterApplied = this.dateRangeFilter.enabled;
    
    // Calculate vendor metrics - use filtered POs if date range is applied
    this.calculateVendorMetrics(filterApplied);
    
    // Get vendors to display
    let vendorsToDisplay = this.vendorMetrics;
    
    // Sort vendors by name for easier viewing
    vendorsToDisplay.sort((a, b) => a.vendorName.localeCompare(b.vendorName));
    
    // Apply pagination to vendors
    const totalPages = Math.ceil(vendorsToDisplay.length / this.vendorPageSize);
    const startIndex = this.vendorCurrentPage * this.vendorPageSize;
    const endIndex = Math.min(startIndex + this.vendorPageSize, vendorsToDisplay.length);
    const displayedVendors = vendorsToDisplay.slice(startIndex, endIndex);
    
    return `
      <div class="overflow-x-auto">
        <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">Vendor Performance Summary</h3>
        
        ${this.dateRangeFilter.enabled ? `
          <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs rounded">
            <span class="font-medium">Date Filter Applied:</span>
            ${this.dateRangeFilter.startDate ? ` From ${this.formatDateSafely(this.dateRangeFilter.startDate)}` : ''}
            ${this.dateRangeFilter.endDate ? ` To ${this.formatDateSafely(this.dateRangeFilter.endDate)}` : ''}
            <button id="clearVendorDateRangeFilterBtn" class="ml-2 text-blue-700 dark:text-blue-400 hover:underline">Clear</button>
          </div>
        ` : ''}
        
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs" style="max-width: 750px">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Vendor
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                PO Count
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Avg Lead Time
              </th>
              <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
            ${displayedVendors.map(vendor => {
              // Calculate percentage of on-time or early deliveries
              const onTimePercent = Math.round(vendor.onTimePercent || 0);
              
              // Determine status based on on-time percentage - use more lenient thresholds
              let statusClass = '';
              let statusText = '';
              
              if (onTimePercent >= 80) {  // Changed from 90% to 80%
                statusClass = 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
                statusText = 'Excellent';
              } else if (onTimePercent >= 60) {  // Changed from 75% to 60%
                statusClass = 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
                statusText = 'Good';
              } else {
                statusClass = 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
                statusText = 'Poor';
              }
              
              // Truncate vendor name if too long (max 18 chars + ellipsis)
              const displayVendorName = vendor.vendorName.length > 18 ? 
                vendor.vendorName.substring(0, 18) + '...' : 
                vendor.vendorName;
              
              return `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="font-medium text-gray-900 dark:text-white" title="${vendor.vendorName}">${displayVendorName}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-gray-700 dark:text-gray-300">${vendor.orderCount}</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="text-gray-700 dark:text-gray-300">${vendor.avgLeadTime || 0} days</span>
                  </td>
                  <td class="px-2 py-2 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                      ${statusText}
                    </span>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
        
        ${this.renderVendorPagination(totalPages)}
        
        <div class="mt-4 text-xs text-gray-500 dark:text-gray-400 text-right">
          Showing ${Math.min(vendorsToDisplay.length, startIndex + 1)}-${endIndex} of ${vendorsToDisplay.length} vendors
        </div>
      </div>
    `;
  }
  
  // Add method for vendor pagination
  renderVendorPagination(totalPages) {
    if (totalPages <= 1) return '';
    
    // Generate page buttons
    let paginationHTML = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(0, Math.min(this.vendorCurrentPage - Math.floor(maxVisiblePages / 2), totalPages - maxVisiblePages));
    if (startPage < 0) startPage = 0;
    const endPage = Math.min(startPage + maxVisiblePages, totalPages);
    
    for (let i = startPage; i < endPage; i++) {
      const isActive = i === this.vendorCurrentPage;
      paginationHTML += `
        <button data-page="${i}" class="px-2 py-1 border ${isActive 
          ? 'border-blue-500 bg-blue-500 text-white' 
          : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-md text-xs">
          ${i + 1}
        </button>
      `;
    }
    
    return `
      <div class="flex items-center justify-between mt-4 text-xs">
        <div class="text-gray-500 dark:text-gray-400">
          Showing <span id="vendorShowingStart">${this.vendorCurrentPage * this.vendorPageSize + 1}</span> to 
          <span id="vendorShowingEnd">${Math.min((this.vendorCurrentPage + 1) * this.vendorPageSize, this.vendorMetrics.length)}</span> of 
          <span id="vendorTotalItems">${this.vendorMetrics.length}</span> vendors
        </div>
        <div class="flex space-x-1">
          <button id="vendorPrevPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.vendorCurrentPage <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div id="vendorPagination" class="flex space-x-1">
            ${paginationHTML}
          </div>
          <button id="vendorNextPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.vendorCurrentPage >= totalPages - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }
  
  // Add method for vendor pagination event handlers
  setupVendorPaginationEventHandlers() {
    // Previous page button
    const prevPageBtn = this.container.querySelector('#vendorPrevPageBtn');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.vendorCurrentPage > 0) {
          this.vendorCurrentPage--;
          this.render();
        }
      });
    }
    
    // Next page button
    const nextPageBtn = this.container.querySelector('#vendorNextPageBtn');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(this.vendorMetrics.length / this.vendorPageSize);
        
        if (this.vendorCurrentPage < totalPages - 1) {
          this.vendorCurrentPage++;
          this.render();
        }
      });
    }
    
    // Individual page buttons
    const pageButtons = this.container.querySelectorAll('#vendorPagination button');
    pageButtons.forEach(button => {
      button.addEventListener('click', () => {
        const page = parseInt(button.getAttribute('data-page'));
        if (!isNaN(page) && page !== this.vendorCurrentPage) {
          this.vendorCurrentPage = page;
          this.render();
        }
      });
    });
  }
  
  // Add method to clear date range filter
  clearDateRangeFilter() {
    this.dateRangeFilter.enabled = false;
    this.dateRangeFilter.startDate = null;
    this.dateRangeFilter.endDate = null;
    this.filteredPOs = [...this.poData];
    this.currentPage = 0; // Reset to first page
    this.vendorCurrentPage = 0; // Reset vendor page too
    this.render();
    this.saveSettingsToStorage(); // Save cleared settings
  }
}

export default VendorMetricsComponent;