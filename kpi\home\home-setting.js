/**
 * Home Settings Manager
 * Manages settings for the KPI Dashboard Home view
 */
export class HomeSettings {
  constructor() {
    this.defaultSettings = {
      showRecentActivity: true,
      showConnectionStatus: true,
      autoRefreshData: false,
      cardsPerRow: 3, // 2, 3, or 4
      cardSize: 'medium', // small, medium, or large
      layout: 'grid', // grid or list
      refreshInterval: 60, // seconds
      defaultCards: ['sales', 'finance', 'purchasing', 'inventory', 'logistics', 'projects']
    };
    
    this.settings = this.loadSettings() || {...this.defaultSettings};
  }
  
  /**
   * Load settings from localStorage
   */
  loadSettings() {
    try {
      const savedSettings = localStorage.getItem('homeSettings');
      return savedSettings ? JSON.parse(savedSettings) : null;
    } catch (e) {
      console.error('Error loading home settings:', e);
      return null;
    }
  }
  
  /**
   * Save settings to localStorage
   */
  saveSettings() {
    try {
      localStorage.setItem('homeSettings', JSON.stringify(this.settings));
      return true;
    } catch (e) {
      console.error('Error saving home settings:', e);
      return false;
    }
  }
  
  /**
   * Get a specific setting
   * @param {string} key - The setting key
   * @param {*} defaultValue - Default value if setting not found
   */
  getSetting(key, defaultValue) {
    return this.settings[key] !== undefined ? this.settings[key] : defaultValue;
  }
  
  /**
   * Update a specific setting
   * @param {string} key - The setting key
   * @param {*} value - The setting value
   */
  updateSetting(key, value) {
    this.settings[key] = value;
    return this.saveSettings();
  }
  
  /**
   * Update multiple settings at once
   * @param {Object} settingsObject - Object containing settings
   */
  updateSettings(settingsObject) {
    this.settings = {...this.settings, ...settingsObject};
    return this.saveSettings();
  }
  
  /**
   * Reset all settings to default
   */
  resetToDefault() {
    this.settings = {...this.defaultSettings};
    return this.saveSettings();
  }
  
  /**
   * Get cards configuration
   * Determines which cards should be displayed and their order
   */
  getCardsConfig() {
    return this.settings.cards || this.defaultSettings.defaultCards;
  }
  
  /**
   * Update cards configuration
   * @param {Array} cardsConfig - Array of card IDs
   */
  updateCardsConfig(cardsConfig) {
    this.settings.cards = cardsConfig;
    return this.saveSettings();
  }
  
  /**
   * Add a card to the configuration
   * @param {string} cardId - The card ID to add
   */
  addCard(cardId) {
    if (!this.settings.cards) {
      this.settings.cards = [...this.defaultSettings.defaultCards];
    }
    
    if (!this.settings.cards.includes(cardId)) {
      this.settings.cards.push(cardId);
      return this.saveSettings();
    }
    
    return false;
  }
  
  /**
   * Remove a card from the configuration
   * @param {string} cardId - The card ID to remove
   */
  removeCard(cardId) {
    if (!this.settings.cards) {
      return false;
    }
    
    const index = this.settings.cards.indexOf(cardId);
    if (index > -1) {
      this.settings.cards.splice(index, 1);
      return this.saveSettings();
    }
    
    return false;
  }
  
  /**
   * Get card metadata for available cards
   * This would typically come from a backend API
   */
  getAvailableCards() {
    // This is a placeholder for actual API implementation
    return [
      {
        id: 'sales',
        title: 'Total Sales',
        category: 'Sales',
        description: 'Shows total sales for the selected period',
        defaultPeriod: '30 Days'
      },
      {
        id: 'finance',
        title: 'Gross Profit',
        category: 'Finance',
        description: 'Shows gross profit for the selected period',
        defaultPeriod: 'Monthly'
      },
      {
        id: 'purchasing',
        title: 'Purchase Orders',
        category: 'Purchasing',
        description: 'Shows total purchase orders for the selected period',
        defaultPeriod: 'Weekly'
      },
      {
        id: 'inventory',
        title: 'Inventory Value',
        category: 'Inventory',
        description: 'Shows current inventory value',
        defaultPeriod: 'Current'
      },
      {
        id: 'logistics',
        title: 'On-Time Delivery',
        category: 'Logistics',
        description: 'Shows on-time delivery percentage',
        defaultPeriod: '30 Days'
      },
      {
        id: 'projects',
        title: 'Active Projects',
        category: 'Projects',
        description: 'Shows count of active projects',
        defaultPeriod: 'Current'
      },
      {
        id: 'customers',
        title: 'Active Customers',
        category: 'Sales',
        description: 'Shows count of active customers',
        defaultPeriod: 'Current'
      },
      {
        id: 'quotes',
        title: 'Open Quotes',
        category: 'Sales',
        description: 'Shows count of open quotes',
        defaultPeriod: 'Current'
      },
      {
        id: 'backorders',
        title: 'Backorders',
        category: 'Inventory',
        description: 'Shows count of backorders',
        defaultPeriod: 'Current'
      },
      {
        id: 'shipping',
        title: 'Shipping Costs',
        category: 'Logistics',
        description: 'Shows shipping costs for the selected period',
        defaultPeriod: 'Monthly'
      }
    ];
  }
}

// Helper utility for card template generation
export class CardTemplates {
  /**
   * Generate HTML for a standard KPI card
   * @param {string} id - Card ID
   * @param {string} title - Card title
   * @param {string} value - Card value
   * @param {string} period - Time period
   * @param {number} change - Percentage change
   * @param {string} component - Component name for "View Details"
   */
  static standardKPI(id, title, value, period, change, component) {
    const isPositive = change >= 0;
    const changeClass = isPositive ? 'text-green-600' : 'text-red-600';
    const changeIcon = isPositive ? 
      `<path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>` : 
      `<path fill="currentColor" d="M7 10l5 5 5-5H7z"></path>`;
    const changeIconClass = isPositive ? 'text-green-500' : 'text-red-500';
    
    return `
      <div class="kpi-card" data-card-id="${id}">
        <div class="drag-handle">
          <i class="fas fa-grip-horizontal"></i>
        </div>
        <div class="resize-handle"></div>
        <div class="flex justify-between items-center">
          <h3 class="font-semibold text-gray-700">${title}</h3>
          <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">${period}</span>
        </div>
        <div class="kpi-value ${isPositive ? 'text-blue-600' : 'text-indigo-600'}">${value}</div>
        <div class="text-sm text-gray-500 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 ${changeIconClass}">
            ${changeIcon}
          </svg>
          <span class="${changeClass}">${isPositive ? '+' : ''}${change}%</span> vs last period
        </div>
        <div class="mt-3 text-right">
          <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="${component}">View Details →</button>
        </div>
      </div>
    `;
  }
  
  /**
   * Generate HTML for the "Add Card" element
   */
  static addCard() {
    return `
      <div class="kpi-card add-card cursor-pointer flex flex-col items-center justify-center" data-card-id="add-card">
        <div class="w-24 h-24 mb-2">
          <img src="../images/kpi/add_card.svg" alt="Add Card" class="w-full h-full">
        </div>
        <h3 class="font-semibold text-gray-700 dark:text-gray-300">Add New KPI</h3>
        <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1">Create a custom metric</p>
      </div>
    `;
  }
} 