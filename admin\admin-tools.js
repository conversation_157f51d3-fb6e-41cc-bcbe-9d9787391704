// admin-tools.js - Handles admin-specific functionality

import { NotificationSystem } from "../core/notifications.js"

// Get the singleton notification instance
const notificationSystem = new NotificationSystem(document.body)

// Track last view operation time to prevent duplicate tab opening
let lastViewOperationTime = 0;

// Track last notification timestamps to prevent duplicates
const notificationTimestamps = new Map();
const NOTIFICATION_DEBOUNCE_TIME = 800; // ms

// Helper function to show notification with debounce
function showNotification(message, type) {
  const key = `${type}:${message}`;
  const now = Date.now();
  
  // Check if this exact notification was shown recently
  if (notificationTimestamps.has(key)) {
    const lastTime = notificationTimestamps.get(key);
    if (now - lastTime < NOTIFICATION_DEBOUNCE_TIME) {
      // Skip showing duplicate notification if shown recently
      return;
    }
  }
  
  // Update timestamp and show notification
  notificationTimestamps.set(key, now);
  notificationSystem.addNotification(message, type);
  
  // Clean up old timestamps after a while
  setTimeout(() => {
    notificationTimestamps.delete(key);
  }, NOTIFICATION_DEBOUNCE_TIME * 2);
}

// Helper function to log audit trail (placeholder)
function logAuditTrail(message) {
  console.log("Audit Trail:", message)
  // Implement actual audit trail logging here
}

// Helper function to check if user has admin privileges
async function checkAdminPrivileges() {
  return new Promise((resolve, reject) => {
    try {
      chrome.storage.local.get(["user"], (userData) => {
        if (chrome.runtime.lastError) {
          console.error("Error retrieving user data:", chrome.runtime.lastError.message);
          showNotification("Error retrieving user data.", "danger");
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        const user = userData.user;
        
        // Check if user exists and has role information
        if (!user) {
          showNotification("User not logged in.", "warning");
          reject(new Error("User not logged in"));
          return;
        }
        
        // Check if user has admin permissions
        if (user.Role && user.Role.toLowerCase() === "admin") {
          resolve(user);
        } else {
          showNotification("Admin access required for this action.", "warning");
          logAuditTrail(`Access denied: User '${user["User Name"]}' with role '${user.Role || "Unknown"}' attempted admin action`);
          reject(new Error("Admin access required"));
        }
      });
    } catch (error) {
      console.error("Error in role-based access check:", error.message);
      showNotification("Access control error.", "danger");
      reject(error);
    }
  });
}

/**
 * View admin function with debouncing to prevent multiple tabs.
 * This uses a simple approach to ensure only one tab is created.
 */
export async function viewAdmin() {
  console.log("viewAdmin function called");
  
  // Simple debounce: Check if function was called recently
  const now = Date.now();
  if (now - lastViewOperationTime < 1000) {
    console.log("View operation debounced - too many clicks");
    return Promise.resolve("Operation in progress");
  }
  
  // Set last operation time to now
  lastViewOperationTime = now;
  
  // Show notification
  showNotification("Preparing to view data...", "info");
  
  try {
    // Check admin privileges first
    const user = await checkAdminPrivileges();
    
    return new Promise((resolve, reject) => {
      // Proceed with retrieving the source code for admin users
      chrome.storage.local.get(["acumaticaSourceCode", "scrapeStats"], (result) => {
        if (chrome.runtime.lastError) {
          // Reset debounce timer on error
          lastViewOperationTime = 0;
          console.error("Error retrieving data from storage:", chrome.runtime.lastError.message);
          showNotification("Error retrieving source code.", "danger");
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }

        const extractedSourceCode = result.acumaticaSourceCode;
        const stats = result.scrapeStats || {};

        if (!extractedSourceCode) {
          // Reset debounce timer on error
          lastViewOperationTime = 0;
          showNotification("No data to view. Please fetch data first.", "warning");
          resolve("No data available");
          return;
        }
        
        try {
          // Add some helpful HTML to the source code for better viewing
          const enhancedHtml = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>Source Code Viewer - ${stats.pageTitle || 'Page Source'}</title>
              <style>
                body { font-family: system-ui, -apple-system, sans-serif; margin: 0; padding: 0; }
                header { background: #2c3e50; color: white; padding: 1rem; position: sticky; top: 0; z-index: 100; }
                .container { padding: 1rem; }
                .stats { background: #f8f9fa; border-radius: 5px; padding: 1rem; margin-bottom: 1rem; }
                .stats-item { margin: 0.5rem 0; }
                pre { background: #f5f5f5; padding: 1rem; border-radius: 5px; overflow-x: auto; margin-top: 1rem; }
                .timestamp { font-size: 0.8rem; color: #7f8c8d; }
              </style>
            </head>
            <body>
              <header>
                <h1>Source Code Viewer</h1>
                <p>${stats.pageTitle || 'Extracted Page Source'}</p>
              </header>
              <div class="container">
                <div class="stats">
                  <h2>Extraction Statistics</h2>
                  <div class="stats-item"><strong>URL:</strong> ${stats.url || 'Unknown'}</div>
                  <div class="stats-item"><strong>Page Title:</strong> ${stats.pageTitle || 'Unknown'}</div>
                  <div class="stats-item"><strong>Iframes:</strong> ${stats.accessedIframes || 0} of ${stats.totalIframes || 0} accessed</div>
                  <div class="stats-item"><strong>Frames:</strong> ${stats.accessedFrames || 0} of ${stats.totalFrames || 0} accessed</div>
                  <div class="timestamp"><strong>Extracted:</strong> ${stats.timestamp || new Date().toISOString()}</div>
                </div>
                <h2>Raw Source Code</h2>
                <pre>${extractedSourceCode.replace(/</g, '&lt;').replace(/>/g, '&gt;')}</pre>
              </div>
            </body>
            </html>
          `;

          const blob = new Blob([enhancedHtml], { type: "text/html" });
          const url = URL.createObjectURL(blob);

          // Only open one tab - this is the critical part
          chrome.tabs.create({ url }, (tab) => {
            // Keep debounce active for another 500ms
            setTimeout(() => {
              lastViewOperationTime = 0;
            }, 500);
            
            if (chrome.runtime.lastError) {
              console.error("Error creating new tab:", chrome.runtime.lastError.message);
              showNotification("Error opening new tab.", "danger");
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              logAuditTrail(`Admin user '${user["User Name"]}' viewed source code`);
              resolve("Data viewed in new tab");
            }
            
            try {
              URL.revokeObjectURL(url);
            } catch (e) {
              console.warn("Could not revoke object URL:", e.message);
            }
          });
        } catch (error) {
          // Reset debounce timer on error
          lastViewOperationTime = 0;
          console.error("Error creating blob or URL:", error.message);
          showNotification("Error creating content view.", "danger");
          reject(error);
        }
      });
    });
  } catch (error) {
    // Reset debounce timer on error
    lastViewOperationTime = 0;
    return Promise.reject(error);
  }
}

/**
 * Debug admin function to diagnose extension issues
 * Reads logs, shows errors, and provides diagnostics
 */
export async function debugAdmin() {
  showNotification("Starting diagnostic process...", "info");
  
  try {
    // Check admin privileges first
    const user = await checkAdminPrivileges();
    
    return new Promise((resolve, reject) => {
      // Gather diagnostic information
      chrome.storage.local.get(null, async (items) => {
        if (chrome.runtime.lastError) {
          console.error("Error retrieving storage data:", chrome.runtime.lastError.message);
          showNotification("Error retrieving diagnostic data.", "danger");
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        
        // Get extension information
        let diagnosticData = {
          timestamp: new Date().toISOString(),
          extensionInfo: {
            id: chrome.runtime.id,
            version: chrome.runtime.getManifest().version
          },
          storageData: {
            keys: Object.keys(items),
            storageUsage: JSON.stringify(items).length,
            // Only include non-sensitive data
            userInfo: items.user ? {
              role: items.user.Role,
              lastLogin: items.user.lastLogin
            } : null
          },
          browserInfo: {},
          errors: []
        };
        
        // Check for recent errors in console logs (simplified - real implementation would need to capture logs)
        // This is a placeholder as extension cannot directly access console logs
        // In a real implementation, you would maintain your own error log in storage
        
        // Get browser information
        if (chrome.system && chrome.system.cpu) {
          try {
            chrome.system.cpu.getInfo((info) => {
              diagnosticData.browserInfo.cpu = info;
              
              // Continue with memory info if available
              if (chrome.system.memory) {
                chrome.system.memory.getInfo((memoryInfo) => {
                  diagnosticData.browserInfo.memory = memoryInfo;
                  
                  // Display diagnostic information
                  displayDiagnostics(diagnosticData, user, resolve);
                });
              } else {
                // Display without memory info
                displayDiagnostics(diagnosticData, user, resolve);
              }
            });
          } catch (error) {
            // Display without system info if API fails
            displayDiagnostics(diagnosticData, user, resolve);
          }
        } else {
          // Display without system info if API not available
          displayDiagnostics(diagnosticData, user, resolve);
        }
      });
    });
  } catch (error) {
    return Promise.reject(error);
  }
}

// Helper function to display diagnostics in a new tab
function displayDiagnostics(diagnosticData, user, resolve) {
  try {
    // Create HTML to display diagnostics
    const diagnosticHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Extension Diagnostics</title>
        <style>
          body { font-family: system-ui, -apple-system, sans-serif; margin: 0; padding: 0; }
          header { background: #2c3e50; color: white; padding: 1rem; position: sticky; top: 0; z-index: 100; }
          .container { padding: 1rem; }
          .card { background: #f8f9fa; border-radius: 5px; padding: 1rem; margin-bottom: 1rem; }
          .card-title { font-weight: bold; font-size: 1.2rem; margin-bottom: 0.5rem; }
          .data-row { display: flex; margin: 0.3rem 0; }
          .data-label { font-weight: bold; width: 200px; }
          .data-value { flex: 1; }
          .error-card { background: #fee; border-left: 4px solid #e74c3c; padding: 0.5rem; margin: 0.5rem 0; }
          .button-row { margin-top: 1rem; }
          button { background: #3498db; color: white; border: none; padding: 0.5rem 1rem; border-radius: 4px; cursor: pointer; }
          button:hover { background: #2980b9; }
          .refresh-button { background: #2ecc71; }
          .refresh-button:hover { background: #27ae60; }
          pre { background: #f5f5f5; padding: 0.5rem; border-radius: 3px; overflow-x: auto; font-size: 0.8rem; }
        </style>
      </head>
      <body>
        <header>
          <h1>Extension Diagnostics</h1>
          <p>Diagnostic data as of ${diagnosticData.timestamp}</p>
        </header>
        <div class="container">
          <div class="card">
            <div class="card-title">Extension Information</div>
            <div class="data-row">
              <div class="data-label">Extension ID:</div>
              <div class="data-value">${diagnosticData.extensionInfo.id}</div>
            </div>
            <div class="data-row">
              <div class="data-label">Version:</div>
              <div class="data-value">${diagnosticData.extensionInfo.version}</div>
            </div>
          </div>
          
          <div class="card">
            <div class="card-title">Storage Information</div>
            <div class="data-row">
              <div class="data-label">Storage Keys:</div>
              <div class="data-value">${diagnosticData.storageData.keys.join(', ')}</div>
            </div>
            <div class="data-row">
              <div class="data-label">Storage Usage:</div>
              <div class="data-value">${Math.round(diagnosticData.storageData.storageUsage / 1024)} KB</div>
            </div>
            ${diagnosticData.storageData.userInfo ? `
            <div class="data-row">
              <div class="data-label">User Role:</div>
              <div class="data-value">${diagnosticData.storageData.userInfo.role || 'Unknown'}</div>
            </div>
            <div class="data-row">
              <div class="data-label">Last Login:</div>
              <div class="data-value">${diagnosticData.storageData.userInfo.lastLogin || 'Unknown'}</div>
            </div>
            ` : ''}
          </div>
          
          ${diagnosticData.browserInfo.cpu ? `
          <div class="card">
            <div class="card-title">System Information</div>
            <div class="data-row">
              <div class="data-label">CPU Architecture:</div>
              <div class="data-value">${diagnosticData.browserInfo.cpu.archName}</div>
            </div>
            <div class="data-row">
              <div class="data-label">CPU Model:</div>
              <div class="data-value">${diagnosticData.browserInfo.cpu.modelName}</div>
            </div>
            ${diagnosticData.browserInfo.memory ? `
            <div class="data-row">
              <div class="data-label">Available Memory:</div>
              <div class="data-value">${Math.round(diagnosticData.browserInfo.memory.availableCapacity / (1024 * 1024))} MB</div>
            </div>
            ` : ''}
          </div>
          ` : ''}
          
          <div class="card">
            <div class="card-title">Errors and Warnings</div>
            ${diagnosticData.errors.length > 0 ? diagnosticData.errors.map(error => `
              <div class="error-card">
                <div><strong>Time:</strong> ${error.time}</div>
                <div><strong>Type:</strong> ${error.type}</div>
                <div><strong>Message:</strong> ${error.message}</div>
                ${error.stack ? `<pre>${error.stack}</pre>` : ''}
              </div>
            `).join('') : '<div>No errors recorded</div>'}
          </div>
          
          <div class="button-row">
            <button class="refresh-button" id="refresh-btn">Refresh & Clear Errors</button>
            <button id="close-btn">Close</button>
          </div>
        </div>
        
        <script>
          // Add event listeners for buttons
          document.getElementById('refresh-btn').addEventListener('click', function() {
            // Send message to background script to refresh extension
            chrome.runtime.sendMessage({ action: 'refreshExtension' }, function(response) {
              alert('Extension refreshed. Error log cleared.');
              window.location.reload();
            });
          });
          
          document.getElementById('close-btn').addEventListener('click', function() {
            window.close();
          });
        </script>
      </body>
      </html>
    `;
    
    const blob = new Blob([diagnosticHtml], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    
    // Open diagnostic info in a new tab
    chrome.tabs.create({ url }, (tab) => {
      if (chrome.runtime.lastError) {
        console.error("Error creating diagnostics tab:", chrome.runtime.lastError.message);
        showNotification("Error displaying diagnostics.", "danger");
      } else {
        logAuditTrail(`Admin user '${user["User Name"]}' ran diagnostics`);
        resolve("Diagnostics displayed in new tab");
      }
      
      try {
        URL.revokeObjectURL(url);
      } catch (e) {
        console.warn("Could not revoke object URL:", e.message);
      }
    });
  } catch (error) {
    showNotification("Error creating diagnostic view.", "danger");
    console.error("Error in diagnostics:", error);
  }
}

/**
 * Export extension logs as a downloadable text file
 */
export async function viewFullLog() {
  showNotification("Preparing log export...", "info");
  
  try {
    // Check admin privileges first
    const user = await checkAdminPrivileges();
    
    return new Promise((resolve, reject) => {
      // In a real implementation, you would retrieve logs from your storage
      // or from a dedicated logging system
      // This is a simplified version that creates demo logs
      
      chrome.storage.local.get(["extensionLogs"], (result) => {
        if (chrome.runtime.lastError) {
          console.error("Error retrieving logs:", chrome.runtime.lastError.message);
          showNotification("Error retrieving logs.", "danger");
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        
        // Create a log content string
        // In real implementation, use actual stored logs
        let logs = result.extensionLogs || [];
        
        // If no logs exist, create sample logs for demo
        if (logs.length === 0) {
          const now = new Date();
          logs = [
            { timestamp: new Date(now - 3600000).toISOString(), level: "INFO", message: "Extension initialized" },
            { timestamp: new Date(now - 3000000).toISOString(), level: "INFO", message: "User logged in" },
            { timestamp: new Date(now - 2400000).toISOString(), level: "INFO", message: "Data fetching started" },
            { timestamp: new Date(now - 2390000).toISOString(), level: "INFO", message: "Data fetched successfully" },
            { timestamp: new Date(now - 1800000).toISOString(), level: "WARNING", message: "Slow response from server" },
            { timestamp: new Date(now - 1200000).toISOString(), level: "ERROR", message: "Failed to update record" },
            { timestamp: new Date(now - 600000).toISOString(), level: "INFO", message: "Extension settings updated" },
            { timestamp: now.toISOString(), level: "INFO", message: "Log export requested" }
          ];
        }
        
        // Format logs as text
        const logContent = logs.map(log => 
          `[${log.timestamp}] [${log.level}] ${log.message}`
        ).join('\n');
        
        // Add header with metadata
        const header = [
          "=== EXTENSION LOG EXPORT ===",
          `Generated: ${new Date().toISOString()}`,
          `Extension ID: ${chrome.runtime.id}`,
          `Extension Version: ${chrome.runtime.getManifest().version}`,
          `Exported by: ${user["User Name"]} (${user.Role})`,
          "===========================",
          "",
          "LOG ENTRIES:",
          ""
        ].join('\n');
        
        // Create the full log content
        const fullLogContent = header + logContent;
        
        // Create a blob and download link
        const blob = new Blob([fullLogContent], { type: "text/plain" });
        const url = URL.createObjectURL(blob);
        
        // Create a hidden download link and trigger it
        const downloadLink = document.createElement("a");
        downloadLink.href = url;
        downloadLink.download = `extension-log-${new Date().toISOString().replace(/:/g, '-')}.txt`;
        downloadLink.style.display = "none";
        document.body.appendChild(downloadLink);
        downloadLink.click();
        
        // Clean up
        setTimeout(() => {
          document.body.removeChild(downloadLink);
          URL.revokeObjectURL(url);
        }, 100);
        
        logAuditTrail(`Admin user '${user["User Name"]}' exported logs`);
        showNotification("Log file downloaded successfully.", "success");
        resolve("Log file created and downloaded");
      });
    });
  } catch (error) {
    return Promise.reject(error);
  }
}