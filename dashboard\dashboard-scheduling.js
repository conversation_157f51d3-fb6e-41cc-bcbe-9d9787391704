// dashboard-scheduling.js - Calendar and scheduling functionality

export class SchedulingSystem {
  constructor(dashboard) {
    this.dashboard = dashboard;
    this.pendingScheduleData = null;
    this.calendarModal = null;
    
    // Create the calendar modal
    this.createCalendarModal();
  }
  
  // Create the calendar modal for scheduling
  createCalendarModal() {
    // Check if modal already exists
    if (document.getElementById('scheduleModal')) {
      return;
    }
    
    // Create modal container
    const modal = document.createElement('div');
    modal.id = 'scheduleModal';
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden';
    
    // Create modal content
    modal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 w-96 max-w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Schedule Process</h3>
          <button id="closeScheduleModal" class="text-gray-500 hover:text-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="mb-4">
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Select date and time to schedule sending to Monday:</p>
          <div class="flex flex-col space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</label>
              <input type="date" id="scheduleDate" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time</label>
              <input type="time" id="scheduleTime" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
          </div>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Reminder</label>
          <select id="scheduleReminder" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <option value="0">No reminder</option>
            <option value="5">5 minutes before</option>
            <option value="15">15 minutes before</option>
            <option value="30">30 minutes before</option>
            <option value="60">1 hour before</option>
          </select>
        </div>
        
        <div class="flex justify-end space-x-2">
          <button id="cancelScheduleBtn" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
            Cancel
          </button>
          <button id="confirmScheduleBtn" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
            Schedule
          </button>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.appendChild(modal);
    
    // Store reference to the modal
    this.calendarModal = modal;
    
    // Set up event listeners
    const closeBtn = document.getElementById('closeScheduleModal');
    const cancelBtn = document.getElementById('cancelScheduleBtn');
    const confirmBtn = document.getElementById('confirmScheduleBtn');
    
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideCalendarModal());
    }
    
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.hideCalendarModal());
    }
    
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => this.handleScheduleConfirm());
    }
    
    // Set default date and time values
    this.setDefaultScheduleDateTime();
  }
  
  // Set default date and time values (tomorrow at 9 AM)
  setDefaultScheduleDateTime() {
    const dateInput = document.getElementById('scheduleDate');
    const timeInput = document.getElementById('scheduleTime');
    
    if (dateInput && timeInput) {
      // Set date to tomorrow
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      // Format date as YYYY-MM-DD
      const year = tomorrow.getFullYear();
      const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
      const day = String(tomorrow.getDate()).padStart(2, '0');
      
      dateInput.value = `${year}-${month}-${day}`;
      
      // Set time to 9:00 AM
      timeInput.value = '09:00';
    }
  }
  
  // Show the calendar modal
  showCalendarModal(scheduleData) {
    // Store the data to be scheduled
    this.pendingScheduleData = scheduleData;
    
    // Reset form with default values
    this.setDefaultScheduleDateTime();
    
    // Show the modal
    if (this.calendarModal) {
      this.calendarModal.classList.remove('hidden');
    }
  }
  
  // Hide the calendar modal
  hideCalendarModal() {
    if (this.calendarModal) {
      this.calendarModal.classList.add('hidden');
    }
    
    // We'll clear the pending data in a timeout to ensure it's available for handleScheduleConfirm
    setTimeout(() => {
      this.pendingScheduleData = null;
    }, 100);
  }
  
  // Handle schedule confirmation
  async handleScheduleConfirm() {
    try {
      const dateInput = document.getElementById('scheduleDate');
      const timeInput = document.getElementById('scheduleTime');
      const reminderSelect = document.getElementById('scheduleReminder');
      
      // Check if we have the pending data
      if (!this.pendingScheduleData) {
        console.error("No pending schedule data available");
        this.dashboard.addNotification("Missing schedule data. Please try again.", "warning");
        return;
      }
      
      if (!dateInput || !timeInput) {
        this.dashboard.addNotification("Missing required scheduling information", "warning");
        return;
      }
      
      // Get selected date and time
      const dateValue = dateInput.value;
      const timeValue = timeInput.value;
      
      if (!dateValue || !timeValue) {
        this.dashboard.addNotification("Please select both date and time", "warning");
        return;
      }
      
      // Combine date and time into a single datetime
      const scheduledDateTime = new Date(`${dateValue}T${timeValue}`);
      
      // Check if the scheduled time is in the past
      if (scheduledDateTime <= new Date()) {
        this.dashboard.addNotification("Cannot schedule for a time in the past", "warning");
        return;
      }
      
      // Get reminder value (minutes before)
      const reminderMinutes = parseInt(reminderSelect.value, 10);
      
      // Store a local copy of the pending data before hiding the modal
      const scheduleData = { ...this.pendingScheduleData };
      
      // Hide the modal
      this.hideCalendarModal();
      
      // Show loading notification
      this.dashboard.addNotification("Scheduling process...", "info");
      
      // Finalize the schedule - use the local copy, not this.pendingScheduleData which might be null now
      const scheduledData = await this.finalizeSchedule(
        scheduleData, 
        scheduledDateTime.toISOString(),
        reminderMinutes
      );
      
      // Only update history if we successfully scheduled
      if (scheduledData) {
        // Update history with the scheduled task
        this.dashboard.updateHistory("scheduleProcess", `Scheduled for ${scheduledDateTime.toLocaleString()}`, this.dashboard.currentPreviewData);
        
        // Show success notification
        this.dashboard.addNotification(`Process scheduled for ${scheduledDateTime.toLocaleString()}`, "success");
      }
      
    } catch (error) {
      console.error("Error confirming schedule:", error);
      this.dashboard.addNotification(`Error scheduling: ${error.message}`, "danger");
    }
  }

  // Finalize a schedule after date selection
  async finalizeSchedule(processData, scheduledDateTime, reminderMinutes = 0) {
    try {
      if (!processData) {
        console.error("No process data provided to finalizeSchedule");
        throw new Error("No process data available");
      }

      // Add scheduling information to the process data
      const scheduledData = {
        ...processData,
        scheduledDateTime: scheduledDateTime,
        reminderMinutes: reminderMinutes,
        status: "Scheduled",
        // Update the action to reflect it's a scheduled task
        action: "scheduleProcess"
      };

      // Schedule the notification using Chrome alarms if available
      if (typeof chrome !== "undefined" && chrome.alarms) {
        const alarmName = `scheduled-task-${scheduledData.id}`;
        const scheduledTime = new Date(scheduledDateTime).getTime();
        
        // Create an alarm for the scheduled time
        chrome.alarms.create(alarmName, {
          when: scheduledTime
        });
        
        // If reminder is set, create a reminder alarm
        if (reminderMinutes > 0) {
          const reminderTime = scheduledTime - (reminderMinutes * 60 * 1000);
          chrome.alarms.create(`${alarmName}-reminder`, {
            when: reminderTime
          });
        }
        
        // Store the alarm details for reference
        await this.storeScheduledTask(scheduledData, alarmName);
        
        console.log(`Scheduled task with alarm: ${alarmName} for ${new Date(scheduledTime).toLocaleString()}`);
      } else {
        // Fallback for non-Chrome environments or testing
        console.log(`Scheduled task for ${new Date(scheduledDateTime).toLocaleString()} (simulation)`);
        
        // Store the scheduled task in localStorage
        await this.storeScheduledTask(scheduledData);
        
        // For testing/demo purposes, we'll set a timeout to simulate the notification
        const now = new Date().getTime();
        const scheduledTime = new Date(scheduledDateTime).getTime();
        const delay = Math.max(0, scheduledTime - now);
        
        if (delay < 2147483647) { // Max setTimeout delay (about 24.8 days)
          setTimeout(() => {
            this.showScheduledNotification(scheduledData);
          }, delay);
          
          // If reminder is set, create a reminder timeout
          if (reminderMinutes > 0) {
            const reminderTime = scheduledTime - (reminderMinutes * 60 * 1000);
            const reminderDelay = Math.max(0, reminderTime - now);
            
            if (reminderDelay < 2147483647) {
              setTimeout(() => {
                this.showReminderNotification(scheduledData, reminderMinutes);
              }, reminderDelay);
            }
          }
        }
      }

      this.dashboard.addNotification(`Process scheduled for ${new Date(scheduledDateTime).toLocaleString()}`, "success");
      return scheduledData;
    } catch (error) {
      console.error("Error finalizing schedule:", error);
      this.dashboard.addNotification(`Error scheduling process: ${error.message}`, "danger");
      return Promise.reject(error);
    }
  }

  // Helper function to store scheduled task
  async storeScheduledTask(taskData, alarmName = null) {
    return new Promise((resolve, reject) => {
      try {
        if (typeof chrome !== "undefined" && chrome.storage) {
          chrome.storage.local.get(["scheduledTasks"], (result) => {
            let tasks = result.scheduledTasks || [];
            
            // Add alarm name if provided
            if (alarmName) {
              taskData.alarmName = alarmName;
            }
            
            // Add to tasks list
            tasks.push(taskData);
            
            chrome.storage.local.set({ scheduledTasks: tasks }, () => {
              if (chrome.runtime.lastError) {
                console.error("Error saving scheduled task:", chrome.runtime.lastError);
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                console.log("Scheduled task saved");
                resolve();
              }
            });
          });
        } else {
          // Fallback to localStorage
          let tasks = [];
          try {
            const tasksStr = localStorage.getItem("scheduledTasks");
            if (tasksStr) {
              tasks = JSON.parse(tasksStr);
            }
          } catch (e) {
            console.warn("Error parsing scheduled tasks:", e);
            tasks = [];
          }
          
          tasks.push(taskData);
          localStorage.setItem("scheduledTasks", JSON.stringify(tasks));
          console.log("Scheduled task saved to localStorage");
          resolve();
        }
      } catch (error) {
        console.error("Error in storeScheduledTask:", error);
        reject(error);
      }
    });
  }

  // Function to show notification for scheduled task
  showScheduledNotification(taskData) {
    // Show a notification that the scheduled task is due
    this.dashboard.addNotification(`Scheduled task for ${taskData.reference} is due now!`, "info");
    
    // You could automatically execute the sendToMonday function here
    // or just notify the user to do it manually
    console.log("Scheduled task triggered:", taskData);
    
    // Create a system notification if possible
    if (typeof chrome !== "undefined" && chrome.notifications) {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "images/icon-128.png",
        title: "Scheduled Task Due",
        message: `Your scheduled task for ${taskData.reference} is due now. Click to send to Monday.`,
        buttons: [{ title: "Send to Monday" }, { title: "Dismiss" }],
        requireInteraction: true
      });
    }
  }

  // Add a new function to show reminder notifications
  showReminderNotification(taskData, minutesBefore) {
    // Show a notification that the scheduled task is coming up
    this.dashboard.addNotification(`Reminder: Task for ${taskData.reference} is scheduled in ${minutesBefore} minutes`, "info");
    
    // Create a system notification if possible
    if (typeof chrome !== "undefined" && chrome.notifications) {
      chrome.notifications.create({
        type: "basic",
        iconUrl: "images/icon-128.png",
        title: "Scheduled Task Reminder",
        message: `Your task for ${taskData.reference} is scheduled in ${minutesBefore} minutes.`,
        buttons: [{ title: "View Details" }, { title: "Dismiss" }],
        requireInteraction: true
      });
    }
  }
}

// Function to initialize scheduling system
export function initializeScheduling(dashboard) {
  return new SchedulingSystem(dashboard);
}
