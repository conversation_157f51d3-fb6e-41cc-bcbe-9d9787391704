// dashboard.js - Core dashboard functionality
import { NotificationSystem } from "../core/notifications.js";
import * as DashboardActions from "./dashboard-actions.js";
import * as DashboardProcess from "./dashboard-process.js";
import { viewAdmin, debugAdmin, viewFullLog } from "../admin/admin-tools.js";
import { initializeScheduling } from "./dashboard-scheduling.js";
import { initializeExports } from "./dashboard-export.js";
import { setupShipOrder } from "./dashboard-shiporder.js";

export class DashboardComponent {
  constructor(container) {
    this.container = container;
    this.notificationSystem = new NotificationSystem();
    this.fullLog = []; // Store all actions
    this.mondayTransfers = []; // Store only monday.com transfers
    this.currentPreviewData = null; // Store current preview data
    this.currentUser = null; // Store current user data
    this.historyItems = []; // Cache history items for easy access
    this.deletedHistoryIds = new Set(); // Track deleted history IDs in memory
    
    // Initialize the scheduling system
    this.schedulingSystem = initializeScheduling(this);
    
    // Initialize the export system
    this.exportSystem = initializeExports(this);

    // Listen for dark mode toggle changes
    const darkModeButton = document.getElementById("darkModeButton");
    if (darkModeButton) {
      darkModeButton.addEventListener("click", () => {
        // Allow a brief timeout for the dark-mode class to be applied to the body
        setTimeout(() => this.handleDarkModeChange(), 50);
      });
    }

    // Load current user information
    this.loadCurrentUser();
    
    // Load deleted history IDs
    this.loadDeletedHistoryIds();
  }

  // Helper method to access scheduling system
  showCalendarModal(scheduleData) {
    this.schedulingSystem.showCalendarModal(scheduleData);
  }
  
  // Helper method to access export system
  printPreviewData(data) {
    this.exportSystem.printPreviewData(data);
  }

  // Load deleted history IDs from storage
  async loadDeletedHistoryIds() {
    try {
      if (typeof chrome !== "undefined" && chrome.storage) {
        // Get from Chrome storage
        const deletedResult = await new Promise((resolve) => {
          chrome.storage.local.get(["deletedHistoryIds"], (data) => {
            resolve(data);
          });
        });
        const deletedIds = deletedResult.deletedHistoryIds || [];
        this.deletedHistoryIds = new Set(deletedIds.map(id => this.normalizeId(id)));
      } else {
        // Get from localStorage
        const deletedIdsStr = localStorage.getItem("deletedHistoryIds");
        if (deletedIdsStr) {
          const deletedIds = JSON.parse(deletedIdsStr);
          this.deletedHistoryIds = new Set(deletedIds.map(id => this.normalizeId(id)));
        }
      }
      
      console.log(`Loaded ${this.deletedHistoryIds.size} permanently deleted IDs`);
    } catch (error) {
      console.error("Error loading deleted history IDs:", error);
      this.deletedHistoryIds = new Set();
    }
  }

  // Load current user data from storage
  loadCurrentUser() {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["user"], (result) => {
        if (result.user) {
          this.currentUser = result.user;
          console.log("User data loaded:", this.currentUser["User Name"]);
        }
      });
    } else {
      try {
        const userStr = localStorage.getItem("user");
        if (userStr) {
          this.currentUser = JSON.parse(userStr);
          console.log("User data loaded from localStorage:", this.currentUser["User Name"]);
        }
      } catch (e) {
        console.warn("Error loading user data from localStorage:", e);
      }
    }
  }

  // Helper method to save preview data both in memory and in storage
  savePreviewData(data) {
    this.currentPreviewData = data;
    
    // Also save to storage for the action functions to access
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.set({ currentPreviewData: data }, () => {
        console.log("Preview data saved to chrome storage");
      });
    } else {
      localStorage.setItem("currentPreviewData", JSON.stringify(data));
      console.log("Preview data saved to localStorage");
    }
  }

  render() {
    this.container.innerHTML = `
    <div class="p-2 dark:bg-gray-900">
      <div class="flex items-center justify-between mb-2">
        <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Dashboard</h2>
        <div class="flex space-x-1">
          <button class="px-2 py-1 text-sm font-medium rounded-md bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200" data-tab="process">Process</button>
          <button class="px-2 py-1 text-sm font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600" data-tab="operations">Operations</button>
          <button class="px-2 py-1 text-sm font-medium rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600" data-tab="tools">Tools</button>
        </div>
      </div>
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-3">
        <div id="tabContent">
          <div id="process" class="tab-content">
            <h3 class="text-md font-semibold mb-2 text-gray-800">Process Overview</h3>
            <div class="flex flex-wrap gap-2 mb-2">
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="fetchData">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path></svg>
                Fetch Data
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="shProcess">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg>
                Sh Process
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="poProcess">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path></svg>
                Po Process
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="soProcess">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg>
                So Process
              </button>
            </div>
            <div class="flex flex-wrap gap-2 mt-2">
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="partLookup">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 21h7a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v11m0 5l4.879-4.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242z"></path></svg>
                Part Lookup
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="validateData">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                Validate Data
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="saveProcess">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path></svg>
                Save Process
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-pink-500 text-white rounded-md hover:bg-pink-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="scheduleProcess">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>
                Schedule Process
              </button>
            </div>
          </div>
          <div id="operations" class="tab-content hidden">
            <h3 class="text-md font-semibold mb-2 text-gray-800">Operations Management</h3>
            <div class="flex flex-wrap gap-2 mb-2">
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="sendToMonday">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path></svg>
                Send to Monday
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-pink-500 text-white rounded-md hover:bg-pink-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="updateAcumatica">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>
                Update Acumatica
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="shipOrder">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path></svg>
                Ship Order
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="bulkActions">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>
                Bulk Actions
              </button>
            </div>
          </div>
          <div id="tools" class="tab-content hidden">
            <h3 class="text-md font-semibold mb-2 text-gray-800">Tools and Resources</h3>
            <div class="flex flex-wrap gap-2 mb-2">
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="clearHistory">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                Clear History
              </button>
              <button class="action-btn flex-1 flex items-center justify-center h-10 px-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="exportData">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap  stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>
                Export
              </button>
              <button class="admin-btn flex-1 flex items-center justify-center h-10 px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="viewAdmin">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                View (Admin)
              </button>
              <button class="admin-btn flex-1 flex items-center justify-center h-10 px-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-200 whitespace-nowrap overflow-hidden" data-action="debugAdmin">
                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path></svg>
                Debug (Admin)
              </button>
            </div>
            <h4 class="text-sm font-semibold mt-4 mb-2 text-gray-800">Admin Tools</h4>
            <button id="viewFullLogButton" class="admin-btn flex items-center justify-center h-10 px-4 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors duration-200">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
              </svg>
              View Full Log
            </button>
          </div>
        </div>
        <div id="historySection" class="mt-4 border-t pt-2 dark:border-gray-700">
          <div class="flex justify-between items-center mb-2">
            <h4 class="text-sm font-semibold text-gray-800">History</h4>
            <button id="refreshHistoryBtn" class="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded">
              <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
          <div id="historyTableContainer">
            <!-- History table will be dynamically inserted here -->
            <div class="p-4 text-center text-gray-500 italic">
              Process orders to see your history
            </div>
          </div>
        </div>
        <div id="previewTable" class="mt-4 border-t pt-2 hidden dark:border-gray-700">
          <div class="flex justify-between items-center mb-2">
            <h4 class="text-sm font-semibold text-gray-800">Preview</h4>
            <div class="flex space-x-2">
              <button id="labelBtn" class="text-sm px-3 py-1 h-8 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md">
                <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                </svg>
                Label
              </button>
              <button id="printBtn" class="text-sm px-3 py-1 h-8 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md">
                <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print
              </button>
              <button id="backToHistoryBtn" class="text-sm px-3 py-1 h-8 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md">
                <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 15l-3-3m0 0l3-3m-3 3h8M3 12a9 9 0 1118 0 9 9 0 01-18 0z"></path>
                </svg>
                Back to History
              </button>
            </div>
          </div>
          <div id="previewTableContainer">
            <!-- Preview table will be dynamically inserted here -->
            <div class="p-4 text-center text-gray-500 italic">
              No preview data available. Please process an order first.
            </div>
          </div>
        </div>
      </div>
    </div>
    `;

    this.setupTabFunctionality();
    this.setupActionButtons();
    this.setupAdminButtons();
    
    // Set up the process buttons using the imported DashboardProcess module
    DashboardProcess.setupProcessButtons(this);
    
    // Setup table controls
    this.setupTableControls();

    // Load initial history data
    this.loadHistoryData();

    // Add an event listener for dark mode changes
    document.addEventListener("darkModeChanged", this.handleDarkModeChange.bind(this));

    // Apply initial dark mode setting
    this.handleDarkModeChange();
  }

  // Handle dark mode changes
  handleDarkModeChange() {
    // Update dashboard title color based on dark mode
    const isDarkMode = document.body.classList.contains("dark-mode");
    const dashboardTitle = this.container.querySelector("h2");

    if (dashboardTitle) {
      if (isDarkMode) {
        dashboardTitle.style.color = "white";
      } else {
        dashboardTitle.style.color = ""; // Reset to default (will use the class color)
      }
    }

    console.log("Dark mode changed - dashboard title updated, component text colors remain black");
  }

  setupTabFunctionality() {
    const tabButtons = this.container.querySelectorAll("button[data-tab]");
    const tabContents = this.container.querySelectorAll(".tab-content");

    tabButtons.forEach((button) => {
      button.addEventListener("click", () => {
        const tabId = button.getAttribute("data-tab");

        // Update button styles
        tabButtons.forEach((btn) => {
          btn.classList.remove("bg-blue-100", "text-blue-700");
          btn.classList.add("bg-gray-100", "text-gray-700");
        });
        button.classList.remove("bg-gray-100", "text-gray-700");
        button.classList.add("bg-blue-100", "text-blue-700");

        // Show the selected tab content
        tabContents.forEach((content) => {
          content.classList.add("hidden");
        });
        const selectedTab = this.container.querySelector(`#${tabId}`);
        if (selectedTab) {
          selectedTab.classList.remove("hidden");
        }
      });
    });
  }

  setupActionButtons() {
    const actionButtons = this.container.querySelectorAll(".action-btn");
    actionButtons.forEach((button) => {
      button.addEventListener("click", async (event) => {
        const action = event.currentTarget.getAttribute("data-action");
        
        // Special handling for scheduleProcess button
        if (action === "scheduleProcess") {
          try {
            if (!this.currentPreviewData) {
              this.addNotification("No data available to schedule.", "warning");
              return;
            }
            
            button.disabled = true;
            button.classList.add("opacity-50", "cursor-not-allowed");
            
            // Get the schedule data
            const scheduleData = await DashboardActions.scheduleProcess(this.currentPreviewData);
            
            // Show the calendar modal
            this.schedulingSystem.showCalendarModal(scheduleData);
          } catch (error) {
            console.error("Error preparing schedule:", error);
            this.addNotification(`Error: ${error.message}`, "danger");
          } finally {
            button.disabled = false;
            button.classList.remove("opacity-50", "cursor-not-allowed");
          }
          return;
        }
        
        // Special handling for sendToMonday button
        if (action === "sendToMonday") {
          try {
            if (!this.currentPreviewData) {
              this.addNotification("No data available to send to Monday.", "warning");
              return;
            }
            
            button.disabled = true;
            button.classList.add("opacity-50", "cursor-not-allowed");
            
            // Pass the current preview data directly to the function
            const result = await DashboardActions.sendToMonday(this.currentPreviewData);
            
            this.addNotification("Sent to Monday successfully", "success");
            this.updateHistory("Processed", "Sent to Monday", this.currentPreviewData);
          } catch (error) {
            console.error("Error in sendToMonday:", error);
            this.addNotification(`Error: ${error.message}`, "danger");
          } finally {
            button.disabled = false;
            button.classList.remove("opacity-50", "cursor-not-allowed");
          }
          return;
        }
        
        // For other actions that are not specially handled in setupProcessButtons
        if (DashboardActions[action] && 
            !["fetchData", "shProcess", "poProcess", "soProcess", "partLookup", "validateData",
              "saveProcess", "scheduleProcess", "sendToMonday", "shipOrder"].includes(action)) {
          try {
            button.disabled = true;
            button.classList.add("opacity-50", "cursor-not-allowed");
            const result = await DashboardActions[action]();
            if (result) {
              this.addNotification(result);
            }

            // Refresh history if the action is clearHistory
            if (action === "clearHistory") {
              this.loadHistoryData();
            }
          } catch (error) {
            console.error(`Error in ${action}:`, error);
            this.addNotification(`Error: ${error.message}`, "danger");
          } finally {
            button.disabled = false;
            button.classList.remove("opacity-50", "cursor-not-allowed");
          }
        }
      });
    });
  }

  setupAdminButtons() {
    // Setup View Admin button
    const viewAdminButton = this.container.querySelector('[data-action="viewAdmin"]');
    if (viewAdminButton) {
      viewAdminButton.addEventListener("click", async () => {
        try {
          viewAdminButton.disabled = true;
          viewAdminButton.classList.add("opacity-50", "cursor-not-allowed");
          await viewAdmin();
        } catch (error) {
          console.error("Error in viewAdmin:", error);
          this.addNotification(`Error: ${error.message}`, "danger");
        } finally {
          viewAdminButton.disabled = false;
          viewAdminButton.classList.remove("opacity-50", "cursor-not-allowed");
        }
      });
    }

    // Setup Debug Admin button
    const debugAdminButton = this.container.querySelector('[data-action="debugAdmin"]');
    if (debugAdminButton) {
      debugAdminButton.addEventListener("click", async () => {
        try {
          debugAdminButton.disabled = true;
          debugAdminButton.classList.add("opacity-50", "cursor-not-allowed");
          await debugAdmin();
        } catch (error) {
          console.error("Error in debugAdmin:", error);
          this.addNotification(`Error: ${error.message}`, "danger");
        } finally {
          debugAdminButton.disabled = false;
          debugAdminButton.classList.remove("opacity-50", "cursor-not-allowed");
        }
      });
    }

    // Setup View Full Log button
    const viewFullLogButton = this.container.querySelector("#viewFullLogButton");
    if (viewFullLogButton) {
      viewFullLogButton.addEventListener("click", async () => {
        try {
          viewFullLogButton.disabled = true;
          viewFullLogButton.classList.add("opacity-50", "cursor-not-allowed");
          await viewFullLog();
        } catch (error) {
          console.error("Error in viewFullLog:", error);
          this.addNotification(`Error: ${error.message}`, "danger");
        } finally {
          viewFullLogButton.disabled = false;
          viewFullLogButton.classList.remove("opacity-50", "cursor-not-allowed");
        }
      });
    }
  }

  setupTableControls() {
    // Back to History button
    const backToHistoryBtn = this.container.querySelector("#backToHistoryBtn");
    if (backToHistoryBtn) {
      backToHistoryBtn.addEventListener("click", () => {
        DashboardProcess.showHistoryTable(this);
      });
    }

    // Refresh History button
    const refreshHistoryBtn = this.container.querySelector("#refreshHistoryBtn");
    if (refreshHistoryBtn) {
      refreshHistoryBtn.addEventListener("click", () => {
        this.loadHistoryData();
      });
    }
    
    // Label button - new
    const labelBtn = this.container.querySelector("#labelBtn");
    if (labelBtn) {
      labelBtn.addEventListener("click", () => {
        this.showLabelPreviewModal();
      });
    }
  }
  
  // Show label preview modal for each part
  showLabelPreviewModal() {
    if (!this.currentPreviewData || !this.currentPreviewData.parts || this.currentPreviewData.parts.length === 0) {
      this.addNotification("No parts available to create labels", "warning");
      return;
    }
    
    // Create modal container if it doesn't exist
    let labelModal = document.getElementById("labelPreviewModal");
    if (labelModal) {
      document.body.removeChild(labelModal);
    }
    
    labelModal = document.createElement("div");
    labelModal.id = "labelPreviewModal";
    labelModal.className = "fixed inset-0 bg-black bg-opacity-50 z-50 overflow-auto flex";
    
    // Get shipment info for address label
    const shipmentInfo = this.currentPreviewData.shipmentInfo || {};
    
    // Header for all labels - based on Envent Engineering Ltd. header
    const labelHeader = `
      <div class="flex items-center justify-between border-b border-gray-400 pb-1 mb-1">
        <div class="flex-shrink-0 mr-2">
          <div class="text-gray-600 text-xl font-bold">■Envent</div>
        </div>
        <div class="text-right text-xs">
          <div class="font-bold">Envent Engineering Ltd.</div>
          <div>2721 Hopewell Place N.E. Calgary, AB T1Y7J7</div>
          <div>Ph. ************  Fax ************  <EMAIL></div>
        </div>
      </div>
    `;
    
    // Generate address label preview
    const addressLabel = `
      <div class="bg-white p-2 mb-3 rounded-md shadow-md border-2 border-gray-300 w-full">
        ${labelHeader}
        <div class="grid grid-cols-2">
          <div class="pr-2">
            <div class="text-xs font-bold mb-1">SHIP TO:</div>
            <div class="text-xs font-bold">${shipmentInfo.companyName || this.currentPreviewData.customer || "N/A"}</div>
            <div class="text-xs">${shipmentInfo.attention || "N/A"}</div>
            <div class="text-xs">${shipmentInfo.phone || "N/A"}</div>
            <div class="text-xs">${shipmentInfo.email || "N/A"}</div>
          </div>
          <div>
            <div class="text-xs">${shipmentInfo.addressLine1 || "N/A"}</div>
            ${shipmentInfo.addressLine2 ? `<div class="text-xs">${shipmentInfo.addressLine2}</div>` : ''}
            <div class="text-xs">${shipmentInfo.city || "N/A"}, ${shipmentInfo.state || "N/A"} ${shipmentInfo.postalCode || "N/A"}</div>
            <div class="text-xs">${shipmentInfo.country || "N/A"}</div>
          </div>
        </div>
      </div>
    `;
    
    // Generate label preview content for each part
    const labelPreviews = this.currentPreviewData.parts.map((part, index) => {
      return `
        <div class="bg-white p-2 mb-2 rounded-md shadow-md border-2 border-gray-300 w-full">
          ${labelHeader}
          <div class="grid grid-cols-3">
            <div class="col-span-1">
              <div class="text-xs font-bold">${part.inventoryID || "N/A"}</div>
            </div>
            <div class="col-span-2">
              <div class="text-xs">${part.description || "No description"}</div>
              <div class="text-xs">Qty: <span class="font-bold">${part.shippedQuantity || "1"}</span></div>
              <div class="text-xs text-gray-500">${part.lotSerialNumber ? `S/N: ${part.lotSerialNumber}` : ""}</div>
            </div>
          </div>
        </div>
      `;
    }).join("");
    
    // Create modal content
    labelModal.innerHTML = `
      <div class="m-auto bg-white rounded-lg shadow-xl w-[650px] max-w-full">
        <div class="flex justify-between items-center p-2 border-b">
          <h3 class="text-lg font-semibold">Label Preview</h3>
          <button id="closeLabelModalBtn" class="text-gray-500 hover:text-gray-700">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="p-2 max-h-[500px] overflow-y-auto">
          <div class="mb-2">
            <div class="flex justify-between items-center mb-1">
              <p class="text-sm text-gray-600">Address Label:</p>
              <button id="printAddressLabelBtn" class="px-2 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print Address Label
              </button>
            </div>
            ${addressLabel}
            
            <div class="flex justify-between items-center mb-1 mt-3">
              <p class="text-sm text-gray-600">Part Labels:</p>
              <button id="printPartLabelsBtn" class="px-2 py-1 text-xs bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                </svg>
                Print Part Labels
              </button>
            </div>
            ${labelPreviews}
          </div>
        </div>
        
        <div class="p-2 border-t flex justify-end">
          <button id="printAllLabelsBtn" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print All Labels
          </button>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.appendChild(labelModal);
    
    // Set up event listeners
    const closeBtn = document.getElementById("closeLabelModalBtn");
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        document.body.removeChild(labelModal);
      });
    }
    
    // Set up print all labels button
    const printAllLabelsBtn = document.getElementById("printAllLabelsBtn");
    if (printAllLabelsBtn) {
      printAllLabelsBtn.addEventListener("click", () => {
        this.printWithDymo('all');
        document.body.removeChild(labelModal);
      });
    }
    
    // Set up print address label button
    const printAddressLabelBtn = document.getElementById("printAddressLabelBtn");
    if (printAddressLabelBtn) {
      printAddressLabelBtn.addEventListener("click", () => {
        this.printWithDymo('address');
        // Don't close the modal after printing just the address label
      });
    }
    
    // Set up print part labels button
    const printPartLabelsBtn = document.getElementById("printPartLabelsBtn");
    if (printPartLabelsBtn) {
      printPartLabelsBtn.addEventListener("click", () => {
        this.printWithDymo('parts');
        // Don't close the modal after printing just the part labels
      });
    }
  }
  
  // Print labels with Dymo
  printWithDymo(labelType = 'all') {
    try {
      // This is a placeholder for actual Dymo SDK integration
      const labelTypeDisplay = labelType === 'all' ? 'all labels' : 
                               labelType === 'address' ? 'address label' : 'part labels';
                               
      this.addNotification(`Printing ${labelTypeDisplay} with Dymo...`, "info");
      
      if (labelType === 'all' || labelType === 'address') {
        // Log the address label information
        const shipmentInfo = this.currentPreviewData.shipmentInfo || {};
        console.log("Printing address label:", {
          company: shipmentInfo.companyName || this.currentPreviewData.customer || "N/A",
          attention: shipmentInfo.attention || "N/A",
          phone: shipmentInfo.phone || "N/A",
          email: shipmentInfo.email || "N/A",
          addressLine1: shipmentInfo.addressLine1 || "N/A",
          addressLine2: shipmentInfo.addressLine2 || "N/A",
          city: shipmentInfo.city || "N/A",
          state: shipmentInfo.state || "N/A",
          postalCode: shipmentInfo.postalCode || "N/A",
          country: shipmentInfo.country || "N/A"
        });
      }
      
      if (labelType === 'all' || labelType === 'parts') {
        // Log the part label information
        console.log(`Printing ${this.currentPreviewData.parts.length} part labels:`, 
          this.currentPreviewData.parts.map(part => ({
            inventoryID: part.inventoryID || "N/A",
            description: part.description || "No description",
            quantity: part.shippedQuantity || "1",
            serialNumber: part.lotSerialNumber || ""
          }))
        );
      }
      
      // In a real implementation, this would use the Dymo Label Framework SDK
      // Example: https://developers.dymo.com/2010/06/02/dymo-label-framework-javascript-library-samples/
      
      setTimeout(() => {
        this.addNotification(`${labelTypeDisplay.charAt(0).toUpperCase() + labelTypeDisplay.slice(1)} sent to Dymo printer successfully`, "success");
      }, 1500);
    } catch (error) {
      console.error("Error printing with Dymo:", error);
      this.addNotification(`Error printing labels: ${error.message}`, "danger");
    }
  }

  // Helper to synchronously get current user information for immediate use
  getCurrentUserInfo() {
    // First try to use the cached user info
    if (this.currentUser) {
      return {
        userName: this.currentUser["User Name"] || "User",
        avatarId: this.currentUser.Avatar || "default"
      };
    }

    // If no cached data, try to get from local storage as fallback
    try {
      const userStr = localStorage.getItem("user");
      if (userStr) {
        const user = JSON.parse(userStr);
        return {
          userName: user["User Name"] || "User",
          avatarId: user.Avatar || "default"
        };
      }
    } catch (e) {
      console.warn("Error retrieving user from localStorage:", e);
    }

    // Default values if nothing is available
    return {
      userName: "User",
      avatarId: "default"
    };
  }

  // Normalize ID to ensure consistent comparison
  normalizeId(id) {
    return String(id).trim();
  }

  // Store a deleted ID permanently to ensure it doesn't reappear
  async storeDeletedId(historyId) {
    try {
      // Normalize the ID to ensure consistent string format
      const normalizedId = this.normalizeId(historyId);
      
      // Add to in-memory set first for immediate effect
      this.deletedHistoryIds.add(normalizedId);
      
      if (typeof chrome !== "undefined" && chrome.storage) {
        // Using Chrome storage
        const result = await new Promise((resolve) => {
          chrome.storage.local.get(["deletedHistoryIds"], (data) => {
            resolve(data);
          });
        });
        
        const deletedIds = result.deletedHistoryIds || [];
        
        // Only add if not already in the array
        if (!deletedIds.includes(normalizedId)) {
          deletedIds.push(normalizedId);
          
          await new Promise((resolve, reject) => {
            chrome.storage.local.set({ deletedHistoryIds: deletedIds }, () => {
              if (chrome.runtime.lastError) {
                reject(new Error(chrome.runtime.lastError.message));
              } else {
                resolve();
              }
            });
          });
          
          console.log(`ID ${normalizedId} added to permanent deleted list. Total deleted: ${deletedIds.length}`);
        }
      } else {
        // Using localStorage
        const deletedIdsStr = localStorage.getItem("deletedHistoryIds");
        const deletedIds = deletedIdsStr ? JSON.parse(deletedIdsStr) : [];
        
        // Only add if not already in the array
        if (!deletedIds.includes(normalizedId)) {
          deletedIds.push(normalizedId);
          localStorage.setItem("deletedHistoryIds", JSON.stringify(deletedIds));
          console.log(`ID ${normalizedId} added to permanent deleted list in localStorage. Total deleted: ${deletedIds.length}`);
        }
      }
    } catch (error) {
      console.error("Error storing deleted ID:", error);
    }
  }

  // Load history data
  async loadHistoryData() {
    try {
      // First get the permanently deleted IDs
      let deletedIds = [];
      
      if (typeof chrome !== "undefined" && chrome.storage) {
        // Get from Chrome storage
        const deletedResult = await new Promise((resolve) => {
          chrome.storage.local.get(["deletedHistoryIds"], (data) => {
            resolve(data);
          });
        });
        deletedIds = deletedResult.deletedHistoryIds || [];
      } else {
        // Get from localStorage
        const deletedIdsStr = localStorage.getItem("deletedHistoryIds");
        if (deletedIdsStr) {
          deletedIds = JSON.parse(deletedIdsStr);
        }
      }
      
      // Update in-memory set of deleted IDs
      this.deletedHistoryIds = new Set(deletedIds.map(id => this.normalizeId(id)));
      
      console.log(`Found ${this.deletedHistoryIds.size} permanently deleted IDs to exclude`);
      
      // Get history items either from Chrome storage or localStorage
      let historyItems = [];

      if (typeof chrome !== "undefined" && chrome.storage) {
        // Get from Chrome storage
        const result = await new Promise((resolve) => {
          chrome.storage.local.get(["processHistory"], (data) => {
            resolve(data);
          });
        });
        historyItems = result.processHistory || [];
      } else {
        // Get from localStorage
        const historyStr = localStorage.getItem("processHistory");
        if (historyStr) {
          historyItems = JSON.parse(historyStr);
        }
      }

      // Filter out any entries that are in our deleted IDs list
      const beforeLength = historyItems.length;
      historyItems = historyItems.filter(item => {
        // Skip entries with IDs in the deleted list
        return !item.id || !this.deletedHistoryIds.has(this.normalizeId(item.id));
      });
      
      if (beforeLength > historyItems.length) {
        console.log(`Filtered out ${beforeLength - historyItems.length} previously deleted entries`);
      }

      // Get the current user info
      const userInfo = this.getCurrentUserInfo();

      // Remove duplicate entries and ensure user info
      const uniqueHistoryItems = this.removeDuplicateHistoryEntries(historyItems);
      
      // Ensure each history item has user info and ID if missing
      uniqueHistoryItems.forEach(item => {
        if (!item.userName) {
          item.userName = userInfo.userName;
        }
        if (!item.avatarId) {
          item.avatarId = userInfo.avatarId;
        }
        // Generate an ID if missing
        if (!item.id) {
          item.id = this.generateUniqueId();
        }
      });

      // Save the updated history items to our cache
      this.historyItems = uniqueHistoryItems;

      // Update the history table
      const historyContainer = this.container.querySelector("#historyTableContainer");
      if (historyContainer) {
        historyContainer.innerHTML = this.createHistoryTableHTML(uniqueHistoryItems);
        this.setupHistoryRowActions();
      }
    } catch (error) {
      console.error("Error loading history:", error);
      this.addNotification("Error loading history data", "danger");
    }
  }

  // Set up action buttons in history rows
  setupHistoryRowActions() {
    // Set up edit buttons
    const editButtons = this.container.querySelectorAll('.history-edit-btn');
    editButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.stopPropagation();
        const historyId = button.getAttribute('data-id');
        this.editHistoryEntry(historyId);
      });
    });

    // Set up delete buttons
    const deleteButtons = this.container.querySelectorAll('.history-delete-btn');
    deleteButtons.forEach(button => {
      button.addEventListener('click', (event) => {
        event.stopPropagation();
        const historyId = button.getAttribute('data-id');
        this.deleteHistoryEntry(historyId);
      });
    });

    // Make entire row clickable for edit
    const historyRows = this.container.querySelectorAll('.history-row');
    historyRows.forEach(row => {
      row.addEventListener('click', () => {
        const historyId = row.getAttribute('data-id');
        this.editHistoryEntry(historyId);
      });
    });
  }

  // Edit a history entry - load it into preview panel
  async editHistoryEntry(historyId) {
    try {
      this.addNotification("Loading entry for editing...", "info");
      
      // Find the specific item with the given ID in our cached history items
      const historyItem = this.historyItems.find(item => item.id === historyId);
      
      if (!historyItem || !historyItem.previewData) {
        this.addNotification("Could not find the requested history entry or no preview data available.", "warning");
        return;
      }
      
      // Save the item to preview data
      this.savePreviewData(historyItem.previewData);
      
      // Show the preview panel with this data
      DashboardProcess.showPreviewTable(this, historyItem.previewData);
      
      this.addNotification("Entry loaded for editing. You can make changes and save again.", "success");
    } catch (error) {
      console.error("Error editing history entry:", error);
      this.addNotification(`Error: ${error.message}`, "danger");
    }
  }

  // Delete a history entry
  async deleteHistoryEntry(historyId) {
    try {
      // Confirm deletion
      if (!confirm("Are you sure you want to delete this history entry?")) {
        return;
      }
      
      console.log("Deleting history entry with ID:", historyId);
      this.addNotification("Deleting history entry...", "info");
      
      // First store the ID in our permanent deleted IDs list
      await this.storeDeletedId(historyId);
      
      // Use the normalized ID for consistent comparison
      const normalizedId = this.normalizeId(historyId);
      
      if (typeof chrome !== "undefined" && chrome.storage) {
        // Using Chrome storage
        chrome.storage.local.get(["processHistory"], (result) => {
          let history = result.processHistory || [];
          
          console.log("Found", history.length, "history entries");
          
          // Log IDs for debugging
          if (history.length > 0) {
            console.log("First few history entry IDs:", history.slice(0, 3).map(item => item.id));
          }
          
          // Filter out the entry with the given ID (using normalized IDs)
          const beforeLength = history.length;
          
          history = history.filter(item => {
            return this.normalizeId(item.id) !== normalizedId;
          });
          
          console.log(`After filtering: ${beforeLength} → ${history.length} entries`);
          
          // Update the storage with filtered history
          chrome.storage.local.set({ processHistory: history }, () => {
            if (chrome.runtime.lastError) {
              console.error("Error saving to storage:", chrome.runtime.lastError);
              this.addNotification("Error saving updated history.", "danger");
              return;
            }
            
            // Update our cached history items
            this.historyItems = this.historyItems.filter(item => this.normalizeId(item.id) !== normalizedId);
            
            // Check if an item was actually removed
            if (beforeLength > history.length) {
              this.addNotification("History entry deleted successfully.", "success");
            } else {
              this.addNotification("No matching entry found to delete.", "warning");
            }
            
            // Update the UI regardless
            const historyContainer = this.container.querySelector("#historyTableContainer");
            if (historyContainer) {
              historyContainer.innerHTML = this.createHistoryTableHTML(this.historyItems);
              this.setupHistoryRowActions();
            }
          });
        });
      } else {
        // Using localStorage
        try {
          let history = [];
          try {
            const historyStr = localStorage.getItem("processHistory");
            history = historyStr ? JSON.parse(historyStr) : [];
          } catch (parseError) {
            console.error("Error parsing history:", parseError);
            this.addNotification("Error reading history data.", "danger");
            return;
          }
          
          console.log("Found", history.length, "history entries in localStorage");
          
          const beforeLength = history.length;
          
          // Filter out the entry with the given ID (using normalized IDs)
          history = history.filter(item => this.normalizeId(item.id) !== normalizedId);
          
          console.log(`After filtering: ${beforeLength} → ${history.length} entries`);
          
          // Save the updated history back to localStorage
          localStorage.setItem("processHistory", JSON.stringify(history));
          
          // Update our cached history items
          this.historyItems = this.historyItems.filter(item => this.normalizeId(item.id) !== normalizedId);
          
          // Check if an item was actually removed
          if (beforeLength > history.length) {
            this.addNotification("History entry deleted successfully.", "success");
          } else {
            this.addNotification("No matching entry found to delete.", "warning");
          }
          
          // Update the UI regardless
          const historyContainer = this.container.querySelector("#historyTableContainer");
          if (historyContainer) {
            historyContainer.innerHTML = this.createHistoryTableHTML(this.historyItems);
            this.setupHistoryRowActions();
          }
        } catch (e) {
          console.error("Error in localStorage delete operation:", e);
          this.addNotification(`Error deleting entry: ${e.message}`, "danger");
        }
      }
    } catch (error) {
      console.error("Error deleting history entry:", error);
      this.addNotification(`Error: ${error.message}`, "danger");
    }
  }

  // Remove duplicate history entries based on timestamp and reference
  removeDuplicateHistoryEntries(historyItems) {
    const uniqueEntries = [];
    const seen = new Set();
    
    historyItems.forEach(item => {
      // Create a unique key for each entry
      const key = `${item.timestamp}-${item.previewData?.reference || ''}-${item.type || ''}`;
      
      if (!seen.has(key)) {
        seen.add(key);
        // Add a unique ID if not present
        if (!item.id) {
          item.id = this.generateUniqueId();
        }
        uniqueEntries.push(item);
      }
    });
    
    return uniqueEntries;
  }

  // Generate a unique ID for history entries
  generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  addNotification(message, type = "info") {
    this.notificationSystem.addNotification(message, type);
  }

  updateHistory(action, result, previewData = null) {
    const timestamp = new Date().toLocaleString();
    
    // Get current user information
    const userInfo = this.getCurrentUserInfo();
    
    // Create the log entry with user info and standardized naming
    const logEntry = {
      action, // This will be "Saved", "Scheduled", "Processed", etc.
      result,
      timestamp,
      previewData,
      userName: userInfo.userName,
      avatarId: userInfo.avatarId,
      type: previewData?.type || "Unknown",
      id: this.generateUniqueId() // Add a unique ID for each entry
    };

    // Add to full log
    this.fullLog.unshift(logEntry);

    // If it's a monday.com transfer, add to mondayTransfers
    if (action === "sendToMonday") {
      this.mondayTransfers.unshift(logEntry);
    }

    // Save to storage - use a standardized action name
    this.saveHistoryToStorage(logEntry);

    // Always update the history display
    this.loadHistoryData();
  }

  // Helper to save history to storage
  saveHistoryToStorage(logEntry) {
    // First check if we need to generate an ID for this entry
    if (!logEntry.id) {
      logEntry.id = this.generateUniqueId();
    }
    
    // Check if this entry's ID is in our deleted list
    const normalizedId = this.normalizeId(logEntry.id);
    if (this.deletedHistoryIds.has(normalizedId)) {
      console.log(`Skipping addition of previously deleted entry with ID: ${logEntry.id}`);
      return;
    }
    
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["processHistory"], (result) => {
        let history = result.processHistory || [];
        
        // Skip if it's a duplicate entry
        const isDuplicate = history.some(item => 
          item.timestamp === logEntry.timestamp && 
          item.previewData?.reference === logEntry.previewData?.reference &&
          item.type === logEntry.type
        );
        
        if (!isDuplicate) {
          history.unshift(logEntry);
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          chrome.storage.local.set({ processHistory: history });
          console.log(`Added entry with ID ${logEntry.id} to history. Total: ${history.length}`);
        }
      });
    } else {
      try {
        let history = JSON.parse(localStorage.getItem("processHistory")) || [];
        
        // Skip if it's a duplicate entry
        const isDuplicate = history.some(item => 
          item.timestamp === logEntry.timestamp && 
          item.previewData?.reference === logEntry.previewData?.reference &&
          item.type === logEntry.type
        );
        
        if (!isDuplicate) {
          history.unshift(logEntry);
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          localStorage.setItem("processHistory", JSON.stringify(history));
          console.log(`Added entry with ID ${logEntry.id} to history. Total: ${history.length}`);
        }
      } catch (e) {
        console.error("Error saving to localStorage:", e);
      }
    }
  }

  displayFullLog() {
    const logContent = JSON.stringify(this.fullLog, null, 2);
    console.log("Full Log:", logContent);
    // In a real implementation, you might want to display this in a modal or a new page
    alert("Full log has been output to the console.");
  }

  createHistoryTableHTML(historyItems) {
    if (!historyItems || historyItems.length === 0) {
      return `
        <div class="p-4 text-center text-gray-500 italic">
          Process orders to see your history
        </div>
      `;
    }

    return `
      <div class="overflow-hidden shadow-sm rounded-lg">
        <table class="min-w-full divide-y divide-gray-200 text-xs" style="table-layout: fixed;">
          <colgroup>
            <col style="width: 140px;"> <!-- User column -->
            <col style="width: 70px;"> <!-- Type column -->
            <col style="width: 150px;"> <!-- Customer column - reduced to 150px -->
            <col style="width: 100px;"> <!-- Reference column -->
            <col style="width: 80px;"> <!-- Date column -->
            <col style="width: 90px;"> <!-- Status column -->
            <col style="width: 80px;"> <!-- Actions column -->
          </colgroup>
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">User</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Type</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Customer</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Reference</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Date</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Status</th>
              <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider truncate">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            ${historyItems
              .map(
                (item) => `
              <tr class="history-row hover:bg-gray-50 cursor-pointer" data-id="${item.id}">
                <td class="px-3 py-2" style="width: 140px; max-width: 140px;">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-8 w-8">
                      <img class="h-8 w-8 rounded-full" src="images/avatars/avatar_${item.avatarId || 'default'}.jpg" alt="${item.userName || 'User'}">
                    </div>
                    <div class="ml-2 min-w-0">
                      <div class="text-xs font-medium text-gray-900 truncate">${item.userName || 'User'}</div>
                      <div class="text-xs text-gray-500 truncate">${
                        // Map action names to display names
                        item.action === "saveProcess" ? "Saved" : 
                        item.action === "scheduleProcess" ? "Scheduled" : 
                        item.action
                      }</div>
                    </div>
                  </div>
                </td>
                <td class="px-3 py-2" style="width: 70px; max-width: 70px;">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    item.type === "SH"
                      ? "bg-green-100 text-green-800"
                      : item.type === "PO"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-purple-100 text-purple-800"
                  }">${item.type}</span>
                </td>
                <td class="px-3 py-2" style="width: 150px; max-width: 150px;">
                  <div class="truncate whitespace-nowrap overflow-hidden" title="${item.previewData?.customer || 'N/A'}">
                    ${item.previewData?.customer || "N/A"}
                  </div>
                </td>
                <td class="px-3 py-2" style="width: 100px; max-width: 100px;">
                  <div class="truncate">${item.previewData?.reference || "N/A"}</div>
                </td>
                <td class="px-3 py-2" style="width: 80px; max-width: 80px;">
                  <div class="truncate">${new Date(item.timestamp).toLocaleDateString()}</div>
                </td>
                <td class="px-3 py-2" style="width: 90px; max-width: 90px;">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full truncate ${
                    item.action === "Saved" || item.action === "saveProcess"
                      ? "bg-green-100 text-green-800"
                      : item.action === "Scheduled" || item.action === "scheduleProcess"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-blue-100 text-blue-800"
                  }">
                    ${
                      item.action === "Saved" || item.action === "saveProcess"
                        ? "Saved"
                        : item.action === "Scheduled" || item.action === "scheduleProcess"
                          ? "Scheduled"
                          : "Processed"
                    }
                  </span>
                </td>
                <td class="px-3 py-2 text-center" style="width: 80px; max-width: 80px;">
                  <div class="flex justify-center space-x-2">
                    <button class="history-edit-btn text-blue-600 hover:text-blue-800" data-id="${item.id}" title="Edit">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                      </svg>
                    </button>
                    <button class="history-delete-btn text-red-600 hover:text-red-800" data-id="${item.id}" title="Delete">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            `,
              )
              .join("")}
          </tbody>
        </table>
      </div>
    `;
  }
}

export default DashboardComponent;