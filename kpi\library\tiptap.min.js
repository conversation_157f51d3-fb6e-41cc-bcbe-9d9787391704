
    /*!
    * tiptap v1.32.1
    * (c) 2021 überdosis GbR (limited liability)
    * @license MIT
    */


/*!
    * tiptap v1.32.1
    * (c) 2021 überdosis GbR (limited liability)
    * @license MIT
    */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("prosemirror-state"),require("prosemirror-view"),require("prosemirror-model"),require("prosemirror-dropcursor"),require("prosemirror-gapcursor"),require("prosemirror-keymap"),require("prosemirror-commands"),require("prosemirror-inputrules"),require("tiptap-utils"),require("vue"),require("tiptap-commands")):"function"==typeof define&&define.amd?define(["exports","prosemirror-state","prosemirror-view","prosemirror-model","prosemirror-dropcursor","prosemirror-gapcursor","prosemirror-keymap","prosemirror-commands","prosemirror-inputrules","tiptap-utils","vue","tiptap-commands"],t):t((e=e||self).tiptap={},e.prosemirrorState,e.prosemirrorView,e.prosemirrorModel,e.prosemirrorDropcursor,e.prosemirrorGapcursor,e.prosemirrorKeymap,e.prosemirrorCommands,e.prosemirrorInputrules,e.tiptapUtils,e.Vue,e.tiptapCommands)}(this,(function(e,t,s,i,n,o,r,a,h,d,c,u){"use strict";c=c&&Object.prototype.hasOwnProperty.call(c,"default")?c.default:c;class l{constructor(e,{editor:t,extension:s,parent:i,node:n,view:o,decorations:r,getPos:a}){this.component=e,this.editor=t,this.extension=s,this.parent=i,this.node=n,this.view=o,this.decorations=r,this.isNode=!!this.node.marks,this.isMark=!this.isNode,this.getPos=this.isMark?this.getMarkPos:a,this.captureEvents=!0,this.dom=this.createDOM(),this.contentDOM=this.vm.$refs.content}createDOM(){const e=c.extend(this.component),t={editor:this.editor,node:this.node,view:this.view,getPos:()=>this.getPos(),decorations:this.decorations,selected:!1,options:this.extension.options,updateAttrs:e=>this.updateAttrs(e)};return"function"==typeof this.extension.setSelection&&(this.setSelection=this.extension.setSelection),"function"==typeof this.extension.update&&(this.update=this.extension.update),this.vm=new e({parent:this.parent,propsData:t}).$mount(),this.vm.$el}update(e,t){return e.type===this.node.type&&(e===this.node&&this.decorations===t||(this.node=e,this.decorations=t,this.updateComponentProps({node:e,decorations:t})),!0)}updateComponentProps(e){if(!this.vm._props)return;const t=c.config.silent;c.config.silent=!0,Object.entries(e).forEach(([e,t])=>{this.vm._props[e]=t}),c.config.silent=t}updateAttrs(e){if(!this.view.editable)return;const{state:t}=this.view,{type:s}=this.node,i=this.getPos(),n={...this.node.attrs,...e},o=this.isMark?t.tr.removeMark(i.from,i.to,s).addMark(i.from,i.to,s.create(n)):t.tr.setNodeMarkup(i,null,n);this.view.dispatch(o)}ignoreMutation(e){return"selection"!==e.type&&(!this.contentDOM||!this.contentDOM.contains(e.target))}stopEvent(e){if("function"==typeof this.extension.stopEvent)return this.extension.stopEvent(e);const t=!!this.extension.schema.draggable;if(t&&"mousedown"===e.type){const t=e.target.closest&&e.target.closest("[data-drag-handle]");t&&(this.dom===t||this.dom.contains(t))&&(this.captureEvents=!1,document.addEventListener("dragend",()=>{this.captureEvents=!0},{once:!0}))}const s="copy"===e.type,i="paste"===e.type,n="cut"===e.type,o=e.type.startsWith("drag")||"drop"===e.type;return!(t&&o||s||i||n)&&this.captureEvents}selectNode(){this.updateComponentProps({selected:!0})}deselectNode(){this.updateComponentProps({selected:!1})}getMarkPos(){const e=this.view.posAtDOM(this.dom),t=this.view.state.doc.resolve(e);return d.getMarkRange(t,this.node.type)}destroy(){this.vm.$destroy()}}class p{constructor(e={}){this.options={...this.defaultOptions,...e}}init(){return null}bindEditor(e=null){this.editor=e}get name(){return null}get type(){return"extension"}get defaultOptions(){return{}}get plugins(){return[]}inputRules(){return[]}pasteRules(){return[]}keys(){return{}}}class m{constructor(e=[],t){e.forEach(e=>{e.bindEditor(t),e.init()}),this.extensions=e}get nodes(){return this.extensions.filter(e=>"node"===e.type).reduce((e,{name:t,schema:s})=>({...e,[t]:s}),{})}get options(){const{view:e}=this;return this.extensions.reduce((t,s)=>({...t,[s.name]:new Proxy(s.options,{set(t,s,i){const n=t[s]!==i;return Object.assign(t,{[s]:i}),n&&e.updateState(e.state),!0}})}),{})}get marks(){return this.extensions.filter(e=>"mark"===e.type).reduce((e,{name:t,schema:s})=>({...e,[t]:s}),{})}get plugins(){return this.extensions.filter(e=>e.plugins).reduce((e,{plugins:t})=>[...e,...t],[])}keymaps({schema:e}){return[...this.extensions.filter(e=>["extension"].includes(e.type)).filter(e=>e.keys).map(t=>t.keys({schema:e})),...this.extensions.filter(e=>["node","mark"].includes(e.type)).filter(e=>e.keys).map(t=>t.keys({type:e[t.type+"s"][t.name],schema:e}))].map(e=>r.keymap(e))}inputRules({schema:e,excludedExtensions:t}){if(!(t instanceof Array)&&t)return[];const s=t instanceof Array?this.extensions.filter(e=>!t.includes(e.name)):this.extensions;return[...s.filter(e=>["extension"].includes(e.type)).filter(e=>e.inputRules).map(t=>t.inputRules({schema:e})),...s.filter(e=>["node","mark"].includes(e.type)).filter(e=>e.inputRules).map(t=>t.inputRules({type:e[t.type+"s"][t.name],schema:e}))].reduce((e,t)=>[...e,...t],[])}pasteRules({schema:e,excludedExtensions:t}){if(!(t instanceof Array)&&t)return[];const s=t instanceof Array?this.extensions.filter(e=>!t.includes(e.name)):this.extensions;return[...s.filter(e=>["extension"].includes(e.type)).filter(e=>e.pasteRules).map(t=>t.pasteRules({schema:e})),...s.filter(e=>["node","mark"].includes(e.type)).filter(e=>e.pasteRules).map(t=>t.pasteRules({type:e[t.type+"s"][t.name],schema:e}))].reduce((e,t)=>[...e,...t],[])}commands({schema:e,view:t}){return this.extensions.filter(e=>e.commands).reduce((s,i)=>{const{name:n,type:o}=i,r={},a=i.commands({schema:e,...["node","mark"].includes(o)?{type:e[o+"s"][n]}:{}}),h=(e,s)=>!!t.editable&&(t.focus(),e(s)(t.state,t.dispatch,t)),d=(e,t)=>{Array.isArray(t)?r[e]=e=>t.forEach(t=>h(t,e)):"function"==typeof t&&(r[e]=e=>h(t,e))};return"object"==typeof a?Object.entries(a).forEach(([e,t])=>{d(e,t)}):d(n,a),{...s,...r}},{})}}function f(e=0,t=0,s=0){return Math.min(Math.max(parseInt(e,10),t),s)}class g extends p{constructor(e={}){super(e)}get type(){return"node"}get view(){return null}get schema(){return null}command(){return()=>{}}}class v extends g{get name(){return"doc"}get schema(){return{content:"block+"}}}class b extends g{get name(){return"paragraph"}get schema(){return{content:"inline*",group:"block",draggable:!1,parseDOM:[{tag:"p"}],toDOM:()=>["p",0]}}commands({type:e}){return()=>u.setBlockType(e)}}class w extends g{get name(){return"text"}get schema(){return{group:"inline"}}}var y={props:{editor:{default:null,type:Object}},watch:{editor:{immediate:!0,handler(e){e&&e.element&&this.$nextTick(()=>{this.$el.appendChild(e.element.firstChild),e.setParentComponent(this)})}}},render:e=>e("div"),beforeDestroy(){this.editor.element=this.$el}};class k{constructor({options:e}){this.options=e,this.preventHide=!1,this.mousedownHandler=this.handleClick.bind(this),this.options.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.blurHandler=()=>{this.preventHide?this.preventHide=!1:this.options.editor.emit("menubar:focusUpdate",!1)},this.options.editor.on("blur",this.blurHandler)}handleClick(){this.preventHide=!0}destroy(){this.options.element.removeEventListener("mousedown",this.mousedownHandler),this.options.editor.off("blur",this.blurHandler)}}var x={props:{editor:{default:null,type:Object}},data:()=>({focused:!1}),watch:{editor:{immediate:!0,handler(e){e&&this.$nextTick(()=>{var s;e.registerPlugin((s={editor:e,element:this.$el},new t.Plugin({key:new t.PluginKey("menu_bar"),view:e=>new k({editorView:e,options:s})}))),this.focused=e.focused,e.on("focus",()=>{this.focused=!0}),e.on("menubar:focusUpdate",e=>{this.focused=e})})}}},render(){return this.editor?this.$scopedSlots.default({focused:this.focused,focus:this.editor.focus,commands:this.editor.commands,isActive:this.editor.isActive,getMarkAttrs:this.editor.getMarkAttrs.bind(this.editor),getNodeAttrs:this.editor.getNodeAttrs.bind(this.editor)}):null}};function M(e,t,s){const i=document.createRange();return i.setEnd(e,null==s?e.nodeValue.length:s),i.setStart(e,Math.max(t,0)),i}function P(e,t){const s=e.getClientRects();return s.length?s[t<0?0:s.length-1]:e.getBoundingClientRect()}function A(e,t,s=!1){const{node:i,offset:n}=e.docView.domFromPos(t);let o,r;if(3===i.nodeType)s&&n<i.nodeValue.length?(r=P(M(i,n-1,n),-1),o="right"):n<i.nodeValue.length&&(r=P(M(i,n,n+1),-1),o="left");else if(i.firstChild){if(n<i.childNodes.length){const e=i.childNodes[n];r=P(3===e.nodeType?M(e):e,-1),o="left"}if((!r||r.top===r.bottom)&&n){const e=i.childNodes[n-1];r=P(3===e.nodeType?M(e):e,1),o="right"}}else r=i.getBoundingClientRect(),o="left";const a=r[o];return{top:r.top,bottom:r.bottom,left:a,right:a}}class O{constructor({options:e,editorView:t}){this.options={element:null,keepInBounds:!0,onUpdate:()=>!1,...e},this.editorView=t,this.isActive=!1,this.left=0,this.bottom=0,this.top=0,this.preventHide=!1,this.mousedownHandler=this.handleClick.bind(this),this.options.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.focusHandler=({view:e})=>{this.update(e)},this.options.editor.on("focus",this.focusHandler),this.blurHandler=({event:e})=>{this.preventHide?this.preventHide=!1:this.hide(e)},this.options.editor.on("blur",this.blurHandler)}handleClick(){this.preventHide=!0}update(e,t){const{state:s}=e;if(e.composing)return;if(t&&t.doc.eq(s.doc)&&t.selection.eq(s.selection))return;if(s.selection.empty)return void this.hide();const{from:i,to:n}=s.selection,o=A(e,i),r=A(e,n,!0),a=this.options.element.offsetParent;if(!a)return void this.hide();const h=a.getBoundingClientRect(),d=this.options.element.getBoundingClientRect(),c=(o.left+r.left)/2-h.left;this.left=Math.round(this.options.keepInBounds?Math.min(h.width-d.width/2,Math.max(c,d.width/2)):c),this.bottom=Math.round(h.bottom-o.top),this.top=Math.round(r.bottom-h.top),this.isActive=!0,this.sendUpdate()}sendUpdate(){this.options.onUpdate({isActive:this.isActive,left:this.left,bottom:this.bottom,top:this.top})}hide(e){e&&e.relatedTarget&&this.options.element.parentNode&&this.options.element.parentNode.contains(e.relatedTarget)||(this.isActive=!1,this.sendUpdate())}destroy(){this.options.element.removeEventListener("mousedown",this.mousedownHandler),this.options.editor.off("focus",this.focusHandler),this.options.editor.off("blur",this.blurHandler)}}var S={props:{editor:{default:null,type:Object},keepInBounds:{default:!0,type:Boolean}},data:()=>({menu:{isActive:!1,left:0,bottom:0}}),watch:{editor:{immediate:!0,handler(e){e&&this.$nextTick(()=>{var s;e.registerPlugin((s={editor:e,element:this.$el,keepInBounds:this.keepInBounds,onUpdate:e=>{e.isActive&&!1===this.menu.isActive?this.$emit("show",e):e.isActive||!0!==this.menu.isActive||this.$emit("hide",e),this.menu=e}},new t.Plugin({key:new t.PluginKey("menu_bubble"),view:e=>new O({editorView:e,options:s})})))})}}},render(){return this.editor?this.$scopedSlots.default({focused:this.editor.view.focused,focus:this.editor.focus,commands:this.editor.commands,isActive:this.editor.isActive,getMarkAttrs:this.editor.getMarkAttrs.bind(this.editor),getNodeAttrs:this.editor.getNodeAttrs.bind(this.editor),menu:this.menu}):null},beforeDestroy(){this.editor.unregisterPlugin("menu_bubble")}};class N{constructor({options:e,editorView:t}){this.options={resizeObserver:!0,element:null,onUpdate:()=>!1,...e},this.preventHide=!1,this.editorView=t,this.isActive=!1,this.top=0,this.mousedownHandler=this.handleClick.bind(this),this.options.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.focusHandler=({view:e})=>{this.update(e)},this.options.editor.on("focus",this.focusHandler),this.blurHandler=({event:e})=>{this.preventHide?this.preventHide=!1:this.hide(e)},this.options.editor.on("blur",this.blurHandler),this.options.resizeObserver&&window.ResizeObserver&&(this.resizeObserver=new ResizeObserver(()=>{this.isActive&&this.update(this.editorView)}),this.resizeObserver.observe(this.editorView.dom))}handleClick(){this.preventHide=!0}update(e,t){const{state:s}=e;if(t&&t.doc.eq(s.doc)&&t.selection.eq(s.selection))return;if(!s.selection.empty)return void this.hide();const i=e.domAtPos(s.selection.anchor);if(!("<br>"===i.node.innerHTML&&"P"===i.node.tagName&&i.node.parentNode===e.dom))return void this.hide();const n=this.options.element.offsetParent;if(!n)return void this.hide();const o=n.getBoundingClientRect(),r=e.coordsAtPos(s.selection.anchor).top-o.top;this.isActive=!0,this.top=r,this.sendUpdate()}sendUpdate(){this.options.onUpdate({isActive:this.isActive,top:this.top})}hide(e){e&&e.relatedTarget&&this.options.element.parentNode&&this.options.element.parentNode.contains(e.relatedTarget)||(this.isActive=!1,this.sendUpdate())}destroy(){this.options.element.removeEventListener("mousedown",this.mousedownHandler),this.resizeObserver&&this.resizeObserver.unobserve(this.editorView.dom),this.options.editor.off("focus",this.focusHandler),this.options.editor.off("blur",this.blurHandler)}}var E={props:{editor:{default:null,type:Object}},data:()=>({menu:{isActive:!1,left:0,bottom:0}}),watch:{editor:{immediate:!0,handler(e){e&&this.$nextTick(()=>{var s;e.registerPlugin((s={editor:e,element:this.$el,onUpdate:e=>{e.isActive&&!1===this.menu.isActive?this.$emit("show",e):e.isActive||!0!==this.menu.isActive||this.$emit("hide",e),this.menu=e}},new t.Plugin({key:new t.PluginKey("floating_menu"),view:e=>new N({editorView:e,options:s})})))})}}},render(){return this.editor?this.$scopedSlots.default({focused:this.editor.view.focused,focus:this.editor.focus,commands:this.editor.commands,isActive:this.editor.isActive,getMarkAttrs:this.editor.getMarkAttrs.bind(this.editor),getNodeAttrs:this.editor.getNodeAttrs.bind(this.editor),menu:this.menu}):null},beforeDestroy(){this.editor.unregisterPlugin("floating_menu")}};Object.defineProperty(e,"NodeSelection",{enumerable:!0,get:function(){return t.NodeSelection}}),Object.defineProperty(e,"Plugin",{enumerable:!0,get:function(){return t.Plugin}}),Object.defineProperty(e,"PluginKey",{enumerable:!0,get:function(){return t.PluginKey}}),Object.defineProperty(e,"TextSelection",{enumerable:!0,get:function(){return t.TextSelection}}),e.Doc=v,e.Editor=class extends class{on(e,t){return this._callbacks=this._callbacks||{},this._callbacks[e]||(this._callbacks[e]=[]),this._callbacks[e].push(t),this}emit(e,...t){this._callbacks=this._callbacks||{};const s=this._callbacks[e];return s&&s.forEach(e=>e.apply(this,t)),this}off(e,t){if(arguments.length){const s=this._callbacks?this._callbacks[e]:null;s&&(t?this._callbacks[e]=s.filter(e=>e!==t):delete this._callbacks[e])}else this._callbacks={};return this}}{constructor(e={}){super(),this.defaultOptions={editorProps:{},editable:!0,autoFocus:null,extensions:[],content:"",topNode:"doc",emptyDocument:{type:"doc",content:[{type:"paragraph"}]},useBuiltInExtensions:!0,disableInputRules:!1,disablePasteRules:!1,dropCursor:{},enableDropCursor:!0,enableGapCursor:!0,parseOptions:{},injectCSS:!0,onInit:()=>{},onTransaction:()=>{},onUpdate:()=>{},onFocus:()=>{},onBlur:()=>{},onPaste:()=>{},onDrop:()=>{}},this.events=["init","transaction","update","focus","blur","paste","drop"],this.init(e)}init(e={}){this.setOptions({...this.defaultOptions,...e}),this.focused=!1,this.selection={from:0,to:0},this.element=document.createElement("div"),this.extensions=this.createExtensions(),this.nodes=this.createNodes(),this.marks=this.createMarks(),this.schema=this.createSchema(),this.plugins=this.createPlugins(),this.keymaps=this.createKeymaps(),this.inputRules=this.createInputRules(),this.pasteRules=this.createPasteRules(),this.view=this.createView(),this.commands=this.createCommands(),this.setActiveNodesAndMarks(),this.options.injectCSS&&function(e){{const t=document.createElement("style");t.type="text/css",t.textContent=e;const{head:s}=document,{firstChild:i}=s;i?s.insertBefore(t,i):s.appendChild(t)}}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n'),null!==this.options.autoFocus&&this.focus(this.options.autoFocus),this.events.forEach(e=>{var t;this.on(e,this.options[(t="on "+e,t.replace(/(?:^\w|[A-Z]|\b\w)/g,(e,t)=>0===t?e.toLowerCase():e.toUpperCase()).replace(/\s+/g,""))]||(()=>{}))}),this.emit("init",{view:this.view,state:this.state}),this.extensions.view=this.view}setOptions(e){this.options={...this.options,...e},this.view&&this.state&&this.view.updateState(this.state)}get builtInExtensions(){return this.options.useBuiltInExtensions?[new v,new w,new b]:[]}get state(){return this.view?this.view.state:null}createExtensions(){return new m([...this.builtInExtensions,...this.options.extensions],this)}createPlugins(){return this.extensions.plugins}createKeymaps(){return this.extensions.keymaps({schema:this.schema})}createInputRules(){return this.extensions.inputRules({schema:this.schema,excludedExtensions:this.options.disableInputRules})}createPasteRules(){return this.extensions.pasteRules({schema:this.schema,excludedExtensions:this.options.disablePasteRules})}createCommands(){return this.extensions.commands({schema:this.schema,view:this.view})}createNodes(){return this.extensions.nodes}createMarks(){return this.extensions.marks}createSchema(){return new i.Schema({topNode:this.options.topNode,nodes:this.nodes,marks:this.marks})}createState(){return t.EditorState.create({schema:this.schema,doc:this.createDocument(this.options.content),plugins:[...this.plugins,h.inputRules({rules:this.inputRules}),...this.pasteRules,...this.keymaps,r.keymap({Backspace:h.undoInputRule}),r.keymap(a.baseKeymap),...this.options.enableDropCursor?[n.dropCursor(this.options.dropCursor)]:[],...this.options.enableGapCursor?[o.gapCursor()]:[],new t.Plugin({key:new t.PluginKey("editable"),props:{editable:()=>this.options.editable}}),new t.Plugin({props:{attributes:{tabindex:0},handleDOMEvents:{focus:(e,t)=>{this.focused=!0,this.emit("focus",{event:t,state:e.state,view:e});const s=this.state.tr.setMeta("focused",!0);this.view.dispatch(s)},blur:(e,t)=>{this.focused=!1,this.emit("blur",{event:t,state:e.state,view:e});const s=this.state.tr.setMeta("focused",!1);this.view.dispatch(s)}}}}),new t.Plugin({props:this.options.editorProps})]})}createDocument(e,t=this.options.parseOptions){if(null===e)return this.schema.nodeFromJSON(this.options.emptyDocument);if("object"==typeof e)try{return this.schema.nodeFromJSON(e)}catch(t){return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",t),this.schema.nodeFromJSON(this.options.emptyDocument)}if("string"==typeof e){const s=`<div>${e}</div>`,n=(new window.DOMParser).parseFromString(s,"text/html").body.firstElementChild;return i.DOMParser.fromSchema(this.schema).parse(n,t)}return!1}createView(){return new s.EditorView(this.element,{state:this.createState(),handlePaste:(...e)=>{this.emit("paste",...e)},handleDrop:(...e)=>{this.emit("drop",...e)},dispatchTransaction:this.dispatchTransaction.bind(this)})}setParentComponent(e=null){e&&this.view.setProps({nodeViews:this.initNodeViews({parent:e,extensions:[...this.builtInExtensions,...this.options.extensions]})})}initNodeViews({parent:e,extensions:t}){return t.filter(e=>["node","mark"].includes(e.type)).filter(e=>e.view).reduce((t,s)=>({...t,[s.name]:(t,i,n,o)=>{const r=s.view;return new l(r,{editor:this,extension:s,parent:e,node:t,view:i,getPos:n,decorations:o})}}),{})}dispatchTransaction(e){const t=this.state.apply(e);this.view.updateState(t),this.selection={from:this.state.selection.from,to:this.state.selection.to},this.setActiveNodesAndMarks(),this.emit("transaction",{getHTML:this.getHTML.bind(this),getJSON:this.getJSON.bind(this),state:this.state,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emitUpdate(e)}emitUpdate(e){this.emit("update",{getHTML:this.getHTML.bind(this),getJSON:this.getJSON.bind(this),state:this.state,transaction:e})}resolveSelection(e=null){if(this.selection&&null===e)return this.selection;if("start"===e||!0===e)return{from:0,to:0};if("end"===e){const{doc:e}=this.state;return{from:e.content.size,to:e.content.size}}return{from:e,to:e}}focus(e=null){if(this.view.focused&&null===e||!1===e)return;const{from:t,to:s}=this.resolveSelection(e);this.setSelection(t,s),setTimeout(()=>this.view.focus(),10)}setSelection(e=0,s=0){const{doc:i,tr:n}=this.state,o=f(e,0,i.content.size),r=f(s,0,i.content.size),a=t.TextSelection.create(i,o,r),h=n.setSelection(a);this.view.dispatch(h)}blur(){this.view.dom.blur()}getSchemaJSON(){return JSON.parse(JSON.stringify({nodes:this.extensions.nodes,marks:this.extensions.marks}))}getHTML(){const e=document.createElement("div"),t=i.DOMSerializer.fromSchema(this.schema).serializeFragment(this.state.doc.content);return e.appendChild(t),e.innerHTML}getJSON(){return this.state.doc.toJSON()}setContent(e={},s=!1,i){const{doc:n,tr:o}=this.state,r=this.createDocument(e,i),a=t.TextSelection.create(n,0,n.content.size),h=o.setSelection(a).replaceSelectionWith(r,!1).setMeta("preventUpdate",!s);this.view.dispatch(h)}clearContent(e=!1){this.setContent(this.options.emptyDocument,e)}setActiveNodesAndMarks(){this.activeMarks=Object.entries(this.schema.marks).reduce((e,[t,s])=>({...e,[t]:(e={})=>d.markIsActive(this.state,s,e)}),{}),this.activeMarkAttrs=Object.entries(this.schema.marks).reduce((e,[t,s])=>({...e,[t]:d.getMarkAttrs(this.state,s)}),{}),this.activeNodes=Object.entries(this.schema.nodes).reduce((e,[t,s])=>({...e,[t]:(e={})=>d.nodeIsActive(this.state,s,e)}),{})}getMarkAttrs(e=null){return this.activeMarkAttrs[e]}getNodeAttrs(e=null){return{...d.getNodeAttrs(this.state,this.schema.nodes[e])}}get isActive(){return Object.entries({...this.activeMarks,...this.activeNodes}).reduce((e,[t,s])=>({...e,[t]:(e={})=>s(e)}),{})}registerPlugin(e=null,t){const s="function"==typeof t?t(e,this.state.plugins):[e,...this.state.plugins],i=this.state.reconfigure({plugins:s});this.view.updateState(i)}unregisterPlugin(e=null){if(!e||!this.view.docView)return;const t=this.state.reconfigure({plugins:this.state.plugins.filter(t=>!t.key.startsWith(e+"$"))});this.view.updateState(t)}destroy(){this.view&&this.view.destroy()}},e.EditorContent=y,e.EditorFloatingMenu=E,e.EditorMenuBar=x,e.EditorMenuBubble=S,e.Extension=p,e.Mark=class extends p{constructor(e={}){super(e)}get type(){return"mark"}get view(){return null}get schema(){return null}command(){return()=>{}}},e.Node=g,e.Paragraph=b,e.Text=w,Object.defineProperty(e,"__esModule",{value:!0})}));