// Metrics component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class OpportunityMetrics {
  constructor(container, opportunityComponent) {
    this.container = container;
    this.opportunityComponent = opportunityComponent;
    this.metrics = null;
    this.isLoading = true;
    this.notificationSystem = new NotificationSystem();
    this.timeframe = 'year'; // default timeframe: year, quarter, month
    this.renderHeader = false; // Default to false - header handled by parent component
    this.dateRange = null; // Date range for filtering
    this.filterStatus = 'all'; // Status filter
    this.productFamilyFilter = 'all'; // Product family filter
    this.yearFilter = new Date().getFullYear().toString(); // Default to current year
    
    // Pagination settings for Monthly Product Breakdown
    this.currentProductPage = 1;
    this.productsPerPage = 10;
    
    // Pagination settings for Company Win Rates
    this.currentCompanyPage = 1;
    this.companiesPerPage = 10;
    
    // Hard-coded product family list (in display order)
    this.productFamilies = [
      'H2S',
      'H2S-TS',
      'TFS',
      'GC',
      'MSERIES',
      'MICHELL',
      'AMI',
      'MERLIN',
      'CHROMATOTEC',
      'ENCLOSURE',
      'MISC',
      'PROBE',
      'SCS',
      'OTHER'
    ];
    
    // Product to family mapping
    this.productFamilyMapping = {
      // H2S family
      'Q-330S': 'H2S',
      'Q-331S': 'H2S',
      'Q-330SDS': 'H2S',
      'Q-331SDS': 'H2S',
      'Q-330S-EX': 'H2S',
      'Q-330S ULTRAFAB': 'H2S',
      'Q-331S PORTABLE': 'H2S',
      'Q-331S ULTRAFAB': 'H2S',
      
      // H2S-TS family
      'Q-330S-TS': 'H2S-TS',
      'Q-331S-TS': 'H2S-TS',
      'Q-330SDS-TS': 'H2S-TS',
      'Q-331SDS-TS': 'H2S-TS',
      
      // GC family
      'Q-132S': 'GC',
      'Q-131S': 'GC',
      
      // TFS family
      'Q-TFS1': 'TFS',
      'Q-TFS2': 'TFS',
      'Q-TFS2 Pelican': 'TFS',
      'Q-TFS1-EX': 'TFS',
      
      // MSERIES family
      'Q-M70': 'MSERIES',
      'Q-M80': 'MSERIES',
      'Q-M90': 'MSERIES',
      'Q-M60': 'MSERIES',
      'Q-M90-PPM': 'MSERIES',
      
      // MICHELL family
      'Q-QMA601': 'MICHELL',
      'Q-CDP301': 'MICHELL',
      'Q-CD2': 'MICHELL',
      'Q-MDM300': 'MICHELL',
      'Q-XTP601': 'MICHELL',
      'Q-XTC601': 'MICHELL',
      'Q-EASIDEW': 'MICHELL',
      'Q-TDL600': 'MICHELL',
      
      // AMI family
      'Q-4010LX': 'AMI',
      'Q-2010BX': 'AMI',
      'Q-210BX': 'AMI',
      'Q-3010BX': 'AMI',
      
      // MERLIN family
      'Q-MERLIN': 'MERLIN',
      
      // CHROMATOTEC family
      'Q-MEDOR': 'CHROMATOTEC',
      
      // ENCLOSURE family
      'Q-ENCLOSURES': 'ENCLOSURE',
      'Q-WALKIN-3SIDED-BUSSTOP': 'ENCLOSURE',
      
      // PROBE family
      'Q-Q-GENIE': 'PROBE',
      'Q-PROBE-750': 'PROBE',
      'Q-PROBE-755': 'PROBE',
      'Q-PROBE-760': 'PROBE',
      'Q-PROBE-AIRCOM': 'PROBE',
      'Q-PROBE-CUSTOM': 'PROBE',
      
      // SCS family
      'Q-050-SCS': 'SCS',
      'Q-105-SCS': 'SCS',
      'Q-120-SCS': 'SCS',
      'Q-122-SCS': 'SCS',
      'Q-130-SCS': 'SCS',
      'Q-170-SCS': 'SCS',
      'Q-33M-SCS': 'SCS',
      'Q-38M-SCS': 'SCS',
      'Q-Q-ACES': 'SCS',
      'Q-Q-ALRS': 'SCS',
      'Q-CUSTOM-SCS': 'SCS',
      'Q-Q-EFS': 'SCS',
      
      // MISC family
      'Q-Q-ACCESSORIES': 'MISC',
      'Q-DOCUMENTATION-CRN': 'MISC',
      'Q-DOCUMENTATION-NACE': 'MISC',
      'Q-DOCUMENTATION-VDDR': 'MISC',
      
      // OTHER family
      'Q-DILUTION PANEL': 'OTHER',
      'Q-GPR1800': 'OTHER',
      'Q-OXY550': 'OTHER'
    };
  }

  async init() {
    console.log("Initializing Opportunity Metrics component");
    this.isLoading = true;
    this.render();
    
    try {
      // Calculate metrics from opportunity data
      await this.calculateMetrics();
      
      this.isLoading = false;
      this.render();
      
      // Only set up event listeners if we're rendering our own header
      if (this.renderHeader) {
        this.setupEventListeners();
      }
    } catch (error) {
      console.error("Error initializing opportunity metrics:", error);
      this.isLoading = false;
      this.showError("Failed to initialize metrics: " + error.message);
      this.render();
    }
  }

  // Helper method to get product family from product ID
  getProductFamily(productId) {
    // Try direct lookup from the mapping
    if (this.productFamilyMapping[productId]) {
      return this.productFamilyMapping[productId];
    }
    
    // If not in direct mapping, try substring matching for partial matches
    for (const [mappingId, family] of Object.entries(this.productFamilyMapping)) {
      if (productId.includes(mappingId)) {
        return family;
      }
    }
    
    // Default to 'OTHER' if no matching family found
    return 'OTHER';
  }

  async calculateMetrics() {
    // Get opportunities from the main component
    const opportunities = this.opportunityComponent.opportunities;

    if (!opportunities || opportunities.length === 0) {
      throw new Error("No opportunity data available");
    }

    // Get filtered opportunities based on filters
    let filteredOpportunities = [...opportunities];

    // Apply date range filter if specified
    if (this.dateRange && this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      filteredOpportunities = filteredOpportunities.filter(opp => {
        let dateToCheck = null;
        // Always prioritize CreatedDate for consistency with main component
        if (opp.ODataInfo?.CreatedDate) {
          dateToCheck = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          // Fall back to LastModified if CreatedDate not available
          dateToCheck = opp.LastModified;
        }

        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        return false;
      });
    }

    // Apply status filter if specified and not 'all'
    if (this.filterStatus && this.filterStatus !== 'all') {
      filteredOpportunities = filteredOpportunities.filter(opp => 
        opp.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }

    // Initialize metrics structure
    this.metrics = {
      summary: {
        totalOpportunities: filteredOpportunities.length,
        totalValue: 0,
        totalProducts: 0,
        averageProductsPerOpportunity: 0
      },
      productMetrics: {
        productsByCount: {},
        productsByValue: {},
        productFamilies: {},
        // Add yearly metrics for product families
        yearlyProductFamilies: {}
      },
      monthlyMetrics: {
        byMonth: {},
        totalByMonth: {},
        winRateByMonth: {}
      },
      // Add new section for monthly product family distribution
      monthlyProductFamilyDistribution: {
        byMonth: {}
      },
      // Add new section for top performing products
      topPerformingProducts: {
        byRevenue: [],
        byQuantity: []
      },
      // Add new section for win rate analysis by product family
      winRateByFamily: {},
      // Add new section for company win rates
      companyWinRates: []
    };

    // Initialize product families structure with all the predefined families
    this.productFamilies.forEach(family => {
      this.metrics.productMetrics.productFamilies[family] = {
        count: 0,
        value: 0,
        products: {}
      };
      
      // Initialize win rate tracking for each family
      this.metrics.winRateByFamily[family] = {
        total: 0,
        won: 0,
        lost: 0,
        pending: 0,
        winRate: 0
      };
    });

    // Calculate product metrics
    if (filteredOpportunities.length > 0) {
      // Calculate total value
      this.metrics.summary.totalValue = filteredOpportunities.reduce((sum, opp) => 
        sum + (parseFloat(opp.Amount) || 0), 0);
      
      // Extract all products from opportunities
      const allProducts = [];
      const productCounts = {};
      const productValues = {};
      
      // Initialize yearly product family metrics
      const yearlyProductFamilies = {};
      
      // Initialize monthly product family distribution
      const monthlyFamilyDistribution = {};
      
      // Top performing products tracking
      const productRevenue = {};
      const productQuantity = {};
      const productDetails = {};
      
      // For win rate by family, track opportunities by family
      const oppsByFamily = {};
      this.productFamilies.forEach(family => {
        oppsByFamily[family] = new Set();
      });
      
      filteredOpportunities.forEach(opp => {
        // Store full opportunity reference for detailed information
        const oppRef = {
          id: opp.id,
          opportunityId: opp.OpportunityID,
          subject: opp.Subject,
          businessAccount: opp.BusinessAccount,
          contactName: opp.ContactDisplayName,
          owner: opp.OwnerEmployeeName,
          status: opp.Status,
          amount: opp.Amount,
          stage: opp.Stage,
          lastModified: opp.LastModified,
          reason: opp.Reason || '',
          estimation: opp.Estimation
        };
        
        // Check if this opportunity has products
        const hasProducts = opp.Products && Array.isArray(opp.Products) && opp.Products.length > 0;
        
        // Get opportunity month
        let oppMonth = null;
        let oppYear = null;
        if (opp.ODataInfo?.CreatedDate) {
          const createdDate = new Date(opp.ODataInfo.CreatedDate);
          oppYear = createdDate.getFullYear().toString();
          oppMonth = createdDate.getMonth() + 1; // 1-12
          const monthKey = `${oppYear}-${oppMonth.toString().padStart(2, '0')}`;
          
          // Initialize month in monthly family distribution if needed
          if (!monthlyFamilyDistribution[monthKey]) {
            monthlyFamilyDistribution[monthKey] = {
              total: 0,
              families: {},
              opportunities: {}
            };
            
            // Initialize each family for this month
            this.productFamilies.forEach(family => {
              monthlyFamilyDistribution[monthKey].families[family] = {
                count: 0,
                value: 0,
                opportunities: []
              };
            });
          }
          
          // Increment total opportunities for this month
          monthlyFamilyDistribution[monthKey].total++;
          
          // Store opportunity reference
          monthlyFamilyDistribution[monthKey].opportunities[opp.id] = oppRef;
        } else if (opp.LastModified instanceof Date) {
          oppYear = opp.LastModified.getFullYear().toString();
          oppMonth = opp.LastModified.getMonth() + 1; // 1-12
          const monthKey = `${oppYear}-${oppMonth.toString().padStart(2, '0')}`;
          
          // Initialize month in monthly family distribution if needed
          if (!monthlyFamilyDistribution[monthKey]) {
            monthlyFamilyDistribution[monthKey] = {
              total: 0,
              families: {},
              opportunities: {}
            };
            
            // Initialize each family for this month
            this.productFamilies.forEach(family => {
              monthlyFamilyDistribution[monthKey].families[family] = {
                count: 0,
                value: 0,
                opportunities: []
              };
            });
          }
          
          // Increment total opportunities for this month
          monthlyFamilyDistribution[monthKey].total++;
          
          // Store opportunity reference
          monthlyFamilyDistribution[monthKey].opportunities[opp.id] = oppRef;
        }
          
        // Initialize year data if it doesn't exist
        if (oppYear && !yearlyProductFamilies[oppYear]) {
          yearlyProductFamilies[oppYear] = {};
          this.productFamilies.forEach(family => {
            yearlyProductFamilies[oppYear][family] = {
              count: 0,
              value: 0,
              products: {}
            };
          });
        }
        
        // Process products if available
        if (hasProducts) {
          // Track unique families in this opportunity to avoid double counting
          const familiesInOpp = new Set();
          
          opp.Products.forEach(product => {
            allProducts.push(product);
            
            // Count products by ID
            const productId = product.inventoryId;
            productCounts[productId] = (productCounts[productId] || 0) + product.quantity;
            productValues[productId] = (productValues[productId] || 0) + product.amount;
            
            // Get the product family using the mapping
            const family = this.getProductFamily(productId);
            
            // Track this family for the opportunity
            familiesInOpp.add(family);
            
            // Track top performing products
            if (!productDetails[productId]) {
              productDetails[productId] = {
                id: productId,
                description: product.description,
                family: family
              };
            }
            
            if (!productRevenue[productId]) productRevenue[productId] = 0;
            if (!productQuantity[productId]) productQuantity[productId] = 0;
            
            productRevenue[productId] += product.amount;
            productQuantity[productId] += product.quantity;
            
            // Update the product family metrics
            if (this.metrics.productMetrics.productFamilies[family]) {
              this.metrics.productMetrics.productFamilies[family].count += product.quantity;
              this.metrics.productMetrics.productFamilies[family].value += product.amount;
              
              if (!this.metrics.productMetrics.productFamilies[family].products[productId]) {
                this.metrics.productMetrics.productFamilies[family].products[productId] = {
                  count: 0,
                  value: 0,
                  description: product.description
                };
              }
              
              this.metrics.productMetrics.productFamilies[family].products[productId].count += product.quantity;
              this.metrics.productMetrics.productFamilies[family].products[productId].value += product.amount;
            }
            
            // Update the yearly product family metrics if we have a year
            if (oppYear && yearlyProductFamilies[oppYear][family]) {
              yearlyProductFamilies[oppYear][family].count += product.quantity;
              yearlyProductFamilies[oppYear][family].value += product.amount;
              
              if (!yearlyProductFamilies[oppYear][family].products[productId]) {
                yearlyProductFamilies[oppYear][family].products[productId] = {
                  count: 0,
                  value: 0,
                  description: product.description
                };
              }
              
              yearlyProductFamilies[oppYear][family].products[productId].count += product.quantity;
              yearlyProductFamilies[oppYear][family].products[productId].value += product.amount;
            }
            
            // Update monthly family distribution if we have a month
            if (oppMonth && oppYear) {
              const monthKey = `${oppYear}-${oppMonth.toString().padStart(2, '0')}`;
              
              if (monthlyFamilyDistribution[monthKey] && 
                  monthlyFamilyDistribution[monthKey].families[family]) {
                monthlyFamilyDistribution[monthKey].families[family].count += product.quantity;
                monthlyFamilyDistribution[monthKey].families[family].value += product.amount;
                
                // Add opportunity reference if not already added
                if (!monthlyFamilyDistribution[monthKey].families[family].opportunities.some(o => o.id === opp.id)) {
                  monthlyFamilyDistribution[monthKey].families[family].opportunities.push(oppRef);
                }
              }
            }
          });
          
          // For each unique family in this opportunity, track the opportunity status
          familiesInOpp.forEach(family => {
            // Add this opportunity to the family's set
            oppsByFamily[family].add(opp.id);
            
            // Update win rate metrics for this family
            this.metrics.winRateByFamily[family].total++;
            
            if (opp.Status && opp.Status.toLowerCase() === 'won') {
              this.metrics.winRateByFamily[family].won++;
            } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
              this.metrics.winRateByFamily[family].lost++;
            } else {
              this.metrics.winRateByFamily[family].pending++;
            }
          });
        }
      });
      
      // Calculate top performing products by revenue
      this.metrics.topPerformingProducts.byRevenue = Object.entries(productRevenue)
        .map(([id, revenue]) => ({
          id,
          revenue,
          quantity: productQuantity[id] || 0,
          description: productDetails[id]?.description || '',
          family: productDetails[id]?.family || 'OTHER'
        }))
        .sort((a, b) => b.revenue - a.revenue)
        .slice(0, 10); // Top 10 products by revenue
      
      // Calculate top performing products by quantity
      this.metrics.topPerformingProducts.byQuantity = Object.entries(productQuantity)
        .map(([id, quantity]) => ({
          id,
          revenue: productRevenue[id] || 0,
          quantity,
          description: productDetails[id]?.description || '',
          family: productDetails[id]?.family || 'OTHER'
        }))
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 10); // Top 10 products by quantity
      
      // Calculate win rates for each family
      Object.keys(this.metrics.winRateByFamily).forEach(family => {
        const familyData = this.metrics.winRateByFamily[family];
        const closedOpps = familyData.won + familyData.lost;
        familyData.winRate = closedOpps > 0 
          ? (familyData.won / closedOpps * 100) 
          : 0;
        
        // Track number of unique opportunities for this family
        familyData.uniqueOpportunities = oppsByFamily[family].size;
      });
      
      // Update metrics
      this.metrics.summary.totalProducts = allProducts.length;
      this.metrics.summary.averageProductsPerOpportunity = 
        allProducts.length / filteredOpportunities.length;
      
      this.metrics.productMetrics.productsByCount = productCounts;
      this.metrics.productMetrics.productsByValue = productValues;
      this.metrics.productMetrics.yearlyProductFamilies = yearlyProductFamilies;
      this.metrics.monthlyProductFamilyDistribution.byMonth = monthlyFamilyDistribution;
      
      // Calculate monthly metrics
      const byMonth = {};
      const totalByMonth = {};
      const winRateByMonth = {};
      
      filteredOpportunities.forEach(opp => {
        let dateToUse = null;
        
        // Use created date if available
        if (opp.ODataInfo?.CreatedDate) {
          dateToUse = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          dateToUse = opp.LastModified;
        }
        
        if (dateToUse && !isNaN(dateToUse.getTime())) {
          const year = dateToUse.getFullYear();
          const month = dateToUse.getMonth() + 1; // 1-12
          const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
          
          // Initialize if needed
          if (!byMonth[monthKey]) {
            byMonth[monthKey] = {
              total: 0,
              won: 0,
              lost: 0,
              pending: 0,
              value: 0,
              wonValue: 0,
              lostValue: 0,
              products: {}
            };
          }
          
          // Count by status
          byMonth[monthKey].total++;
          byMonth[monthKey].value += parseFloat(opp.Amount) || 0;
          
          if (opp.Status && opp.Status.toLowerCase() === 'won') {
            byMonth[monthKey].won++;
            byMonth[monthKey].wonValue += parseFloat(opp.Amount) || 0;
          } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
            byMonth[monthKey].lost++;
            byMonth[monthKey].lostValue += parseFloat(opp.Amount) || 0;
          } else {
            byMonth[monthKey].pending++;
          }
          
          // Count products by month
          if (opp.Products && Array.isArray(opp.Products)) {
            opp.Products.forEach(product => {
              const productId = product.inventoryId;
              if (!byMonth[monthKey].products[productId]) {
                byMonth[monthKey].products[productId] = {
                  count: 0,
                  value: 0,
                  description: product.description,
                  family: this.getProductFamily(productId) // Store family for each product
                };
              }
              
              byMonth[monthKey].products[productId].count += product.quantity;
              byMonth[monthKey].products[productId].value += product.amount;
            });
          }
        }
      });
      
      // Calculate running totals by month and win rates
      const sortedMonths = Object.keys(byMonth).sort();
      let runningTotal = 0;
      let runningWon = 0;
      let runningLost = 0;
      
      sortedMonths.forEach(month => {
        runningTotal += byMonth[month].total;
        runningWon += byMonth[month].won;
        runningLost += byMonth[month].lost;
        
        totalByMonth[month] = runningTotal;
        
        // Calculate win rate (won / (won + lost))
        const closedOpps = byMonth[month].won + byMonth[month].lost;
        winRateByMonth[month] = closedOpps > 0 
          ? (byMonth[month].won / closedOpps * 100).toFixed(1) 
          : '0.0';
      });
      
      this.metrics.monthlyMetrics.byMonth = byMonth;
      this.metrics.monthlyMetrics.totalByMonth = totalByMonth;
      this.metrics.monthlyMetrics.winRateByMonth = winRateByMonth;
      
      // Track company win rates
      const companyStats = {};
      
      filteredOpportunities.forEach(opp => {
        // ... existing code processing opportunity ...
        
        // Track company win rates
        const company = opp.BusinessAccount;
        const companyName = opp.ContactInfo?.companyName || company;
        
        if (company) {
          if (!companyStats[company]) {
            companyStats[company] = {
              id: company,
              name: companyName,
              total: 0,
              won: 0,
              lost: 0,
              pending: 0,
              value: 0,
              wonValue: 0,
              lostValue: 0,
              productFamilies: new Set(), // Track product families for this company
              yearData: {}, // Track data by year
              oppIds: new Set() // Track unique opportunity IDs
            };
          }
          
          // Get opportunity year
          let oppYear = 'unknown';
          if (opp.ODataInfo?.CreatedDate) {
            oppYear = new Date(opp.ODataInfo.CreatedDate).getFullYear().toString();
          } else if (opp.LastModified instanceof Date) {
            oppYear = opp.LastModified.getFullYear().toString();
          }
          
          // Initialize year data if needed
          if (!companyStats[company].yearData[oppYear]) {
            companyStats[company].yearData[oppYear] = {
              total: 0,
              won: 0,
              lost: 0,
              pending: 0,
              value: 0,
              wonValue: 0,
              lostValue: 0,
              productFamilies: new Set(), // Track product families by year
              oppIds: new Set() // Track unique opportunity IDs by year
            };
          }
          
          // Track unique opportunity
          companyStats[company].oppIds.add(opp.id);
          companyStats[company].yearData[oppYear].oppIds.add(opp.id);
          
          // Update overall stats
          companyStats[company].total++;
          companyStats[company].value += parseFloat(opp.Amount) || 0;
          
          // Update year-specific stats
          companyStats[company].yearData[oppYear].total++;
          companyStats[company].yearData[oppYear].value += parseFloat(opp.Amount) || 0;
          
          if (opp.Status?.toLowerCase() === 'won') {
            companyStats[company].won++;
            companyStats[company].wonValue += parseFloat(opp.Amount) || 0;
            companyStats[company].yearData[oppYear].won++;
            companyStats[company].yearData[oppYear].wonValue += parseFloat(opp.Amount) || 0;
          } else if (opp.Status?.toLowerCase() === 'lost') {
            companyStats[company].lost++;
            companyStats[company].lostValue += parseFloat(opp.Amount) || 0;
            companyStats[company].yearData[oppYear].lost++;
            companyStats[company].yearData[oppYear].lostValue += parseFloat(opp.Amount) || 0;
          } else {
            companyStats[company].pending++;
            companyStats[company].yearData[oppYear].pending++;
          }
          
          // Track product families
          if (opp.Products && Array.isArray(opp.Products)) {
            opp.Products.forEach(product => {
              const productId = product.inventoryId;
              const family = this.getProductFamily(productId);
              
              // Add to company's overall product families
              companyStats[company].productFamilies.add(family);
              
              // Add to company's year-specific product families
              companyStats[company].yearData[oppYear].productFamilies.add(family);
            });
          }
        }
        
        // ... rest of existing opportunity processing ...
      });
      
      // Convert company stats to array and calculate win rates
      this.metrics.companyWinRates = Object.values(companyStats).map(company => {
        const closedOpps = company.won + company.lost;
        const winRate = closedOpps > 0 ? (company.won / closedOpps * 100) : 0;
        
        return {
          ...company,
          winRate,
          closedOpps,
          productFamilies: Array.from(company.productFamilies),
          yearData: Object.entries(company.yearData).reduce((acc, [year, data]) => {
            acc[year] = {
              ...data,
              winRate: (data.won + data.lost > 0) ? (data.won / (data.won + data.lost) * 100) : 0,
              productFamilies: Array.from(data.productFamilies),
              uniqueOppCount: data.oppIds.size
            };
            return acc;
          }, {}),
          uniqueOppCount: company.oppIds.size
        };
      });
      
      // Sort by opportunity count (highest first) by default
      this.metrics.companyWinRates.sort((a, b) => b.uniqueOppCount - a.uniqueOppCount);
    }

    console.log("Metrics calculated:", this.metrics);
    return this.metrics;
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  }

  formatNumber(value, decimals = 0) {
    return new Intl.NumberFormat('en-US', {
      maximumFractionDigits: decimals
    }).format(value);
  }

  formatMonth(monthKey) {
    const [year, month] = monthKey.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1, 1);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderMetrics();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Analyzing opportunity metrics...</p>
      </div>
    `;
  }

  renderMetrics() {
    if (!this.metrics) {
      this.container.innerHTML = `
        <p class="text-center text-gray-500 dark:text-gray-400">No metrics available</p>
      `;
      return;
    }

    const { summary, productMetrics, monthlyMetrics } = this.metrics;

    // Create header content - only included if renderHeader is true
    let headerContent = '';
    if (this.renderHeader) {
      headerContent = `
        <!-- Timeframe selector -->
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Opportunity Metrics</h2>
          <div class="inline-flex rounded-md shadow-sm" role="group">
            <button type="button" id="timeframe-year" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'year' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-l-lg">
              Year
            </button>
            <button type="button" id="timeframe-quarter" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'quarter' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} border-l border-gray-200 dark:border-gray-600">
              Quarter
            </button>
            <button type="button" id="timeframe-month" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-r-lg border-l border-gray-200 dark:border-gray-600">
              Month
            </button>
          </div>
        </div>
      `;
    }

    // Get sorted months for the monthly metrics
    const sortedMonths = Object.keys(monthlyMetrics.byMonth).sort();
    
    // Get unique years from the months data
    const years = [...new Set(sortedMonths.map(month => month.split('-')[0]))].sort();
    if (!years.includes(this.yearFilter) && years.length > 0 && this.yearFilter !== 'all') {
      this.yearFilter = years[years.length - 1]; // Set to latest year if current filter not available
    }

    // Filter months by selected year (or use all if 'all' is selected)
    const filteredMonths = this.yearFilter === 'all' 
      ? sortedMonths 
      : sortedMonths.filter(month => month.startsWith(this.yearFilter + '-'));

    // Get the selected month for product details
    const selectedMonth = filteredMonths.length > 0 ? filteredMonths[filteredMonths.length - 1] : null;
    
    // Pre-calculate pagination details for the selected month
    let totalProductPages = 1;
    let totalProductItems = 0;
    
    if (selectedMonth) {
      const productsForMonth = this.getFilteredProductsForMonth(selectedMonth);
      totalProductItems = productsForMonth.length;
      totalProductPages = Math.ceil(totalProductItems / this.productsPerPage);
    }

    // Process monthly data filtered by both year and product family
    const processedMonthlyData = {};
    
    // For each month in the filtered months, calculate aggregates based on product family filter
    filteredMonths.forEach(monthKey => {
      const monthData = monthlyMetrics.byMonth[monthKey];
      
      // Start with a clean copy of aggregates for the month
      processedMonthlyData[monthKey] = {
        total: 0,
        won: 0,
        lost: 0,
        pending: 0,
        value: 0,
        wonValue: 0,
        lostValue: 0
      };
      
      // If filtering by a specific product family, recalculate month totals
      if (this.productFamilyFilter !== 'all') {
        // Check if this month has any products from the selected family
        const hasProductsFromFamily = Object.entries(monthData.products).some(([productId, product]) => {
          const family = product.family || this.getProductFamily(productId);
          return family === this.productFamilyFilter;
        });
        
        if (hasProductsFromFamily) {
          // Include only opportunities with products from the selected family
          Object.entries(monthData.products).forEach(([productId, product]) => {
            const family = product.family || this.getProductFamily(productId);
            if (family === this.productFamilyFilter) {
              processedMonthlyData[monthKey].value += product.value;
              
              // Increment counts based on ratio of product value to total month value
              const contributionRatio = product.value / monthData.value;
              
              // Distribute won/lost/pending based on product contribution
              if (monthData.won > 0) {
                const wonContribution = Math.round(monthData.won * contributionRatio);
                processedMonthlyData[monthKey].won += wonContribution;
                processedMonthlyData[monthKey].wonValue += (monthData.wonValue * contributionRatio);
              }
              
              if (monthData.lost > 0) {
                const lostContribution = Math.round(monthData.lost * contributionRatio);
                processedMonthlyData[monthKey].lost += lostContribution;
                processedMonthlyData[monthKey].lostValue += (monthData.lostValue * contributionRatio);
              }
              
              if (monthData.pending > 0) {
                const pendingContribution = Math.round(monthData.pending * contributionRatio);
                processedMonthlyData[monthKey].pending += pendingContribution;
              }
              
              // Increment total based on sum of won, lost, and pending
              processedMonthlyData[monthKey].total = processedMonthlyData[monthKey].won + 
                                                     processedMonthlyData[monthKey].lost + 
                                                     processedMonthlyData[monthKey].pending;
            }
          });
        }
      } else {
        // If no product family filter, use the original month data
        processedMonthlyData[monthKey] = { ...monthData };
      }
    });
    
    // Calculate win rates for the processed monthly data
    const processedWinRateByMonth = {};
    Object.keys(processedMonthlyData).forEach(month => {
      const data = processedMonthlyData[month];
      const closedOpps = data.won + data.lost;
      processedWinRateByMonth[month] = closedOpps > 0 
        ? (data.won / closedOpps * 100).toFixed(1) 
        : '0.0';
    });

    // Get product family data for selected year
    const yearlyFamilyData = this.yearFilter === 'all' 
      ? productMetrics.productFamilies 
      : (productMetrics.yearlyProductFamilies[this.yearFilter] || {});

    // For 'All Years' setting, find the total across all years
    const yearTotal = this.yearFilter === 'all' 
      ? summary.totalValue 
      : Object.values(yearlyFamilyData).reduce((sum, fam) => sum + (fam.value || 0), 0) || summary.totalValue;

    this.container.innerHTML = `
      ${headerContent}

      <!-- Global Filters -->
      <div class="flex justify-end items-center mb-4">
        <div class="flex items-center">
          <label class="text-xs text-gray-500 dark:text-gray-400 mr-2">Filters:</label>
          <select id="year-filter" class="px-2 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md">
            <option value="all" ${this.yearFilter === 'all' ? 'selected' : ''}>All Years</option>
            ${years.map(year => `
              <option value="${year}" ${this.yearFilter === year ? 'selected' : ''}>${year}</option>
            `).join('')}
          </select>
          <select id="product-family-filter" class="px-2 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-r-md border-l-0">
            <option value="all">All Families</option>
            ${this.productFamilies.map(family => `
              <option value="${family}" ${this.productFamilyFilter === family ? 'selected' : ''}>${family}</option>
            `).join('')}
          </select>
        </div>
      </div>

      <!-- Product Metrics Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Product Family Metrics ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <!-- Product Families Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Family
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Count
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Value
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  % of Total
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.productFamilies.length > 0 ? 
                this.productFamilies.map(family => {
                  // Use yearly data if available, otherwise fallback to all-time data
                  const familyData = yearlyFamilyData[family] || 
                                    (this.productFamilyFilter === 'all' || this.productFamilyFilter === family ? 
                                     productMetrics.productFamilies[family] : 
                                     { count: 0, value: 0 });
                  
                  const percentOfTotal = familyData.value > 0 
                    ? (familyData.value / yearTotal * 100).toFixed(1) 
                    : '0.0';
                  
                  // Hide families with zero count if filtering
                  if (this.productFamilyFilter !== 'all' && this.productFamilyFilter !== family) {
                    return '';
                  }
                  
                  // Hide families with zero count in the selected year
                  if (familyData.count === 0) {
                    return '';
                  }
                  
                  return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700 dark:text-gray-300">
                      ${family}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${this.formatNumber(familyData.count)}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${this.formatCurrency(familyData.value)}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${percentOfTotal}%
                    </td>
                  </tr>
                  `;
                }).join('') : 
                `<tr><td colspan="4" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No product family data available</td></tr>`
              }
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Monthly Metrics Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Monthly Metrics ${this.productFamilyFilter !== 'all' ? `(${this.productFamilyFilter} Family)` : ''} ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <!-- Monthly Metrics Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Month
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Opps
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Won
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Lost
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Win Rate
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Value
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${filteredMonths.length > 0 ? 
                filteredMonths.map(month => {
                  const monthData = processedMonthlyData[month];
                  
                  // Skip months with no data after family filtering
                  if (this.productFamilyFilter !== 'all' && monthData.total === 0) {
                    return '';
                  }
                  
                  return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700 dark:text-gray-300">
                      ${this.formatMonth(month)}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${monthData.total}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-green-600 dark:text-green-400">
                      ${monthData.won}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-red-600 dark:text-red-400">
                      ${monthData.lost}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right ${parseFloat(processedWinRateByMonth[month]) > 50 ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}">
                      ${processedWinRateByMonth[month]}%
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${this.formatCurrency(monthData.value)}
                    </td>
                  </tr>
                  `;
                }).join('') : 
                `<tr><td colspan="6" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No monthly data available ${this.yearFilter !== 'all' ? `for ${this.yearFilter}` : ''}</td></tr>`
              }
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Monthly Product Family Distribution Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Monthly Product Family Distribution ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <!-- Monthly Product Family Distribution Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Month
                </th>
                ${this.productFamilyFilter === 'all' ? 
                  this.productFamilies.map(family => `
                    <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      ${family}
                    </th>
                  `).join('') : 
                  `<th class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    ${this.productFamilyFilter}
                  </th>`
                }
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${filteredMonths.length > 0 ? 
                filteredMonths.map(month => {
                  const monthData = this.metrics.monthlyProductFamilyDistribution.byMonth[month] || { 
                    total: 0, 
                    families: {} 
                  };
                  
                  // Skip months with no data
                  if (monthData.total === 0) {
                    return '';
                  }
                  
                  return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700 dark:text-gray-300">
                      ${this.formatMonth(month)}
                    </td>
                    ${this.productFamilyFilter === 'all' ? 
                      this.productFamilies.map(family => {
                        const familyData = monthData.families[family] || { count: 0, opportunities: [] };
                        const count = familyData.opportunities.length;
                        
                        return `
                          <td class="px-3 py-2 whitespace-nowrap text-sm text-center ${count > 0 ? 'text-blue-600 dark:text-blue-400 cursor-pointer hover:underline' : 'text-gray-500 dark:text-gray-400'}" ${count > 0 ? `data-month="${month}" data-family="${family}" id="family-cell-${month}-${family.replace(/\s+/g, '-')}"` : ''}>
                            ${count > 0 ? count : '-'}
                          </td>
                        `;
                      }).join('') : 
                      (() => {
                        const familyData = monthData.families[this.productFamilyFilter] || { count: 0, opportunities: [] };
                        const count = familyData.opportunities.length;
                        
                        return `
                          <td class="px-3 py-2 whitespace-nowrap text-sm text-center ${count > 0 ? 'text-blue-600 dark:text-blue-400 cursor-pointer hover:underline' : 'text-gray-500 dark:text-gray-400'}" ${count > 0 ? `data-month="${month}" data-family="${this.productFamilyFilter}" id="family-cell-${month}-${this.productFamilyFilter.replace(/\s+/g, '-')}"` : ''}>
                            ${count > 0 ? count : '-'}
                          </td>
                        `;
                      })()
                    }
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${monthData.total}
                    </td>
                  </tr>
                  `;
                }).join('') : 
                `<tr><td colspan="${this.productFamilyFilter === 'all' ? this.productFamilies.length + 2 : 3}" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No product family data available ${this.yearFilter !== 'all' ? `for ${this.yearFilter}` : ''}</td></tr>`
              }
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Top Performing Products Dashboard -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Top Performing Products ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Top Products by Revenue -->
          <div>
            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Top Products by Revenue</h4>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Product</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Family</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Revenue</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${this.metrics.topPerformingProducts.byRevenue.length > 0 ?
                    this.metrics.topPerformingProducts.byRevenue.map((product, index) => `
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 ${index < 3 ? 'bg-blue-50 dark:bg-blue-900/20' : ''}">
                        <td class="px-3 py-2 text-sm">
                          <div class="font-medium text-gray-800 dark:text-gray-200">${this.escapeHtml(product.id)}</div>
                          <div class="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">${this.escapeHtml(product.description)}</div>
                        </td>
                        <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">${product.family}</td>
                        <td class="px-3 py-2 text-sm text-right font-medium ${index < 3 ? 'text-green-600 dark:text-green-400' : 'text-gray-500 dark:text-gray-400'}">
                          ${this.formatCurrency(product.revenue)}
                        </td>
                      </tr>
                    `).join('') :
                    `<tr><td colspan="3" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No product data available</td></tr>`
                  }
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Top Products by Quantity -->
          <div>
            <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3">Top Products by Quantity</h4>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Product</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Family</th>
                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 tracking-wider">Quantity</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${this.metrics.topPerformingProducts.byQuantity.length > 0 ?
                    this.metrics.topPerformingProducts.byQuantity.map((product, index) => `
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 ${index < 3 ? 'bg-blue-50 dark:bg-blue-900/20' : ''}">
                        <td class="px-3 py-2 text-sm">
                          <div class="font-medium text-gray-800 dark:text-gray-200">${this.escapeHtml(product.id)}</div>
                          <div class="text-xs text-gray-500 dark:text-gray-400 truncate max-w-xs">${this.escapeHtml(product.description)}</div>
                        </td>
                        <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">${product.family}</td>
                        <td class="px-3 py-2 text-sm text-right font-medium ${index < 3 ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}">
                          ${this.formatNumber(product.quantity)}
                        </td>
                      </tr>
                    `).join('') :
                    `<tr><td colspan="3" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No product data available</td></tr>`
                  }
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Win Rate Analysis by Product Family -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Win Rate Analysis by Product Family ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Family</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Opportunities</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Won</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Lost</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Pending</th>
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Win Rate</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${(() => {
                // Filter families with at least one opportunity
                const activeFamilies = this.productFamilies.filter(family => 
                  this.metrics.winRateByFamily[family] && 
                  this.metrics.winRateByFamily[family].total > 0
                );
                
                // Sort by win rate descending
                const sortedFamilies = activeFamilies.sort((a, b) => {
                  return this.metrics.winRateByFamily[b].winRate - this.metrics.winRateByFamily[a].winRate;
                });
                
                if (sortedFamilies.length === 0) {
                  return `<tr><td colspan="6" class="px-3 py-2 text-center text-sm text-gray-500 dark:text-gray-400">No win rate data available</td></tr>`;
                }
                
                return sortedFamilies.map(family => {
                  const data = this.metrics.winRateByFamily[family];
                  const winRate = data.winRate.toFixed(1);
                  
                  // Skip families with no opportunities if a filter is applied
                  if (this.productFamilyFilter !== 'all' && this.productFamilyFilter !== family) {
                    return '';
                  }
                  
                  // Get color class based on win rate
                  let winRateClass = 'text-gray-500 dark:text-gray-400';
                  if (data.winRate >= 70) {
                    winRateClass = 'text-green-600 dark:text-green-400';
                  } else if (data.winRate >= 40) {
                    winRateClass = 'text-blue-600 dark:text-blue-400';
                  } else if (data.winRate > 0) {
                    winRateClass = 'text-orange-600 dark:text-orange-400';
                  } else if (data.lost > 0) {
                    winRateClass = 'text-red-600 dark:text-red-400';
                  }
                  
                  return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700 dark:text-gray-300">
                      ${family}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                      ${data.uniqueOpportunities || data.total}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-green-600 dark:text-green-400">
                      ${data.won}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-red-600 dark:text-red-400">
                      ${data.lost}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-yellow-600 dark:text-yellow-400">
                      ${data.pending}
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap">
                      <div class="flex items-center justify-center">
                        <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2">
                          <div class="h-2.5 rounded-full ${winRateClass.replace('text-', 'bg-')}" style="width: ${Math.min(100, data.winRate)}%"></div>
                        </div>
                        <span class="text-sm ${winRateClass} font-medium">${winRate}%</span>
                      </div>
                    </td>
                  </tr>
                  `;
                }).join('');
              })()}
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Win Rate Analysis by Company -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
        <h3 class="text-base font-medium text-gray-700 dark:text-gray-300 mb-4">
          Win Rate Analysis by Company ${this.yearFilter !== 'all' ? `- ${this.yearFilter}` : ''}
        </h3>
        
        <!-- Company win rates table -->
        <div id="company-win-rates-table" class="overflow-x-auto">
          ${this.renderCompanyWinRatesTable()}
        </div>
        
        <!-- Pagination Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div id="company-count-display" class="text-sm text-gray-500 dark:text-gray-400">
            ${this.getCompanyPaginationText()}
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="company-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentCompanyPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentCompanyPage <= 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="company-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentCompanyPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentCompanyPage <= 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span id="company-page-display" class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentCompanyPage} of ${Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage) || 1}
            </span>
            
            <button id="company-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentCompanyPage >= Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentCompanyPage >= Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage) ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="company-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentCompanyPage >= Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage) ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentCompanyPage >= Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage) ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
      
      <!-- Monthly Product Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-base font-medium text-gray-700 dark:text-gray-300">
            Monthly Product Breakdown ${this.productFamilyFilter !== 'all' ? `(${this.productFamilyFilter} Family)` : ''}
          </h3>
          
          <!-- Month Selector -->
          <div class="flex items-center space-x-2">
            <label class="text-xs text-gray-500 dark:text-gray-400">Month:</label>
            <select id="month-selector" class="px-2 py-1 text-sm bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
              ${filteredMonths.map((month, index) => `
                <option value="${month}" ${index === filteredMonths.length - 1 ? 'selected' : ''}>${this.formatMonth(month)}</option>
              `).join('')}
            </select>
          </div>
        </div>
        
        <!-- Product Detail Table for Selected Month -->
        <div id="month-product-detail" class="overflow-x-auto">
          ${this.renderMonthProductDetail(selectedMonth)}
        </div>
        
        <!-- Pagination Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div id="product-count-display" class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${totalProductItems > 0 ? '1' : '0'} to 
            ${Math.min(this.productsPerPage, totalProductItems)} of 
            ${totalProductItems} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="product-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage <= 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="product-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage <= 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span id="product-page-display" class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentProductPage} of ${totalProductPages}
            </span>
            
            <button id="product-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage >= totalProductPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage >= totalProductPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="product-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage >= totalProductPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage >= totalProductPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;
    
    // Set up event listeners for the filters
    setTimeout(() => {
      const monthSelector = document.getElementById('month-selector');
      const productFamilyFilter = document.getElementById('product-family-filter');
      const yearFilter = document.getElementById('year-filter');
      
      // Product pagination buttons
      const productFirstPage = document.getElementById('product-first-page');
      const productPrevPage = document.getElementById('product-prev-page');
      const productNextPage = document.getElementById('product-next-page');
      const productLastPage = document.getElementById('product-last-page');
      
      if (monthSelector) {
        monthSelector.addEventListener('change', (e) => {
          const selectedMonth = e.target.value;
          this.currentProductPage = 1; // Reset to first page when month changes
          this.updateMonthProductDetail(selectedMonth);
        });
      }
      
      if (productFamilyFilter) {
        productFamilyFilter.addEventListener('change', (e) => {
          this.productFamilyFilter = e.target.value;
          this.currentProductPage = 1; // Reset to first page when filter changes
          this.render();
        });
      }
      
      if (yearFilter) {
        yearFilter.addEventListener('change', (e) => {
          this.yearFilter = e.target.value;
          this.currentProductPage = 1; // Reset to first page when year changes
          this.render();
        });
      }
      
      // Set up pagination event listeners
      if (productFirstPage) {
        productFirstPage.addEventListener('click', () => {
          if (this.currentProductPage > 1) {
            this.currentProductPage = 1;
            this.updateMonthProductDetail(monthSelector?.value);
          }
        });
      }
      
      if (productPrevPage) {
        productPrevPage.addEventListener('click', () => {
          if (this.currentProductPage > 1) {
            this.currentProductPage--;
            this.updateMonthProductDetail(monthSelector?.value);
          }
        });
      }
      
      if (productNextPage) {
        productNextPage.addEventListener('click', () => {
          const selectedMonth = monthSelector?.value;
          if (selectedMonth) {
            const totalProducts = this.getFilteredProductsForMonth(selectedMonth).length;
            const totalPages = Math.ceil(totalProducts / this.productsPerPage);
            
            if (this.currentProductPage < totalPages) {
              this.currentProductPage++;
              this.updateMonthProductDetail(selectedMonth);
            }
          }
        });
      }
      
      if (productLastPage) {
        productLastPage.addEventListener('click', () => {
          const selectedMonth = monthSelector?.value;
          if (selectedMonth) {
            const totalProducts = this.getFilteredProductsForMonth(selectedMonth).length;
            const totalPages = Math.ceil(totalProducts / this.productsPerPage);
            
            if (this.currentProductPage < totalPages) {
              this.currentProductPage = totalPages;
              this.updateMonthProductDetail(selectedMonth);
            }
          }
        });
      }
      
      // Initialize product detail with correct pagination on first render
      if (selectedMonth) {
        this.updateMonthProductDetail(selectedMonth);
      }

      // Attach click handlers to family cells in the distribution table
      this.attachFamilyCellHandlers();
      
      // Set up company win rates pagination
      this.setupCompanyPagination();
    }, 100);
  }

  // Add new method to attach handlers to family cells
  attachFamilyCellHandlers() {
    const familyCells = document.querySelectorAll('[id^="family-cell-"]');
    
    familyCells.forEach(cell => {
      const month = cell.getAttribute('data-month');
      const family = cell.getAttribute('data-family');
      
      if (month && family) {
        cell.addEventListener('click', () => {
          console.log(`Clicked on ${family} for ${month}`);
          this.showFamilyDetailsModal(month, family);
        });
      }
    });
  }

  // Helper method to update the monthly product detail
  updateMonthProductDetail(monthKey) {
    const productDetailContainer = document.getElementById('month-product-detail');
    const pageDisplay = document.getElementById('product-page-display');
    const countDisplay = document.getElementById('product-count-display');
    const firstPageBtn = document.getElementById('product-first-page');
    const prevPageBtn = document.getElementById('product-prev-page');
    const nextPageBtn = document.getElementById('product-next-page');
    const lastPageBtn = document.getElementById('product-last-page');
    
    if (!productDetailContainer || !monthKey) return;
    
    // Render the product detail with pagination
    productDetailContainer.innerHTML = this.renderMonthProductDetail(monthKey);
    
    // Get total products and calculate pagination values
    const totalProducts = this.getFilteredProductsForMonth(monthKey).length;
    const totalPages = Math.ceil(totalProducts / this.productsPerPage);
    const startItem = totalProducts === 0 ? 0 : (this.currentProductPage - 1) * this.productsPerPage + 1;
    const endItem = Math.min(startItem + this.productsPerPage - 1, totalProducts);
    
    // Update page display
    if (pageDisplay) {
      pageDisplay.textContent = `${this.currentProductPage} of ${totalPages}`;
    }
    
    // Update count display
    if (countDisplay) {
      countDisplay.textContent = `Showing ${startItem} to ${endItem} of ${totalProducts} results`;
    }
    
    // Update button states
    if (firstPageBtn && prevPageBtn) {
      const isFirstPage = this.currentProductPage <= 1;
      firstPageBtn.disabled = isFirstPage;
      prevPageBtn.disabled = isFirstPage;
      firstPageBtn.classList.toggle('opacity-50', isFirstPage);
      firstPageBtn.classList.toggle('cursor-not-allowed', isFirstPage);
      prevPageBtn.classList.toggle('opacity-50', isFirstPage);
      prevPageBtn.classList.toggle('cursor-not-allowed', isFirstPage);
    }
    
    if (nextPageBtn && lastPageBtn) {
      const isLastPage = this.currentProductPage >= totalPages;
      nextPageBtn.disabled = isLastPage;
      lastPageBtn.disabled = isLastPage;
      nextPageBtn.classList.toggle('opacity-50', isLastPage);
      nextPageBtn.classList.toggle('cursor-not-allowed', isLastPage);
      lastPageBtn.classList.toggle('opacity-50', isLastPage);
      lastPageBtn.classList.toggle('cursor-not-allowed', isLastPage);
    }
  }

  // Helper method to get filtered products for a month
  getFilteredProductsForMonth(monthKey) {
    if (!this.metrics || !monthKey || !this.metrics.monthlyMetrics.byMonth[monthKey]) {
      return [];
    }
    
    const monthData = this.metrics.monthlyMetrics.byMonth[monthKey];
    const products = monthData.products;
    
    if (!products || Object.keys(products).length === 0) {
      return [];
    }
    
    // Get product data with families attached
    const productsWithFamilies = Object.entries(products).map(([productId, productData]) => {
      return {
        id: productId,
        ...productData,
        family: productData.family || this.getProductFamily(productId)
      };
    });
    
    // Filter by selected family if filtering is applied
    const filteredProducts = this.productFamilyFilter === 'all' 
      ? productsWithFamilies 
      : productsWithFamilies.filter(product => product.family === this.productFamilyFilter);
    
    // Sort products by value (highest first)
    return filteredProducts.sort((a, b) => b.value - a.value);
  }

  renderMonthProductDetail(monthKey) {
    if (!this.metrics || !monthKey || !this.metrics.monthlyMetrics.byMonth[monthKey]) {
      return `<p class="text-center text-sm text-gray-500 dark:text-gray-400">No product data available for this month</p>`;
    }
    
    const monthData = this.metrics.monthlyMetrics.byMonth[monthKey];
    
    // Get sorted and filtered products
    const sortedProducts = this.getFilteredProductsForMonth(monthKey);
    
    if (sortedProducts.length === 0) {
      return `<p class="text-center text-sm text-gray-500 dark:text-gray-400">No ${this.productFamilyFilter !== 'all' ? this.productFamilyFilter : ''} products found for this month</p>`;
    }
    
    // Calculate pagination
    const startIndex = (this.currentProductPage - 1) * this.productsPerPage;
    const endIndex = Math.min(startIndex + this.productsPerPage, sortedProducts.length);
    const paginatedProducts = sortedProducts.slice(startIndex, endIndex);
    
    return `
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Product ID
            </th>
            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Family
            </th>
            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Description
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Count
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Value
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              % of Month
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          ${paginatedProducts.map(product => {
            const percentOfMonth = (product.value / monthData.value * 100).toFixed(1);
            
            return `
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400">
                ${product.id}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-700 dark:text-gray-300">
                ${product.family}
              </td>
              <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 max-w-md overflow-hidden text-ellipsis">
                ${product.description || 'N/A'}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                ${this.formatNumber(product.count)}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                ${this.formatCurrency(product.value)}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                ${percentOfMonth}%
              </td>
            </tr>
            `;
          }).join('')}
        </tbody>
      </table>
    `;
  }

  setupEventListeners() {
    // Only set up timeframe buttons if we're rendering our own header
    if (!this.renderHeader) return;
    
    // Add event listeners for timeframe buttons
    const timeframeButtons = [
      { id: 'timeframe-year', value: 'year' },
      { id: 'timeframe-quarter', value: 'quarter' },
      { id: 'timeframe-month', value: 'month' }
    ];
    
    timeframeButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', () => {
          this.timeframe = button.value;
          this.calculateMetrics().then(() => {
            this.render();
            this.setupEventListeners();
          });
        });
      }
    });

    // Add event listener for showing family details
    document.addEventListener('showFamilyDetails', (event) => {
      const { month, family } = event.detail;
      this.showFamilyDetailsModal(month, family);
    });
    
    // Add event listener for viewing opportunity details
    document.addEventListener('viewOpportunity', (event) => {
      const id = event.detail.id;
      // Call the parent component's viewOpportunity method
      if (this.opportunityComponent && typeof this.opportunityComponent.viewOpportunity === 'function') {
        this.opportunityComponent.viewOpportunity(id);
      } else {
        console.error('Cannot view opportunity details: parent component method not available');
      }
    });
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'metrics-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('metrics-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    }
  }

  // Helper function to escape HTML to prevent XSS
  escapeHtml(text) {
    if (!text) return '';
    
    return String(text)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  // Add new method to render family details modal
  showFamilyDetailsModal(month, family) {
    const monthData = this.metrics.monthlyProductFamilyDistribution.byMonth[month];
    if (!monthData || !monthData.families[family]) {
      this.notificationSystem.addNotification(`No data found for ${family} in ${this.formatMonth(month)}`, "error");
      return;
    }
    
    const familyData = monthData.families[family];
    const opportunities = familyData.opportunities;
    
    if (!opportunities || opportunities.length === 0) {
      this.notificationSystem.addNotification(`No opportunities found for ${family} in ${this.formatMonth(month)}`, "error");
      return;
    }
    
    // Create modal content
    const modalContent = `
      <div class="modal-content overflow-y-auto p-6" style="max-height: 80vh;">
        <div class="flex flex-col md:flex-row justify-between items-start mb-6">
          <div>
            <h2 class="text-xl font-semibold mb-2">${family} Family - ${this.formatMonth(month)}</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              ${opportunities.length} opportunities with ${family} products, 
              total value ${this.formatCurrency(familyData.value)}
            </p>
          </div>
          <div class="mt-2 md:mt-0">
            <div class="flex items-center space-x-2">
              <span class="inline-block w-3 h-3 rounded-full bg-green-500"></span>
              <span class="text-sm">Won: ${opportunities.filter(o => o.status?.toLowerCase() === 'won').length}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="inline-block w-3 h-3 rounded-full bg-red-500"></span>
              <span class="text-sm">Lost: ${opportunities.filter(o => o.status?.toLowerCase() === 'lost').length}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="inline-block w-3 h-3 rounded-full bg-yellow-500"></span>
              <span class="text-sm">Pending: ${opportunities.filter(o => !['won', 'lost'].includes(o.status?.toLowerCase())).length}</span>
            </div>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  ID
                </th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Subject
                </th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Account
                </th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Owner
                </th>
                <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Amount
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${opportunities.map(opp => `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-blue-600 dark:text-blue-400 cursor-pointer hover:underline" 
                      onclick="document.dispatchEvent(new CustomEvent('viewOpportunity', {detail: {id: '${opp.id}'}}));">
                    ${this.escapeHtml(opp.opportunityId)}
                  </td>
                  <td class="px-3 py-2 text-sm text-gray-800 dark:text-gray-200">
                    ${this.escapeHtml(opp.subject)}
                  </td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                    ${this.escapeHtml(opp.businessAccount)}
                  </td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    ${this.escapeHtml(opp.owner)}
                  </td>
                  <td class="px-3 py-2 whitespace-nowrap text-center">
                    <span class="px-2 py-1 text-xs font-semibold rounded-full 
                      ${opp.status?.toLowerCase() === 'won' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 
                      opp.status?.toLowerCase() === 'lost' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' : 
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'}">
                      ${this.escapeHtml(opp.status)}
                    </span>
                  </td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                    ${this.formatCurrency(opp.amount)}
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>

        <div class="flex justify-end mt-6">
          <button id="family-details-close" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
        </div>
      </div>
    `;
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.id = 'family-details-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-5xl';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners
    const closeButton = document.getElementById('family-details-close');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        document.body.removeChild(modalOverlay);
      });
    }
    
    // Add keyboard event for Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape' && document.getElementById('family-details-modal')) {
        document.body.removeChild(modalOverlay);
      }
    }, { once: true });
  }

  setupCompanyPagination() {
    const firstPageBtn = document.getElementById('company-first-page');
    const prevPageBtn = document.getElementById('company-prev-page');
    const nextPageBtn = document.getElementById('company-next-page');
    const lastPageBtn = document.getElementById('company-last-page');
    
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentCompanyPage > 1) {
          this.currentCompanyPage = 1;
          this.updateCompanyWinRatesTable();
        }
      });
    }
    
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentCompanyPage > 1) {
          this.currentCompanyPage--;
          this.updateCompanyWinRatesTable();
        }
      });
    }
    
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage);
        if (this.currentCompanyPage < totalPages) {
          this.currentCompanyPage++;
          this.updateCompanyWinRatesTable();
        }
      });
    }
    
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(this.metrics.companyWinRates.length / this.companiesPerPage);
        if (this.currentCompanyPage < totalPages) {
          this.currentCompanyPage = totalPages;
          this.updateCompanyWinRatesTable();
        }
      });
    }
  }
  
  getCompanyPaginationText() {
    if (!this.metrics || !this.metrics.companyWinRates) {
      return 'Showing 0 to 0 of 0 results';
    }
    
    const totalCompanies = this.filteredCompanyCount || this.metrics.companyWinRates.length;
    const startItem = totalCompanies === 0 ? 0 : (this.currentCompanyPage - 1) * this.companiesPerPage + 1;
    const endItem = Math.min(startItem + this.companiesPerPage - 1, totalCompanies);
    
    return `Showing ${startItem} to ${endItem} of ${totalCompanies} results`;
  }
  
  renderCompanyWinRatesTable() {
    if (!this.metrics || !this.metrics.companyWinRates || this.metrics.companyWinRates.length === 0) {
      return `<p class="text-center text-sm text-gray-500 dark:text-gray-400 py-4">No company data available</p>`;
    }
    
    // Filter companies based on year and product family
    let filteredCompanies = [...this.metrics.companyWinRates];
    
    // Apply year filter if not "all"
    if (this.yearFilter !== 'all') {
      filteredCompanies = filteredCompanies.filter(company => {
        // Check if this company has data for the selected year
        return company.yearData && company.yearData[this.yearFilter] && 
               company.yearData[this.yearFilter].total > 0;
      });
    }
    
    // Apply product family filter if not "all"
    if (this.productFamilyFilter !== 'all') {
      filteredCompanies = filteredCompanies.filter(company => {
        if (this.yearFilter === 'all') {
          // Check across all years
          return company.productFamilies.includes(this.productFamilyFilter);
        } else {
          // Check just for the selected year
          return company.yearData[this.yearFilter]?.productFamilies.includes(this.productFamilyFilter);
        }
      });
    }
    
    // Sort by opportunity count (highest first)
    filteredCompanies.sort((a, b) => {
      if (this.yearFilter === 'all') {
        return b.uniqueOppCount - a.uniqueOppCount;
      } else {
        // Get opportunity count for the specific year
        const aCount = a.yearData[this.yearFilter]?.uniqueOppCount || 0;
        const bCount = b.yearData[this.yearFilter]?.uniqueOppCount || 0;
        return bCount - aCount;
      }
    });
    
    // If no companies match filters
    if (filteredCompanies.length === 0) {
      return `<p class="text-center text-sm text-gray-500 dark:text-gray-400 py-4">No companies match the selected filters</p>`;
    }
    
    // Calculate pagination
    const startIndex = (this.currentCompanyPage - 1) * this.companiesPerPage;
    const endIndex = Math.min(startIndex + this.companiesPerPage, filteredCompanies.length);
    const paginatedCompanies = filteredCompanies.slice(startIndex, endIndex);
    
    // Store the total for pagination updates
    this.filteredCompanyCount = filteredCompanies.length;
    
    return `
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead class="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Company
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Opportunities
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Won
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Lost
            </th>
            <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Value
            </th>
            <th class="px-3 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              Win Rate
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          ${paginatedCompanies.map(company => {
            // Use year-specific data if a year is selected, otherwise use overall data
            let displayData = { ...company };
            
            if (this.yearFilter !== 'all' && company.yearData[this.yearFilter]) {
              const yearData = company.yearData[this.yearFilter];
              displayData = {
                ...company,
                total: yearData.total,
                won: yearData.won,
                lost: yearData.lost,
                pending: yearData.pending,
                value: yearData.value,
                winRate: yearData.winRate,
                uniqueOppCount: yearData.uniqueOppCount
              };
            }
            
            const winRateRounded = displayData.winRate.toFixed(1);
            
            // Get color class based on win rate
            let winRateClass = 'text-gray-500 dark:text-gray-400';
            if (displayData.winRate >= 70) {
              winRateClass = 'text-green-600 dark:text-green-400';
            } else if (displayData.winRate >= 40) {
              winRateClass = 'text-blue-600 dark:text-blue-400';
            } else if (displayData.winRate > 0) {
              winRateClass = 'text-orange-600 dark:text-orange-400';
            } else if (displayData.lost > 0) {
              winRateClass = 'text-red-600 dark:text-red-400';
            }
            
            // Determine the display name - prefer full company name over ID
            const displayName = company.name && company.name !== company.id ? company.name : company.id;
            
            return `
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td class="px-3 py-2 whitespace-nowrap text-sm">
                <div class="font-medium text-gray-800 dark:text-gray-200">${this.escapeHtml(displayName)}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">${this.escapeHtml(company.id)}</div>
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                ${displayData.uniqueOppCount || displayData.total}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-green-600 dark:text-green-400">
                ${displayData.won}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-red-600 dark:text-red-400">
                ${displayData.lost}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">
                ${this.formatCurrency(displayData.value)}
              </td>
              <td class="px-3 py-2 whitespace-nowrap">
                <div class="flex items-center justify-center">
                  <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2">
                    <div class="h-2.5 rounded-full ${winRateClass.replace('text-', 'bg-')}" style="width: ${Math.min(100, displayData.winRate)}%"></div>
                  </div>
                  <span class="text-sm ${winRateClass} font-medium">${winRateRounded}%</span>
                </div>
              </td>
            </tr>
            `;
          }).join('')}
        </tbody>
      </table>
    `;
  }
  
  updateCompanyWinRatesTable() {
    const tableContainer = document.getElementById('company-win-rates-table');
    const pageDisplay = document.getElementById('company-page-display');
    const countDisplay = document.getElementById('company-count-display');
    const firstPageBtn = document.getElementById('company-first-page');
    const prevPageBtn = document.getElementById('company-prev-page');
    const nextPageBtn = document.getElementById('company-next-page');
    const lastPageBtn = document.getElementById('company-last-page');
    
    if (!tableContainer) return;
    
    // Update table content
    tableContainer.innerHTML = this.renderCompanyWinRatesTable();
    
    // Get total pages using filtered count
    const totalCompanies = this.filteredCompanyCount || this.metrics.companyWinRates.length;
    const totalPages = Math.ceil(totalCompanies / this.companiesPerPage);
    
    // Update page display
    if (pageDisplay) {
      pageDisplay.textContent = `${this.currentCompanyPage} of ${totalPages || 1}`;
    }
    
    // Update count display
    if (countDisplay) {
      countDisplay.textContent = this.getCompanyPaginationText();
    }
    
    // Update button states
    if (firstPageBtn && prevPageBtn) {
      const isFirstPage = this.currentCompanyPage <= 1;
      firstPageBtn.disabled = isFirstPage;
      prevPageBtn.disabled = isFirstPage;
      firstPageBtn.classList.toggle('opacity-50', isFirstPage);
      firstPageBtn.classList.toggle('cursor-not-allowed', isFirstPage);
      prevPageBtn.classList.toggle('opacity-50', isFirstPage);
      prevPageBtn.classList.toggle('cursor-not-allowed', isFirstPage);
    }
    
    if (nextPageBtn && lastPageBtn) {
      const isLastPage = this.currentCompanyPage >= totalPages;
      nextPageBtn.disabled = isLastPage;
      lastPageBtn.disabled = isLastPage;
      nextPageBtn.classList.toggle('opacity-50', isLastPage);
      nextPageBtn.classList.toggle('cursor-not-allowed', isLastPage);
      lastPageBtn.classList.toggle('opacity-50', isLastPage);
      lastPageBtn.classList.toggle('cursor-not-allowed', isLastPage);
    }
  }
} 