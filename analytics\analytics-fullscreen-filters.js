// analytics-fullscreen-filters.js
// Standardized time filters and data processing for analytics dashboard

export class FilterManager {
  constructor(dashboard) {
    this.dashboard = dashboard;
    this.standardFilters = ['week', 'month', 'quarter', 'year'];
    this.filterLabels = {
      week: 'Weekly',
      month: 'Monthly',
      quarter: 'Quarterly',
      year: 'Yearly'
    };
    this.activeFilters = new Map(); // Track active filters for each chart
    this.lastRefresh = new Date();
    this.isProcessing = false;
    this.pendingRefresh = false;
  }

  /**
   * Initialize filter manager
   */
  init() {
    // Standardize all filter dropdowns
    this.standardizeFilterDropdowns();
    
    // Setup global loading state handler
    this.setupLoadingState();
    
    // Add event listeners
    this.setupEventListeners();
  }

  /**
   * Standardize all filter dropdowns in the dashboard
   */
  standardizeFilterDropdowns() {
    // Get all filter dropdowns
    const filterSelects = document.querySelectorAll('select[id$="Filter"]');
    
    filterSelects.forEach(select => {
      // Skip non-time filters like carrier filters
      if (select.id === 'historyCarrierFilter' || 
          select.id === 'historyCountryFilter') {
        return;
      }
      
      // Save current value if it exists
      const currentValue = select.value;
      
      // Clear existing options
      select.innerHTML = '';
      
      // Add standard options
      this.standardFilters.forEach(filter => {
        const option = document.createElement('option');
        option.value = filter;
        option.textContent = this.getFilterLabel(filter, select.id);
        select.appendChild(option);
      });
      
      // Restore value if it was one of our standard values
      if (this.standardFilters.includes(currentValue)) {
        select.value = currentValue;
      } else {
        select.value = 'month'; // Default to monthly
      }
      
      // Store initial value
      this.activeFilters.set(select.id, select.value);
    });
  }

  /**
   * Get appropriate filter label based on context
   */
  getFilterLabel(filter, selectId) {
    // Special cases based on filter context
    if (selectId === 'freightCostFilter' || selectId === 'shipmentByDayFilter') {
      switch (filter) {
        case 'week': return 'Last 7 days';
        case 'month': return 'Last 30 days';
        case 'quarter': return 'Last 90 days';
        case 'year': return 'Last 365 days';
      }
    }
    
    if (selectId.includes('Volume') || selectId.includes('Trends')) {
      switch (filter) {
        case 'week': return 'By Day';
        case 'month': return 'By Week';
        case 'quarter': return 'By Month';
        case 'year': return 'By Quarter';
      }
    }
    
    // Default labels
    return this.filterLabels[filter] || filter;
  }

  /**
   * Setup loading state handler
   */
  setupLoadingState() {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'filterLoadingOverlay';
    overlay.className = 'fixed inset-0 bg-gray-900 bg-opacity-30 z-50 flex items-center justify-center hidden';
    overlay.innerHTML = `
      <div class="bg-white rounded-lg shadow-xl p-6 max-w-sm w-full">
        <div class="flex items-center">
          <div class="mr-4">
            <div class="loading-spinner w-10 h-10 border-4 border-blue-500 border-t-transparent"></div>
          </div>
          <div>
            <h3 class="text-lg font-semibold text-gray-800">Processing Data</h3>
            <p class="text-sm text-gray-600 mt-1">Updating charts with new filter settings...</p>
          </div>
        </div>
      </div>
    `;
    document.body.appendChild(overlay);
  }

  /**
   * Setup filter event listeners
   */
  setupEventListeners() {
    // Get all filter dropdowns
    const filterSelects = document.querySelectorAll('select[id$="Filter"]');
    
    filterSelects.forEach(select => {
      select.addEventListener('change', (e) => {
        const filterId = e.target.id;
        const newValue = e.target.value;
        const oldValue = this.activeFilters.get(filterId);
        
        // Update active filter
        this.activeFilters.set(filterId, newValue);
        
        // Refresh affected charts
        this.refreshChartsForFilter(filterId, newValue, oldValue);
      });
    });
  }

  /**
   * Show loading overlay
   */
  showLoading() {
    this.isProcessing = true;
    const overlay = document.getElementById('filterLoadingOverlay');
    if (overlay) {
      overlay.classList.remove('hidden');
    }
  }

  /**
   * Hide loading overlay
   */
  hideLoading() {
    this.isProcessing = false;
    const overlay = document.getElementById('filterLoadingOverlay');
    if (overlay) {
      overlay.classList.add('hidden');
    }
    
    // Check if there's a pending refresh
    if (this.pendingRefresh) {
      this.pendingRefresh = false;
      setTimeout(() => this.refreshAllCharts(), 100);
    }
    
    // Update last refresh time
    this.lastRefresh = new Date();
    this.updateLastRefreshed();
  }

  /**
   * Update last refreshed timestamp
   */
  updateLastRefreshed() {
    const lastUpdatedElement = document.getElementById('lastUpdated');
    if (lastUpdatedElement) {
      lastUpdatedElement.textContent = `Last updated: ${this.lastRefresh.toLocaleTimeString()}`;
    }
  }

  /**
   * Refresh charts affected by a filter change
   */
  refreshChartsForFilter(filterId, newValue, oldValue) {
    // If already processing, queue the refresh
    if (this.isProcessing) {
      this.pendingRefresh = true;
      return;
    }
    
    this.showLoading();
    
    setTimeout(() => {
      try {
        if (this.dashboard && typeof this.dashboard.renderCharts === 'function') {
          this.dashboard.renderCharts();
        } else {
          // Fallback to refreshing individual charts
          this.refreshIndividualCharts(filterId);
        }
      } catch (error) {
        console.error('Error refreshing charts:', error);
        this.showError('An error occurred while updating charts');
      } finally {
        this.hideLoading();
      }
    }, 100);
  }

  /**
   * Refresh individual charts based on filter ID
   */
  refreshIndividualCharts(filterId) {
    const chartId = filterId.replace('Filter', '');
    const chartElement = document.getElementById(`${chartId}Chart`);
    
    if (!chartElement) {
      console.warn(`Chart element not found for filter: ${filterId}`);
      return;
    }
    
    // Map of filter IDs to render functions
    const renderFunctions = {
      'freightCostFilter': 'renderFreightCostChart',
      'shipmentVolumeFilter': 'renderShipmentVolumeChart',
      'shipmentByDayFilter': 'renderShipmentByDayChart',
      'shipmentByCarrierFilter': 'renderShipmentByCarrierChart',
      'costTrendsFilter': 'renderCostTrendsChart',
      'costByDestinationFilter': 'renderCostByDestinationChart',
      'carrierCostFilter': 'renderCarrierCostChart',
      'packageCostFilter': 'renderPackageCostChart',
      'carrierPerformanceFilter': 'renderCarrierPerformanceChart',
      'carrierCostVolumeFilter': 'renderCarrierCostVolumeChart',
      'carrierDestinationFilter': 'renderCarrierDestinationChart',
      'topProductsFilter': 'renderTopProductsChart',
      'topDestinationFilter': 'renderTopDestinationChart',
      'customerShipmentsFilter': 'renderCustomerShipmentsChart',
      'shippingMethodFilter': 'renderShippingMethodChart'
    };
    
    const renderFunction = renderFunctions[filterId];
    if (renderFunction && this.dashboard && typeof this.dashboard[renderFunction] === 'function') {
      this.dashboard[renderFunction]();
    } else {
      console.warn(`Render function not found for filter: ${filterId}`);
    }
  }

  /**
   * Refresh all charts
   */
  refreshAllCharts() {
    // If already processing, queue the refresh
    if (this.isProcessing) {
      this.pendingRefresh = true;
      return;
    }
    
    this.showLoading();
    
    setTimeout(() => {
      try {
        if (this.dashboard && typeof this.dashboard.renderCharts === 'function') {
          this.dashboard.renderCharts();
        }
      } catch (error) {
        console.error('Error refreshing all charts:', error);
        this.showError('An error occurred while updating charts');
      } finally {
        this.hideLoading();
      }
    }, 100);
  }

  /**
   * Show error message
   */
  showError(message) {
    const statusMessage = document.getElementById('statusMessage');
    if (statusMessage) {
      statusMessage.innerHTML = `
        <div class="error-message">
          ${message}
          <button class="ml-2 text-red-800 font-medium" onclick="this.parentElement.remove()">Dismiss</button>
        </div>
      `;
      statusMessage.classList.remove('hidden');
    }
  }

  /**
   * Get data based on time filter with improved error handling
   */
  getTimeFilteredData(filter, data) {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    
    try {
      const now = new Date();
      const cutoffDate = this.getCutoffDate(filter);
      
      // Filter data with robust date handling
      return data.filter(item => {
        if (!item.Date) return false;
        
        const recordDate = this.parseDate(item.Date);
        if (!recordDate) return false;
        
        return recordDate >= cutoffDate && recordDate <= now;
      });
    } catch (error) {
      console.error('Error filtering data by time:', error);
      return [];
    }
  }

  /**
   * Get cutoff date based on filter
   */
  getCutoffDate(filter) {
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (filter) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case 'quarter':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        cutoffDate.setDate(now.getDate() - 365);
        break;
      default:
        cutoffDate.setDate(now.getDate() - 30); // Default to month
    }
    
    return cutoffDate;
  }

  /**
   * Parse date with robust error handling
   */
  parseDate(dateString) {
    if (!dateString) return null;
    
    try {
      // If already a Date object
      if (dateString instanceof Date) {
        return isNaN(dateString.getTime()) ? null : dateString;
      }
      
      // If it's a numeric timestamp
      if (!isNaN(dateString)) {
        const timestamp = parseInt(dateString);
        const date = new Date(timestamp);
        return isNaN(date.getTime()) ? null : date;
      }
      
      // Handle string date formats
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        return date;
      }
      
      // Try custom parsing for different formats
      // MM/DD/YYYY or MM-DD-YYYY
      const formats = [
        /^(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})$/,
        /^(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})$/,
        /^(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2})$/
      ];
      
      for (const regex of formats) {
        const match = regex.exec(dateString);
        if (match) {
          // Handle different format orders
          const [_, part1, part2, part3] = match;
          
          // YYYY-MM-DD
          if (part1.length === 4) {
            return new Date(parseInt(part1), parseInt(part2) - 1, parseInt(part3));
          } 
          // MM/DD/YYYY
          else if (part3.length === 4) {
            return new Date(parseInt(part3), parseInt(part1) - 1, parseInt(part2));
          }
          // MM/DD/YY
          else if (part3.length === 2) {
            // Assume 20XX for years less than 50, 19XX for years >= 50
            const year = parseInt(part3) < 50 ? 2000 + parseInt(part3) : 1900 + parseInt(part3);
            return new Date(year, parseInt(part1) - 1, parseInt(part2));
          }
        }
      }
      
      return null;
    } catch (error) {
      console.warn(`Error parsing date: ${dateString}`, error);
      return null;
    }
  }

  /**
   * Generate time periods based on filter type for chart labels
   */
  generateTimePeriods(filter, count = 12) {
    const periods = [];
    const now = new Date();
    
    switch (filter) {
      case 'week':
        // Daily for last 7 days
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(now.getDate() - i);
          periods.push({
            label: date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
            startDate: new Date(date.setHours(0, 0, 0, 0)),
            endDate: new Date(date.setHours(23, 59, 59, 999))
          });
        }
        break;
        
      case 'month':
        // Weekly for last 4-5 weeks
        const weeksToShow = 5;
        for (let i = weeksToShow - 1; i >= 0; i--) {
          const endDate = new Date(now);
          endDate.setDate(now.getDate() - (i * 7));
          const startDate = new Date(endDate);
          startDate.setDate(endDate.getDate() - 6);
          
          periods.push({
            label: `Week ${weeksToShow - i}: ${startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`,
            startDate: new Date(startDate.setHours(0, 0, 0, 0)),
            endDate: new Date(endDate.setHours(23, 59, 59, 999))
          });
        }
        break;
        
      case 'quarter':
        // Monthly for last 3 months
        for (let i = 2; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(1); // First day of current month
          date.setMonth(now.getMonth() - i);
          
          const endDate = new Date(date);
          endDate.setMonth(date.getMonth() + 1);
          endDate.setDate(0); // Last day of month
          
          periods.push({
            label: date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
            startDate: new Date(date.setHours(0, 0, 0, 0)),
            endDate: new Date(endDate.setHours(23, 59, 59, 999))
          });
        }
        break;
        
      case 'year':
        // Quarterly for last 4 quarters
        const currentQuarter = Math.floor(now.getMonth() / 3);
        const currentYear = now.getFullYear();
        
        for (let i = 3; i >= 0; i--) {
          let quarter = (currentQuarter - i) % 4;
          if (quarter < 0) quarter += 4;
          
          let year = currentYear;
          if (currentQuarter - i < 0) {
            year -= 1;
          }
          
          const startDate = new Date(year, quarter * 3, 1);
          const endDate = new Date(year, quarter * 3 + 3, 0);
          
          periods.push({
            label: `Q${quarter + 1} ${year}`,
            startDate: new Date(startDate.setHours(0, 0, 0, 0)),
            endDate: new Date(endDate.setHours(23, 59, 59, 999))
          });
        }
        break;
    }
    
    return periods;
  }
  
  /**
   * Group data by time period based on filter
   */
  groupDataByTimePeriod(data, filter, valueField = 'Freight Cost') {
    if (!data || data.length === 0) return [];
    
    const periods = this.generateTimePeriods(filter);
    const result = periods.map(period => {
      const periodData = data.filter(item => {
        const itemDate = this.parseDate(item.Date);
        return itemDate && itemDate >= period.startDate && itemDate <= period.endDate;
      });
      
      // Calculate sum of values
      const sum = periodData.reduce((total, item) => {
        const value = parseFloat(item[valueField]) || 0;
        return total + value;
      }, 0);
      
      return {
        period: period.label,
        value: sum,
        count: periodData.length
      };
    });
    
    return result;
  }
} 