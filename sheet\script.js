document.addEventListener('DOMContentLoaded', function() {
    // Configuration
    const ROWS = 100;
    const COLS = 26; // A to Z
    const spreadsheetData = {};
    let activeCell = null;
    let selectedRange = { start: null, end: null };
    let isSelecting = false;
    let isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

    // DOM Elements
    const spreadsheet = document.getElementById('spreadsheet');
    const columnHeaders = document.getElementById('column-headers');
    const rowHeaders = document.getElementById('row-headers');
    const cellReference = document.querySelector('.cell-reference');
    const formulaInput = document.querySelector('.formula-input');
    const spreadsheetContainer = document.querySelector('.spreadsheet-container');
    
    // Initialize the spreadsheet
    function initSpreadsheet() {
        // Create column headers (A, B, C, ...)
        for (let i = 0; i < COLS; i++) {
            const columnHeader = document.createElement('div');
            columnHeader.className = 'column-header';
            columnHeader.textContent = String.fromCharCode(65 + i); // A=65, B=66, etc.
            
            // Add column resize functionality
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle horizontal';
            resizeHandle.addEventListener('mousedown', handleColumnResize);
            columnHeader.appendChild(resizeHandle);
            
            columnHeaders.appendChild(columnHeader);
        }
        
        // Create row headers (1, 2, 3, ...)
        for (let i = 0; i < ROWS; i++) {
            const rowHeader = document.createElement('div');
            rowHeader.className = 'row-header';
            rowHeader.textContent = i + 1;
            
            // Add row resize functionality
            const resizeHandle = document.createElement('div');
            resizeHandle.className = 'resize-handle vertical';
            resizeHandle.addEventListener('mousedown', handleRowResize);
            rowHeader.appendChild(resizeHandle);
            
            rowHeaders.appendChild(rowHeader);
        }
        
        // Create the grid structure
        spreadsheet.style.gridTemplateColumns = `repeat(${COLS}, var(--cell-width))`;
        spreadsheet.style.gridTemplateRows = `repeat(${ROWS}, var(--cell-height))`;
        
        // Create cells
        for (let row = 0; row < ROWS; row++) {
            for (let col = 0; col < COLS; col++) {
                const cell = document.createElement('div');
                cell.className = 'cell';
                cell.contentEditable = 'true';
                cell.dataset.row = row;
                cell.dataset.col = col;
                cell.dataset.cellId = getCellId(row, col);
                
                cell.addEventListener('click', handleCellClick);
                cell.addEventListener('input', handleCellInput);
                cell.addEventListener('keydown', handleCellKeyDown);
                cell.addEventListener('blur', handleCellBlur);
                cell.addEventListener('focus', handleCellFocus);
                cell.addEventListener('mousedown', handleCellMouseDown);
                
                spreadsheet.appendChild(cell);
            }
        }
        
        // Add spreadsheet container event listeners for selection
        spreadsheetContainer.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        
        // Add context menu
        setupContextMenu();
        
        // Add toolbar button functionality
        setupToolbar();
        
        // Add theme toggle
        setupThemeToggle();
    }
    
    // Handle column resize
    function handleColumnResize(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const columnHeader = e.target.parentElement;
        const columnIndex = Array.from(columnHeader.parentElement.children).indexOf(columnHeader);
        const startX = e.clientX;
        const startWidth = columnHeader.offsetWidth;
        
        function onMouseMove(moveEvent) {
            const newWidth = startWidth + (moveEvent.clientX - startX);
            if (newWidth >= 40) { // Minimum width
                document.documentElement.style.setProperty('--cell-width', `${newWidth}px`);
                // Update all cells in this column
                const cells = document.querySelectorAll(`.cell[data-col="${columnIndex}"]`);
                cells.forEach(cell => {
                    cell.style.width = `${newWidth}px`;
                });
            }
        }
        
        function onMouseUp() {
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    }
    
    // Handle row resize
    function handleRowResize(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const rowHeader = e.target.parentElement;
        const rowIndex = Array.from(rowHeader.parentElement.children).indexOf(rowHeader);
        const startY = e.clientY;
        const startHeight = rowHeader.offsetHeight;
        
        function onMouseMove(moveEvent) {
            const newHeight = startHeight + (moveEvent.clientY - startY);
            if (newHeight >= 20) { // Minimum height
                document.documentElement.style.setProperty('--cell-height', `${newHeight}px`);
                // Update all cells in this row
                const cells = document.querySelectorAll(`.cell[data-row="${rowIndex}"]`);
                cells.forEach(cell => {
                    cell.style.height = `${newHeight}px`;
                });
            }
        }
        
        function onMouseUp() {
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
        
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    }
    
    // Helper function to get cell ID (e.g., "A1", "B2") from row and column
    function getCellId(row, col) {
        const colLabel = String.fromCharCode(65 + col);
        const rowLabel = row + 1;
        return `${colLabel}${rowLabel}`;
    }
    
    // Helper function to parse cell ID into row and column indices
    function parseCellId(cellId) {
        const colLabel = cellId.match(/[A-Z]+/)[0];
        const rowLabel = cellId.match(/[0-9]+/)[0];
        
        const col = colLabel.charCodeAt(0) - 65; // Convert A->0, B->1, etc.
        const row = parseInt(rowLabel) - 1; // Convert 1->0, 2->1, etc.
        
        return { row, col };
    }
    
    // Helper function to get cell element by cell ID
    function getCellElement(cellId) {
        return document.querySelector(`.cell[data-cell-id="${cellId}"]`);
    }
    
    // Handle cell mouse down (for selection)
    function handleCellMouseDown(event) {
        // Start selection
        isSelecting = true;
        const cell = event.target;
        selectedRange.start = cell.dataset.cellId;
        selectedRange.end = cell.dataset.cellId;
        
        // Clear previous selection
        clearSelection();
        
        // Set this cell as active
        if (activeCell) {
            activeCell.classList.remove('active');
        }
        
        cell.classList.add('active');
        activeCell = cell;
        
        const cellId = cell.dataset.cellId;
        cellReference.textContent = cellId;
        
        // Update formula input
        const cellData = spreadsheetData[cellId] || {};
        formulaInput.value = cellData.formula || cell.textContent;
    }
    
    // Handle mouse move (for selection)
    function handleMouseMove(event) {
        if (!isSelecting || !activeCell) return;
        
        // Get the cell element under the mouse
        const elementUnderMouse = document.elementFromPoint(event.clientX, event.clientY);
        
        if (elementUnderMouse && elementUnderMouse.classList.contains('cell')) {
            // Update the selection range
            selectedRange.end = elementUnderMouse.dataset.cellId;
            
            // Update the visual selection
            updateSelection();
            
            // Update the cell reference
            updateCellReferenceForRange();
        }
    }
    
    // Handle mouse up (end selection)
    function handleMouseUp() {
        isSelecting = false;
    }
    
    // Update the visual selection
    function updateSelection() {
        if (!selectedRange.start || !selectedRange.end) return;
        
        // Clear previous selection
        clearSelection();
        
        // Get start and end cell coordinates
        const start = parseCellId(selectedRange.start);
        const end = parseCellId(selectedRange.end);
        
        // Determine the rectangular selection area
        const minRow = Math.min(start.row, end.row);
        const maxRow = Math.max(start.row, end.row);
        const minCol = Math.min(start.col, end.col);
        const maxCol = Math.max(start.col, end.col);
        
        // Highlight all cells in the selection
        for (let row = minRow; row <= maxRow; row++) {
            for (let col = minCol; col <= maxCol; col++) {
                const cellId = getCellId(row, col);
                const cell = getCellElement(cellId);
                if (cell && cell !== activeCell) {
                    cell.classList.add('selected');
                }
            }
        }
    }
    
    // Clear all selected cells
    function clearSelection() {
        document.querySelectorAll('.cell.selected').forEach(cell => {
            cell.classList.remove('selected');
        });
    }
    
    // Update cell reference for range
    function updateCellReferenceForRange() {
        if (selectedRange.start === selectedRange.end) {
            cellReference.textContent = selectedRange.start;
        } else {
            cellReference.textContent = `${selectedRange.start}:${selectedRange.end}`;
        }
    }
    
    // Handle cell click event
    function handleCellClick(event) {
        // This is now handled by handleCellMouseDown and handleMouseUp
        // for better support of selection ranges
    }
    
    // Handle cell focus
    function handleCellFocus(event) {
        const cell = event.target;
        // Highlight any formula references
        if (cell.textContent.startsWith('=')) {
            highlightReferencedCells(cell.textContent);
        }
    }
    
    // Highlight cells referenced in a formula
    function highlightReferencedCells(formula) {
        // Clear previous highlights
        document.querySelectorAll('.cell.referenced').forEach(cell => {
            cell.classList.remove('referenced');
        });
        
        // Find cell references in the formula
        const cellRefPattern = /[A-Z][0-9]+/g;
        const cellRefs = formula.match(cellRefPattern) || [];
        
        // Highlight each referenced cell
        cellRefs.forEach(cellId => {
            const cell = getCellElement(cellId);
            if (cell) {
                cell.classList.add('referenced');
            }
        });
    }
    
    // Handle cell input event
    function handleCellInput(event) {
        const cell = event.target;
        const cellId = cell.dataset.cellId;
        
        // Store the new value
        if (!spreadsheetData[cellId]) {
            spreadsheetData[cellId] = {};
        }
        
        spreadsheetData[cellId].value = cell.textContent;
        formulaInput.value = cell.textContent;
        
        // If this is a formula, highlight referenced cells
        if (cell.textContent.startsWith('=')) {
            highlightReferencedCells(cell.textContent);
        }
        
        // Update cells that reference this one
        updateDependentCells(cellId);
    }
    
    // Update cells that depend on the changed cell
    function updateDependentCells(changedCellId) {
        // For each cell in the spreadsheet
        Object.keys(spreadsheetData).forEach(cellId => {
            const cellData = spreadsheetData[cellId];
            
            // If it has a formula that references the changed cell
            if (cellData.formula && cellData.formula.includes(changedCellId)) {
                const cell = getCellElement(cellId);
                if (cell) {
                    // Re-evaluate the formula
                    try {
                        const result = evaluateFormula(cellData.formula.substring(1));
                        cell.textContent = result;
                        cellData.value = result;
                    } catch (error) {
                        cell.textContent = '#ERROR!';
                        cellData.value = '#ERROR!';
                    }
                }
            }
        });
    }
    
    // Handle cell keydown event
    function handleCellKeyDown(event) {
        if (!activeCell) return;
        
        const currentRow = parseInt(activeCell.dataset.row);
        const currentCol = parseInt(activeCell.dataset.col);
        
        switch (event.key) {
            case 'ArrowUp':
                if (currentRow > 0) navigateToCell(currentRow - 1, currentCol);
                event.preventDefault();
                break;
            case 'ArrowDown':
                if (currentRow < ROWS - 1) navigateToCell(currentRow + 1, currentCol);
                event.preventDefault();
                break;
            case 'ArrowLeft':
                if (currentCol > 0) navigateToCell(currentRow, currentCol - 1);
                event.preventDefault();
                break;
            case 'ArrowRight':
                if (currentCol < COLS - 1) navigateToCell(currentRow, currentCol + 1);
                event.preventDefault();
                break;
            case 'Tab':
                if (currentCol < COLS - 1) {
                    navigateToCell(currentRow, currentCol + 1);
                } else if (currentRow < ROWS - 1) {
                    navigateToCell(currentRow + 1, 0);
                }
                event.preventDefault();
                break;
            case 'Enter':
                if (currentRow < ROWS - 1) {
                    navigateToCell(currentRow + 1, currentCol);
                }
                event.preventDefault();
                break;
            case 'Escape':
                activeCell.blur();
                break;
            case 'Delete':
            case 'Backspace':
                if (event.ctrlKey || !activeCell.textContent || window.getSelection().toString()) {
                    // If Ctrl+Delete or cell is empty or text is selected, don't handle specially
                    break;
                }
                // Otherwise clear the contents of all selected cells
                clearSelectedCells();
                event.preventDefault();
                break;
            default:
                // Only for printable characters and not modifier key combinations
                if (event.key.length === 1 && !event.ctrlKey && !event.altKey && !event.metaKey) {
                    // If multiple cells are selected, clear them first when user begins typing
                    if (document.querySelectorAll('.cell.selected').length > 0) {
                        clearSelectedCells();
                    }
                }
                break;
        }
    }
    
    // Clear the contents of selected cells
    function clearSelectedCells() {
        // Clear active cell
        if (activeCell) {
            activeCell.textContent = '';
            const activeCellId = activeCell.dataset.cellId;
            if (spreadsheetData[activeCellId]) {
                spreadsheetData[activeCellId].value = '';
                spreadsheetData[activeCellId].formula = '';
            }
        }
        
        // Clear all selected cells
        document.querySelectorAll('.cell.selected').forEach(cell => {
            cell.textContent = '';
            const cellId = cell.dataset.cellId;
            if (spreadsheetData[cellId]) {
                spreadsheetData[cellId].value = '';
                spreadsheetData[cellId].formula = '';
            }
        });
    }

    // Navigate to a specific cell
    function navigateToCell(row, col) {
        const cellId = getCellId(row, col);
        const cell = getCellElement(cellId);
        
        if (cell) {
            // Clear selection
            clearSelection();
            
            if (activeCell) {
                activeCell.classList.remove('active');
            }
            
            cell.classList.add('active');
            activeCell = cell;
            cellReference.textContent = cellId;
            
            // Update selected range
            selectedRange.start = cellId;
            selectedRange.end = cellId;
            
            // Update formula input
            const cellData = spreadsheetData[cellId] || {};
            formulaInput.value = cellData.formula || cell.textContent;
            
            // Scroll cell into view if needed
            scrollCellIntoView(cell);
            
            // Focus on the cell
            cell.focus();
        }
    }
    
    // Scroll cell into view
    function scrollCellIntoView(cell) {
        const container = spreadsheetContainer;
        const cellRect = cell.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        // Check if cell is out of view horizontally
        if (cellRect.right > containerRect.right) {
            container.scrollLeft += cellRect.right - containerRect.right + 20;
        } else if (cellRect.left < containerRect.left) {
            container.scrollLeft -= containerRect.left - cellRect.left + 20;
        }
        
        // Check if cell is out of view vertically
        if (cellRect.bottom > containerRect.bottom) {
            container.scrollTop += cellRect.bottom - containerRect.bottom + 20;
        } else if (cellRect.top < containerRect.top) {
            container.scrollTop -= containerRect.top - cellRect.top + 20;
        }
    }
    
    // Handle cell blur event
    function handleCellBlur(event) {
        // Clear referenced cells highlighting
        document.querySelectorAll('.cell.referenced').forEach(cell => {
            cell.classList.remove('referenced');
        });
    }
    
    // Handle formula input changes
    formulaInput.addEventListener('input', function(event) {
        if (!activeCell) return;
        
        const formula = event.target.value;
        const cellId = activeCell.dataset.cellId;
        
        // Store the formula
        if (!spreadsheetData[cellId]) {
            spreadsheetData[cellId] = {};
        }
        
        spreadsheetData[cellId].formula = formula;
        
        // If formula starts with '=', parse it as a formula
        if (formula.startsWith('=')) {
            try {
                // Update referenced cells highlighting
                highlightReferencedCells(formula);
                
                // Simple formula parsing (for demo)
                // For a real spreadsheet, you'd need a more robust formula parser
                const result = evaluateFormula(formula.substring(1));
                activeCell.textContent = result;
                spreadsheetData[cellId].value = result;
            } catch (error) {
                activeCell.textContent = '#ERROR!';
                spreadsheetData[cellId].value = '#ERROR!';
            }
        } else {
            // Not a formula, just set the text directly
            activeCell.textContent = formula;
            spreadsheetData[cellId].value = formula;
        }
    });
    
    // Evaluate a simple formula
    function evaluateFormula(formula) {
        // This is a very basic implementation
        // In a real spreadsheet, you'd need a much more robust formula parser
        
        // Replace cell references with their values
        const cellRefPattern = /[A-Z][0-9]+/g;
        const formulaWithValues = formula.replace(cellRefPattern, function(cellId) {
            const cell = spreadsheetData[cellId];
            return cell && cell.value ? cell.value : '0';
        });
        
        // Use Function constructor to evaluate the formula
        // Note: This approach is not secure for untrusted input
        try {
            return Function(`"use strict"; return (${formulaWithValues})`)();
        } catch (error) {
            throw new Error('Invalid formula');
        }
    }
    
    // Handle key events on formula input
    formulaInput.addEventListener('keydown', function(event) {
        if (event.key === 'Enter' && activeCell) {
            activeCell.focus();
            event.preventDefault();
        }
    });
    
    // Setup context menu
    function setupContextMenu() {
        const contextMenu = document.createElement('div');
        contextMenu.className = 'context-menu';
        contextMenu.innerHTML = `
            <div class="context-menu-item" data-action="cut"><i class="fas fa-cut"></i> Cut</div>
            <div class="context-menu-item" data-action="copy"><i class="fas fa-copy"></i> Copy</div>
            <div class="context-menu-item" data-action="paste"><i class="fas fa-paste"></i> Paste</div>
            <div class="context-menu-divider"></div>
            <div class="context-menu-item" data-action="insert-row"><i class="fas fa-plus"></i> Insert Row</div>
            <div class="context-menu-item" data-action="insert-column"><i class="fas fa-plus"></i> Insert Column</div>
            <div class="context-menu-item" data-action="delete-row"><i class="fas fa-minus"></i> Delete Row</div>
            <div class="context-menu-item" data-action="delete-column"><i class="fas fa-minus"></i> Delete Column</div>
            <div class="context-menu-divider"></div>
            <div class="context-menu-item" data-action="clear"><i class="fas fa-eraser"></i> Clear Contents</div>
        `;
        document.body.appendChild(contextMenu);
        
        // Context menu event listeners
        spreadsheetContainer.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            
            const target = e.target;
            if (target.classList.contains('cell') || target.classList.contains('row-header') || target.classList.contains('column-header')) {
                // Position the context menu
                contextMenu.style.left = `${e.pageX}px`;
                contextMenu.style.top = `${e.pageY}px`;
                contextMenu.style.display = 'block';
                
                // Store the target for use in action handlers
                contextMenu.dataset.targetCellId = target.dataset.cellId || '';
                contextMenu.dataset.targetType = target.classList.contains('cell') ? 'cell' : 
                                                target.classList.contains('row-header') ? 'row' : 'column';
                
                // Disable inappropriate actions based on context
                Array.from(contextMenu.querySelectorAll('.context-menu-item')).forEach(item => {
                    const action = item.dataset.action;
                    if (action === 'cut' || action === 'copy' || action === 'paste' || action === 'clear') {
                        item.classList.toggle('disabled', contextMenu.dataset.targetType !== 'cell');
                    }
                });
            }
        });
        
        // Hide context menu when clicking elsewhere
        document.addEventListener('click', function() {
            contextMenu.style.display = 'none';
        });
        
        // Hide context menu on scroll
        spreadsheetContainer.addEventListener('scroll', function() {
            contextMenu.style.display = 'none';
        });
        
        // Handle context menu actions
        contextMenu.addEventListener('click', function(e) {
            const action = e.target.closest('.context-menu-item')?.dataset.action;
            if (!action || e.target.closest('.disabled')) return;
            
            const targetType = contextMenu.dataset.targetType;
            const targetCellId = contextMenu.dataset.targetCellId;
            
            switch (action) {
                case 'cut':
                case 'copy':
                case 'paste':
                case 'clear':
                    if (targetType === 'cell') {
                        // These actions would be implemented in a real spreadsheet
                        if (action === 'clear') {
                            clearSelectedCells();
                        }
                    }
                    break;
                case 'insert-row':
                case 'delete-row':
                case 'insert-column':
                case 'delete-column':
                    // These actions would be implemented in a real spreadsheet
                    break;
            }
            
            contextMenu.style.display = 'none';
        });
    }
    
    // Setup toolbar buttons
    function setupToolbar() {
        // Add toolbar button event listeners
        document.querySelectorAll('.toolbar-btn').forEach(button => {
            button.addEventListener('click', function() {
                const action = this.dataset.action;
                if (!action) return;
                
                // Toggle active state for formatting buttons
                if (['bold', 'italic', 'underline', 'strikethrough', 'left', 'center', 'right'].includes(action)) {
                    this.classList.toggle('active');
                }
                
                // Apply formatting to selected cells
                if (activeCell) {
                    applyFormatting(action);
                }
            });
        });
        
        // Format select event listener
        const formatSelect = document.querySelector('.format-select');
        if (formatSelect) {
            formatSelect.addEventListener('change', function() {
                if (activeCell) {
                    applyFormatting('format', this.value);
                }
            });
        }
    }
    
    // Apply formatting to selected cells
    function applyFormatting(action, value) {
        // This is a simplified implementation
        // In a real spreadsheet, you'd store these formats in the cell data
        const cells = [activeCell];
        document.querySelectorAll('.cell.selected').forEach(cell => cells.push(cell));
        
        cells.forEach(cell => {
            if (!cell) return;
            
            switch (action) {
                case 'bold':
                    cell.style.fontWeight = cell.style.fontWeight === 'bold' ? 'normal' : 'bold';
                    break;
                case 'italic':
                    cell.style.fontStyle = cell.style.fontStyle === 'italic' ? 'normal' : 'italic';
                    break;
                case 'underline':
                    cell.style.textDecoration = cell.style.textDecoration === 'underline' ? 'none' : 'underline';
                    break;
                case 'strikethrough':
                    cell.style.textDecoration = cell.style.textDecoration === 'line-through' ? 'none' : 'line-through';
                    break;
                case 'left':
                    cell.style.textAlign = 'left';
                    break;
                case 'center':
                    cell.style.textAlign = 'center';
                    break;
                case 'right':
                    cell.style.textAlign = 'right';
                    break;
                case 'format':
                    // Apply text formatting based on selected format
                    switch (value) {
                        case 'Title':
                            cell.style.fontSize = '16px';
                            cell.style.fontWeight = 'bold';
                            break;
                        case 'Subtitle':
                            cell.style.fontSize = '14px';
                            cell.style.fontWeight = 'bold';
                            cell.style.color = '#666';
                            break;
                        case 'Heading 1':
                            cell.style.fontSize = '18px';
                            cell.style.fontWeight = 'bold';
                            break;
                        case 'Heading 2':
                            cell.style.fontSize = '16px';
                            cell.style.fontWeight = 'bold';
                            break;
                        default: // Normal text
                            cell.style.fontSize = '';
                            cell.style.fontWeight = '';
                            cell.style.color = '';
                            break;
                    }
                    break;
            }
        });
    }
    
    // Setup theme toggle
    function setupThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.className = 'theme-toggle';
        themeToggle.innerHTML = isDarkMode ? 
            '<i class="fas fa-sun"></i>' : 
            '<i class="fas fa-moon"></i>';
        themeToggle.setAttribute('data-tooltip', isDarkMode ? 'Switch to light mode' : 'Switch to dark mode');
        
        // Add to the footer
        const footerRight = document.querySelector('.footer-right');
        if (footerRight) {
            footerRight.prepend(themeToggle);
        }
        
        // Toggle theme on click
        themeToggle.addEventListener('click', function() {
            isDarkMode = !isDarkMode;
            
            // Update button icon
            this.innerHTML = isDarkMode ? 
                '<i class="fas fa-sun"></i>' : 
                '<i class="fas fa-moon"></i>';
            this.setAttribute('data-tooltip', isDarkMode ? 'Switch to light mode' : 'Switch to dark mode');
            
            // Toggle dark mode class on the body
            document.body.classList.toggle('dark-mode', isDarkMode);
        });
    }
    
    // Handle tabs
    document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', function() {
            if (this.classList.contains('add-sheet')) {
                // Add new sheet functionality would go here
                return;
            }
            
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // Initialize the spreadsheet
    initSpreadsheet();
    
    // Set initial active cell
    navigateToCell(0, 0);
    
    // ==========================================
    // IndexedDB Integration
    // ==========================================
    
    // DOM Elements for DB Import
    const dbImportBtn = document.getElementById('db-import-btn');
    const dbImportModal = document.getElementById('db-import-modal');
    const dbNameSelect = document.getElementById('db-name');
    const storeNameSelect = document.getElementById('store-name');
    const importBtn = document.getElementById('import-btn');
    const closeBtn = document.querySelector('.close-btn');
    const cancelBtn = document.querySelector('.cancel-btn');
    const previewTable = document.querySelector('.preview-table');
    const noPreview = document.querySelector('.no-preview');
    
    // IndexedDB variables
    let currentDB = null;
    let currentObjectStore = null;
    let previewData = [];
    
    // Show the modal when the import button is clicked
    dbImportBtn.addEventListener('click', function() {
        dbImportModal.classList.add('show');
        populateDatabaseList();
    });
    
    // Close the modal
    function closeModal() {
        dbImportModal.classList.remove('show');
    }
    
    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);
    
    // Close modal when clicking outside content
    dbImportModal.addEventListener('click', function(event) {
        if (event.target === dbImportModal) {
            closeModal();
        }
    });
    
    // Get list of all available IndexedDB databases
    async function populateDatabaseList() {
        try {
            // Show loading state
            dbNameSelect.innerHTML = '<option value="">Loading databases...</option>';
            dbNameSelect.disabled = true;
            storeNameSelect.innerHTML = '<option value="">Select a store...</option>';
            storeNameSelect.disabled = true;
            importBtn.disabled = true;
            
            // Get all database names
            const databases = await indexedDB.databases();
            
            // Clear loading state
            dbNameSelect.innerHTML = '<option value="">Select a database...</option>';
            
            if (databases.length === 0) {
                const option = document.createElement('option');
                option.value = "";
                option.textContent = "No IndexedDB databases found";
                dbNameSelect.appendChild(option);
                dbNameSelect.disabled = true;
            } else {
                dbNameSelect.disabled = false;
                
                // Add each database to select, sorted alphabetically
                databases
                    .sort((a, b) => a.name.localeCompare(b.name))
                    .forEach(db => {
                        const option = document.createElement('option');
                        option.value = db.name;
                        option.textContent = db.name;
                        dbNameSelect.appendChild(option);
                    });
            }
        } catch (error) {
            console.error('Error getting database list:', error);
            dbNameSelect.innerHTML = '<option value="">Error loading databases</option>';
            
            // Show error notification
            showNotification('Failed to access IndexedDB. Make sure your browser supports it.', 'error');
        }
    }
    
    // When database is selected, populate object stores
    dbNameSelect.addEventListener('change', function() {
        const dbName = this.value;
        
        // Reset preview and store select
        storeNameSelect.innerHTML = '<option value="">Select a store...</option>';
        storeNameSelect.disabled = true;
        previewTable.style.display = 'none';
        noPreview.style.display = 'block';
        noPreview.textContent = 'Select a database and store to preview data';
        importBtn.disabled = true;
        
        if (!dbName) return;
        
        // Show loading state
        storeNameSelect.innerHTML = '<option value="">Loading stores...</option>';
        
        // Open the database to get its object stores
        const request = indexedDB.open(dbName);
        
        request.onerror = function(event) {
            console.error('Error opening database:', event.target.error);
            storeNameSelect.innerHTML = '<option value="">Error loading stores</option>';
            
            // Show error notification
            showNotification(`Failed to open database: ${event.target.error.message}`, 'error');
        };
        
        request.onsuccess = function(event) {
            currentDB = event.target.result;
            populateObjectStores(currentDB);
        };
    });
    
    // Populate object stores select
    function populateObjectStores(db) {
        storeNameSelect.innerHTML = '<option value="">Select a store...</option>';
        
        const objectStoreNames = Array.from(db.objectStoreNames);
        
        if (objectStoreNames.length === 0) {
            const option = document.createElement('option');
            option.value = "";
            option.textContent = "No object stores found in this database";
            storeNameSelect.appendChild(option);
            storeNameSelect.disabled = true;
        } else {
            storeNameSelect.disabled = false;
            
            // Add each object store to select, sorted alphabetically
            objectStoreNames
                .sort((a, b) => a.localeCompare(b))
                .forEach(storeName => {
                    const option = document.createElement('option');
                    option.value = storeName;
                    option.textContent = storeName;
                    storeNameSelect.appendChild(option);
                });
        }
    }
    
    // When object store is selected, show preview
    storeNameSelect.addEventListener('change', function() {
        const storeName = this.value;
        
        if (!storeName || !currentDB) {
            importBtn.disabled = true;
            previewTable.style.display = 'none';
            noPreview.style.display = 'block';
            noPreview.textContent = 'Select a store to preview data';
            return;
        }
        
        // Show loading state
        noPreview.textContent = 'Loading preview...';
        noPreview.style.display = 'block';
        previewTable.style.display = 'none';
        
        // Get preview data from the object store
        currentObjectStore = storeName;
        getPreviewData(currentDB, storeName).then(() => {
            importBtn.disabled = false;
        }).catch(error => {
            console.error('Error getting preview data:', error);
            noPreview.textContent = 'Error loading preview data';
            importBtn.disabled = true;
        });
    });
    
    // Get preview data from an object store
    async function getPreviewData(db, storeName, limit = 5) {
        return new Promise((resolve, reject) => {
            const transaction = db.transaction(storeName, 'readonly');
            const objectStore = transaction.objectStore(storeName);
            
            // Get count first to show a better message if empty
            const countRequest = objectStore.count();
            
            countRequest.onsuccess = function() {
                if (countRequest.result === 0) {
                    noPreview.textContent = 'This object store is empty';
                    noPreview.style.display = 'block';
                    previewTable.style.display = 'none';
                    resolve([]);
                    return;
                }
                
                // Now get the actual preview data
                const request = objectStore.openCursor();
                previewData = [];
                let count = 0;
                
                request.onsuccess = function(event) {
                    const cursor = event.target.result;
                    
                    if (cursor && count < limit) {
                        previewData.push(cursor.value);
                        count++;
                        cursor.continue();
                    } else {
                        // Get total count for display
                        const countRequest = objectStore.count();
                        countRequest.onsuccess = function() {
                            const totalCount = countRequest.result;
                            
                            // Render preview table
                            renderPreviewTable(previewData, totalCount);
                            resolve(previewData);
                        };
                        
                        countRequest.onerror = function(event) {
                            reject(event.target.error);
                        };
                    }
                };
                
                request.onerror = function(event) {
                    reject(event.target.error);
                };
            };
            
            countRequest.onerror = function(event) {
                reject(event.target.error);
            };
        });
    }
    
    // Render preview table with data
    function renderPreviewTable(data, totalCount) {
        if (!data || data.length === 0) {
            previewTable.style.display = 'none';
            noPreview.style.display = 'block';
            noPreview.textContent = 'No data found in this object store';
            return;
        }
        
        previewTable.innerHTML = '';
        noPreview.style.display = 'none';
        previewTable.style.display = 'table';
        
        // Create table header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        // Get all possible keys from all objects
        const allKeys = new Set();
        data.forEach(item => {
            if (typeof item === 'object' && item !== null) {
                Object.keys(item).forEach(key => allKeys.add(key));
            }
        });
        
        // Create header cells
        allKeys.forEach(key => {
            const th = document.createElement('th');
            th.textContent = key;
            headerRow.appendChild(th);
        });
        
        // Add extra column if data isn't an object
        if (allKeys.size === 0 && data.length > 0) {
            const th = document.createElement('th');
            th.textContent = 'Value';
            headerRow.appendChild(th);
        }
        
        thead.appendChild(headerRow);
        previewTable.appendChild(thead);
        
        // Create table body
        const tbody = document.createElement('tbody');
        
        // Add data rows
        data.forEach(item => {
            const row = document.createElement('tr');
            
            if (allKeys.size > 0) {
                // Handle object data
                allKeys.forEach(key => {
                    const td = document.createElement('td');
                    
                    // Format the value based on its type
                    const value = item[key];
                    formatTableCell(td, value);
                    
                    row.appendChild(td);
                });
            } else {
                // Handle primitive data
                const td = document.createElement('td');
                formatTableCell(td, item);
                row.appendChild(td);
            }
            
            tbody.appendChild(row);
        });
        
        previewTable.appendChild(tbody);
        
        // Add preview footer if there are more records
        if (totalCount > data.length) {
            const tfoot = document.createElement('tfoot');
            const footerRow = document.createElement('tr');
            const footerCell = document.createElement('td');
            footerCell.colSpan = allKeys.size || 1;
            footerCell.className = 'preview-footer';
            footerCell.textContent = `Showing ${data.length} of ${totalCount} records`;
            footerRow.appendChild(footerCell);
            tfoot.appendChild(footerRow);
            previewTable.appendChild(tfoot);
        }
    }
    
    // Format table cell based on data type
    function formatTableCell(td, value) {
        if (value === undefined || value === null) {
            td.innerHTML = '<span class="null-value">null</span>';
        } else if (typeof value === 'object') {
            if (Array.isArray(value)) {
                td.innerHTML = `<span class="array-value">[Array: ${value.length} items]</span>`;
                td.title = JSON.stringify(value);
            } else {
                const displayValue = JSON.stringify(value).substring(0, 100);
                td.innerHTML = `<span class="object-value">${displayValue}${displayValue.length >= 100 ? '...' : ''}</span>`;
                td.title = JSON.stringify(value);
            }
        } else if (typeof value === 'boolean') {
            td.innerHTML = `<span class="boolean-value">${value}</span>`;
        } else if (typeof value === 'number') {
            td.innerHTML = `<span class="number-value">${value}</span>`;
        } else {
            td.textContent = String(value);
        }
    }
    
    // Handle import button click
    importBtn.addEventListener('click', async function() {
        if (!currentDB || !currentObjectStore) {
            showNotification('Please select a database and object store first.', 'warning');
            return;
        }
        
        const includeHeaders = document.getElementById('headers-included').checked;
        const clearBeforeImport = document.getElementById('clear-data').checked;
        
        try {
            // Show loading indicator
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Importing...';
            this.disabled = true;
            
            // Get all data from the object store
            const allData = await getAllData(currentDB, currentObjectStore);
            
            // Import the data into the spreadsheet
            importDataToSpreadsheet(allData, includeHeaders, clearBeforeImport);
            
            // Close the modal
            closeModal();
            
            // Show success notification
            showNotification(`Successfully imported ${allData.length} records from "${currentObjectStore}"`, 'success');
        } catch (error) {
            console.error('Error importing data:', error);
            showNotification(`Failed to import data: ${error.message}`, 'error');
        } finally {
            // Reset button state
            this.innerHTML = 'Import Data';
            this.disabled = false;
        }
    });
    
    // Get all data from an object store
    function getAllData(db, storeName) {
        return new Promise((resolve, reject) => {
            const transaction = db.transaction(storeName, 'readonly');
            const objectStore = transaction.objectStore(storeName);
            const request = objectStore.getAll();
            
            request.onsuccess = function(event) {
                resolve(event.target.result);
            };
            
            request.onerror = function(event) {
                reject(event.target.error);
            };
        });
    }
    
    // Import data to spreadsheet
    function importDataToSpreadsheet(data, includeHeaders, clearBeforeImport) {
        if (!data || data.length === 0) {
            return;
        }
        
        // Clear current data if needed
        if (clearBeforeImport) {
            clearSpreadsheet();
        }
        
        // Determine if data is array of objects or primitive values
        const isObjectData = data.length > 0 && typeof data[0] === 'object' && data[0] !== null;
        
        if (isObjectData) {
            // Import object data (each object becomes a row)
            importObjectData(data, includeHeaders);
        } else {
            // Import primitive data (each value becomes a cell in column A)
            importPrimitiveData(data, includeHeaders);
        }
        
        // Navigate to cell A1 to show the imported data
        navigateToCell(0, 0);
    }
    
    // Import data where each item is an object (row)
    function importObjectData(data, includeHeaders) {
        // Get all possible keys from all objects
        const allKeys = new Set();
        data.forEach(item => {
            if (typeof item === 'object' && item !== null) {
                Object.keys(item).forEach(key => allKeys.add(key));
            }
        });
        
        const keysArray = Array.from(allKeys);
        let startRow = 0;
        
        // Add headers if needed
        if (includeHeaders) {
            keysArray.forEach((key, index) => {
                if (index >= COLS) return; // Skip if we run out of columns
                
                const cellId = getCellId(0, index);
                const cell = getCellElement(cellId);
                
                if (cell) {
                    cell.textContent = key;
                    
                    // Store the value
                    if (!spreadsheetData[cellId]) {
                        spreadsheetData[cellId] = {};
                    }
                    spreadsheetData[cellId].value = key;
                }
            });
            
            startRow = 1; // Start data on row 2
        }
        
        // Add data rows
        data.forEach((item, rowIndex) => {
            if (startRow + rowIndex >= ROWS) return; // Skip if we run out of rows
            
            keysArray.forEach((key, colIndex) => {
                if (colIndex >= COLS) return; // Skip if we run out of columns
                
                const cellId = getCellId(startRow + rowIndex, colIndex);
                const cell = getCellElement(cellId);
                
                if (cell) {
                    const value = item[key];
                    let displayValue = '';
                    
                    // Format the value based on its type
                    if (value !== undefined && value !== null) {
                        if (typeof value === 'object') {
                            displayValue = JSON.stringify(value);
                        } else {
                            displayValue = String(value);
                        }
                    }
                    
                    cell.textContent = displayValue;
                    
                    // Store the value
                    if (!spreadsheetData[cellId]) {
                        spreadsheetData[cellId] = {};
                    }
                    spreadsheetData[cellId].value = displayValue;
                }
            });
        });
    }
    
    // Import data where each item is a primitive value (row in column A)
    function importPrimitiveData(data, includeHeaders) {
        let startRow = 0;
        
        // Add header if needed
        if (includeHeaders) {
            const cellId = getCellId(0, 0);
            const cell = getCellElement(cellId);
            
            if (cell) {
                cell.textContent = 'Value';
                
                // Store the value
                if (!spreadsheetData[cellId]) {
                    spreadsheetData[cellId] = {};
                }
                spreadsheetData[cellId].value = 'Value';
            }
            
            startRow = 1; // Start data on row 2
        }
        
        // Add data rows
        data.forEach((value, rowIndex) => {
            if (startRow + rowIndex >= ROWS) return; // Skip if we run out of rows
            
            const cellId = getCellId(startRow + rowIndex, 0);
            const cell = getCellElement(cellId);
            
            if (cell) {
                let displayValue = '';
                
                // Format the value based on its type
                if (value !== undefined && value !== null) {
                    if (typeof value === 'object') {
                        displayValue = JSON.stringify(value);
                    } else {
                        displayValue = String(value);
                    }
                }
                
                cell.textContent = displayValue;
                
                // Store the value
                if (!spreadsheetData[cellId]) {
                    spreadsheetData[cellId] = {};
                }
                spreadsheetData[cellId].value = displayValue;
            }
        });
    }
    
    // Clear the spreadsheet
    function clearSpreadsheet() {
        for (let row = 0; row < ROWS; row++) {
            for (let col = 0; col < COLS; col++) {
                const cellId = getCellId(row, col);
                const cell = getCellElement(cellId);
                
                if (cell) {
                    cell.textContent = '';
                    cell.style = ''; // Clear any styling
                    
                    // Clear stored data
                    if (spreadsheetData[cellId]) {
                        spreadsheetData[cellId].value = '';
                        spreadsheetData[cellId].formula = '';
                    }
                }
            }
        }
    }
    
    // Show a notification toast
    function showNotification(message, type = 'info') {
        // Create notification element if it doesn't exist
        let notification = document.querySelector('.notification');
        if (!notification) {
            notification = document.createElement('div');
            notification.className = 'notification';
            document.body.appendChild(notification);
        }
        
        // Set notification content and type
        notification.textContent = message;
        notification.className = `notification ${type}`;
        
        // Add the show class to trigger animation
        setTimeout(() => notification.classList.add('show'), 10);
        
        // Auto hide after a delay
        setTimeout(() => {
            notification.classList.remove('show');
            
            // Remove element after animation
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}); 