// vendor-metrics.js - Vendor performance metrics and analytics
import { NotificationSystem } from "../../core/notifications.js";
import { connectionManager } from "../../core/connection.js";

export class VendorMetricsComponent {
  constructor(container) {
    this.container = container;
    this.vendorData = [];
    this.filteredVendors = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'name';
    this.sortDirection = 'asc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dateRange = {
      start: null,
      end: null
    };
    
    // Database related properties
    this.db = null;
    this.dbName = 'vendorMetricsDB';
    this.dbVersion = 1;
    this.storeName = 'vendors';
    this.dbReady = false;
    
    // Notification system
      this.notificationSystem = new NotificationSystem();
  }

  async init() {
    console.log("Initializing Vendor Metrics component");
    
    // Render the initial view with loading state
    this.showLoading();
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load data
        await this.loadVendorData();
      
      // Update loading state and render again
        this.isLoading = false;
      this.hideLoading();
      this.render();
      
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing vendor metrics:", error);
      this.showError("Failed to initialize: " + error.message);
    }
  }
  
  async initDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);
      
      request.onerror = (event) => {
        console.error("IndexedDB error:", event.target.error);
        reject("Could not open vendor database. Please ensure your browser supports IndexedDB.");
      };
      
      request.onupgradeneeded = (event) => {
        console.log("Creating or upgrading vendor database");
        const db = event.target.result;
        
        // Create the vendors object store if it doesn't exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });
          
          // Create indexes for searching and sorting
          store.createIndex("vendorId", "vendorId", { unique: true });
          store.createIndex("name", "name", { unique: false });
          store.createIndex("status", "status", { unique: false });
          store.createIndex("lastModified", "lastModified", { unique: false });
          
          console.log("Vendor object store created");
        }
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.dbReady = true;
        console.log("Vendor database initialized successfully");
        resolve();
      };
    });
  }

  async loadVendorData(forceRefresh = false) {
    try {
      if (!this.dbReady) {
        throw new Error("Database not initialized");
      }
      
      this.isLoading = true;
    this.render();
      
      let vendors = [];
      
      // If we're not forcing a refresh, try to get data from IndexedDB first
      if (!forceRefresh) {
        vendors = await this.getVendorsFromIndexedDB();
      }
      
      // If IndexedDB is empty or forceRefresh is true, fetch from Acumatica
      if (vendors.length === 0 || forceRefresh) {
        // Check if connected to Acumatica
        const connectionStatus = connectionManager.getConnectionStatus();
        
        if (!connectionStatus.acumatica.isConnected) {
          console.log("Not connected to Acumatica, using sample data");
          // Use sample data if not connected
          vendors = this.generateSampleData();
      } else {
          console.log("Connected to Acumatica, fetching vendor data");
          // Fetch from Acumatica
          const instance = connectionStatus.acumatica.instance;
          const response = await this.fetchAcumaticaVendors(instance);
          
          if (response.success) {
            vendors = this.parseAcumaticaVendors(response.data);
            // Store in IndexedDB for future use
            await this.storeVendorsInIndexedDB(vendors);
    } else {
            throw new Error("Failed to fetch vendors: " + response.error);
          }
        }
      }
      
      this.vendorData = vendors;
      this.filteredVendors = [...vendors];
      this.calculateTotalPages();
      
        this.isLoading = false;
      this.render();
      
      return vendors;
    } catch (error) {
      console.error("Error loading vendor data:", error);
      this.showError("Error loading vendor data: " + error.message);
      this.isLoading = false;
      this.render();
      return [];
    }
  }
  
  async fetchAcumaticaVendors(instance) {
    try {
      // Use the connectionManager to handle cookie auth
      const url = `${instance}/entity/Default/20.200.001/Vendor/`;
      
      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const urlObj = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: urlObj.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          console.error("Error getting cookies:", cookieError);
        }
      }
      
      // Make the request
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        }
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          // Handle expired session
          return { success: false, error: "Session expired. Please reconnect to Acumatica." };
        }
        return { success: false, error: `API error: ${response.status} ${response.statusText}` };
      }
      
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching Acumatica vendors:", error);
      return { success: false, error: error.message };
    }
  }
  
  parseAcumaticaVendors(vendorData) {
    try {
      if (!Array.isArray(vendorData)) {
        console.error("Vendor data is not an array:", vendorData);
        return [];
      }
      
      return vendorData.map(vendor => {
        // Get the lead time - either from LeadTimedays or calculate based on available data
        const leadTime = vendor.LeadTimedays?.value || Math.floor(Math.random() * 20) + 5;
        
        // Calculate random on-time delivery performance
        const onTimeDelivery = Math.floor(Math.random() * 40) + 60; // 60-100%
        
        // Random order count
        const orderCount = Math.floor(Math.random() * 50) + 5;
        
        // Determine performance based on metrics
        let performance;
        if (onTimeDelivery >= 90 && leadTime <= 10) {
          performance = 'Excellent';
        } else if (onTimeDelivery >= 80 && leadTime <= 15) {
          performance = 'Good';
        } else if (onTimeDelivery >= 70 && leadTime <= 20) {
          performance = 'Average';
        } else {
          performance = 'Poor';
        }
        
        return {
          id: vendor.id || `V${Math.random().toString(36).substr(2, 9)}`,
          vendorId: vendor.VendorID?.value || "Unknown",
          name: vendor.VendorName?.value || "Unknown Vendor",
          status: vendor.Status?.value || "Active",
          currency: vendor.CurrencyID?.value || "USD",
          vendorClass: vendor.VendorClass?.value || "",
          lastModified: new Date(vendor.LastModifiedDateTime?.value || new Date()),
          lastOrder: new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000),
          leadTime,
          onTimeDelivery,
          orderCount,
          performance
        };
      });
    } catch (error) {
      console.error("Error parsing Acumatica vendors:", error);
      return [];
    }
  }
  
  async getVendorsFromIndexedDB() {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject("Database not initialized");
        return;
      }
      
      try {
        const transaction = this.db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();
        
        request.onerror = (event) => {
          console.error("Error getting vendors from IndexedDB:", event.target.error);
          reject("Failed to retrieve vendors from local storage");
        };
        
        request.onsuccess = (event) => {
          const vendors = event.target.result;
          console.log(`Retrieved ${vendors.length} vendors from IndexedDB`);
          resolve(vendors);
        };
    } catch (error) {
        console.error("Error in getVendorsFromIndexedDB:", error);
        reject(error.message);
      }
    });
  }
  
  async storeVendorsInIndexedDB(vendors) {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject("Database not initialized");
      return;
    }
    
      try {
        const transaction = this.db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        
        // Clear existing data
        const clearRequest = store.clear();
        
        clearRequest.onsuccess = () => {
          console.log("Cleared existing vendor data");
          
          // Add new vendors
          let successCount = 0;
          vendors.forEach(vendor => {
            const request = store.add(vendor);
            request.onsuccess = () => {
              successCount++;
              if (successCount === vendors.length) {
                console.log(`Successfully stored ${successCount} vendors in IndexedDB`);
                resolve();
              }
            };
            request.onerror = (event) => {
              console.error("Error storing vendor:", vendor, event.target.error);
            };
          });
        };
        
        clearRequest.onerror = (event) => {
          console.error("Error clearing vendor store:", event.target.error);
          reject("Failed to clear vendor store");
        };
        
        transaction.oncomplete = () => {
          console.log("Transaction completed");
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error:", event.target.error);
          reject("Transaction failed");
        };
      } catch (error) {
        console.error("Error in storeVendorsInIndexedDB:", error);
        reject(error.message);
      }
    });
  }
  
  showLoading() {
    this.isLoading = true;
    const existingLoader = document.getElementById('vendor-metrics-loader');
    
    if (!existingLoader) {
      const loader = document.createElement('div');
      loader.id = 'vendor-metrics-loader';
      loader.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
      loader.innerHTML = `
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p class="mt-4 text-gray-600 dark:text-gray-400">Loading vendor data...</p>
        </div>
      `;
      document.body.appendChild(loader);
    }
  }
  
  hideLoading() {
    this.isLoading = false;
    const loader = document.getElementById('vendor-metrics-loader');
    if (loader) {
      loader.remove();
    }
  }
  
  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.id = 'vendor-metrics-error';
    errorDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4';
    errorDiv.innerHTML = `
      <p>${message}</p>
      <button class="mt-2 px-3 py-1 bg-red-200 hover:bg-red-300 rounded text-red-800">Dismiss</button>
    `;
    
    // Add to container
    this.container.prepend(errorDiv);
    
    // Add event listener to dismiss button
    errorDiv.querySelector('button').addEventListener('click', () => {
      this.hideError();
    });
  }
  
  hideError() {
    const errorDiv = document.getElementById('vendor-metrics-error');
    if (errorDiv) {
      errorDiv.remove();
    }
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading vendor data...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Vendor Metrics</h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="vendor-search" 
                placeholder="Search vendors..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
            <select id="performance-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Vendors</option>
              <option value="excellent" ${this.filterStatus === 'excellent' ? 'selected' : ''}>Excellent</option>
              <option value="good" ${this.filterStatus === 'good' ? 'selected' : ''}>Good</option>
              <option value="average" ${this.filterStatus === 'average' ? 'selected' : ''}>Average</option>
              <option value="poor" ${this.filterStatus === 'poor' ? 'selected' : ''}>Poor</option>
                </select>
            
            <div class="flex gap-2">
              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </button>
              
              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                </button>
                
              <!-- Add Vendor Button -->
              <button id="add-vendor-button" class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center" title="Add Vendor">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
        <!-- Vendor Metrics Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="id">
                  Vendor ID <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="name">
                  Name <span class="sort-indicator">${this.sortField === 'name' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="leadTime">
                  Avg Lead Time <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="onTimeDelivery">
                  On-Time % <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderCount">
                  Orders <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="performance">
                  Performance <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderTableRows()}
            </tbody>
          </table>
        </div>

        <!-- Updated Pagination to match the customer_metrics.js format -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredVendors.length)} to 
            ${Math.min(this.currentPage * this.itemsPerPage, this.filteredVendors.length)} of 
            ${this.filteredVendors.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  renderTableRows() {
    if (this.filteredVendors.length === 0) {
      return `
        <tr>
          <td colspan="7" class="px-3 py-8 text-center text-gray-500 dark:text-gray-400">
            No vendors found matching your criteria
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredVendors.length);
    const currentPageItems = this.filteredVendors.slice(start, end);

    return currentPageItems.map(vendor => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(vendor.vendorId)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(vendor.name)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${vendor.leadTime} days
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${vendor.onTimeDelivery}%
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${vendor.orderCount}
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getPerformanceClass(vendor.performance)}">
            ${this.escapeHtml(vendor.performance)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${vendor.id}" class="view-vendor text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
            <i class="fas fa-eye"></i>
          </button>
          <button data-id="${vendor.id}" class="edit-vendor text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  setupEventListeners() {
    // Search input
    const searchInput = this.container.querySelector('#vendor-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = this.container.querySelector('#clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Performance filter
    const performanceFilter = this.container.querySelector('#performance-filter');
    if (performanceFilter) {
      performanceFilter.addEventListener('change', () => {
        this.filterStatus = performanceFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Sort headers
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Pagination
    const firstPageBtn = this.container.querySelector('#first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
          this.setupEventListeners();
        }
      });
    }

    const prevPageBtn = this.container.querySelector('#prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
      this.render();
          this.setupEventListeners();
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
          this.setupEventListeners();
        }
      });
    }

    const lastPageBtn = this.container.querySelector('#last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
          this.setupEventListeners();
        }
      });
    }

    // View Vendor buttons
    const viewButtons = this.container.querySelectorAll('.view-vendor');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const vendorId = button.getAttribute('data-id');
        this.viewVendorDetails(vendorId);
      });
    });

    // Edit Vendor buttons
    const editButtons = this.container.querySelectorAll('.edit-vendor');
    editButtons.forEach(button => {
      button.addEventListener('click', () => {
        const vendorId = button.getAttribute('data-id');
        this.editVendor(vendorId);
      });
    });

    // Add Vendor button
    const addVendorButton = this.container.querySelector('#add-vendor-button');
    if (addVendorButton) {
      addVendorButton.addEventListener('click', () => {
        this.addVendor();
      });
    }

    // Date Range button
    const dateRangeButton = this.container.querySelector('#date-range-button');
    if (dateRangeButton) {
      dateRangeButton.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }

    // Refresh button
    const refreshButton = this.container.querySelector('#refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
      });
    }

    // Export button
    const exportButton = this.container.querySelector('#export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportVendorData();
      });
    }

    // Settings button
    const settingsButton = this.container.querySelector('#settings-button');
    if (settingsButton) {
      settingsButton.addEventListener('click', () => {
        this.showSettings();
      });
    }
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.vendorData;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(vendor => 
        vendor.vendorId.toLowerCase().includes(term) ||
        vendor.name.toLowerCase().includes(term)
      );
    }
    
    // Filter by performance
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(vendor => 
        vendor.performance.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Filter by date range if specified
    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999); // Include the entire end date
      
      filtered = filtered.filter(vendor => {
        const lastOrderDate = new Date(vendor.lastOrder);
        return lastOrderDate >= startDate && lastOrderDate <= endDate;
      });
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (this.sortField) {
        case 'id':
          comparison = a.vendorId.localeCompare(b.vendorId);
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'leadTime':
          comparison = a.leadTime - b.leadTime;
          break;
        case 'onTimeDelivery':
          comparison = a.onTimeDelivery - b.onTimeDelivery;
          break;
        case 'orderCount':
          comparison = a.orderCount - b.orderCount;
          break;
        case 'performance':
          const perfOrder = { 'Excellent': 1, 'Good': 2, 'Average': 3, 'Poor': 4 };
          comparison = perfOrder[a.performance] - perfOrder[b.performance];
          break;
        default:
          comparison = a.name.localeCompare(b.name);
      }
      
      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredVendors = filtered;
    this.calculateTotalPages();
    this.render();
    this.setupEventListeners();
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredVendors.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  viewVendorDetails(vendorId) {
    const vendor = this.vendorData.find(v => v.id === vendorId);
    if (!vendor) return;
    
    // Create modal content
    const modalContent = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">${this.escapeHtml(vendor.name)} (${this.escapeHtml(vendor.vendorId)})</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">General Information</h4>
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p class="text-sm mb-1"><span class="font-medium">Status:</span> ${this.escapeHtml(vendor.status)}</p>
              <p class="text-sm mb-1"><span class="font-medium">Class:</span> ${this.escapeHtml(vendor.vendorClass)}</p>
              <p class="text-sm mb-1"><span class="font-medium">Currency:</span> ${this.escapeHtml(vendor.currency)}</p>
              <p class="text-sm"><span class="font-medium">Last Modified:</span> ${new Date(vendor.lastModified).toLocaleDateString()}</p>
            </div>
          </div>
          
          <div>
            <h4 class="text-sm font-semibold text-gray-600 dark:text-gray-400 mb-2">Performance Metrics</h4>
            <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded">
              <p class="text-sm mb-1"><span class="font-medium">Lead Time:</span> ${vendor.leadTime} days</p>
              <p class="text-sm mb-1"><span class="font-medium">On-Time Delivery:</span> ${vendor.onTimeDelivery}%</p>
              <p class="text-sm mb-1"><span class="font-medium">Order Count:</span> ${vendor.orderCount}</p>
              <p class="text-sm">
                <span class="font-medium">Performance:</span> 
                <span class="px-2 py-0.5 text-xs font-semibold rounded-full ${this.getPerformanceClass(vendor.performance)}">
                  ${this.escapeHtml(vendor.performance)}
                </span>
              </p>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="vendor-details-close" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
          <button id="vendor-details-edit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Edit
          </button>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'vendor-details-modal';
    
    const modalDialog = document.createElement('div');
    modalDialog.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full';
    modalDialog.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalDialog);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners
    const closeButton = document.getElementById('vendor-details-close');
    const editButton = document.getElementById('vendor-details-edit');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (closeButton) {
      closeButton.addEventListener('click', closeModal);
    }
    
    if (editButton) {
      editButton.addEventListener('click', () => {
        closeModal();
        this.editVendor(vendorId);
      });
    }
    
    // Close on Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }

  editVendor(vendorId) {
    const vendor = this.vendorData.find(v => v.id === vendorId);
    if (!vendor) return;
    
    alert(`Editing Vendor: ${vendor.name}`);
    // In a real app, you would implement a proper edit form
  }

  addVendor() {
    alert('Adding new Vendor');
    // In a real app, you would implement a proper creation form
  }

  showDateRangePicker() {
    // Simple implementation - in real app would use a proper date picker component
    alert('Date Range Picker would open here');
    
    // Mock date range selection
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90); // Last 90 days
    
    this.dateRange = {
      start: startDate,
      end: new Date()
    };
    
    // Apply filters with the new date range
    this.currentPage = 1;
    this.applyFilters();
  }

  refreshData() {
    // Don't show loading indicator here as loadVendorData already handles it
    
    // Force refresh from Acumatica
    this.loadVendorData(true)
      .then(() => {
        this.notificationSystem.addNotification("Vendor data refreshed successfully", "success");
      })
      .catch(error => {
        this.notificationSystem.addNotification("Error refreshing data: " + error.message, "error");
      });
  }

  exportVendorData() {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      csvContent += 'Vendor ID,Name,Lead Time (days),On-Time %,Order Count,Performance,Status,Currency,Last Modified\n';
      
      // Add each row of data
      this.filteredVendors.forEach(vendor => {
        const row = [
          vendor.vendorId,
          vendor.name,
          vendor.leadTime,
          vendor.onTimeDelivery,
          vendor.orderCount,
          vendor.performance,
          vendor.status,
          vendor.currency,
          new Date(vendor.lastModified).toLocaleDateString()
        ].map(cell => `"${cell}"`).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `vendor_metrics_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      
      this.notificationSystem.addNotification('Vendor data exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting data:', error);
      this.notificationSystem.addNotification('Failed to export data: ' + error.message, 'error');
    }
  }

  showSettings() {
    const settingsHtml = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Vendor Metrics Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="15" ${this.itemsPerPage === 15 ? 'selected' : ''}>15</option>
            <option value="20" ${this.itemsPerPage === 20 ? 'selected' : ''}>20</option>
          </select>
            </div>
            
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="id" ${this.sortField === 'id' ? 'selected' : ''}>Vendor ID</option>
            <option value="name" ${this.sortField === 'name' ? 'selected' : ''}>Name</option>
            <option value="leadTime" ${this.sortField === 'leadTime' ? 'selected' : ''}>Lead Time</option>
            <option value="onTimeDelivery" ${this.sortField === 'onTimeDelivery' ? 'selected' : ''}>On-Time %</option>
            <option value="orderCount" ${this.sortField === 'orderCount' ? 'selected' : ''}>Order Count</option>
            <option value="performance" ${this.sortField === 'performance' ? 'selected' : ''}>Performance</option>
          </select>
            </div>
            
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
              </label>
            </div>
          </div>
          
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
            </button>
          <button id="settings-save" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
            </button>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContent.innerHTML = settingsHtml;
    
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('settings-cancel');
    const saveButton = document.getElementById('settings-save');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (saveButton) {
      saveButton.addEventListener('click', () => {
        // Get settings values
        const itemsPerPageSelect = document.getElementById('settings-items-per-page');
        const defaultSortSelect = document.getElementById('settings-default-sort');
        const sortDirectionRadios = document.getElementsByName('settings-sort-direction');
        
        if (itemsPerPageSelect) {
          this.itemsPerPage = parseInt(itemsPerPageSelect.value);
        }
        
        if (defaultSortSelect) {
          this.sortField = defaultSortSelect.value;
        }
        
        let selectedDirection = 'asc';
        sortDirectionRadios.forEach(radio => {
          if (radio.checked) {
            selectedDirection = radio.value;
          }
        });
        this.sortDirection = selectedDirection;
        
        // Apply settings and re-render
        this.calculateTotalPages();
        this.applyFilters();
        
        // Close modal
        closeModal();
      });
    }
  }

  getPerformanceClass(performance) {
    switch (performance.toLowerCase()) {
      case 'excellent':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'good':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'average':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'poor':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  // Generate sample data when Acumatica connection is not available
  generateSampleData() {
    const performanceCategories = ['Excellent', 'Good', 'Average', 'Poor'];
    const statuses = ['Active', 'Inactive', 'Hold', 'One-Time'];
    const currencies = ['USD', 'CAD', 'EUR', 'GBP'];
    const vendorClasses = ['STANDARD', 'VENCAD', 'PREFERRED', 'VENUSD', 'FREIGHT'];
    
    const vendorNames = [
      'Acme Supply Co', 
      'Global Materials Ltd', 
      'Tech Components Inc', 
      'Quality Hardware LLC', 
      'Precision Parts Corp',
      'Atlas Manufacturing',
      'Zenith Industrial Supply',
      'Metro Equipment Co',
      'Superior Materials Inc',
      'Royal Metal Works',
      'Eagle Fasteners',
      'Delta Electronics',
      'Omega Tools Inc',
      'Phoenix Industrial',
      'Northern Supply Chain',
      'Universal Logistics',
      'Pioneer Equipment',
      'Dynamic Solutions Inc',
      'Reliable Systems',
      'Elite Industrial Products'
    ];
    
    const sampleData = [];
    
    for (let i = 0; i < vendorNames.length; i++) {
      const orderCount = Math.floor(Math.random() * 90) + 10;
      const onTimeDelivery = Math.floor(Math.random() * 50) + 50;
      const leadTime = Math.floor(Math.random() * 25) + 5;
      const lastModified = new Date(Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000);
      const lastOrder = new Date(Date.now() - Math.floor(Math.random() * 90) * 24 * 60 * 60 * 1000);
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const currency = currencies[Math.floor(Math.random() * currencies.length)];
      const vendorClass = vendorClasses[Math.floor(Math.random() * vendorClasses.length)];
      
      // Determine performance category based on metrics
      let performance;
      if (onTimeDelivery >= 90 && leadTime <= 10) {
        performance = 'Excellent';
      } else if (onTimeDelivery >= 80 && leadTime <= 15) {
        performance = 'Good';
      } else if (onTimeDelivery >= 70 && leadTime <= 20) {
        performance = 'Average';
    } else {
        performance = 'Poor';
      }
      
      sampleData.push({
        id: `sample-${i}`,
        vendorId: `V${10000 + i}`,
        name: vendorNames[i],
        leadTime,
        onTimeDelivery,
        orderCount,
        performance,
        status,
        currency,
        vendorClass,
        lastModified,
        lastOrder,
        contactEmail: `contact@${vendorNames[i].toLowerCase().replace(/\s+/g, '')}.com`,
        phone: `(${Math.floor(Math.random() * 900) + 100}) ${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`
      });
    }
    
    return sampleData;
  }
}