// Handle Acumatica login request
async function handleAcumaticaLogin(data, sendResponse) {
  try {
    const { instance, username, password, company } = data;
    
    // SOAP login request
    const soapEnvelope = `
      <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">
        <soap:Body>
          <Login xmlns="http://www.acumatica.com/generic/">
            <name>${username}</name>
            <password>${password}</password>
            <company>${company}</company>
          </Login>
        </soap:Body>
      </soap:Envelope>
    `;
    
    // Make SOAP request
    const response = await fetch(`${instance}/Soap/Default.asmx`, {
      method: 'POST',
      headers: {
        'Content-Type': 'text/xml',
        'SOAPAction': 'http://www.acumatica.com/generic/login'
      },
      body: soapEnvelope,
      credentials: 'include'
    });
    
    // Check response
    if (!response.ok) {
      const text = await response.text();
      sendResponse({ 
        success: false, 
        error: `<PERSON><PERSON> failed with status ${response.status}: ${text}` 
      });
      return;
    }
    
    const text = await response.text();
    if (text.includes('faultcode') || text.includes('faultstring')) {
      const errorMatch = text.match(/<faultstring>(.*?)<\/faultstring>/);
      if (errorMatch && errorMatch[1]) {
        sendResponse({ 
          success: false, 
          error: errorMatch[1] 
        });
      } else {
        sendResponse({ 
          success: false, 
          error: 'SOAP fault occurred' 
        });
      }
      return;
    }
    
    sendResponse({ success: true });
  } catch (error) {
    console.error('Background Acumatica login error:', error);
    sendResponse({ 
      success: false, 
      error: error.message || 'Unknown error occurred' 
    });
  }
} 