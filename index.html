<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Envent Bridge Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>


  <!-- Add this in your HTML head section -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <style>
      /* Base styles */
      body, html {
          width: 800px;
          height: 600px;
          margin: 0;
          padding: 0;
          overflow: hidden;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      }

      /* Dark mode styles */
      .dark {
          background-color: #1a1a1a;
          color: #ffffff;
      }

      .dark .sidebar {
          background-color: #2d2d2d;
          border-color: #3d3d2d;
      }

      .dark .header {
          background-color: #2d2d2d;
          border-color: #3d3d2d;
      }

      .dark .content-area {
          background-color: #1a1a1a;
      }

      .dark .tab-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      .dark .tab-button.active {
          background-color: #3d3d3d;
      }

      .dark .action-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      /* Layout components */
      .dashboard-container {
          display: grid;
          grid-template-columns: auto 1fr;
          grid-template-rows: 60px 1fr;
          height: 100vh;
          background-color: #f5f5f5;
      }

      /* Header styles - START */
      .header {
          grid-column: 1 / -1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 1rem;
          background-color: #ffffff;
          border-bottom: 1px solid #e5e5e5;
          z-index: 10;
      }

      .logo {
          flex: 0 0 auto;
      }

      .search-bar {
          position: relative;
          width: 200px;
          height: 32px;
          margin-right: 0.5rem;
      }

      .search-input {
          width: 100%;
          height: 100%;
          padding: 0 32px 0 12px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          font-size: 0.875rem;
          transition: all 0.3s ease;
      }

      .search-input:focus {
          outline: none;
          border-color: #0066ff;
          box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.2);
      }

      .search-button {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
      }

      .search-button:hover {
          color: #0066ff;
      }

      .header-actions {
          display: flex;
          align-items: center;
          gap: 0.5rem;
      }

      .user-profile {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          background-color: #f5f5f5;
      }

      .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
      }

      .icon-button {
          padding: 0.5rem;
          border: none;
          border-radius: 4px;
          background: none;
          cursor: pointer;
      }
      /* Header styles - END */

      /* Sidebar styles - START */
      .sidebar {
          grid-row: 2 / -1;
          background-color: #ffffff;
          border-right: 1px solid #e5e5e5;
          padding: 1rem;
          transition: width 0.3s ease;
          width: 200px;
          overflow-x: hidden;
          display: flex;
          flex-direction: column;
          z-index: 10; /* Add this line */
      }

      .sidebar.collapsed {
          width: 60px;
          overflow: visible;
          padding: 1rem 0.5rem;
      }

      .sidebar.collapsed .nav-item {
          justify-content: center;
      }

      .sidebar.collapsed .nav-item span {
          display: none;
      }

      .sidebar.collapsed .nav-item svg {
          margin: 0;
      }

      .nav-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0.75rem;
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s, color 0.2s;
          font-weight: 600;
          font-size: 0.813rem;
          color: #737791;
          white-space: nowrap;
      }

      .nav-item svg {
          width: 20px;
          height: 20px;
          stroke: currentColor;
          stroke-width: 1.5;
          stroke-linecap: round;
          stroke-linejoin: round;
          fill: none;
          transition: transform 0.3s ease;
      }

      .sidebar.collapsed .nav-item#toggleSidebar svg {
          transform: rotate(180deg);
      }

      .nav-item.flash {
          animation: flash 0.3s;
      }

      @keyframes flash {
        0% { background-color: #0066ff; color: white; }
        100% { background-color: #E8EFFF; color: #0066ff; }
      }

      /* Sidebar styles - END */

      /* Main content styles - START */
      .content-area {
          grid-row: 2 / -1;
          position: relative;
          overflow-y: auto;
          transition: all 0.3s ease;
          height: 100%;
          width: 100%;
          padding: 0;
          margin: 0;
          box-sizing: border-box;
      }

      .content-area.ai-style-change-2 {
          position: relative;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: block;
      }

      /* Removed margin-left styles for content-area */

      .tabs {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 1rem;
      }

      .tab-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          background-color: #f5f5f5;
          cursor: pointer;
          transition: background-color 0.2s;
      }

      .tab-button.active {
          background-color: #e5e5e5;
      }

      .action-buttons {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 1rem;
      }

      .action-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          background-color: #f5f5f5;
          cursor: pointer;
          transition: background-color 0.2s;
          font-size: 0.875rem;
          font-weight: 500;
          color: #333;
      }

      .action-button:hover {
          background-color: #e0e0e0;
      }

      .preview-table {
          background-color: #ffffff;
          border-radius: 4px;
          padding: 1rem;
          min-height: 400px;
      }
      /* Main content styles - END */

      /* Utility classes */
      .icon-button {
          padding: 0.5rem;
          border: none;
          border-radius: 4px;
          background: none;
          cursor: pointer;
      }

      /* Header action buttons */
      .icon-button {
          width: 27px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          cursor: pointer;
          transition: background-color 0.2s, color 0.2s;
      }

      .notification-button {
          background-color: #0066ff;
          color: white;
      }
      
      .connection-button {
          background-color: #0078d7;
          color: white;
      }
      
      .connection-button.connected {
          background-color: #10b981;
          position: relative;
      }
      
      .connection-button .connection-count {
          position: absolute;
          top: -5px;
          right: -5px;
          background-color: #ef4444;
          color: white;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          font-size: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      .dark-mode-button {
          background-color: #333;
          color: white;
      }

      .logout-button {
          background-color: #ff3333;
          color: white;
      }

      .icon-button:hover {
          opacity: 0.8;
      }

      /* Dark mode styles */
      body.dark-mode {
          background-color: #1a1a1a;
          color: #ffffff;
      }

      body.dark-mode .dashboard-container {
          background-color: #1a1a1a;
      }

      body.dark-mode .header {
          background-color: #2d2d2d;
          border-color: #3d3d3d;
      }

      body.dark-mode .sidebar {
          background-color: #2d2d2d;
          border-color: #3d3d3d;
      }

      body.dark-mode .content-area {
          background-color: #1a1a1a;
      }

      body.dark-mode .tab-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .tab-button.active {
          background-color: #3d3d3d;
      }

      body.dark-mode .action-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .preview-table {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .icon-button {
          color: #ffffff;
      }

      body.dark-mode .search-input {
          background-color: #2d2d2d;
          color: #ffffff;
          border-color: #3d3d3d;
      }

      body.dark-mode .search-button {
          color: #a0aec0;
      }

      body.dark-mode .search-button:hover {
          color: #0066ff;
      }

      body.dark-mode .user-profile {
          background-color: #3d3d3d;
      }

      .toggle-sidebar {
          position: absolute;
          top: 1rem;
          right: -12px;
          background-color: #ffffff;
          border: 1px solid #e5e5e5;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          transition: transform 0.3s ease;
      }

      .sidebar.collapsed .toggle-sidebar {
          transform: rotate(180deg);
      }

      .icon-button i {
          font-size: 13px;
      }

      .nav-item svg, .search-button svg {
          width: 16px;
          height: 16px;
          stroke: currentColor;
          stroke-width: 1.5;
          stroke-linecap: round;
          stroke-linejoin: round;
          fill: none;
          transition: transform 0.3s ease;
      }

      .nav-item-spacer {
        flex-grow: 1;
      }

      .sidebar.collapsed #collapseSidebar svg {
        transform: rotate(180deg);
      }

      /* New styles for Admin Dashboard hover effect */
      .nav-item.admin-dashboard {
          background-color: #E8EFFF;
      }
      .nav-item.admin-dashboard,
      .nav-item.admin-dashboard svg {
          color: #737791;
          stroke: #737791;
      }

      .nav-item.active {
          background-color: #E8EFFF;
          color: #0066ff;
      }

      .nav-item.active svg {
          stroke: #0066ff;
      }

      #collapseSidebar svg {
        transition: transform 0.3s ease;
      }

      #dynamicContent {
          position: absolute;
          top: 18px;
          left: 18px;
          right: 18px;
          bottom: 0;
          display: block;
          margin: 0;
          padding: 0;
          box-sizing: border-box;
          overflow-y: auto;
      }
  </style>
</head>
<body>
  <div class="dashboard-container">
      <!-- Section 1: Header -->
      <header class="header">
  <div class="logo">
      <span style="color: #0066ff; font-size: 1.5rem; font-weight: bold;">Envent Bridge</span>
  </div>
  
  <div class="header-actions">
      <div class="search-bar">
          <input type="text" class="search-input" placeholder="Search...">
          <button class="search-button">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
          </button>
      </div>
      
      <button class="icon-button notification-button" id="notificationButton" title="Notifications">
          <i class="fas fa-bell"></i>
      </button>
      
      <button class="icon-button connection-button" id="connectionButton" title="External Connections">
          <i class="fas fa-plug"></i>
      </button>
      
      <button class="icon-button dark-mode-button" id="darkModeButton" title="Toggle Dark Mode">
          <i class="fas fa-moon"></i>
      </button>
      
      <button class="icon-button logout-button" id="logoutButton" title="Logout">
          <i class="fas fa-sign-out-alt"></i>
      </button>
      
      <div class="user-profile">
          <img id="userAvatar" class="user-avatar" src="images/avatars/default.jpg" alt="User Avatar">
          <span id="userName">User Name</span>
      </div>
  </div>
</header>
      <!-- Header Section - END -->

      <!-- Section 2: Sidebar Navigation -->
      <nav class="sidebar" id="sidebar">

<div class="nav-item active" data-component="dashboard">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
<rect x="3" y="3" width="7" height="7"></rect>
<rect x="14" y="3" width="7" height="7"></rect>
<rect x="14" y="14" width="7" height="7"></rect>
<rect x="3" y="14" width="7" height="7"></rect>
</svg>
    <span>Dashboard</span>
</div>
<div class="nav-item" data-component="analytics">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
<line x1="18" y1="20" x2="18" y2="10"></line>
<line x1="12" y1="20" x2="12" y2="4"></line>
<line x1="6" y1="20" x2="6" y2="14"></line>
</svg>
    <span>Analytics</span>
</div>
<div class="nav-item" data-component="partmaster">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
<path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
<polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
<line x1="12" y1="22.08" x2="12" y2="12"></line>
</svg>
    <span>Supply Chain</span>
</div>
<div class="nav-item" data-component="assistant">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
   <rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect>
   <rect x="9" y="9" width="6" height="6"></rect>
   <line x1="9" y1="1" x2="9" y2="4"></line>
   <line x1="15" y1="1" x2="15" y2="4"></line>
   <line x1="9" y1="20" x2="9" y2="23"></line>
   <line x1="15" y1="20" x2="15" y2="23"></line>
   <line x1="20" y1="9" x2="23" y2="9"></line>
   <line x1="20" y1="14" x2="23" y2="14"></line>
   <line x1="1" y1="9" x2="4" y2="9"></line>
   <line x1="1" y1="14" x2="4" y2="14"></line>
 </svg>
    <span>Assistant</span>
</div>
<div class="nav-item" data-component="projectmap">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
        <circle cx="12" cy="10" r="3"></circle>
    </svg>
    <span>Project Map</span>
</div>
<div class="nav-item" data-component="settings">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="3"></circle>
        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82 1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
    </svg>
    <span>Settings</span>
</div>
<div class="nav-item" id="collapseSidebar">
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M15 18l-6-6 6-6"/>
  </svg>
  <span>Collapse Menu</span>
</div>
<div class="nav-item-spacer"></div>
<div class="nav-item admin-dashboard" data-component="admindashboard">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
<path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
</svg>
   <span>Admin Dashboard</span>
</div>
</nav>
      <!-- Sidebar Navigation - END -->

      <!-- Section 3: Main Content Area -->
      <main class="content-area ai-style-change-2">
          <div id="dynamicContent">
  <!-- Content will be dynamically populated based on active navigation item -->
</div>
      </main>
      <!-- Main Content Area - END -->
  </div>

  <!-- Section 4: Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="analytics/apexcharts.min.js"></script>
  <script type="module" src="core/settings.js"></script>
  <script type="module" src="dashboard/dashboard.js"></script>
  <script type="module" src="dashboard/dashboard-actions.js"></script>
  <script type="module" src="dashboard/dashboard-process.js"></script>
  <script type="module" src="analytics/analytics.js"></script>
  <script type="module" src="map/map.js"></script>
  <script type="module" src="index.js"></script>
  <script>
      document.addEventListener('DOMContentLoaded', function() {
          const sidebar = document.querySelector('.sidebar');
          const toggleButton = document.getElementById('collapseSidebar');
          const navItems = document.querySelectorAll('.nav-item');

          toggleButton.addEventListener('click', function() {
              sidebar.classList.toggle('collapsed');
          });

          navItems.forEach(item => {
              item.addEventListener('click', function() {
                  navItems.forEach(i => i.classList.remove('active'));
                  this.classList.add('active');
                  this.classList.add('flash');
                  setTimeout(() => {
                      this.classList.remove('flash');
                  }, 300);
              });
          });

          // Log Chart.js availability
          console.log('Chart.js available:', typeof Chart !== 'undefined');
      });
  </script>
</body>
</html>