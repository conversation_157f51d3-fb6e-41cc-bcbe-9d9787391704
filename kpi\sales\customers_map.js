// Customer Map Component for Sales KPI Dashboard
export class CustomerMapComponent {
  constructor(container) {
    this.container = container;
    this.map = null;
    this.markers = [];
    this.customers = [];
    this.geocodedAddresses = {}; // Cache for geocoded addresses
    this.bounds = null; // For map bounds
    this.mapboxToken = 'pk.eyJ1IjoibWFoZGktZWJhZGkiLCJhIjoiY204YjViYW0xMHAwZTJqcHlxbnp0N2EzbiJ9.SSUsIDq3g5JMy9GYyKQTYg'; // Mapbox token
    this.infoWindow = null; // For customer popups
    
    // Event handlers (will be set by parent component)
    this.onMarkerClick = null;
    this.onBackToTable = null;
  }

  async init() {
    try {
      console.log('Initializing CustomerMapComponent');
      
      // Initialize the map
      this.initializeMap();
      
      // Wait for the map to load
      await new Promise((resolve) => {
        if (this.map) {
          this.map.on('load', () => {
            console.log('Mapbox map loaded successfully');
            resolve();
          });
          
          // Also resolve after a timeout in case the load event doesn't fire
          setTimeout(() => {
            console.log('Map load timeout - continuing anyway');
            resolve();
          }, 3000);
        } else {
          console.error('Map not initialized properly');
          resolve(); // Continue anyway
        }
      });
      
      // Initialize the geocoder cache from localStorage if available
      this.loadGeocodedAddressesFromCache();
      
      // Setup event listeners for map interactions
      this.setupEventListeners();
      
      console.log('CustomerMapComponent initialization complete');
      return true;
    } catch (error) {
      console.error('Error initializing customer map component:', error);
      throw new Error('Failed to initialize map component: ' + error.message);
    }
  }

  initializeMap() {
    console.log('Initializing map with container:', this.container);
    
    if (!window.mapboxgl) {
      console.error('Mapbox GL JS not loaded');
      throw new Error('Mapbox GL JS is not loaded. Please ensure the library is loaded before initializing the map.');
    }
    
    // Check if the container is valid
    if (!this.container) {
      console.error('Map container is null or undefined');
      throw new Error('Map container is not available');
    }
    
    try {
      // Initialize mapboxgl with token
      mapboxgl.accessToken = this.mapboxToken;
      console.log('Setting Mapbox access token:', this.mapboxToken);
      
      // Log container dimensions for debugging
      console.log('Container dimensions:', 
        this.container.offsetWidth, 
        this.container.offsetHeight,
        'Style:',
        window.getComputedStyle(this.container).width,
        window.getComputedStyle(this.container).height,
        'Display:',
        window.getComputedStyle(this.container).display
      );
      
      // Make sure container is visible and has dimensions
      this.container.style.display = 'block';
      
      // Create map instance
      console.log('Creating Mapbox map instance');
      this.map = new mapboxgl.Map({
        container: this.container,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [-100.0, 40.0], // Default center (Americas)
        zoom: 3,
        attributionControl: true,
        preserveDrawingBuffer: true // Helps with certain rendering issues
      });
      
      console.log('Mapbox map instance created');
      
      // Add zoom and rotation controls to the map
      this.map.addControl(new mapboxgl.NavigationControl(), 'top-right');
      
      // Add fullscreen control
      this.map.addControl(new mapboxgl.FullscreenControl(), 'top-right');
      
      // Add geocoding control for searching locations if available
      if (window.MapboxGeocoder) {
        console.log('Adding MapboxGeocoder control');
        try {
          this.geocoder = new MapboxGeocoder({
            accessToken: mapboxgl.accessToken,
            mapboxgl: mapboxgl,
            marker: false,
            placeholder: 'Search for a location'
          });
          
          this.map.addControl(this.geocoder, 'top-left');
        } catch (error) {
          console.error('Error adding geocoder control:', error);
          // Continue without geocoder
        }
      } else {
        console.log('MapboxGeocoder not available, skipping search control');
      }
      
      // Initialize bounds
      this.bounds = new mapboxgl.LngLatBounds();
      
      // Create popup for customer info
      this.popup = new mapboxgl.Popup({
        closeButton: true,
        closeOnClick: false,
        className: 'customer-popup'
      });
      
      // Debug: Add a single test marker to verify the map is working
      this.map.on('load', () => {
        const testMarker = new mapboxgl.Marker({color: '#F84C4C'})
          .setLngLat([-100.0, 40.0])
          .addTo(this.map);
          
        console.log('Added test marker to map');
      });
    } catch (error) {
      console.error('Error in initializeMap:', error);
      throw error;
    }
  }

  setupEventListeners() {
    // Map event listeners, if needed
    this.map.on('click', (e) => {
      // Handle map click events
    });
  }

  updateCustomers(customers) {
    this.customers = customers;
    this.displayCustomersOnMap();
  }

  async displayCustomersOnMap() {
    try {
      console.log(`Displaying ${this.customers.length} customers on map`);
      
      // Clear existing markers
      this.clearMarkers();
      
      // Reset bounds
      this.bounds = new mapboxgl.LngLatBounds();
      
      // Check if map is initialized
      if (!this.map) {
        console.error('Map not initialized, cannot add markers');
        return;
      }
      
      // Loop through customers and add markers
      for (const customer of this.customers) {
        await this.addCustomerMarker(customer);
      }
      
      // Fit map to bounds if we have markers
      if (!this.bounds.isEmpty()) {
        console.log('Fitting map to bounds with markers');
        this.map.fitBounds(this.bounds, {
          padding: 50, // Add some padding around the bounds
          maxZoom: 15 // Limit max zoom level
        });
      } else {
        console.warn('No valid markers added, cannot fit map to bounds');
      }
    } catch (error) {
      console.error('Error displaying customers on map:', error);
    }
  }

  async addCustomerMarker(customer) {
    try {
      // Get the customer's coordinates
      const coordinates = await this.getCustomerCoordinates(customer);
      if (!coordinates) return; // Skip if no coordinates available
      
      // Create marker element
      const el = document.createElement('div');
      el.className = 'customer-marker';
      el.innerHTML = `<div class="marker-inner ${this.getMarkerColorClass(customer.status)}"></div>`;
      
      // Create marker
      const marker = new mapboxgl.Marker({
        element: el,
        anchor: 'bottom'
      })
        .setLngLat(coordinates)
        .addTo(this.map);
      
      // Add to markers array for future reference
      this.markers.push(marker);
      
      // Extend bounds to include this marker
      this.bounds.extend(coordinates);
      
      // Add popup functionality
      el.addEventListener('click', () => {
        this.showCustomerPopup(customer, coordinates, marker);
      });
      
      return marker;
    } catch (error) {
      console.error(`Error adding marker for customer ${customer.customerID}:`, error);
      return null;
    }
  }

  showCustomerPopup(customer, coordinates, marker) {
    // Format currency
    const formattedSales = new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: customer.currency || 'USD'
    }).format(customer.totalSales);
    
    // Format date
    const formattedDate = customer.lastOrder instanceof Date 
      ? customer.lastOrder.toLocaleDateString()
      : new Date(customer.lastOrder).toLocaleDateString();
    
    // Create popup content
    const popupContent = `
      <div class="customer-popup-content">
        <h3 class="text-lg font-semibold">${customer.customerName}</h3>
        <p class="text-sm text-gray-600 dark:text-gray-300">${customer.customerID}</p>
        <div class="my-2">
          <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${this.getStatusClass(customer.status)}">
            ${customer.status}
          </span>
        </div>
        <div class="mt-2 space-y-1">
          <p><span class="font-medium">Location:</span> ${customer.city || 'N/A'}, ${customer.country || 'N/A'}</p>
          <p><span class="font-medium">Total Sales:</span> ${formattedSales}</p>
          <p><span class="font-medium">Last Order:</span> ${formattedDate}</p>
        </div>
        <div class="mt-3 flex justify-between">
          <button id="viewDetails-${customer.id}" class="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm">
            Details
          </button>
          <button id="contactCustomer-${customer.id}" class="px-3 py-1 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 text-sm">
            Contact
          </button>
        </div>
      </div>
    `;
    
    // Close any existing popup
    if (this.popup) {
      this.popup.remove();
    }
    
    // Create and display the popup
    this.popup = new mapboxgl.Popup({
      closeButton: true,
      closeOnClick: false,
      className: 'customer-popup'
    })
      .setLngLat(coordinates)
      .setHTML(popupContent)
      .addTo(this.map);
    
    // Add event listeners to popup buttons
    setTimeout(() => {
      const detailsBtn = document.getElementById(`viewDetails-${customer.id}`);
      if (detailsBtn) {
        detailsBtn.addEventListener('click', () => {
          // Call the parent's handler if available
          if (this.onMarkerClick) {
            this.onMarkerClick(customer);
          }
          // Close the popup
          this.popup.remove();
        });
      }
      
      const contactBtn = document.getElementById(`contactCustomer-${customer.id}`);
      if (contactBtn) {
        contactBtn.addEventListener('click', () => {
          // Implement contact functionality or show a modal
          alert(`Contact functionality for ${customer.customerName} will be implemented in a future update.`);
        });
      }
    }, 100);
  }

  clearMarkers() {
    // Remove all markers from the map
    for (const marker of this.markers) {
      marker.remove();
    }
    this.markers = [];
    
    // Close any open popup
    if (this.popup) {
      this.popup.remove();
    }
  }

  getMarkerColorClass(status) {
    // Return CSS class based on customer status
    switch (status.toLowerCase()) {
      case 'active':
        return 'marker-green';
      case 'inactive':
        return 'marker-red';
      case 'on hold':
        return 'marker-yellow';
      default:
        return 'marker-gray';
    }
  }

  getStatusClass(status) {
    // Return Tailwind CSS classes for status badges
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  async getCustomerCoordinates(customer) {
    try {
      // Check if we already have geocoded this address
      const addressKey = this.getAddressKey(customer);
      if (this.geocodedAddresses[addressKey]) {
        console.log(`Using cached coordinates for ${customer.customerName}`);
        return this.geocodedAddresses[addressKey];
      }
      
      console.log(`Geocoding address for customer: ${customer.customerName}`);
      
      // Build the address from customer data
      let addressParts = [];
      
      // Add address components if available
      if (customer.rawData?.MainContact?.Address?.AddressLine1?.value) {
        addressParts.push(customer.rawData.MainContact.Address.AddressLine1.value);
      }
      
      if (customer.city && customer.city !== 'N/A') addressParts.push(customer.city);
      
      if (customer.rawData?.MainContact?.Address?.State?.value) {
        addressParts.push(customer.rawData.MainContact.Address.State.value);
      }
      
      if (customer.rawData?.MainContact?.Address?.PostalCode?.value) {
        addressParts.push(customer.rawData.MainContact.Address.PostalCode.value);
      }
      
      if (customer.country && customer.country !== 'N/A') addressParts.push(customer.country);
      
      // If we don't have enough address parts, try to use just city and country
      if (addressParts.length < 2 && customer.city && customer.city !== 'N/A' && customer.country && customer.country !== 'N/A') {
        addressParts = [customer.city, customer.country];
      }
      
      // If still not enough, use company name and country as a fallback
      if (addressParts.length < 2 && customer.companyName && customer.companyName !== 'N/A' && customer.country && customer.country !== 'N/A') {
        addressParts = [customer.companyName, customer.country];
      }
      
      // If we still don't have enough, try just the country
      if (addressParts.length < 1 && customer.country && customer.country !== 'N/A') {
        addressParts = [customer.country];
      }
      
      // For demo purposes - if we have mock data, assign random coordinates
      if (addressParts.length < 1 || addressParts[0] === 'N/A') {
        if (customer.customerID.startsWith('CU')) {
          console.log(`Using random coordinates for demo customer: ${customer.customerName}`);
          // Generate random coordinates within North America
          const randomLng = -100 + (Math.random() * 30 - 15);
          const randomLat = 40 + (Math.random() * 20 - 10);
          const coordinates = [randomLng, randomLat];
          
          // Cache the random coordinates
          this.geocodedAddresses[addressKey] = coordinates;
          this.saveGeocodedAddressesToCache();
          
          return coordinates;
        }
        
        console.warn(`Couldn't build a valid address for customer ${customer.customerID}`);
        return null;
      }
      
      // Join the address parts
      const address = addressParts.join(', ');
      console.log(`Geocoding address: ${address}`);
      
      // Geocode the address
      const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(address)}.json?access_token=${this.mapboxToken}&limit=1`;
      const response = await fetch(geocodeUrl);
      
      if (!response.ok) {
        console.error(`Geocoding API returned status ${response.status}: ${response.statusText}`);
        throw new Error(`Geocoding API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Check if we got valid results
      if (data.features && data.features.length > 0) {
        const coordinates = data.features[0].center;
        console.log(`Geocoded ${address} to coordinates: [${coordinates[0]}, ${coordinates[1]}]`);
        
        // Cache the geocoded address
        this.geocodedAddresses[addressKey] = coordinates;
        this.saveGeocodedAddressesToCache();
        
        return coordinates;
      } else {
        console.warn(`Geocoding failed for address: ${address}`, data);
        
        // For demo purposes - assign random coordinates
        if (customer.customerID.startsWith('CU')) {
          console.log(`Using random fallback coordinates for ${customer.customerName}`);
          // Generate random coordinates within North America
          const randomLng = -100 + (Math.random() * 30 - 15);
          const randomLat = 40 + (Math.random() * 20 - 10);
          return [randomLng, randomLat];
        }
        
        return null;
      }
    } catch (error) {
      console.error('Error geocoding customer address:', error);
      
      // For demo purposes - if geocoding fails, assign random coordinates
      if (customer.customerID.startsWith('CU')) {
        console.log(`Using random fallback coordinates for ${customer.customerName} due to error`);
        // Generate random coordinates within North America
        const randomLng = -100 + (Math.random() * 30 - 15);
        const randomLat = 40 + (Math.random() * 20 - 10);
        return [randomLng, randomLat];
      }
      
      return null;
    }
  }

  getAddressKey(customer) {
    // Create a unique key for this address to use in the geocoding cache
    const parts = [];
    
    if (customer.rawData?.MainContact?.Address?.AddressLine1?.value) {
      parts.push(customer.rawData.MainContact.Address.AddressLine1.value);
    }
    
    if (customer.city) parts.push(customer.city);
    if (customer.country) parts.push(customer.country);
    
    if (customer.rawData?.MainContact?.Address?.PostalCode?.value) {
      parts.push(customer.rawData.MainContact.Address.PostalCode.value);
    }
    
    return parts.join('|').toLowerCase().replace(/\s+/g, '');
  }

  loadGeocodedAddressesFromCache() {
    try {
      const cached = localStorage.getItem('customer_geocoded_addresses');
      if (cached) {
        this.geocodedAddresses = JSON.parse(cached);
        console.log(`Loaded ${Object.keys(this.geocodedAddresses).length} geocoded addresses from cache`);
      }
    } catch (error) {
      console.error('Error loading geocoded addresses from cache:', error);
      this.geocodedAddresses = {};
    }
  }

  saveGeocodedAddressesToCache() {
    try {
      localStorage.setItem('customer_geocoded_addresses', JSON.stringify(this.geocodedAddresses));
    } catch (error) {
      console.error('Error saving geocoded addresses to cache:', error);
    }
  }
}

// Define MapboxGeocoder class for address search
class MapboxGeocoder {
  constructor(options) {
    this.accessToken = options.accessToken;
    this.mapboxgl = options.mapboxgl;
    this.marker = options.marker || false;
    this.placeholder = options.placeholder || 'Search';
    this.map = null;
    this.container = null;
  }

  onAdd(map) {
    this.map = map;
    this.container = document.createElement('div');
    this.container.className = 'mapboxgl-ctrl mapboxgl-ctrl-geocoder';
    
    // Create input element
    const input = document.createElement('input');
    input.type = 'text';
    input.placeholder = this.placeholder;
    input.className = 'mapboxgl-ctrl-geocoder--input';
    this.container.appendChild(input);
    
    // Add event listener for input
    input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        this.geocode(input.value);
      }
    });
    
    return this.container;
  }

  onRemove() {
    this.container.parentNode.removeChild(this.container);
    this.map = null;
  }

  async geocode(query) {
    try {
      const geocodeUrl = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(query)}.json?access_token=${this.accessToken}&limit=1`;
      const response = await fetch(geocodeUrl);
      const data = await response.json();
      
      if (data.features && data.features.length > 0) {
        const coordinates = data.features[0].center;
        
        // Fly to the location
        this.map.flyTo({
          center: coordinates,
          zoom: 14
        });
        
        // Add a marker if needed
        if (this.marker) {
          new this.mapboxgl.Marker()
            .setLngLat(coordinates)
            .addTo(this.map);
        }
      } else {
        console.warn('No results found for search query:', query);
      }
    } catch (error) {
      console.error('Error geocoding search query:', error);
    }
  }
} 