!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={})}(this,(function(t){"use strict";function e(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}let n=()=>{};const s=!1;function r(t){let e;return()=>(void 0===e&&(e=t()),e)}const i=t=>t,o=(t,e,n)=>{const s=e-t;return 0===s?1:(n-t)/s};class a{constructor(){this.subscriptions=[]}add(t){var n,s;return n=this.subscriptions,s=t,-1===n.indexOf(s)&&n.push(s),()=>e(this.subscriptions,t)}notify(t,e,n){const s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,n);else for(let r=0;r<s;r++){const s=this.subscriptions[r];s&&s(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const l=t=>1e3*t,u=t=>t/1e3;function c(t,e){return e?t*(1e3/e):0}const h=r((()=>void 0!==window.ScrollTimeline));class d{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map((t=>t.finished)))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let n=0;n<this.animations.length;n++)this.animations[n][t]=e}attachTimeline(t,e){const n=this.animations.map((n=>h()&&n.attachTimeline?n.attachTimeline(t):"function"==typeof e?e(n):void 0));return()=>{n.forEach(((t,e)=>{t&&t(),this.animations[e].stop()}))}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach((e=>e[t]()))}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class p extends d{then(t,e){return this.finished.finally(t).then((()=>{}))}}const f=t=>t.startsWith("--"),m=(t,e,n)=>{f(e)?t.style.setProperty(e,n):t.style[e]=n},g=(t,e)=>f(e)?t.style.getPropertyValue(e):t.style[e],y=t=>null!==t;const v=r((()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0})),w=new Set(["borderWidth","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderRadius","radius","borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius","width","maxWidth","height","maxHeight","top","right","bottom","left","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","margin","marginTop","marginRight","marginBottom","marginLeft","backgroundPositionX","backgroundPositionY"]);const b={value:null,addProjectionMetrics:null},T=t=>Array.isArray(t)&&"number"==typeof t[0],x={};function S(t,e){const n=r(t);return()=>x[e]??n()}const M=S((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),A=(t,e,n=10)=>{let s="";const r=Math.max(Math.round(e/n),2);for(let e=0;e<r;e++)s+=t(e/(r-1))+", ";return`linear(${s.substring(0,s.length-2)})`},V=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,k={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:V([0,.65,.55,1]),circOut:V([.55,0,1,.45]),backIn:V([.31,.01,.66,-.59]),backOut:V([.33,1.53,.69,.99])};function P(t,e){return t?"function"==typeof t&&M()?A(t,e):T(t)?V(t):Array.isArray(t)?t.map((t=>P(t,e)||k.easeOut)):k[t]:void 0}function E(t,e,n,{delay:s=0,duration:r=300,repeat:i=0,repeatType:o="loop",ease:a="easeInOut",times:l}={},u=void 0){const c={[e]:n};l&&(c.offset=l);const h=P(a,r);Array.isArray(h)&&(c.easing=h);return t.animate(c,{delay:s,duration:r,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal",pseudoElement:u})}function C(t){return"function"==typeof t&&"applyToOptions"in t}const F=new WeakMap;class O{constructor(t){if("animation"in t)return void(this.animation=t.animation);const{element:e,name:n,keyframes:s,pseudoElement:r,allowFlatten:i=!1}=t;let{transition:o}=t;this.allowFlatten=i;const a=function(t){const e=F.get(t)||new Map;return F.set(t,e),e}(e),l=((t,e)=>`${t}:${e}`)(n,r||""),u=a.get(l);u&&u.stop();const c=function(t,e,n,s){Array.isArray(n)||(n=[n]);for(let r=0;r<n.length;r++)null===n[r]&&(n[r]=0!==r||s?n[r-1]:g(t,e)),"number"==typeof n[r]&&w.has(e)&&(n[r]=n[r]+"px");return!s&&!v()&&n.length<2&&n.unshift(g(t,e)),n}(e,n,s,r);o.type,o=function({type:t,...e}){return C(t)?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(o),this.animation=E(e,n,c,o,r),!1===o.autoplay&&this.animation.pause(),this.removeAnimation=()=>a.delete(l),this.animation.onfinish=()=>{r?this.commitStyles():m(e,n,function(t,{repeat:e,repeatType:n="loop"},s){const r=t.filter(y),i=e&&"loop"!==n&&e%2==1?0:r.length-1;return i&&void 0!==s?s:r[i]}(c,o)),this.cancel()},a.set(l,this)}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.finish()}cancel(){try{this.animation.cancel()}catch(t){}this.removeAnimation()}stop(){const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.commitStyles(),this.cancel())}commitStyles(){this.animation.commitStyles?.()}get duration(){console.log(this.animation.effect?.getComputedTiming());const t=this.animation.effect?.getComputedTiming().duration||0;return u(Number(t))}get time(){return u(Number(this.animation.currentTime)||0)}set time(t){this.animation.currentTime=l(t)}get speed(){return this.animation.playbackRate}set speed(t){this.animation.playbackRate=t}get state(){return this.animation.playState}get startTime(){return Number(this.animation.startTime)}get finished(){return this.animation.finished}flatten(){this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"})}attachTimeline(t){return this.animation.timeline=t,this.animation.onfinish=null,i}then(t,e){return this.finished.then(t).catch(e)}}function R(t,e){return t?.[e]??t?.default??t}const B=2e4;function L(t){let e=0;let n=t.next(e);for(;!n.done&&e<B;)e+=50,n=t.next(e);return e>=B?1/0:e}function I(t,e=100,n){const s=n({...t,keyframes:[0,e]}),r=Math.min(L(s),B);return{type:"keyframes",ease:t=>s.next(r*t).value/e,duration:u(r)}}function D(t){return Boolean("function"==typeof t&&M()||!t||"string"==typeof t&&(t in k||M())||T(t)||Array.isArray(t)&&t.every(D))}function W(t,e){t.timeline=e,t.onfinish=null}const N=["read","resolveKeyframes","update","preRender","render","postRender"];function K(t,e){let n=!1,s=!0;const r={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,o=N.reduce(((t,n)=>(t[n]=function(t,e){let n=new Set,s=new Set,r=!1,i=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(c.schedule(e),t()),l++,e(a)}const c={schedule:(t,e=!1,i=!1)=>{const a=i&&r?n:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{a=t,r?i=!0:(r=!0,[n,s]=[s,n],n.forEach(u),e&&b.value&&b.value.frameloop[e].push(l),l=0,n.clear(),r=!1,i&&(i=!1,c.process(t)))}};return c}(i,e?n:void 0),t)),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=o,p=()=>{const i=performance.now();n=!1,r.delta=s?1e3/60:Math.max(Math.min(i-r.timestamp,40),1),r.timestamp=i,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),c.process(r),h.process(r),d.process(r),r.isProcessing=!1,n&&e&&(s=!1,t(p))};return{schedule:N.reduce(((e,i)=>{const a=o[i];return e[i]=(e,i=!1,o=!1)=>(n||(n=!0,s=!0,r.isProcessing||t(p)),a.schedule(e,i,o)),e}),{}),cancel:t=>{for(let e=0;e<N.length;e++)o[N[e]].cancel(t)},state:r,steps:o}}const{schedule:$,cancel:j,state:z,steps:Y}=K("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i,!0);let U;function X(){U=void 0}const H={now:()=>(void 0===U&&H.set(z.isProcessing||s?z.timestamp:performance.now()),U),set:t=>{U=t,queueMicrotask(X)}},q=!1,G=!1;function Z(){return q||G}function _(t,e,n){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);const r=n?.[t]??s.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}function J(t,e){const n=_(t),s=new AbortController;return[n,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function Q(t){return!("touch"===t.pointerType||Z())}const tt=(t,e)=>!!e&&(t===e||tt(t,e.parentElement)),et=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const nt=new WeakSet;function st(t){return e=>{"Enter"===e.key&&t(e)}}function rt(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function it(t){return(t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary)(t)&&!Z()}class ot{constructor(t,e={}){this.version="12.6.3",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=H.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=H.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new a);const n=this.events[t].add(e);return"change"===t?()=>{n(),$.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=H.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return c(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function at(t,e){return new ot(t,e)}const lt=(t,e,n)=>n>e?e:n<t?t:n;function ut(t,e,n){const s=Math.max(e-5,0);return c(n-t(s),e-s)}const ct=100,ht=10,dt=1,pt=0,ft=800,mt=.3,gt=.3,yt={granular:.01,default:2},vt={granular:.005,default:.5},wt=.01,bt=10,Tt=.05,xt=1,St=.001;function Mt({duration:t=ft,bounce:e=mt,velocity:n=pt,mass:s=dt}){let r,i,o=1-e;o=lt(Tt,xt,o),t=lt(wt,bt,u(t)),o<1?(r=e=>{const s=e*o,r=s*t,i=s-n,a=Vt(e,o),l=Math.exp(-r);return St-i/a*l},i=e=>{const s=e*o*t,i=s*n+n,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-s),u=Vt(Math.pow(e,2),o);return(-r(e)+St>0?-1:1)*((i-a)*l)/u}):(r=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,i=e=>Math.exp(-e*t)*(t*t*(n-e)));const a=function(t,e,n){let s=n;for(let n=1;n<At;n++)s-=t(s)/e(s);return s}(r,i,5/t);if(t=l(t),isNaN(a))return{stiffness:ct,damping:ht,duration:t};{const e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}const At=12;function Vt(t,e){return t*Math.sqrt(1-e*e)}const kt=["duration","bounce"],Pt=["stiffness","damping","mass"];function Et(t,e){return e.some((e=>void 0!==t[e]))}function Ct(t=gt,e=mt){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:r}=n;const i=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:c,damping:h,mass:d,duration:p,velocity:f,isResolvedFromDuration:m}=function(t){let e={velocity:pt,stiffness:ct,damping:ht,mass:dt,isResolvedFromDuration:!1,...t};if(!Et(t,Pt)&&Et(t,kt))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(1.2*n),r=s*s,i=2*lt(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:dt,stiffness:r,damping:i}}else{const n=Mt(t);e={...e,...n,mass:dt},e.isResolvedFromDuration=!0}return e}({...n,velocity:-u(n.velocity||0)}),g=f||0,y=h/(2*Math.sqrt(c*d)),v=o-i,w=u(Math.sqrt(c/d)),b=Math.abs(v)<5;let T;if(s||(s=b?yt.granular:yt.default),r||(r=b?vt.granular:vt.default),y<1){const t=Vt(w,y);T=e=>{const n=Math.exp(-y*w*e);return o-n*((g+y*w*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}}else if(1===y)T=t=>o-Math.exp(-w*t)*(v+(g+w*v)*t);else{const t=w*Math.sqrt(y*y-1);T=e=>{const n=Math.exp(-y*w*e),s=Math.min(t*e,300);return o-n*((g+y*w*v)*Math.sinh(s)+t*v*Math.cosh(s))/t}}const x={calculatedDuration:m&&p||null,next:t=>{const e=T(t);if(m)a.done=t>=p;else{let n=0;y<1&&(n=0===t?l(g):ut(T,t,e));const i=Math.abs(n)<=s,u=Math.abs(o-e)<=r;a.done=i&&u}return a.value=a.done?o:e,a},toString:()=>{const t=Math.min(L(x),B),e=A((e=>x.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return x}Ct.applyToOptions=t=>{const e=I(t,100,Ct);return t.ease=M()?e.ease:"easeOut",t.duration=l(e.duration),t.type="keyframes",t};const Ft=(t,e,n)=>{const s=e-t;return((n-t)%s+s)%s+t},Ot=t=>Array.isArray(t)&&"number"!=typeof t[0];function Rt(t,e){return Ot(t)?t[Ft(0,t.length,e)]:t}const Bt=(t,e,n)=>t+(e-t)*n;function Lt(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const r=o(0,e,s);t.push(Bt(n,1,r))}}function It(t){const e=[0];return Lt(e,t.length-1),e}const Dt=t=>Boolean(t&&t.getVelocity);function Wt(t){return"object"==typeof t&&!Array.isArray(t)}function Nt(t,e,n,s){return"string"==typeof t&&Wt(e)?_(t,n,s):t instanceof NodeList?Array.from(t):Array.isArray(t)?t:[t]}function Kt(t,e,n){return t*(e+1)}function $t(t,e,n,s){return"number"==typeof e?e:e.startsWith("-")||e.startsWith("+")?Math.max(0,t+parseFloat(e)):"<"===e?n:s.get(e)??t}function jt(t,n,s,r,i,o){!function(t,n,s){for(let r=0;r<t.length;r++){const i=t[r];i.at>n&&i.at<s&&(e(t,i),r--)}}(t,i,o);for(let e=0;e<n.length;e++)t.push({value:n[e],at:Bt(i,o,r[e]),easing:Rt(s,e)})}function zt(t,e){for(let n=0;n<t.length;n++)t[n]=t[n]/(e+1)}function Yt(t,e){return t.at===e.at?null===t.value?1:null===e.value?-1:0:t.at-e.at}function Ut(t,e){return!e.has(t)&&e.set(t,{}),e.get(t)}function Xt(t,e){return e[t]||(e[t]=[]),e[t]}function Ht(t){return Array.isArray(t)?t:[t]}function qt(t,e){return t&&t[e]?{...t,...t[e]}:{...t}}const Gt=t=>"number"==typeof t,Zt=t=>t.every(Gt),_t=new WeakMap,Jt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Qt=new Set(Jt),te=new Set(["width","height","top","left","right","bottom",...Jt]),ee=t=>(t=>Array.isArray(t))(t)?t[t.length-1]||0:t;function ne(t){const e=[{},{}];return t?.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function se(t,e,n,s){if("function"==typeof e){const[r,i]=ne(s);e=e(void 0!==n?n:t.custom,r,i)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[r,i]=ne(s);e=e(void 0!==n?n:t.custom,r,i)}return e}function re(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,at(n))}function ie(t,e){const n=function(t,e,n){const s=t.getProps();return se(s,e,void 0!==n?n:s.custom,t)}(t,e);let{transitionEnd:s={},transition:r={},...i}=n||{};i={...i,...s};for(const e in i){re(t,e,ee(i[e]))}}function oe(t,e){const n=t.getValue("willChange");if(s=n,Boolean(Dt(s)&&s.add))return n.add(e);var s}const ae=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),le="data-"+ae("framerAppearId");function ue(t){return t.props[le]}const ce=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function he(t,e,n,s){if(t===e&&n===s)return i;const r=e=>function(t,e,n,s,r){let i,o,a=0;do{o=e+(n-e)/2,i=ce(o,s,r)-t,i>0?n=o:e=o}while(Math.abs(i)>1e-7&&++a<12);return o}(e,0,1,t,n);return t=>0===t||1===t?t:ce(r(t),e,s)}const de=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,pe=t=>e=>1-t(1-e),fe=he(.33,1.53,.69,.99),me=pe(fe),ge=de(me),ye=t=>(t*=2)<1?.5*me(t):.5*(2-Math.pow(2,-10*(t-1))),ve=t=>1-Math.sin(Math.acos(t)),we=pe(ve),be=de(ve),Te=t=>/^0[^.\s]+$/u.test(t);const xe={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Se={...xe,transform:t=>lt(0,1,t)},Me={...xe,default:1},Ae=t=>Math.round(1e5*t)/1e5,Ve=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const ke=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Pe=(t,e)=>n=>Boolean("string"==typeof n&&ke.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),Ee=(t,e,n)=>s=>{if("string"!=typeof s)return s;const[r,i,o,a]=s.match(Ve);return{[t]:parseFloat(r),[e]:parseFloat(i),[n]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},Ce={...xe,transform:t=>Math.round((t=>lt(0,255,t))(t))},Fe={test:Pe("rgb","red"),parse:Ee("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Ce.transform(t)+", "+Ce.transform(e)+", "+Ce.transform(n)+", "+Ae(Se.transform(s))+")"};const Oe={test:Pe("#"),parse:function(t){let e="",n="",s="",r="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),r=t.substring(4,5),e+=e,n+=n,s+=s,r+=r),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:r?parseInt(r,16)/255:1}},transform:Fe.transform},Re=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Be=Re("deg"),Le=Re("%"),Ie=Re("px"),De=Re("vh"),We=Re("vw"),Ne={...Le,parse:t=>Le.parse(t)/100,transform:t=>Le.transform(100*t)},Ke={test:Pe("hsl","hue"),parse:Ee("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+Le.transform(Ae(e))+", "+Le.transform(Ae(n))+", "+Ae(Se.transform(s))+")"},$e={test:t=>Fe.test(t)||Oe.test(t)||Ke.test(t),parse:t=>Fe.test(t)?Fe.parse(t):Ke.test(t)?Ke.parse(t):Oe.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Fe.transform(t):Ke.transform(t)},je=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const ze="number",Ye="color",Ue=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Xe(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},r=[];let i=0;const o=e.replace(Ue,(t=>($e.test(t)?(s.color.push(i),r.push(Ye),n.push($e.parse(t))):t.startsWith("var(")?(s.var.push(i),r.push("var"),n.push(t)):(s.number.push(i),r.push(ze),n.push(parseFloat(t))),++i,"${}"))).split("${}");return{values:n,split:o,indexes:s,types:r}}function He(t){return Xe(t).values}function qe(t){const{split:e,types:n}=Xe(t),s=e.length;return t=>{let r="";for(let i=0;i<s;i++)if(r+=e[i],void 0!==t[i]){const e=n[i];r+=e===ze?Ae(t[i]):e===Ye?$e.transform(t[i]):t[i]}return r}}const Ge=t=>"number"==typeof t?0:t;const Ze={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Ve)?.length||0)+(t.match(je)?.length||0)>0},parse:He,createTransformer:qe,getAnimatableNone:function(t){const e=He(t);return qe(t)(e.map(Ge))}},_e=new Set(["brightness","contrast","saturate","opacity"]);function Je(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[s]=n.match(Ve)||[];if(!s)return t;const r=n.replace(s,"");let i=_e.has(e)?1:0;return s!==n&&(i*=100),e+"("+i+r+")"}const Qe=/\b([a-z-]*)\(.*?\)/gu,tn={...Ze,getAnimatableNone:t=>{const e=t.match(Qe);return e?e.map(Je).join(" "):t}},en={borderWidth:Ie,borderTopWidth:Ie,borderRightWidth:Ie,borderBottomWidth:Ie,borderLeftWidth:Ie,borderRadius:Ie,radius:Ie,borderTopLeftRadius:Ie,borderTopRightRadius:Ie,borderBottomRightRadius:Ie,borderBottomLeftRadius:Ie,width:Ie,maxWidth:Ie,height:Ie,maxHeight:Ie,top:Ie,right:Ie,bottom:Ie,left:Ie,padding:Ie,paddingTop:Ie,paddingRight:Ie,paddingBottom:Ie,paddingLeft:Ie,margin:Ie,marginTop:Ie,marginRight:Ie,marginBottom:Ie,marginLeft:Ie,backgroundPositionX:Ie,backgroundPositionY:Ie},nn={rotate:Be,rotateX:Be,rotateY:Be,rotateZ:Be,scale:Me,scaleX:Me,scaleY:Me,scaleZ:Me,skew:Be,skewX:Be,skewY:Be,distance:Ie,translateX:Ie,translateY:Ie,translateZ:Ie,x:Ie,y:Ie,z:Ie,perspective:Ie,transformPerspective:Ie,opacity:Se,originX:Ne,originY:Ne,originZ:Ie},sn={...xe,transform:Math.round},rn={...en,...nn,zIndex:sn,size:Ie,fillOpacity:Se,strokeOpacity:Se,numOctaves:sn},on={...rn,color:$e,backgroundColor:$e,outlineColor:$e,fill:$e,stroke:$e,borderColor:$e,borderTopColor:$e,borderRightColor:$e,borderBottomColor:$e,borderLeftColor:$e,filter:tn,WebkitFilter:tn},an=t=>on[t];function ln(t,e){let n=an(t);return n!==tn&&(n=Ze),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const un=new Set(["auto","none","0"]);const cn=t=>180*t/Math.PI,hn=t=>{const e=cn(Math.atan2(t[1],t[0]));return pn(e)},dn={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:hn,rotateZ:hn,skewX:t=>cn(Math.atan(t[1])),skewY:t=>cn(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},pn=t=>((t%=360)<0&&(t+=360),t),fn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),mn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),gn={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:fn,scaleY:mn,scale:t=>(fn(t)+mn(t))/2,rotateX:t=>pn(cn(Math.atan2(t[6],t[5]))),rotateY:t=>pn(cn(Math.atan2(-t[2],t[0]))),rotateZ:hn,rotate:hn,skewX:t=>cn(Math.atan(t[4])),skewY:t=>cn(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function yn(t){return t.includes("scale")?1:0}function vn(t,e){if(!t||"none"===t)return yn(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,r;if(n)s=gn,r=n;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=dn,r=e}if(!r)return yn(e);const i=s[e],o=r[1].split(",").map(wn);return"function"==typeof i?i(o):o[i]}function wn(t){return parseFloat(t.trim())}const bn=t=>t===xe||t===Ie,Tn=new Set(["x","y","z"]),xn=Jt.filter((t=>!Tn.has(t)));const Sn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>vn(e,"x"),y:(t,{transform:e})=>vn(e,"y")};Sn.translateX=Sn.x,Sn.translateY=Sn.y;const Mn=new Set;let An=!1,Vn=!1;function kn(){if(Vn){const t=Array.from(Mn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return xn.forEach((n=>{const s=t.getValue(n);void 0!==s&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{t.getValue(e)?.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}Vn=!1,An=!1,Mn.forEach((t=>t.complete())),Mn.clear()}function Pn(){Mn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(Vn=!0)}))}class En{constructor(t,e,n,s,r,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=s,this.element=r,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Mn.add(this),An||(An=!0,$.read(Pn),$.resolveKeyframes(kn))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:s}=this;for(let r=0;r<t.length;r++)if(null===t[r])if(0===r){const r=s?.get(),i=t[t.length-1];if(void 0!==r)t[0]=r;else if(n&&e){const s=n.readValue(e,i);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=i),s&&void 0===r&&s.set(t[0])}else t[r]=t[r-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Mn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Mn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Cn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Fn=t=>e=>"string"==typeof e&&e.startsWith(t),On=Fn("--"),Rn=Fn("var(--"),Bn=t=>!!Rn(t)&&Ln.test(t.split("/*")[0].trim()),Ln=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,In=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Dn(t,e,n=1){const[s,r]=function(t){const e=In.exec(t);if(!e)return[,];const[,n,s,r]=e;return[`--${n??s}`,r]}(t);if(!s)return;const i=window.getComputedStyle(e).getPropertyValue(s);if(i){const t=i.trim();return Cn(t)?parseFloat(t):t}return Bn(r)?Dn(r,e,n+1):r}const Wn=t=>e=>e.test(t),Nn=[xe,Ie,Le,Be,We,De,{test:t=>"auto"===t,parse:t=>t}],Kn=t=>Nn.find(Wn(t));class $n extends En{constructor(t,e,n,s,r){super(t,e,n,s,r,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let n=0;n<t.length;n++){let s=t[n];if("string"==typeof s&&(s=s.trim(),Bn(s))){const r=Dn(s,e.current);void 0!==r&&(t[n]=r),n===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!te.has(n)||2!==t.length)return;const[s,r]=t,i=Kn(s),o=Kn(r);if(i!==o)if(bn(i)&&bn(o))for(let e=0;e<t.length;e++){const n=t[e];"string"==typeof n&&(t[e]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let e=0;e<t.length;e++)("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||Te(s))&&n.push(e);var s;n.length&&function(t,e,n){let s,r=0;for(;r<t.length&&!s;){const e=t[r];"string"==typeof e&&!un.has(e)&&Xe(e).values.length&&(s=t[r]),r++}if(s&&n)for(const r of e)t[r]=ln(n,s)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Sn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const s=e[e.length-1];void 0!==s&&t.getValue(n,s).jump(s,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:n}=this;if(!t||!t.current)return;const s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);const r=n.length-1,i=n[r];n[r]=Sn[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==i&&void 0===this.finalKeyframe&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,n])=>{t.getValue(e).set(n)})),this.resolveNoneKeyframes()}}const jn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Ze.test(t)&&"0"!==t||t.startsWith("url(")));function zn(t,e,n,s){const r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;const i=t[t.length-1],o=jn(r,e),a=jn(i,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||C(n))&&s)}const Yn=t=>null!==t;function Un(t,{repeat:e,repeatType:n="loop"},s){const r=t.filter(Yn),i=e&&"loop"!==n&&e%2==1?0:r.length-1;return i&&void 0!==s?s:r[i]}class Xn{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:i="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=H.now(),this.options={autoplay:t,delay:e,type:n,repeat:s,repeatDelay:r,repeatType:i,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(Pn(),kn()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=H.now(),this.hasAttemptedResolve=!0;const{name:n,type:s,velocity:r,delay:i,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!zn(t,n,s,r)){if(!i)return a&&a(Un(t,this.options,e)),o&&o(),void this.resolveFinishedPromise();this.options.duration=0}const u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear")}updateFinishedPromise(){this.currentFinishedPromise=new Promise((t=>{this.resolveFinishedPromise=t}))}}function Hn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function qn(t,e){return n=>n>0?e:t}const Gn=(t,e,n)=>{const s=t*t,r=n*(e*e-s)+s;return r<0?0:Math.sqrt(r)},Zn=[Oe,Fe,Ke];function _n(t){const e=(n=t,Zn.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let s=e.parse(t);return e===Ke&&(s=function({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,n/=100;let r=0,i=0,o=0;if(e/=100){const s=n<.5?n*(1+e):n+e-n*e,a=2*n-s;r=Hn(a,s,t+1/3),i=Hn(a,s,t),o=Hn(a,s,t-1/3)}else r=i=o=n;return{red:Math.round(255*r),green:Math.round(255*i),blue:Math.round(255*o),alpha:s}}(s)),s}const Jn=(t,e)=>{const n=_n(t),s=_n(e);if(!n||!s)return qn(t,e);const r={...n};return t=>(r.red=Gn(n.red,s.red,t),r.green=Gn(n.green,s.green,t),r.blue=Gn(n.blue,s.blue,t),r.alpha=Bt(n.alpha,s.alpha,t),Fe.transform(r))},Qn=(t,e)=>n=>e(t(n)),ts=(...t)=>t.reduce(Qn),es=new Set(["none","hidden"]);function ns(t,e){return n=>Bt(t,e,n)}function ss(t){return"number"==typeof t?ns:"string"==typeof t?Bn(t)?qn:$e.test(t)?Jn:os:Array.isArray(t)?rs:"object"==typeof t?$e.test(t)?Jn:is:qn}function rs(t,e){const n=[...t],s=n.length,r=t.map(((t,n)=>ss(t)(t,e[n])));return t=>{for(let e=0;e<s;e++)n[e]=r[e](t);return n}}function is(t,e){const n={...t,...e},s={};for(const r in n)void 0!==t[r]&&void 0!==e[r]&&(s[r]=ss(t[r])(t[r],e[r]));return t=>{for(const e in s)n[e]=s[e](t);return n}}const os=(t,e)=>{const n=Ze.createTransformer(e),s=Xe(t),r=Xe(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?es.has(t)&&!r.values.length||es.has(e)&&!s.values.length?function(t,e){return es.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):ts(rs(function(t,e){const n=[],s={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const i=e.types[r],o=t.indexes[i][s[i]],a=t.values[o]??0;n[r]=a,s[i]++}return n}(s,r),r.values),n):qn(t,e)};function as(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Bt(t,e,n);return ss(t)(t,e)}function ls({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:r=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let f=n*e;const m=h+f,g=void 0===o?m:o(m);g!==m&&(f=g-h);const y=t=>-f*Math.exp(-t/s),v=t=>g+y(t),w=t=>{const e=y(t),n=v(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let b,T;const x=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(b=t,T=Ct({keyframes:[d.value,p(d.value)],velocity:ut(v,t,d.value),damping:r,stiffness:i,restDelta:u,restSpeed:c}))};return x(0),{calculatedDuration:null,next:t=>{let e=!1;return T||void 0!==b||(e=!0,w(t),x(t)),void 0!==b&&t>=b?T.next(t-b):(!e&&w(t),d)}}}const us=he(.42,0,1,1),cs=he(0,0,.58,1),hs=he(.42,0,.58,1),ds={linear:i,easeIn:us,easeInOut:hs,easeOut:cs,circIn:ve,circInOut:be,circOut:we,backIn:me,backInOut:ge,backOut:fe,anticipate:ye},ps=t=>{if(T(t)){t.length;const[e,n,s,r]=t;return he(e,n,s,r)}return"string"==typeof t?ds[t]:t};function fs(t,e,{clamp:n=!0,ease:s,mixer:r}={}){const a=t.length;if(e.length,1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];const l=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());const u=function(t,e,n){const s=[],r=n||as,o=t.length-1;for(let n=0;n<o;n++){let o=r(t[n],t[n+1]);if(e){const t=Array.isArray(e)?e[n]||i:e;o=ts(t,o)}s.push(o)}return s}(e,s,r),c=u.length,h=n=>{if(l&&n<t[0])return e[0];let s=0;if(c>1)for(;s<t.length-2&&!(n<t[s+1]);s++);const r=o(t[s],t[s+1],n);return u[s](r)};return n?e=>h(lt(t[0],t[a-1],e)):h}function ms({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const r=Ot(s)?s.map(ps):ps(s),i={done:!1,value:e[0]},o=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:It(e),t),a=fs(o,e,{ease:Array.isArray(r)?r:(l=e,u=r,l.map((()=>u||hs)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(i.value=a(e),i.done=e>=t,i)}}const gs=t=>{const e=({timestamp:e})=>t(e);return{start:()=>$.update(e,!0),stop:()=>j(e),now:()=>z.isProcessing?z.timestamp:H.now()}},ys={decay:ls,inertia:ls,tween:ms,keyframes:ms,spring:Ct},vs=t=>t/100;class ws extends Xn{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:s,keyframes:r}=this.options,i=s?.KeyframeResolver||En;this.resolver=new i(r,((t,e)=>this.onKeyframesResolved(t,e)),e,n,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r,velocity:i=0}=this.options,o=C(e)?e:ys[e]||ms;let a,l;o!==ms&&"number"!=typeof t[0]&&(a=ts(vs,as(t[0],t[1])),t=[0,100]);const u=o({...this.options,keyframes:t});"mirror"===r&&(l=o({...this.options,keyframes:[...t].reverse(),velocity:-i})),null===u.calculatedDuration&&(u.calculatedDuration=L(u));const{calculatedDuration:c}=u,h=c+s;return{generator:u,mirroredGenerator:l,mapPercentToKeyframes:a,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(n+1)-s}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:s,generator:r,mirroredGenerator:i,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return r.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:f,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,w=r;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,f&&(n-=f/c)):"mirror"===p&&(w=i)),v=lt(0,1,n)*c}const b=y?{done:!1,value:a[0]}:w.next(v);o&&(b.value=o(b.value));let{done:T}=b;y||null===l||(T=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return x&&void 0!==s&&(b.value=Un(a,this.options,s)),m&&m(b.value),x&&this.finish(),b}get duration(){const{resolved:t}=this;return t?u(t.calculatedDuration):0}get time(){return u(this.currentTime)}set time(t){t=l(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=u(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=gs,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=n??this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){this._resolved?(this.state="paused",this.holdTime=this.currentTime??0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}get finished(){return this.currentFinishedPromise}}const bs=new Set(["opacity","clipPath","filter","transform"]),Ts=r((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));const xs={anticipate:ye,backInOut:ge,circInOut:be};class Ss extends Xn{constructor(t){super(t);const{name:e,motionValue:n,element:s,keyframes:r}=this.options;this.resolver=new $n(r,((t,e)=>this.onKeyframesResolved(t,e)),e,n,s),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:s,ease:r,type:i,motionValue:o,name:a,startTime:l}=this.options;if(!o.owner||!o.owner.current)return!1;var u;if("string"==typeof r&&M()&&r in xs&&(r=xs[r]),C((u=this.options).type)||"spring"===u.type||!D(u.ease)){const{onComplete:e,onUpdate:o,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new ws({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let s={done:!1,value:t[0]};const r=[];let i=0;for(;!s.done&&i<2e4;)s=n.sample(i),r.push(s.value),i+=10;return{times:void 0,keyframes:r,duration:i-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,s=c.times,r=c.ease,i="keyframes"}const c=E(o.owner.current,a,t,{...this.options,duration:n,times:s,ease:r});return c.startTime=l??this.calcStartTime(),this.pendingTimeline?(W(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:n}=this.options;o.set(Un(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:s,type:i,ease:r,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return u(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return u(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=l(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}get finished(){return this.resolved.animation.finished}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return i;const{animation:n}=e;W(n,t)}else this.pendingTimeline=t;return i}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:s,type:r,ease:i,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:a,element:u,...c}=this.options,h=new ws({...c,keyframes:n,duration:s,type:r,ease:i,times:o,isGenerator:!0}),d=l(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:r,damping:i,type:o}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Ts()&&n&&bs.has(n)&&("transform"!==n||!l)&&!a&&!s&&"mirror"!==r&&0!==i&&"inertia"!==o}}const Ms={type:"spring",stiffness:500,damping:25,restSpeed:10},As={type:"keyframes",duration:.8},Vs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ks=(t,{keyframes:e})=>e.length>2?As:Qt.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Ms:Vs;const Ps=(t,e,n,s={},r,i)=>o=>{const a=R(s,t)||{},u=a.delay||s.delay||0;let{elapsed:c=0}=s;c-=l(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:i?void 0:r};(function({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:r,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(a)||(h={...h,...ks(t,h)}),h.duration&&(h.duration=l(h.duration)),h.repeatDelay&&(h.repeatDelay=l(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),h.allowFlatten=!a.type&&!a.ease,d&&!i&&void 0!==e.get()){const t=Un(h.keyframes,a);if(void 0!==t)return $.update((()=>{h.onUpdate(t),h.onComplete()})),new p([])}return!i&&Ss.supports(h)?new Ss(h):new ws(h)};function Es({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,s}function Cs(t,e,{delay:n=0,transitionOverride:s,type:r}={}){let{transition:i=t.getDefaultTransition(),transitionEnd:o,...a}=e;s&&(i=s);const l=[],u=r&&t.animationState&&t.animationState.getState()[r];for(const e in a){const s=t.getValue(e,t.latestValues[e]??null),r=a[e];if(void 0===r||u&&Es(u,e))continue;const o={delay:n,...R(i||{},e)};let c=!1;if(window.MotionHandoffAnimation){const n=ue(t);if(n){const t=window.MotionHandoffAnimation(n,e,$);null!==t&&(o.startTime=t,c=!0)}}oe(t,e),s.start(Ps(e,s,r,t.shouldReduceMotion&&te.has(e)?{type:!1}:o,t,c));const h=s.animation;h&&l.push(h)}return o&&Promise.all(l).then((()=>{$.update((()=>{o&&ie(t,o)}))})),l}const Fs=()=>({x:{min:0,max:0},y:{min:0,max:0}}),Os={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Rs={};for(const t in Os)Rs[t]={isEnabled:e=>Os[t].some((t=>!!e[t]))};const Bs="undefined"!=typeof window,Ls={current:null},Is={current:!1};const Ds=[...Nn,$e,Ze];const Ws=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function Ns(t){return null!==(e=t.animate)&&"object"==typeof e&&"function"==typeof e.start||Ws.some((e=>function(t){return"string"==typeof t||Array.isArray(t)}(t[e])));var e}const Ks=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class $s{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:s,blockInitialAnimation:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=En,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=H.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,$.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=i;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=Boolean(r),this.isControllingVariants=Ns(e),this.isVariantNode=function(t){return Boolean(Ns(t)||t.variants)}(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const t in h){const e=h[t];void 0!==a[t]&&Dt(e)&&e.set(a[t],!1)}}mount(t){this.current=t,_t.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Is.current||function(){if(Is.current=!0,Bs)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Ls.current=t.matches;t.addListener(e),e()}else Ls.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Ls.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),j(this.notifyUpdate),j(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=Qt.has(t);n&&this.onBindTransform&&this.onBindTransform();const s=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&$.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),r=e.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{s(),r(),i&&i(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Rs){const e=Rs[t];if(!e)continue;const{isEnabled:n,Feature:s}=e;if(!this.features[t]&&s&&n(this.props)&&(this.features[t]=new s(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<Ks.length;e++){const n=Ks[e];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);const s=t["on"+n];s&&(this.propEventSubscriptions[n]=this.on(n,s))}this.prevMotionValues=function(t,e,n){for(const s in e){const r=e[s],i=n[s];if(Dt(r))t.addValue(s,r);else if(Dt(i))t.addValue(s,at(r,{owner:t}));else if(i!==r)if(t.hasValue(s)){const e=t.getValue(s);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{const e=t.getStaticValue(s);t.addValue(s,at(void 0!==e?e:r,{owner:t}))}}for(const s in n)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=at(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){let n=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=n&&("string"==typeof n&&(Cn(n)||Te(n))?n=parseFloat(n):(s=n,!Ds.find(Wn(s))&&Ze.test(e)&&(n=ln(t,e))),this.setBaseTarget(t,Dt(n)?n.get():n)),Dt(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let n;if("string"==typeof e||"object"==typeof e){const s=se(this.props,e,this.presenceContext?.custom);s&&(n=s[t])}if(e&&void 0!==n)return n;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Dt(s)?void 0!==this.initialValues[t]&&void 0===n?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new a),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class js extends $s{constructor(){super(...arguments),this.KeyframeResolver=$n}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Dt(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}const zs=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Ys={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Us=Jt.length;function Xs(t,e,n){const{style:s,vars:r,transformOrigin:i}=t;let o=!1,a=!1;for(const t in e){const n=e[t];if(Qt.has(t))o=!0;else if(On(t))r[t]=n;else{const e=zs(n,rn[t]);t.startsWith("origin")?(a=!0,i[t]=e):s[t]=e}}if(e.transform||(o||n?s.transform=function(t,e,n){let s="",r=!0;for(let i=0;i<Us;i++){const o=Jt[i],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=zs(a,rn[o]);l||(r=!1,s+=`${Ys[o]||o}(${t}) `),n&&(e[o]=t)}}return s=s.trim(),n?s=n(e,r?"":s):r&&(s="none"),s}(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=i;s.transformOrigin=`${t} ${e} ${n}`}}const Hs={offset:"stroke-dashoffset",array:"stroke-dasharray"},qs={offset:"strokeDashoffset",array:"strokeDasharray"};function Gs(t,e,n){return"string"==typeof t?t:Ie.transform(e+n*t)}function Zs(t,{attrX:e,attrY:n,attrScale:s,originX:r,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(Xs(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:f}=t;d.transform&&(f&&(p.transform=d.transform),delete d.transform),f&&(void 0!==r||void 0!==i||p.transform)&&(p.transformOrigin=function(t,e,n){return`${Gs(e,t.x,t.width)} ${Gs(n,t.y,t.height)}`}(f,void 0!==r?r:.5,void 0!==i?i:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==s&&(d.scale=s),void 0!==o&&function(t,e,n=1,s=0,r=!0){t.pathLength=1;const i=r?Hs:qs;t[i.offset]=Ie.transform(-s);const o=Ie.transform(e),a=Ie.transform(n);t[i.array]=`${o} ${a}`}(d,o,a,l,!1)}const _s=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Js(t,{style:e,vars:n},s,r){Object.assign(t.style,e,r&&r.getProjectionStyles(s));for(const e in n)t.style.setProperty(e,n[e])}const Qs={};function tr(t,{layout:e,layoutId:n}){return Qt.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!Qs[t]||"opacity"===t)}function er(t,e,n){const{style:s}=t,r={};for(const i in s)(Dt(s[i])||e.style&&Dt(e.style[i])||tr(i,t)||void 0!==n?.getValue(i)?.liveStyle)&&(r[i]=s[i]);return r}class nr extends js{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Fs,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(Qt.has(e)){const t=an(e);return t&&t.default||0}return e=_s.has(e)?e:ae(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return function(t,e,n){const s=er(t,e,n);for(const n in t)(Dt(t[n])||Dt(e[n]))&&(s[-1!==Jt.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]);return s}(t,e,n)}onBindTransform(){this.current&&!this.renderState.dimensions&&$.postRender(this.updateDimensions)}build(t,e,n){Zs(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,s){!function(t,e,n,s){Js(t,e,void 0,s);for(const n in e.attrs)t.setAttribute(_s.has(n)?n:ae(n),e.attrs[n])}(t,e,0,s)}mount(t){var e;this.isSVGTag="string"==typeof(e=t.tagName)&&"svg"===e.toLowerCase(),super.mount(t)}}class sr extends js{constructor(){super(...arguments),this.type="html",this.renderInstance=Js}readValueFromInstance(t,e){if(Qt.has(e))return((t,e)=>{const{transform:n="none"}=getComputedStyle(t);return vn(n,e)})(t,e);{const s=(n=t,window.getComputedStyle(n)),r=(On(e)?s.getPropertyValue(e):s[e])||0;return"string"==typeof r?r.trim():r}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return function(t,e){return function({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}(t,e)}build(t,e,n){Xs(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return er(t,e,n)}}class rr extends $s{constructor(){super(...arguments),this.type="object"}readValueFromInstance(t,e){if(function(t,e){return t in e}(e,t)){const n=t[e];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(t,e){delete e.output[t]}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}build(t,e){Object.assign(t.output,e)}renderInstance(t,{output:e}){Object.assign(t,e)}sortInstanceNodePosition(){return 0}}function ir(t){const e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=function(t){return t instanceof SVGElement&&"svg"!==t.tagName}(t)?new nr(e):new sr(e);n.mount(t),_t.set(t,n)}function or(t){const e=new rr({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});e.mount(t),_t.set(t,e)}function ar(t,e,n,s){const r=[];if(function(t,e){return Dt(t)||"number"==typeof t||"string"==typeof t&&!Wt(e)}(t,e))r.push(function(t,e,n){const s=Dt(t)?t:at(t);return s.start(Ps("",s,e,n)),s.animation}(t,Wt(e)&&e.default||e,n&&n.default||n));else{const i=Nt(t,e,s),o=i.length;for(let t=0;t<o;t++){const s=i[t],a=s instanceof Element?ir:or;_t.has(s)||a(s);const l=_t.get(s),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(t,o)),r.push(...Cs(l,{...e,transition:u},{}))}}return r}function lr(t,e,n){const s=[],r=function(t,{defaultTransition:e={},...n}={},s,r){const i=e.duration||.3,a=new Map,u=new Map,c={},h=new Map;let d=0,p=0,f=0;for(let n=0;n<t.length;n++){const o=t[n];if("string"==typeof o){h.set(o,p);continue}if(!Array.isArray(o)){h.set(o.name,$t(p,o.at,d,h));continue}let[a,m,g={}]=o;void 0!==g.at&&(p=$t(p,g.at,d,h));let y=0;const v=(t,n,s,o=0,a=0)=>{const u=Ht(t),{delay:c=0,times:h=It(u),type:d="keyframes",repeat:m,repeatType:g,repeatDelay:v=0,...w}=n;let{ease:b=e.ease||"easeOut",duration:T}=n;const x="function"==typeof c?c(o,a):c,S=u.length,M=C(d)?d:r?.[d];if(S<=2&&M){let t=100;if(2===S&&Zt(u)){const e=u[1]-u[0];t=Math.abs(e)}const e={...w};void 0!==T&&(e.duration=l(T));const n=I(e,t,M);b=n.ease,T=n.duration}T??(T=i);const A=p+x;1===h.length&&0===h[0]&&(h[1]=1);const V=h.length-u.length;if(V>0&&Lt(h,V),1===u.length&&u.unshift(null),m){T=Kt(T,m);const t=[...u],e=[...h];b=Array.isArray(b)?[...b]:[b];const n=[...b];for(let s=0;s<m;s++){u.push(...t);for(let r=0;r<t.length;r++)h.push(e[r]+(s+1)),b.push(0===r?"linear":Rt(n,r-1))}zt(h,m)}const k=A+T;jt(s,u,b,h,A,k),y=Math.max(x+T,y),f=Math.max(k,f)};if(Dt(a))v(m,g,Xt("default",Ut(a,u)));else{const t=Nt(a,m,s,c),e=t.length;for(let n=0;n<e;n++){const s=Ut(t[n],u);for(const t in m)v(m[t],qt(g,t),Xt(t,s),n,e)}}d=p,p+=y}return u.forEach(((t,s)=>{for(const r in t){const i=t[r];i.sort(Yt);const l=[],u=[],c=[];for(let t=0;t<i.length;t++){const{at:e,value:n,easing:s}=i[t];l.push(n),u.push(o(0,f,e)),c.push(s||"easeOut")}0!==u[0]&&(u.unshift(0),l.unshift(l[0]),c.unshift("easeInOut")),1!==u[u.length-1]&&(u.push(1),l.push(null)),a.has(s)||a.set(s,{keyframes:{},transition:{}});const h=a.get(s);h.keyframes[r]=l,h.transition[r]={...e,duration:f,ease:c,times:u,...n}}})),a}(t,e,n,{spring:Ct});return r.forEach((({keyframes:t,transition:e},n)=>{s.push(...ar(n,t,e))})),s}function ur(t){return function(e,n,s){let r=[];var i;i=e,r=Array.isArray(i)&&i.some(Array.isArray)?lr(e,n,t):ar(e,n,s,t);const o=new p(r);return t&&t.animations.push(o),o}}const cr=ur();const hr=t=>function(e,n,s){return new p(function(t,e,n,s){const r=_(t,s),i=r.length,o=[];for(let t=0;t<i;t++){const s=r[t],a={...n};"function"==typeof a.delay&&(a.delay=a.delay(t,i));for(const t in e){const n=e[t],r={...R(a,t)};r.duration&&(r.duration=l(r.duration)),r.delay&&(r.delay=l(r.delay)),o.push(new O({element:s,name:t,keyframes:n,transition:r,allowFlatten:!a.type&&!a.ease}))}}return o}(e,n,s,t))},dr=hr();function pr(t,e){let n;const s=()=>{const{currentTime:s}=e,r=(null===s?0:s.value)/100;n!==r&&t(r),n=r};return $.update(s,!0),()=>j(s)}const fr=new WeakMap;let mr;function gr({target:t,contentRect:e,borderBoxSize:n}){fr.get(t)?.forEach((s=>{s({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function yr(t){t.forEach(gr)}function vr(t,e){mr||"undefined"!=typeof ResizeObserver&&(mr=new ResizeObserver(yr));const n=_(t);return n.forEach((t=>{let n=fr.get(t);n||(n=new Set,fr.set(t,n)),n.add(e),mr?.observe(t)})),()=>{n.forEach((t=>{const n=fr.get(t);n?.delete(e),n?.size||mr?.unobserve(t)}))}}const wr=new Set;let br;function Tr(t){return wr.add(t),br||(br=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};wr.forEach((t=>t(e)))},window.addEventListener("resize",br)),()=>{wr.delete(t),!wr.size&&br&&(br=void 0)}}const xr={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Sr(t,e,n,s){const r=n[e],{length:i,position:a}=xr[e],l=r.current,u=n.time;r.current=t[`scroll${a}`],r.scrollLength=t[`scroll${i}`]-t[`client${i}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=o(0,r.scrollLength,r.current);const h=s-u;r.velocity=h>50?0:c(r.current-l,h)}const Mr={start:0,center:.5,end:1};function Ar(t,e,n=0){let s=0;if(t in Mr&&(t=Mr[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?s=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?s=e/100*document.documentElement.clientWidth:t.endsWith("vh")?s=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(s=e*t),n+s}const Vr=[0,0];function kr(t,e,n,s){let r=Array.isArray(t)?t:Vr,i=0,o=0;return"number"==typeof t?r=[t,t]:"string"==typeof t&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,Mr[t]?t:"0"]),i=Ar(r[0],n,s),o=Ar(r[1],e),i-o}const Pr={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Er={x:0,y:0};function Cr(t,e,n){const{offset:s=Pr.All}=n,{target:r=t,axis:i="y"}=n,o="y"===i?"height":"width",a=r!==t?function(t,e){const n={x:0,y:0};let s=t;for(;s&&s!==e;)if(s instanceof HTMLElement)n.x+=s.offsetLeft,n.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){const t=s.getBoundingClientRect();s=s.parentElement;const e=s.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(s instanceof SVGGraphicsElement))break;{const{x:t,y:e}=s.getBBox();n.x+=t,n.y+=e;let r=null,i=s.parentNode;for(;!r;)"svg"===i.tagName&&(r=i),i=s.parentNode;s=r}}return n}(r,t):Er,l=r===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(r),u={width:t.clientWidth,height:t.clientHeight};e[i].offset.length=0;let c=!e[i].interpolate;const h=s.length;for(let t=0;t<h;t++){const n=kr(s[t],u[o],l[o],a[i]);c||n===e[i].interpolatorOffsets[t]||(c=!0),e[i].offset[t]=n}c&&(e[i].interpolate=fs(e[i].offset,It(s),{clamp:!1}),e[i].interpolatorOffsets=[...e[i].offset]),e[i].progress=lt(0,1,e[i].interpolate(e[i].current))}function Fr(t,e,n,s={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let s=e;for(;s&&s!==t;)n.x.targetOffset+=s.offsetLeft,n.y.targetOffset+=s.offsetTop,s=s.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,s.target,n),update:e=>{!function(t,e,n){Sr(t,"x",e,n),Sr(t,"y",e,n),e.time=n}(t,n,e),(s.offset||s.target)&&Cr(t,n,s)},notify:()=>e(n)}}const Or=new WeakMap,Rr=new WeakMap,Br=new WeakMap,Lr=t=>t===document.documentElement?window:t;function Ir(t,{container:e=document.documentElement,...n}={}){let s=Br.get(e);s||(s=new Set,Br.set(e,s));const r=Fr(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(s.add(r),!Or.has(e)){const t=()=>{for(const t of s)t.measure()},n=()=>{for(const t of s)t.update(z.timestamp)},r=()=>{for(const t of s)t.notify()},a=()=>{$.read(t,!1,!0),$.read(n,!1,!0),$.update(r,!1,!0)};Or.set(e,a);const l=Lr(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&Rr.set(e,(o=a,"function"==typeof(i=e)?Tr(i):vr(i,o))),l.addEventListener("scroll",a,{passive:!0})}var i,o;const a=Or.get(e);return $.read(a,!1,!0),()=>{j(a);const t=Br.get(e);if(!t)return;if(t.delete(r),t.size)return;const n=Or.get(e);Or.delete(e),n&&(Lr(e).removeEventListener("scroll",n),Rr.get(e)?.(),window.removeEventListener("resize",n))}}const Dr=new Map;function Wr({source:t,container:e=document.documentElement,axis:n="y"}={}){t&&(e=t),Dr.has(e)||Dr.set(e,{});const s=Dr.get(e);return s[n]||(s[n]=h()?new ScrollTimeline({source:e,axis:n}):function({source:t,container:e,axis:n="y"}){t&&(e=t);const s={value:0},r=Ir((t=>{s.value=100*t[n].progress}),{container:e,axis:n});return{currentTime:s,cancel:r}}({source:e,axis:n})),s[n]}function Nr(t){return t&&(t.target||t.offset)}const Kr={some:0,all:1};const $r=(t,e)=>Math.abs(t-e);t.MotionValue=ot,t.animate=cr,t.animateMini=dr,t.anticipate=ye,t.backIn=me,t.backInOut=ge,t.backOut=fe,t.cancelFrame=j,t.circIn=ve,t.circInOut=be,t.circOut=we,t.clamp=lt,t.createScopedAnimate=ur,t.cubicBezier=he,t.delay=function(t,e){return function(t,e){const n=H.now(),s=({timestamp:r})=>{const i=r-n;i>=e&&(j(s),t(i-e))};return $.read(s,!0),()=>j(s)}(t,l(e))},t.distance=$r,t.distance2D=function(t,e){const n=$r(t.x,e.x),s=$r(t.y,e.y);return Math.sqrt(n**2+s**2)},t.easeIn=us,t.easeInOut=hs,t.easeOut=cs,t.frame=$,t.frameData=z,t.hover=function(t,e,n={}){const[s,r,i]=J(t,n),o=t=>{if(!Q(t))return;const{target:n}=t,s=e(n,t);if("function"!=typeof s||!n)return;const i=t=>{Q(t)&&(s(t),n.removeEventListener("pointerleave",i))};n.addEventListener("pointerleave",i,r)};return s.forEach((t=>{t.addEventListener("pointerenter",o,r)})),i},t.inView=function(t,e,{root:n,margin:s,amount:r="some"}={}){const i=_(t),o=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=o.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t.target,t);"function"==typeof n?o.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),o.delete(t.target))}))}),{root:n,rootMargin:s,threshold:"number"==typeof r?r:Kr[r]});return i.forEach((t=>a.observe(t))),()=>a.disconnect()},t.inertia=ls,t.interpolate=fs,t.invariant=n,t.isDragActive=Z,t.keyframes=ms,t.mirrorEasing=de,t.mix=as,t.motionValue=at,t.noop=i,t.pipe=ts,t.press=function(t,e,n={}){const[s,r,i]=J(t,n),o=t=>{const s=t.currentTarget;if(!it(t)||nt.has(s))return;nt.add(s);const i=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),it(t)&&nt.has(s)&&(nt.delete(s),"function"==typeof i&&i(t,{success:e}))},a=t=>{o(t,s===window||s===document||n.useGlobalTarget||tt(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return s.forEach((t=>{var e;(n.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const n=t.currentTarget;if(!n)return;const s=st((()=>{if(nt.has(n))return;rt(n,"down");const t=st((()=>{rt(n,"up")}));n.addEventListener("keyup",t,e),n.addEventListener("blur",(()=>rt(n,"cancel")),e)}));n.addEventListener("keydown",s,e),n.addEventListener("blur",(()=>n.removeEventListener("keydown",s)),e)})(t,r))),e=t,et.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),i},t.progress=o,t.reverseEasing=pe,t.scroll=function(t,{axis:e="y",...n}={}){const s={axis:e,...n};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)||Nr(e)?Ir((n=>{t(n[e.axis].progress,n)}),e):pr(t,Wr(e))}(t,s):function(t,e){if(t.flatten(),Nr(e))return t.pause(),Ir((n=>{t.time=t.duration*n[e.axis].progress}),e);{const n=Wr(e);return t.attachTimeline?t.attachTimeline(n,(t=>(t.pause(),pr((e=>{t.time=t.duration*e}),n)))):i}}(t,s)},t.scrollInfo=Ir,t.spring=Ct,t.stagger=function(t=.1,{startDelay:e=0,from:n=0,ease:s}={}){return(r,i)=>{const o="number"==typeof n?n:function(t,e){if("first"===t)return 0;{const n=e-1;return"last"===t?n:n/2}}(n,i),a=Math.abs(o-r);let l=t*a;if(s){const e=i*t;l=ps(s)(l/e)*e}return e+l}},t.steps=function(t,e="end"){return n=>{const s=(n="end"===e?Math.min(n,.999):Math.max(n,.001))*t,r="end"===e?Math.floor(s):Math.ceil(s);return lt(0,1,r/t)}},t.time=H,t.transform=function(...t){const e=!Array.isArray(t[0]),n=e?0:-1,s=t[0+n],r=t[1+n],i=t[2+n],o=t[3+n],a=fs(r,i,{mixer:(l=i[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...o});var l;return e?a(s):a},t.wrap=Ft}));
