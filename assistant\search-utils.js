// Search and Query Utilities for Assistant
export class SearchUtils {
  constructor(assistant) {
    this.assistant = assistant;
  }

  // Part search result formatting
  formatPartResults(parts, query) {
    if (parts.length === 0) {
      return `I couldn't find any parts matching "${query}". Please try a different search term or be more specific.`;
    }
    
    let html = `<div class="part-results">
      <p class="mb-2">I found ${parts.length} part(s) matching "${query}":</p>
      <div class="space-y-2">`;
      
    parts.slice(0, 5).forEach(part => {
      html += `
        <div class="bg-gray-50 p-2 rounded border border-gray-200">
          <div class="flex justify-between">
            <span class="font-bold text-blue-600">${part["Part Number"] || 'N/A'}</span>
            <span class="text-sm text-gray-500">${part["Stock Status"] || 'N/A'}</span>
          </div>
          <p class="text-sm">${part["Description"] || 'No description available'}</p>
          <div class="mt-1 text-xs text-gray-600 grid grid-cols-2 gap-x-2">
            <span>Supplier: ${part["Supplier"] || 'N/A'}</span>
            <span>Mfr: ${part["Manufacturer"] || 'N/A'}</span>
            <span>Supplier P/N: ${part["Supplier P/N"] || 'N/A'}</span>
            <span>Price: ${part["Default Price"] ? '$' + part["Default Price"] : 'N/A'}</span>
          </div>
        </div>
      `;
    });
    
    if (parts.length > 5) {
      html += `<p class="text-sm text-gray-500">Plus ${parts.length - 5} more part(s)...</p>`;
    }
    
    html += `</div></div>`;
    return html;
  }

  // Supplier parts list formatting
  formatSupplierParts(supplier, parts) {
    if (parts.length === 0) {
      return `I couldn't find any parts from supplier "${supplier}".`;
    }
    
    let html = `<div class="supplier-parts">
      <p class="mb-2">Here are parts from ${supplier} (showing ${Math.min(parts.length, 10)} of ${parts.length}):</p>
      <div class="space-y-2 max-h-60 overflow-y-auto border border-gray-200 rounded p-2">`;
      
    parts.slice(0, 10).forEach(part => {
      html += `
        <div class="bg-gray-50 p-2 rounded border border-gray-200">
          <div class="flex justify-between">
            <span class="font-bold text-blue-600">${part["Part Number"] || 'N/A'}</span>
            <span class="text-sm text-gray-500">${part["Stock Status"] || 'N/A'}</span>
          </div>
          <p class="text-sm">${part["Description"] || 'No description available'}</p>
        </div>
      `;
    });
    
    html += `</div></div>`;
    return html;
  }

  isSpecialQuestion(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Check for special case questions
    if (normalizedMessage === 'are you online?' || 
        normalizedMessage === 'are you online' || 
        normalizedMessage === 'online?') {
      return true;
    }
    
    if (normalizedMessage === 'who built you?' || 
        normalizedMessage === 'who built you' || 
        normalizedMessage === 'who made you?' || 
        normalizedMessage === 'who made you' ||
        normalizedMessage === 'who created you?' ||
        normalizedMessage === 'who created you') {
      return true;
    }

    // Check for help-related questions
    if (normalizedMessage === 'help' ||
        normalizedMessage === 'what can you do?' ||
        normalizedMessage === 'what can you help with?' ||
        normalizedMessage.includes('help me with')) {
      return true;
    }
    
    return false;
  }

  handleSpecialQuestion(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    if (normalizedMessage === 'are you online?' || 
        normalizedMessage === 'are you online' || 
        normalizedMessage === 'online?') {
      this.assistant.addMessage({
        role: 'assistant',
        content: "Yes, I'm online and ready to help!",
        timestamp: new Date()
      });
      return;
    }
    
    if (normalizedMessage === 'who built you?' || 
        normalizedMessage === 'who built you' || 
        normalizedMessage === 'who made you?' || 
        normalizedMessage === 'who made you' ||
        normalizedMessage === 'who created you?' ||
        normalizedMessage === 'who created you') {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I was built by Mahdi Ebadi to help with parts management and order tracking.",
        timestamp: new Date()
      });
      return;
    }

    if (normalizedMessage === 'help' ||
        normalizedMessage === 'what can you do?' ||
        normalizedMessage === 'what can you help with?' ||
        normalizedMessage.includes('help me with')) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I can help you with several tasks:\n\n" +
                 "• Finding parts by number, description, or supplier\n" +
                 "• Tracking orders (use format: 'Track PO12345' or 'Where is my Westech order?')\n" +
                 "• Searching Monday.com (use format: 'Monday PO12345')\n" +
                 "• Answering questions about Envent Engineering and products\n\n" +
                 "For the best results with Monday.com searches, simply type 'Monday' followed by the order number like 'Monday 28615' or with a type prefix like 'Monday PO28615'.",
        timestamp: new Date()
      });
      return;
    }
  }

  // PART SEARCH METHODS
  // Use AI for smarter part search detection
  async isPartSearchQuery(message) {
    // First check with traditional pattern matching for speed
    const partSearchKeywords = [
      'find part', 'search part', 'look for part', 'part number', 
      'find fitting', 'search for', 'can you find', 'description',
      'manufacturer', 'supplier', 'tubing', 'valve', 'connector', 'part',
      'price', 'cost of part', 'show me part', 'find product'
    ];
    
    const lowercaseMessage = message.toLowerCase();
    
    // Check for part numbers (format like 1000123 or similar patterns)
    const partNumberRegex = /\b\d{5,8}\b/;
    if (partNumberRegex.test(message)) return true;
    
    // Fast check with keywords
    const hasKeyword = partSearchKeywords.some(keyword => 
      lowercaseMessage.includes(keyword.toLowerCase())
    );
    
    if (hasKeyword) return true;
    
    // If the assistant has AI capabilities and is initialized, use it for intent detection
    if (this.assistant.ai && this.assistant.ai.initialized) {
      try {
        // Try to classify the intent using the basic AI service
        const intent = await this.assistant.ai.classifyIntent(message);
        return intent === 'search_parts';
      } catch (error) {
        console.error("Error classifying part search intent:", error);
      }
    }
    
    return false;
  }

  async handlePartSearch(message) {
    if (!this.assistant.partsData || this.assistant.partsData.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I'm sorry, but the parts database isn't available right now. Please try again later.",
        timestamp: new Date()
      });
      return;
    }

    // Extract search terms
    const searchTerms = this.extractSearchTerms(message);
    
    if (!searchTerms || searchTerms.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "What specific part are you looking for? Please provide a part number, description, or manufacturer details.",
        timestamp: new Date()
      });
      return;
    }

    // Search for parts - using both traditional and semantic search if AI is available
    const results = await this.searchParts(searchTerms, message);
    
    // Update conversation context
    this.assistant.updateConversationContext('partSearch', {
      searchTerms: searchTerms,
      resultCount: results.length
    });
    
    // Return formatted results
    this.assistant.addMessage({
      role: 'assistant',
      content: {
        type: 'partResults',
        parts: results,
        query: searchTerms.join(' ')
      },
      timestamp: new Date()
    });
  }

  extractSearchTerms(message) {
    // Check for direct part number
    const partNumberMatch = message.match(/\b(\d{5,8})\b/);
    if (partNumberMatch) {
      return [partNumberMatch[1]];
    }
    
    // Remove common phrases that aren't relevant to the search
    const cleanMessage = message
      .replace(/can you|please|find|search for|look for|i need|i'm looking for|do you have|is there/gi, '')
      .trim();
    
    // Split into words, filter out common words and keep terms with 3+ characters
    return cleanMessage
      .split(/\s+/)
      .filter(term => 
        term.length >= 3 && 
        !['the', 'and', 'for', 'with', 'part', 'parts', 'that', 'this', 'from'].includes(term.toLowerCase())
      );
  }

  async searchParts(searchTerms, originalQuery = '') {
    // Track this search
    if (originalQuery) {
      this.assistant.recentSearches.push({
        query: originalQuery,
        timestamp: new Date()
      });
      // Keep only the last 10 searches
      if (this.assistant.recentSearches.length > 10) {
        this.assistant.recentSearches.shift();
      }
    }
    
    let results = [];
    
    // Try to use the AI for semantic search if available
    if (this.assistant.ai && this.assistant.ai.initialized) {
      try {
        // Use the simple AI embeddings to perform semantic search
        results = await this.assistant.ai.semanticSearch(
          originalQuery || searchTerms.join(' '), 
          this.assistant.partsData,
          (part) => {
            const textExtractor = (part) => {
              return [
                part["Part Number"] || '', 
                part["Description"] || '', 
                part["Manufacturer"] || '', 
                part["Supplier"] || '',
                part["Part Type"] || '',
                part["Notes"] || ''
              ].join(' ');
            };
            return textExtractor(part);
          },
          10 // top 10 results
        );
        
        // If we got results from semantic search, use them
        if (results && results.length > 0) {
          console.log("Using semantic search results:", results.length);
          return results;
        }
      } catch (error) {
        console.error("Error in semantic search:", error);
        // Fall back to traditional search
      }
    }
    
    // Fallback to traditional search if semantic search failed or returned no results
    results = this.traditionalSearch(searchTerms);
    return results;
  }
  
  // Traditional keyword-based search with scoring
  traditionalSearch(searchTerms) {
    // Convert search terms to lowercase and create regex patterns
    const patterns = searchTerms.map(term => new RegExp(term.toLowerCase(), 'i'));
    
    // Search for parts with scoring to prioritize better matches
    const scoredResults = this.assistant.partsData
      .filter(part => {
        // Skip if part is not an object or null
        if (!part || typeof part !== 'object') return false;
        
        // Check if any pattern matches any searchable field
        return patterns.some(pattern => {
          // Search in relevant fields
          const searchableFields = [
            "Part Number", "Description", "Supplier", "Supplier P/N",
            "Manufacturer", "Country of Origin", "HS Code"
          ];
          
          return searchableFields.some(field => 
            part[field] && pattern.test(part[field].toString())
          );
        });
      })
      .map(part => {
        // Calculate a relevance score
        let score = 0;
        
        // Check each search term against each field
        searchTerms.forEach(term => {
          const pattern = new RegExp(term.toLowerCase(), 'i');
          
          // Part number is highest priority
          if (part["Part Number"] && pattern.test(part["Part Number"])) {
            score += 100;
          }
          
          // Supplier P/N is high priority
          if (part["Supplier P/N"] && pattern.test(part["Supplier P/N"])) {
            score += 80;
          }
          
          // Description is medium priority
          if (part["Description"] && pattern.test(part["Description"])) {
            score += 50;
          }
          
          // Other fields are lower priority
          ["Supplier", "Manufacturer"].forEach(field => {
            if (part[field] && pattern.test(part[field])) {
              score += 30;
            }
          });
        });
        
        return { part, score };
      })
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score) // Sort by descending score
      .map(item => item.part); // Extract just the part objects
    
    return scoredResults.slice(0, 10); // Limit to 10 results
  }

  // SUPPLIER QUERY METHODS
  // Check if message is asking about a supplier
  async isSupplierQuery(message) {
    const supplierKeywords = [
      'supplier', 'vendor', 'company', 'manufacturer', 
      'distributor', 'parts from', 'supplied by', 'make',
      'who makes', 'who supplies', 'oem'
    ];
    
    const lowercaseMessage = message.toLowerCase();
    
    // Fast check with keywords
    const hasKeyword = supplierKeywords.some(keyword => 
      lowercaseMessage.includes(keyword.toLowerCase())
    );
    
    // If we have suppliers list and query mentions a supplier name
    const suppliers = this.getUniqueSuppliers();
    const mentionsSupplier = suppliers.some(supplier => 
      lowercaseMessage.includes(supplier.toLowerCase())
    );
    
    if (hasKeyword || mentionsSupplier) return true;
    
    // If the assistant has AI capabilities and is initialized, use it for intent detection
    if (this.assistant.ai && this.assistant.ai.initialized) {
      try {
        // First, check if it's a supplier intent
        const intent = await this.assistant.ai.classifyIntent(message);
        if (intent === 'supplier_inquiry') return true;
        
        // Then, try to extract any company names using NER
        const entities = await this.assistant.ai.extractEntities(message);
        if (entities && entities.companies && entities.companies.length > 0) {
          console.log("Found company entities:", entities.companies);
          
          // Check if any of the extracted companies match our known suppliers
          const extractedCompanies = entities.companies.map(c => c.toLowerCase());
          const matchedSuppliers = suppliers.some(supplier => 
            extractedCompanies.includes(supplier.toLowerCase())
          );
          
          if (matchedSuppliers) return true;
        }
      } catch (error) {
        console.error("Error in supplier query detection:", error);
      }
    }
    
    return false;
  }

  handleSupplierQuery(message) {
    if (!this.assistant.partsData || this.assistant.partsData.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I'm sorry, but the parts database isn't available right now. Please try again later.",
        timestamp: new Date()
      });
      return;
    }

    // Check if there's a confirmation request in the message
    if ((message.toLowerCase().includes('yes') || 
         message.toLowerCase().includes('show') || 
         message.toLowerCase().includes('list')) && 
        this.assistant.lastSupplierQuery) {
      // User confirmed they want to see all parts from this supplier
      const parts = this.getPartsBySupplier(this.assistant.lastSupplierQuery);
      
      this.assistant.addMessage({
        role: 'assistant',
        content: {
          type: 'supplierParts',
          supplier: this.assistant.lastSupplierQuery,
          parts: parts
        },
        timestamp: new Date()
      });
      
      // Update conversation context
      this.assistant.updateConversationContext('supplierSearch', {
        supplier: this.assistant.lastSupplierQuery,
        resultCount: parts.length
      });
      
      // Clear the last supplier query
      this.assistant.lastSupplierQuery = null;
      return;
    }
    
    // Extract supplier name
    const supplierName = this.extractSupplierName(message);
    
    if (!supplierName) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "Which supplier or manufacturer are you interested in?",
        timestamp: new Date()
      });
      return;
    }

    // Count parts from this supplier
    const partCount = this.countPartsBySupplier(supplierName);
    
    if (partCount === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: `I couldn't find any parts from supplier "${supplierName}". Please check the spelling or try another supplier.`,
        timestamp: new Date()
      });
    } else {
      // Ask if user wants to see all parts
      this.assistant.lastSupplierQuery = supplierName;
      this.assistant.addMessage({
        role: 'assistant',
        content: `I found ${partCount} parts from ${supplierName}. Would you like to see them all?`,
        timestamp: new Date()
      });
    }
  }

  extractSupplierName(message) {
    // Common patterns for supplier queries
    const patterns = [
      /from\s+([\w\s&]+)(?:\s|$)/i,            // parts from Supplier
      /by\s+([\w\s&]+)(?:\s|$)/i,              // parts by Supplier
      /manufacturer\s+([\w\s&]+)(?:\s|$)/i,     // manufacturer Supplier
      /supplier\s+([\w\s&]+)(?:\s|$)/i,         // supplier Supplier
      /([\w\s&]+)\s+parts/i                    // Supplier parts
    ];
    
    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match && match[1] && match[1].length > 2) {
        // Filter out common words that might be captured but aren't supplier names
        const term = match[1].trim();
        if (!['part', 'parts', 'all', 'any', 'the', 'this', 'that', 'show'].includes(term.toLowerCase())) {
          return term;
        }
      }
    }
    
    // If no match found with patterns, look for known supplier names
    const knownSuppliers = this.getUniqueSuppliers();
    for (const supplier of knownSuppliers) {
      if (message.toLowerCase().includes(supplier.toLowerCase())) {
        return supplier;
      }
    }
    
    return null;
  }

  getUniqueSuppliers() {
    if (!this.assistant.partsData || !Array.isArray(this.assistant.partsData)) {
      return [];
    }
    
    // Extract unique supplier names
    const suppliers = new Set();
    this.assistant.partsData.forEach(part => {
      if (part && part.Supplier) {
        suppliers.add(part.Supplier);
      }
    });
    
    return Array.from(suppliers);
  }

  countPartsBySupplier(supplierName) {
    if (!this.assistant.partsData || !Array.isArray(this.assistant.partsData) || !supplierName) {
      return 0;
    }
    
    const regex = new RegExp(supplierName, 'i');
    return this.assistant.partsData.filter(part => 
      part && part.Supplier && regex.test(part.Supplier)
    ).length;
  }

  getPartsBySupplier(supplierName) {
    if (!this.assistant.partsData || !Array.isArray(this.assistant.partsData) || !supplierName) {
      return [];
    }
    
    const regex = new RegExp(supplierName, 'i');
    return this.assistant.partsData.filter(part => 
      part && part.Supplier && regex.test(part.Supplier)
    );
  }

  // ORDER TRACKING METHODS
  isOrderTrackingQuery(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Check for order tracking keywords
    const trackingKeywords = [
      'track', 'where is', 'status of', 'order', 'shipment', 
      'tracking', 'package', 'delivery', 'shipped', 'find order'
    ];
    
    // If any of these keywords are in the message, it might be an order tracking query
    const hasTrackingKeywords = trackingKeywords.some(keyword => 
      normalizedMessage.includes(keyword)
    );
    
    // Check for order number patterns (SO#, PO#, any number sequence)
    const orderNumberPatterns = [
      /\b[A-Za-z]{2}#?\s*\d+\b/i,  // SO#12345, PO#12345, PT 12345, SH#12345
      /\border\s*(?:number|#)?\s*(\d+)/i,  // order number 12345, order #12345, order 12345
      /\b\d{4,6}\b/  // Just plain 4-6 digit numbers
    ];
    
    const hasOrderNumber = orderNumberPatterns.some(pattern => 
      pattern.test(normalizedMessage)
    );
    
    // Check if the message has a customer or company name that might be in our data
    const hasCompanyName = this.messageContainsCompanyName(normalizedMessage);
    
    // If it has tracking keywords AND (an order number OR a company name), it's likely a tracking query
    return (hasTrackingKeywords && (hasOrderNumber || hasCompanyName)) || 
           (hasTrackingKeywords && normalizedMessage.length < 20);  // Short tracking queries
  }

  messageContainsCompanyName(message) {
    if (!this.assistant.historyData || this.assistant.historyData.length === 0) return false;
    
    // Extract company and customer names from history data
    const companyNames = new Set();
    
    this.assistant.historyData.forEach(record => {
      if (record["Customer Name"]) companyNames.add(record["Customer Name"].toLowerCase());
      if (record["Company Name"]) companyNames.add(record["Company Name"].toLowerCase());
    });
    
    // Check if any company name appears in the message
    return Array.from(companyNames).some(name => 
      name.length > 3 && message.includes(name)
    );
  }

  async handleOrderTracking(message) {
    // Extract order information
    const orderInfo = this.extractOrderInfo(message);
    
    // Fall back to local history search
    if (!this.assistant.historyData || this.assistant.historyData.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I'm sorry, but I don't have access to order history data at the moment. Please try again later or try searching on Monday.com directly.",
        timestamp: new Date()
      });
      return;
    }

    const { orderType, orderNumber, companyName } = orderInfo;
    
    if (!orderNumber && !companyName) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I couldn't identify an order number or company name. Please provide a valid order number (e.g., SO#12345, 28615) or customer name.",
        timestamp: new Date()
      });
      return;
    }

    // Search for order in local history
    const order = this.findOrder(orderNumber, orderType, companyName);
    
    // Update conversation context
    if (order) {
      this.assistant.updateConversationContext('orderTracking', {
        lastSearchedOrder: orderNumber || order["Order Number"] || order["Reference Number"],
        orderType: orderType || order["Order Type"]
      });
    }
    
    // Return tracking information
    this.assistant.addMessage({
      role: 'assistant',
      content: {
        type: 'orderTracking',
        order: order
      },
      timestamp: new Date()
    });
  }

  extractOrderInfo(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Look for order type prefix + number pattern (e.g., PO12345, SO-12345, SH 12345)
    const prefixPattern = /\b(po|so|sh)[ #-]*(\d{4,6})\b/i;
    const prefixMatch = normalizedMessage.match(prefixPattern);
    
    let orderType = null;
    let orderNumber = null;
    
    if (prefixMatch) {
      orderType = prefixMatch[1].toUpperCase();
      orderNumber = prefixMatch[2];
    } else {
      // Try to find just a number that could be an order number
      const numberPattern = /\border(?:[ #-]*(?:number|#))?[ #-]*(\d{4,6})\b|\b(\d{4,6})\b/i;
      const numberMatch = normalizedMessage.match(numberPattern);
      
      if (numberMatch) {
        orderNumber = numberMatch[1] || numberMatch[2];
        
        // Try to infer order type from context
        if (normalizedMessage.includes('purchase') || normalizedMessage.includes('vendor')) {
          orderType = 'PO';
        } else if (normalizedMessage.includes('sales')) {
          orderType = 'SO';
        } else if (normalizedMessage.includes('ship') || normalizedMessage.includes('package')) {
          orderType = 'SH';
        }
      }
    }
    
    // Try to extract company name
    let companyName = null;
    if (this.assistant.historyData && this.assistant.historyData.length > 0) {
      const companyNames = new Set();
      
      this.assistant.historyData.forEach(record => {
        if (record["Customer Name"]) companyNames.add(record["Customer Name"].toLowerCase());
        if (record["Company Name"]) companyNames.add(record["Company Name"].toLowerCase());
      });
      
      // Find the longest matching company name in the message
      let longestMatch = "";
      companyNames.forEach(name => {
        if (name.length > 3 && normalizedMessage.includes(name) && name.length > longestMatch.length) {
          longestMatch = name;
        }
      });
      
      if (longestMatch) {
        companyName = longestMatch;
      }
    }
    
    return { orderType, orderNumber, companyName };
  }

  findOrder(orderNumber, orderType, companyName) {
    if (!this.assistant.historyData || !Array.isArray(this.assistant.historyData)) {
      return null;
    }

    // Build an array of potential match functions to try
    const matchFunctions = [];
    
    // If we have an order number, add number-based matching functions
    if (orderNumber) {
      // Exact Reference Number match
      matchFunctions.push(record => 
        record["Reference Number"] && record["Reference Number"].toString() === orderNumber
      );
      
      // Exact Sales Order Number match
      matchFunctions.push(record => 
        record["Sales Order Number"] && record["Sales Order Number"].toString() === orderNumber
      );
      
      // Exact Order Number match
      matchFunctions.push(record => 
        record["Order Number"] && record["Order Number"].toString() === orderNumber
      );
      
      // Partial Reference Number match
      matchFunctions.push(record => 
        record["Reference Number"] && record["Reference Number"].toString().includes(orderNumber)
      );
      
      // Partial Sales Order Number match
      matchFunctions.push(record => 
        record["Sales Order Number"] && record["Sales Order Number"].toString().includes(orderNumber)
      );
      
      // Partial Order Number match
      matchFunctions.push(record => 
        record["Order Number"] && record["Order Number"].toString().includes(orderNumber)
      );
    }
    
    // If we have an order type, add type-based matching
    if (orderType) {
      matchFunctions.push(record => 
        record["Order Type"] && record["Order Type"].toString().toUpperCase() === orderType
      );
    }
    
    // If we have a company name, add company-based matching
    if (companyName) {
      // Customer Name exact match
      matchFunctions.push(record => 
        record["Customer Name"] && record["Customer Name"].toLowerCase() === companyName
      );
      
      // Company Name exact match
      matchFunctions.push(record => 
        record["Company Name"] && record["Company Name"].toLowerCase() === companyName
      );
      
      // Customer Name contains match
      matchFunctions.push(record => 
        record["Customer Name"] && record["Customer Name"].toLowerCase().includes(companyName)
      );
      
      // Company Name contains match
      matchFunctions.push(record => 
        record["Company Name"] && record["Company Name"].toLowerCase().includes(companyName)
      );
    }
    
    // Try each match function until we find a matching record
    for (const matchFn of matchFunctions) {
      const match = this.assistant.historyData.find(matchFn);
      if (match) return match;
    }
    
    return null;
  }

  // KNOWLEDGE QUESTION METHODS
  async isKnowledgeQuestion(message) {
    // First try simple heuristics
    const lowercaseMessage = message.toLowerCase();
    
    const questionStarters = [
      'what is', 'how do', 'how does', 'why is', 'when was', 
      'who is', 'where can', 'can you', 'tell me about'
    ];
    
    const isQuestion = questionStarters.some(starter => 
      lowercaseMessage.startsWith(starter) || 
      lowercaseMessage.includes(' ' + starter + ' ')
    ) || message.includes('?');
    
    // If the assistant has AI capabilities and is initialized, use it for intent detection
    if (this.assistant.ai && this.assistant.ai.initialized) {
      try {
        // Try to classify the intent using AI
        const intent = await this.assistant.ai.classifyIntent(message);
        return intent === 'technical_question';
      } catch (error) {
        console.error("Error classifying knowledge question intent:", error);
      }
    }
    
    return isQuestion;
  }
  
  async handleKnowledgeQuestion(message) {
    if (!this.assistant.knowledgeBase || this.assistant.knowledgeBase.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I don't have any knowledge base information available at the moment.",
        timestamp: new Date()
      });
      return;
    }
    
    const keywords = this.extractKeywords(message);
    let relevantInfo = this.searchKnowledgeBase(keywords);
    
    // No results from keyword search, try with AI if available
    if (relevantInfo.length === 0 && this.assistant.ai && this.assistant.ai.initialized) {
      try {
        // Use QA capabilities from AI
        const answer = await this.assistant.ai.answerQuestion(
          message,
          this.assistant.knowledgeBase.map(item => item.content).join(' ')
        );
        
        if (answer && answer.trim().length > 0) {
          this.assistant.addMessage({
            role: 'assistant',
            content: answer,
            timestamp: new Date()
          });
          return;
        }
      } catch (error) {
        console.error("Error using AI for knowledge questions:", error);
        // Fall back to standard approach
      }
    }
    
    if (relevantInfo.length === 0) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "I don't have specific information about that in my knowledge base. Can you try rephrasing your question?",
        timestamp: new Date()
      });
      return;
    }
  }

  extractKeywords(message) {
    // Extract meaningful keywords from the message
    const stopWords = ["a", "an", "the", "this", "that", "these", "those", "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did", "can", "could", "will", "would", "shall", "should", "may", "might", "must", "for", "of", "about", "with", "in", "on", "at", "by", "to", "from", "as", "into", "during", "until", "while", "throughout", "through", "because", "since", "so", "although", "even", "if", "unless", "whereas", "whether"];
    
    return message
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.includes(word));
  }

  searchKnowledgeBase(keywords) {
    if (!this.assistant.knowledgeBase || !this.assistant.knowledgeBase.content) return null;
    
    // Search through knowledge base content
    const content = this.assistant.knowledgeBase.content;
    const matches = [];
    
    // Search each section
    for (const sectionKey in content) {
      const section = content[sectionKey];
      
      // If section is a string, search directly
      if (typeof section === 'string') {
        if (this.contentMatchesKeywords(section, keywords)) {
          matches.push(section);
        }
      } 
      // If section is an object, search its subsections
      else if (typeof section === 'object') {
        for (const subsectionKey in section) {
          const subsection = section[subsectionKey];
          
          // Simple string subsection
          if (typeof subsection === 'string') {
            if (this.contentMatchesKeywords(subsection, keywords)) {
              matches.push(subsection);
            }
          } 
          // Deeper nested content
          else if (typeof subsection === 'object') {
            for (const key in subsection) {
              const content = subsection[key];
              if (typeof content === 'string' && this.contentMatchesKeywords(content, keywords)) {
                matches.push(content);
              }
            }
          }
        }
      }
    }
    
    // Return the most relevant match (shortest one that contains all keywords)
    if (matches.length > 0) {
      matches.sort((a, b) => a.length - b.length);
      return matches[0];
    }
    
    return null;
  }

  contentMatchesKeywords(content, keywords) {
    if (!content || !keywords || keywords.length === 0) return false;
    
    const contentLower = content.toLowerCase();
    return keywords.every(keyword => contentLower.includes(keyword));
  }
}