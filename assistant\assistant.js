// Assistant Component - <PERSON> Chatbot Interface with Parts Database and Monday.com Integration
import { MondayApiService } from './monday-api.js';
import { SearchUtils } from './search-utils.js';
import { connectionManager } from '../core/connection.js';
import { partImageHelper } from '../masterparts/part-image-helper.js';
import { AiService } from './ai-service.js';

export class AssistantComponent {
  constructor(container) {
    this.container = container;
    this.messages = [];
    this.isWaitingForResponse = false;
    this.typingTimeout = null;
    this.partsData = null;
    this.user = null;
    this.historyData = null;
    this.knowledgeBase = null;
    this.qaData = null;
    this.lastSupplierQuery = null;
    this.mondayApiKey = null;
    this.mondayBoards = {};
    this.recentSearches = []; // Track recent searches to improve responses
    this.conversationContext = {}; // Store conversation context
    this.greetingMessages = [
      "Hello! How can I help you find parts today?",
      "Hi there! I'm the Envent Bridge Assistant. What can I help you with?",
      "Welcome! Looking for specific parts or order information?",
      "Greetings! How may I assist you with your parts management needs?"
    ];
    
    // Initialize service classes
    this.mondayService = new MondayApiService(this);
    this.searchUtils = new SearchUtils(this);
    
    // Initialize AI service
    this.ai = new AiService();
    this.aiInitialized = false;
    
    // Predefined questions for the question list
    this.predefinedQuestions = [
      // Part Search Questions
      "Find parts by Swagelok",
      "Search for SST tubing parts",
      "What's the price of part 1000000?",
      "Show me valves from Parker",
      "Do we have any fittings in stock?",
      "Show image for part 1000000",
      "Send the image for 1500000",
      
      // Order Tracking Questions
      "Track order 28615",
      "Where is my order for United Filtration?",
      "Check status of PO29109",
      "Show shipment to Westech Industrial",
      "What's the tracking number for H2 Solutions order?",
      
      // Monday.com Specific Questions
      "Search Monday.com for order 28615",
      "Find order in Monday.com for Westech",
      "Track my Monday order SO9056",
      "Look up PO29109 in Monday.com",
      "Get shipping details from Monday for United Filtration",
      
      // Company Knowledge Questions
      "When was Envent Engineering founded?",
      "What is the primary focus of Envent Engineering?",
      "What gases do Envent's instruments measure?",
      "What is one of Envent's flagship product lines?",
      "Which technology is central to the H₂S analyzers?",
      
      // Technical Questions
      "What purpose do Gas Chromatographs serve?",
      "What is the primary function of the M-Series Monitors?",
      "How do Tunable Filter Spectrometers work?",
      "How long can the sensing tape remain effective?",
      "What response time does the Rapid Response Algorithm have?",
      
      // Basic Questions
      "Who built you?",
      "Are you online?",
      "What can you help me with?",
      "Show me available suppliers",
      "Find products in stock",
      
      // Attribute Update Questions
      "Update stock item 1000000",
      "Update HS CODE for part 1500000",
      "Update Country of Origin for parts 1000001,1000002,1000003",
      
      // Math & Calculation Questions
      "What is 2x2?",
      "Calculate 15% of 250",
      "How many inches in 3.5 feet?",
      "Convert 10kg to pounds"
    ];
  }

  async init() {
    console.log("Initializing Assistant...");
    
    // Load user data
    await this.loadUserData();
    
    // Load parts database
    await this.loadPartsData();
    
    // Load history data for order tracking
    await this.loadHistoryData();
    
    // Load knowledge base from JSON files
    await this.loadKnowledgeBase();
    
    // Setup Monday.com API if available
    await this.mondayService.setupMondayApi();
    
    // Initialize AI capabilities in the background
    this.initAI();
    
    // Render the chat interface
    this.render();
    this.setupEventListeners();
    
    // Add initial greeting message
    this.addMessage({
      role: 'assistant',
      content: this.getPersonalizedGreeting(),
      timestamp: new Date()
    });
    
    this.scrollToBottom();
    console.log("Assistant initialization complete");
  }
  
  // Initialize AI capabilities in the background
  async initAI() {
    try {
      const aiInitialized = await this.ai.init();
      this.aiInitialized = aiInitialized;
      
      if (aiInitialized) {
        console.log("Simple AI initialized successfully");
        
        // Update UI to show AI is active
        const aiStatusIndicator = document.getElementById('aiStatusIndicator');
        if (aiStatusIndicator) {
          aiStatusIndicator.classList.remove('hidden');
          aiStatusIndicator.classList.add('text-yellow-600');
          aiStatusIndicator.classList.remove('text-green-600', 'text-blue-500');
          aiStatusIndicator.setAttribute('title', 'Basic AI capabilities active');
          aiStatusIndicator.querySelector('span').textContent = 'AI (Basic)';
        }
      } else {
        console.warn("AI initialization failed");
      }
    } catch (error) {
      console.error("Error initializing AI:", error);
    }
  }

  async loadUserData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['user'], (result) => {
        if (result.user) {
          this.user = result.user;
          console.log(`Loaded user data: ${this.user["User Name"]}`);
        } else {
          console.warn("No user data found in storage");
        }
        resolve();
      });
    });
  }

  async loadPartsData() {
    try {
      // First try to load from chrome.storage
      return new Promise((resolve) => {
        chrome.storage.local.get(['masterparts'], (result) => {
          if (result.masterparts && Array.isArray(result.masterparts) && result.masterparts.length > 0) {
            this.partsData = result.masterparts;
            console.log(`Loaded ${this.partsData.length} parts from storage`);
            resolve(true);
          } else {
            // If not in storage, request from background script
            chrome.runtime.sendMessage(
              { action: "loadMasterPartsData" },
              (response) => {
                if (chrome.runtime.lastError) {
                  console.error("Error loading parts data:", chrome.runtime.lastError);
                  resolve(false);
                  return;
                }
                
                if (response && response.success && response.data) {
                  this.partsData = response.data;
                  console.log(`Loaded ${this.partsData.length} parts from background script`);
                  resolve(true);
                } else {
                  console.error("Failed to load parts data");
                  resolve(false);
                }
              }
            );
          }
        });
      });
    } catch (error) {
      console.error("Error loading parts data:", error);
      this.partsData = [];
      return false;
    }
  }

  async loadHistoryData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['analyticsHistoryData'], (result) => {
        if (result.analyticsHistoryData && Array.isArray(result.analyticsHistoryData)) {
          this.historyData = result.analyticsHistoryData;
          console.log(`Loaded ${this.historyData.length} shipping records from history`);
        } else {
          console.warn("No history data found in storage");
          this.historyData = [];
        }
        resolve();
      });
    });
  }

  async loadKnowledgeBase() {
    try {
      // Load company information (1.json)
      const companyInfoUrl = chrome.runtime.getURL('json/1.json');
      const companyInfoResponse = await fetch(companyInfoUrl);
      if (companyInfoResponse.ok) {
        this.knowledgeBase = await companyInfoResponse.json();
        console.log("Loaded company knowledge base from 1.json");
      } else {
        console.warn("Failed to load company information from 1.json");
      }

      // Load Q&A pairs (2.json)
      const qaUrl = chrome.runtime.getURL('json/2.json');
      const qaResponse = await fetch(qaUrl);
      if (qaResponse.ok) {
        this.qaData = await qaResponse.json();
        console.log(`Loaded ${this.qaData?.qa_pairs?.length || 0} Q&A pairs from 2.json`);
      } else {
        console.warn("Failed to load Q&A data from 2.json");
      }
    } catch (error) {
      console.error("Error loading knowledge base:", error);
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="flex flex-col h-full">
        <!-- Chat Messages -->
        <div id="chatMessages" class="flex-grow p-4 space-y-2 overflow-y-auto">
          <!-- Messages will be added here dynamically -->
        </div>
        
        <!-- Input Section -->
        <div class="border-t border-gray-200 px-4 py-3">
          <div class="relative">
            <textarea id="messageInput" rows="2" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-24" placeholder="Type your message..."></textarea>
            <div class="absolute right-2 bottom-2 flex space-x-1">
              <button id="uploadFileBtn" class="p-2 text-gray-500 hover:text-gray-700 rounded-full hover:bg-gray-100" title="Upload File">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
              </button>
              <button id="sendMessageBtn" class="p-2 text-gray-700 rounded-full" title="Send Message">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
            </div>
          </div>
          <div class="flex items-center justify-between mt-2 text-xs text-gray-500">
            <div class="flex flex-wrap gap-1">
              <button id="suggestedPrompt1" class="px-2 py-1 rounded-full border border-gray-300 hover:bg-gray-100">Find parts</button>
              <button id="suggestedPrompt2" class="px-2 py-1 rounded-full border border-gray-300 hover:bg-gray-100">Track my order</button>
              <button id="mondayPrompt" class="px-2 py-1 rounded-full border border-gray-300 hover:bg-gray-100">
                Monday.com order
              </button>
              <button id="questionsListBtn" class="px-2 py-1 rounded-full border border-gray-300 hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Questions
              </button>
            </div>
            <div class="flex items-center">
              <div id="aiStatusIndicator" class="hidden flex items-center mr-3 text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <span class="text-xs">AI</span>
              </div>
              <div id="typingIndicator" class="hidden items-center">
                <div class="flex space-x-1 mr-1">
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                </div>
                Assistant is typing...
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Questions List Modal -->
      <div id="questionsModal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl p-3 w-11/12 max-h-[360px] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center pb-2 border-b">
            <h3 class="font-semibold text-gray-800">Sample Questions</h3>
            <button id="closeQuestionsBtn" class="text-gray-500 hover:text-gray-700">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <div class="flex-grow overflow-y-auto py-2" style="max-height: 320px;">
            <div id="questionsList" class="grid grid-cols-2 gap-1 text-xs pr-1">
              <!-- Questions will be added here -->
            </div>
          </div>
        </div>
      </div>
    `;

    // Populate questions in the modal
    this.populateQuestionsModal();
  }

  populateQuestionsModal() {
    const questionsList = document.getElementById('questionsList');
    if (!questionsList) return;

    // Clear existing questions
    questionsList.innerHTML = '';

    // Add questions in a more compact way
    this.predefinedQuestions.forEach((question) => {
      const questionItem = document.createElement('div');
      questionItem.className = 'px-2 py-1 m-1 rounded hover:bg-gray-100 cursor-pointer border border-gray-100';
      questionItem.dataset.question = question;
      questionItem.innerText = question;
      
      questionsList.appendChild(questionItem);

      // Add click event
      questionItem.addEventListener('click', () => {
        this.usePresetQuestion(question);
      });
    });
  }

  usePresetQuestion(question) {
    // Close the modal
    const modal = document.getElementById('questionsModal');
    if (modal) modal.classList.add('hidden');

    // Set the question in the input
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
      messageInput.value = question;
      messageInput.focus();
    }
  }

  setupEventListeners() {
    const sendMessageBtn = document.getElementById('sendMessageBtn');
    const messageInput = document.getElementById('messageInput');
    const uploadFileBtn = document.getElementById('uploadFileBtn');
    const suggestedPrompts = [
      document.getElementById('suggestedPrompt1'),
      document.getElementById('suggestedPrompt2')
    ];
    const mondayPrompt = document.getElementById('mondayPrompt');
    const questionsListBtn = document.getElementById('questionsListBtn');
    const closeQuestionsBtn = document.getElementById('closeQuestionsBtn');

    if (sendMessageBtn) {
      sendMessageBtn.addEventListener('click', () => this.sendMessage());
    }

    if (messageInput) {
      messageInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          this.sendMessage();
        }
      });
    }

    if (uploadFileBtn) {
      uploadFileBtn.addEventListener('click', () => this.showFileUploadModal());
    }

    suggestedPrompts.forEach(button => {
      if (button) {
        button.addEventListener('click', () => {
          if (messageInput) {
            messageInput.value = button.textContent;
            messageInput.focus();
          }
        });
      }
    });
    
    // Monday.com prompt
    if (mondayPrompt) {
      mondayPrompt.addEventListener('click', () => {
        if (messageInput) {
          messageInput.value = "Search Monday.com for order ";
          messageInput.focus();
        }
      });
    }

    // Questions list button
    if (questionsListBtn) {
      questionsListBtn.addEventListener('click', () => {
        const modal = document.getElementById('questionsModal');
        if (modal) modal.classList.remove('hidden');
      });
    }

    // Close questions modal button
    if (closeQuestionsBtn) {
      closeQuestionsBtn.addEventListener('click', () => {
        const modal = document.getElementById('questionsModal');
        if (modal) modal.classList.add('hidden');
      });
    }

    // Close modal when clicking outside
    const questionsModal = document.getElementById('questionsModal');
    if (questionsModal) {
      questionsModal.addEventListener('click', (e) => {
        if (e.target === questionsModal) {
          questionsModal.classList.add('hidden');
        }
      });
    }
  }

  async sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();
    
    if (!message || this.isWaitingForResponse) {
      return;
    }
    
    // Clear input field
    messageInput.value = '';
    
    // Add user message to the chat
    this.addMessage({
      role: 'user',
      content: message,
      timestamp: new Date()
    });
    
    // Show typing indicator
    this.showTypingIndicator();
    this.isWaitingForResponse = true;
    
    // Process the message and generate a response
    setTimeout(async () => {
      await this.processUserMessage(message);
      this.hideTypingIndicator();
      this.isWaitingForResponse = false;
      this.scrollToBottom();
    }, 500);
  }

  addMessage(message) {
    this.messages.push(message);
    this.renderMessage(message);
  }

  renderMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    if (!chatMessages) return;

    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.role === 'assistant' ? 'assistant-message' : 'user-message'}`;

    const isAssistant = message.role === 'assistant';
    const avatarBgColor = isAssistant ? 'bg-blue-600' : 'bg-gray-700';
    const messageBgColor = isAssistant ? 'bg-white' : 'bg-blue-600';
    const messageTextColor = isAssistant ? 'text-gray-800' : 'text-white';
    
    // Get user avatar if it exists
    let userAvatar = '';
    if (!isAssistant && this.user && this.user.Avatar) {
      userAvatar = `<img src="images/avatars/avatar_${this.user.Avatar}.jpg" class="h-8 w-8 rounded-full object-cover" alt="${this.user["User Name"]}" />`;
    } else if (!isAssistant) {
      userAvatar = `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
         </svg>`;
    }
    
    const avatarIcon = isAssistant 
      ? `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
         </svg>`
      : userAvatar;

    messageElement.innerHTML = `
      <div class="flex items-start ${isAssistant ? '' : 'justify-end'}">
        ${isAssistant ? `
          <div class="flex-shrink-0 w-8 h-8 rounded-full ${avatarBgColor} flex items-center justify-center mr-2">
            ${avatarIcon}
          </div>
        ` : ''}
        
        <div class="flex flex-col ${isAssistant ? 'items-start' : 'items-end'}">
          <div class="${messageBgColor} ${messageTextColor} rounded-lg py-2 px-3 max-w-md shadow-sm">
            ${this.formatMessageContent(message.content)}
          </div>
          <div class="text-xs text-gray-500 mt-1">
            ${this.formatTimestamp(message.timestamp)}
          </div>
        </div>
        
        ${!isAssistant ? `
          <div class="flex-shrink-0 w-8 h-8 rounded-full ${this.user && this.user.Avatar ? '' : avatarBgColor} flex items-center justify-center ml-2">
            ${avatarIcon}
          </div>
        ` : ''}
      </div>
    `;

    chatMessages.appendChild(messageElement);
    this.scrollToBottom();
  }

  formatMessageContent(content) {
    // Handle part image results
    if (typeof content === 'object' && content.type === 'partImage') {
      return `
        <div class="part-image-container border rounded-md p-3 bg-gray-50 dark:bg-gray-800">
          <div class="flex flex-col items-center">
            <h4 class="font-medium text-sm mb-2">${content.partId}: ${content.partDescription}</h4>
            <div class="image-wrapper border rounded-md p-2 bg-white flex items-center justify-center" style="min-height: 150px; width: 100%;">
              <img src="${content.imageUrl}" alt="Part ${content.partId}" class="max-h-32 max-w-full object-contain">
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">${content.fileName}</p>
            ${content.error ? `<p class="text-xs text-red-500 mt-1">${content.error}</p>` : ''}
          </div>
        </div>
      `;
    }
    
    // Handle part search results
    if (typeof content === 'object' && content.type === 'partResults') {
      return this.searchUtils.formatPartResults(content.parts, content.query);
    }
    
    // Handle order tracking results
    if (typeof content === 'object' && content.type === 'orderTracking') {
      return this.mondayService.formatOrderTracking(content.order);
    }
    
    // Handle supplier parts list
    if (typeof content === 'object' && content.type === 'supplierParts') {
      return this.searchUtils.formatSupplierParts(content.supplier, content.parts);
    }
    
    // Handle attribute update results
    if (typeof content === 'object' && content.type === 'attributeUpdate') {
      return `
        <div class="attribute-update-container border rounded-md p-3 bg-green-50 dark:bg-green-900">
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <h4 class="font-medium">${content.title}</h4>
          </div>
          <div class="mt-2 text-sm">
            <p>${content.message}</p>
            ${content.details ? `<p class="mt-1 text-xs text-gray-500">${content.details}</p>` : ''}
          </div>
        </div>
      `;
    }
    
    // Convert URLs to clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    return content.replace(urlRegex, url => `<a href="${url}" target="_blank" class="underline hover:text-blue-400">${url}</a>`);
  }

  formatTimestamp(timestamp) {
    const now = new Date();
    const messageDate = new Date(timestamp);
    
    if (now.toDateString() === messageDate.toDateString()) {
      // Today, show time only
      return messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      // Not today, show date and time
      return messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' }) + 
             ' ' + messageDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  }

  scrollToBottom() {
    const chatMessages = document.getElementById('chatMessages');
    if (chatMessages) {
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }
  }

  showTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
      typingIndicator.classList.remove('hidden');
      typingIndicator.classList.add('flex');
    }
    this.isWaitingForResponse = true;
  }

  hideTypingIndicator() {
    const typingIndicator = document.getElementById('typingIndicator');
    if (typingIndicator) {
      typingIndicator.classList.add('hidden');
      typingIndicator.classList.remove('flex');
    }
    this.isWaitingForResponse = false;
  }

  async processUserMessage(message) {
    try {
      // Try to identify message type
      
      // 1. Check for special questions first (simple ones)
      if (this.searchUtils.isSpecialQuestion(message)) {
        this.searchUtils.handleSpecialQuestion(message);
        return;
      }
      
      // 2. Check if it's a math/calculation query and handle with AI if available
      if (this.aiInitialized && this.isMathQuestion(message)) {
        await this.handleMathQuestion(message);
        return;
      }
      
      // 3. Check if it's a part image request
      if (this.isPartImageRequest(message)) {
        await this.handlePartImageRequest(message);
        return;
      }
      
      // 4. Check if it's a stock item update request
      if (this.isStockItemUpdateRequest(message)) {
        await this.handleStockItemUpdate(message);
        return;
      }
      
      // 5. Check if it's a Monday.com query
      if (this.mondayService.isMondayQuery(message)) {
        await this.mondayService.handleMondayQuery(message);
        return;
      }
      
      // 6. Check if it's a part search query
      if (await this.searchUtils.isPartSearchQuery(message)) {
        await this.searchUtils.handlePartSearch(message);
        return;
      }
      
      // 7. Check if it's a supplier query
      if (await this.searchUtils.isSupplierQuery(message)) {
        await this.searchUtils.handleSupplierQuery(message);
        return;
      }
      
      // 8. Check if it's an order tracking query
      if (this.searchUtils.isOrderTrackingQuery(message)) {
        await this.searchUtils.handleOrderTracking(message);
        return;
      }
      
      // 9. Check if it's a knowledge base question
      if (await this.searchUtils.isKnowledgeQuestion(message)) {
        await this.searchUtils.handleKnowledgeQuestion(message);
        return;
      }
      
      // 10. If no specific type is identified, generate a generic response
      this.addMessage({
        role: 'assistant',
        content: this.generateGenericResponse(message),
        timestamp: new Date()
      });
      
    } catch (error) {
      console.error("Error processing message:", error);
      
      // Provide a fallback response in case of error
      this.addMessage({
        role: 'assistant',
        content: "I encountered an error while processing your request. Please try again or ask something else.",
        timestamp: new Date()
      });
    }
  }
  
  // Detect if a message is a math question
  isMathQuestion(message) {
    const mathPatterns = [
      /\d+\s*[\+\-\*\/x÷]\s*\d+/i, // Basic operations like 2+2, 5*3
      /\bcalculate\b/i,
      /\bconvert\b/i,
      /\bwhat\s+is\s+\d/i, // "what is 5 plus 3"
      /\bhow\s+much\s+is\b/i,
      /\d+\s*percent\s+of\s+\d+/i,
      /\d+\s*\%\s+of\s+\d+/i,
      /square\s+root/i,
      /\bcubic\b/i,
      /\bpercentage\b/i,
      /\bmean\b/i,
      /\baverage\b/i,
    ];
    
    return mathPatterns.some(pattern => pattern.test(message));
  }
  
  // Handle math questions using AI
  async handleMathQuestion(message) {
    try {
      // Simple math patterns to handle directly
      const simpleAdditionPattern = /(\d+)\s*\+\s*(\d+)/;
      const simpleSubtractionPattern = /(\d+)\s*\-\s*(\d+)/;
      const simpleMultiplicationPattern = /(\d+)\s*[\*x×]\s*(\d+)/i;
      const simpleDivisionPattern = /(\d+)\s*[\/÷]\s*(\d+)/;
      const percentPattern = /(\d+(?:\.\d+)?)\s*\%\s*of\s*(\d+(?:\.\d+)?)/i;
      
      // Try to match simple patterns first
      let result = null;
      
      const addMatch = message.match(simpleAdditionPattern);
      if (addMatch) {
        result = Number(addMatch[1]) + Number(addMatch[2]);
      }
      
      const subMatch = message.match(simpleSubtractionPattern);
      if (subMatch) {
        result = Number(subMatch[1]) - Number(subMatch[2]);
      }
      
      const mulMatch = message.match(simpleMultiplicationPattern);
      if (mulMatch) {
        result = Number(mulMatch[1]) * Number(mulMatch[2]);
      }
      
      const divMatch = message.match(simpleDivisionPattern);
      if (divMatch && Number(divMatch[2]) !== 0) {
        result = Number(divMatch[1]) / Number(divMatch[2]);
      }
      
      const percentMatch = message.match(percentPattern);
      if (percentMatch) {
        result = (Number(percentMatch[1]) / 100) * Number(percentMatch[2]);
      }
      
      // Return the result
      if (result !== null) {
        this.addMessage({
          role: 'assistant',
          content: `The answer is ${result}`,
          timestamp: new Date()
        });
        return;
      }
      
      // If not a simple pattern, use the embedding model to match to prepared answers
      const convertInchesToFeet = /convert\s+(\d+(?:\.\d+)?)\s*(?:inches|inch|in)/i;
      const convertFeetToInches = /convert\s+(\d+(?:\.\d+)?)\s*(?:feet|foot|ft)/i;
      const convertKgToPounds = /convert\s+(\d+(?:\.\d+)?)\s*(?:kg|kilograms|kilogram)/i;
      const convertPoundsToKg = /convert\s+(\d+(?:\.\d+)?)\s*(?:pounds|pound|lbs|lb)/i;
      
      const inchMatch = message.match(convertInchesToFeet);
      if (inchMatch) {
        const inches = Number(inchMatch[1]);
        const feet = inches / 12;
        this.addMessage({
          role: 'assistant',
          content: `${inches} inches = ${feet.toFixed(2)} feet`,
          timestamp: new Date()
        });
        return;
      }
      
      const feetMatch = message.match(convertFeetToInches);
      if (feetMatch) {
        const feet = Number(feetMatch[1]);
        const inches = feet * 12;
        this.addMessage({
          role: 'assistant',
          content: `${feet} feet = ${inches} inches`,
          timestamp: new Date()
        });
        return;
      }
      
      const kgMatch = message.match(convertKgToPounds);
      if (kgMatch) {
        const kg = Number(kgMatch[1]);
        const pounds = kg * 2.20462;
        this.addMessage({
          role: 'assistant',
          content: `${kg} kg = ${pounds.toFixed(2)} pounds`,
          timestamp: new Date()
        });
        return;
      }
      
      const poundsMatch = message.match(convertPoundsToKg);
      if (poundsMatch) {
        const pounds = Number(poundsMatch[1]);
        const kg = pounds / 2.20462;
        this.addMessage({
          role: 'assistant',
          content: `${pounds} pounds = ${kg.toFixed(2)} kg`,
          timestamp: new Date()
        });
        return;
      }
      
      // Fall back to general answer
      this.addMessage({
        role: 'assistant',
        content: "I can help with basic calculations. Please provide a specific calculation like '2+2' or '15% of 200'.",
        timestamp: new Date()
      });
    } catch (error) {
      console.error("Error handling math question:", error);
      this.addMessage({
        role: 'assistant',
        content: "I encountered an error processing your calculation. Please try a simpler calculation.",
        timestamp: new Date()
      });
    }
  }

  // Determine if the message is requesting a stock item update
  isStockItemUpdateRequest(message) {
    const lowerMessage = message.toLowerCase();
    return (
      lowerMessage.includes("update stock item") || 
      lowerMessage.includes("update part") || 
      (lowerMessage.includes("update") && 
        (lowerMessage.includes("hs code") || 
         lowerMessage.includes("country of origin") || 
         lowerMessage.includes("address of manufacturer") || 
         lowerMessage.includes("us eccn")))
    );
  }

  // Check if a message is requesting a part image
  isPartImageRequest(message) {
    const lowerMessage = message.toLowerCase();
    return (
      (lowerMessage.includes("send") || lowerMessage.includes("show") || lowerMessage.includes("get")) &&
      lowerMessage.includes("image") &&
      (lowerMessage.includes("part") || lowerMessage.includes("for") || /\d{4,}/.test(message))
    );
  }

  // Handle requests for part images
  async handlePartImageRequest(message) {
    // Extract the part number from the message
    let partNumber = null;
    
    // Try to match a part number format (assuming part numbers are typically 4+ digits)
    const partNumberMatch = message.match(/\b(\d{4,})\b/);
    if (partNumberMatch) {
      partNumber = partNumberMatch[1];
    } else {
      // Try to extract part number after phrases like "part" or "for"
      const phrases = ["part", "for", "image of", "picture of"];
      
      for (const phrase of phrases) {
        const regex = new RegExp(`${phrase}\\s+([\\w-]+)`, 'i');
        const match = message.match(regex);
        if (match && match[1]) {
          // If we found something that looks like a part ID
          partNumber = match[1];
          break;
        }
      }
    }

    // If no part number found, respond accordingly
    if (!partNumber) {
      this.addMessage({
        role: 'assistant',
        content: "I couldn't identify which part you're looking for. Please specify a part number, for example: 'show image for part 1000000'.",
        timestamp: new Date()
      });
      return;
    }

    // Check if the part exists in our data
    const part = this.partsData.find(p => p['Part Number'] === partNumber);
    if (!part) {
      this.addMessage({
        role: 'assistant',
        content: `I couldn't find part number ${partNumber} in our database. Please check the part number and try again.`,
        timestamp: new Date()
      });
      return;
    }

    // Update conversation context
    this.updateConversationContext('partImageRequest', { partNumber });

    // Get connection status to check if we can retrieve the image
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.acumatica.isConnected) {
      this.addMessage({
        role: 'assistant',
        content: `I found part ${partNumber} (${part['Description'] || 'No description available'}), but I can't retrieve the image because we are not connected to Acumatica. Please connect first.`,
        timestamp: new Date()
      });
      return;
    }

    try {
      // Loading message
      this.addMessage({
        role: 'assistant',
        content: `Retrieving image for part ${partNumber} (${part['Description'] || 'No description available'})...`,
        timestamp: new Date()
      });

      // Fetch the image
      const imageResult = await partImageHelper.getPartImage(partNumber);
      
      if (imageResult.success) {
        // Prepare the image display content
        this.addMessage({
          role: 'assistant',
          content: {
            type: 'partImage',
            partId: partNumber,
            partDescription: part['Description'] || 'No description available',
            imageUrl: imageResult.imageUrl,
            fileName: imageResult.fileName || `Part ${partNumber}`
          },
          timestamp: new Date()
        });
      } else {
        // No image available
        const noImageUrl = chrome.runtime.getURL('images/extra/no-thumbnail.png');
        this.addMessage({
          role: 'assistant',
          content: {
            type: 'partImage',
            partId: partNumber,
            partDescription: part['Description'] || 'No description available',
            imageUrl: noImageUrl,
            fileName: `No image available for Part ${partNumber}`,
            error: imageResult.error
          },
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('Error retrieving part image:', error);
      this.addMessage({
        role: 'assistant',
        content: `There was an error retrieving the image for part ${partNumber}. Error: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  // Handle stock item update requests
  async handleStockItemUpdate(message) {
    // Check if connected to Acumatica
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.acumatica.isConnected) {
      this.addMessage({
        role: 'assistant',
        content: "You need to connect to Acumatica first before updating stock items. Please go to the connections panel and connect.",
        timestamp: new Date()
      });
      return;
    }

    // Extract part numbers from the message
    const partNumbers = this.extractPartNumbers(message);
    if (!partNumbers || partNumbers.length === 0) {
      this.addMessage({
        role: 'assistant',
        content: "Please specify which part(s) you want to update. Example: 'Update stock item 1000000' or 'Update HS CODE for parts 1000001,1000002'",
        timestamp: new Date()
      });
      return;
    }

    // Show processing message
    this.addMessage({
      role: 'assistant',
      content: `Processing update request for ${partNumbers.length} part(s): ${partNumbers.join(", ")}...`,
      timestamp: new Date()
    });

    try {
      // Create update form for attributes
      await this.showAttributeUpdateForm(partNumbers);
    } catch (error) {
      console.error("Error handling stock item update:", error);
      this.addMessage({
        role: 'assistant',
        content: `Error preparing update form: ${error.message}`,
        timestamp: new Date()
      });
    }
  }

  // Extract part numbers from user message
  extractPartNumbers(message) {
    // Look for comma-separated numbers after phrases like "stock item", "part", etc.
    const regex = /(?:stock item|part|parts|item|items)\s+([0-9,\s]+)/i;
    const match = message.match(regex);
    
    if (match && match[1]) {
      // Split by comma and clean up each part number
      return match[1].split(',').map(num => num.trim()).filter(num => num.length > 0);
    }
    
    // If no specific pattern found, look for any sequence of digits
    const numberMatches = message.match(/\b\d{5,}\b/g);
    return numberMatches || [];
  }

  // Show form for updating attributes
  async showAttributeUpdateForm(partNumbers) {
    try {
      // Create modal for attribute update
      const updateModal = document.createElement('div');
      updateModal.id = 'attributeUpdateModal';
      updateModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
      
      // Get part data from master parts JSON
      const partDataList = [];
      for (const partNumber of partNumbers) {
        const partData = this.partsData.find(p => p['Part Number'] === partNumber);
        if (partData) {
          partDataList.push(partData);
        } else {
          console.warn(`Part ${partNumber} not found in master parts data`);
          partDataList.push({ 'Part Number': partNumber, 'Description': 'Unknown part' });
        }
      }
      
      // Different UI for single part vs. multi-part
      const isSinglePart = partNumbers.length === 1;
      
      // Prepare UI content based on whether it's single or multi-part
      let contentHTML;
      if (isSinglePart) {
        contentHTML = this.createSinglePartUpdateForm(partDataList[0]);
      } else {
        contentHTML = this.createMultiPartUpdateForm(partDataList);
      }
      
      // Create the modal with fixed width
      updateModal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 overflow-y-auto" style="width: 400px; max-height: 85vh;">
          <div class="text-base font-semibold mb-2 flex justify-between items-center">
            <span>Update Stock Item Attributes</span>
            <button id="cancelUpdateBtn" class="text-gray-500 hover:text-gray-700">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <p class="text-xs mb-3 text-gray-500">Updating ${partNumbers.length} part(s)</p>
          
          ${contentHTML}
          
          <div class="flex justify-end space-x-2 mt-3">
            <button type="button" id="cancelUpdateBtnBottom" class="px-3 py-1 text-sm bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400">
              Cancel
            </button>
            <button type="button" id="confirmUpdateBtn" class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Update
            </button>
          </div>
        </div>
      `;
      
      document.body.appendChild(updateModal);
      
      // Set up event listeners for the form
      this.setupAttributeUpdateFormEvents(updateModal, partDataList);
      
    } catch (error) {
      console.error("Error creating attribute update form:", error);
      this.addMessage({
        role: 'assistant',
        content: `Error preparing the update form: ${error.message}`,
        timestamp: new Date()
      });
    }
  }
  
  // Create form HTML for a single part update
  createSinglePartUpdateForm(partData) {
    const partNumber = partData['Part Number'];
    const description = partData['Description'] || 'No description';
    
    return `
      <div class="part-form" data-part-number="${partNumber}">
        <div class="mb-2 pb-1 border-b border-gray-200">
          <div class="text-sm font-semibold">${partNumber}</div>
          <div class="text-xs text-gray-500 truncate" title="${description}">${description}</div>
        </div>
        
        <div class="attribute-fields space-y-2 overflow-y-auto" style="max-height: 320px;">
          ${this.createAttributeFields(partData)}
        </div>
      </div>
    `;
  }
  
  // Create tabbed form HTML for multiple parts
  createMultiPartUpdateForm(partDataList) {
    const tabs = partDataList.map((partData, index) => {
      const partNumber = partData['Part Number'];
      return `
        <button class="tab-button text-xs py-1 px-2 border-b-2 ${index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}" 
          data-part-number="${partNumber}">
          ${partNumber}
        </button>
      `;
    }).join('');
    
    const forms = partDataList.map((partData, index) => {
      const partNumber = partData['Part Number'];
      const description = partData['Description'] || 'No description';
      
      return `
        <div class="part-form ${index === 0 ? '' : 'hidden'}" data-part-number="${partNumber}">
          <div class="mb-2 pb-1 border-b border-gray-200">
            <div class="text-xs text-gray-500 truncate" title="${description}">${description}</div>
          </div>
          
          <div class="attribute-fields space-y-2 overflow-y-auto" style="max-height: 250px;">
            ${this.createAttributeFields(partData)}
          </div>
          
          <div class="mt-2 pt-1 border-t border-gray-200">
            <label class="flex items-center text-xs">
              <input type="checkbox" class="mr-1 apply-to-all-checkbox" data-part-number="${partNumber}">
              Apply these values to all parts
            </label>
          </div>
        </div>
      `;
    }).join('');
    
    return `
      <div class="tabs-container">
        <div class="tabs-header flex space-x-1 overflow-x-auto pb-1 mb-2">
          ${tabs}
        </div>
        <div class="tabs-content">
          ${forms}
        </div>
      </div>
    `;
  }
  
  // Create attribute input fields based on part data
  createAttributeFields(partData) {
    // Mapping between master parts JSON fields and Acumatica attribute IDs
    const attributeMapping = [
      { id: 'AOM', label: 'Address of Manufacturer', field: 'Manufacturer & Address' },
      { id: 'COO', label: 'Country of Origin', field: 'Country of Origin' },
      { id: 'ECCN', label: 'US ECCN', field: 'ECCN' },
      { id: 'HSCODE', label: 'HS CODE', field: 'HS Code' }
      // Family field removed as requested since it's a dropdown in Acumatica
    ];
    
    return attributeMapping.map(attr => {
      const currentValue = partData[attr.field] || '';
      
      return `
        <div class="attribute-field">
          <label class="block text-xs font-medium mb-1">${attr.label}</label>
          <input type="text" 
                 class="w-full px-2 py-1 text-sm border rounded-md" 
                 data-attr-id="${attr.id}" 
                 value="${currentValue}" 
                 placeholder="${attr.label}">
        </div>
      `;
    }).join('');
  }
  
  // Set up event handlers for the attribute update form
  setupAttributeUpdateFormEvents(modal, partDataList) {
    const partNumbers = partDataList.map(part => part['Part Number']);
    
    // Handle both cancel buttons
    const cancelBtns = [
      document.getElementById('cancelUpdateBtn'),
      document.getElementById('cancelUpdateBtnBottom')
    ];
    
    cancelBtns.forEach(btn => {
      if (btn) {
        btn.addEventListener('click', () => {
          modal.remove();
          this.addMessage({
            role: 'assistant',
            content: "Stock item update cancelled.",
            timestamp: new Date()
          });
        });
      }
    });
    
    // Handle tab switching for multi-part updates
    const tabButtons = modal.querySelectorAll('.tab-button');
    if (tabButtons.length > 0) {
      tabButtons.forEach(tab => {
        tab.addEventListener('click', () => {
          // Update active tab
          tabButtons.forEach(t => {
            t.classList.remove('border-blue-500', 'text-blue-600');
            t.classList.add('border-transparent', 'text-gray-500');
          });
          tab.classList.remove('border-transparent', 'text-gray-500');
          tab.classList.add('border-blue-500', 'text-blue-600');
          
          // Show corresponding form
          const partNumber = tab.getAttribute('data-part-number');
          const forms = modal.querySelectorAll('.part-form');
          forms.forEach(form => {
            if (form.getAttribute('data-part-number') === partNumber) {
              form.classList.remove('hidden');
            } else {
              form.classList.add('hidden');
            }
          });
        });
      });
    }
    
    // Handle "Apply to all" checkboxes
    const applyToAllCheckboxes = modal.querySelectorAll('.apply-to-all-checkbox');
    applyToAllCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        if (!checkbox.checked) return;
        
        // Get values from the current part's form
        const partNumber = checkbox.getAttribute('data-part-number');
        const currentForm = modal.querySelector(`.part-form[data-part-number="${partNumber}"]`);
        const inputValues = {};
        
        currentForm.querySelectorAll('input[data-attr-id]').forEach(input => {
          const attrId = input.getAttribute('data-attr-id');
          inputValues[attrId] = input.value;
        });
        
        // Apply these values to all other parts
        modal.querySelectorAll('.part-form').forEach(form => {
          if (form.getAttribute('data-part-number') !== partNumber) {
            form.querySelectorAll('input[data-attr-id]').forEach(input => {
              const attrId = input.getAttribute('data-attr-id');
              if (inputValues[attrId] !== undefined) {
                input.value = inputValues[attrId];
              }
            });
          }
        });
        
        // Uncheck the box after applying
        checkbox.checked = false;
      });
    });
    
    // Handle update button
    const confirmBtn = document.getElementById('confirmUpdateBtn');
    if (confirmBtn) {
      confirmBtn.addEventListener('click', async () => {
        // Collect attribute values from each form
        const partAttributes = {};
        
        modal.querySelectorAll('.part-form').forEach(form => {
          const partNumber = form.getAttribute('data-part-number');
          const attributes = {};
          
          form.querySelectorAll('input[data-attr-id]').forEach(input => {
            const attrId = input.getAttribute('data-attr-id');
            const value = input.value.trim();
            if (value) {
              attributes[attrId] = value;
            }
          });
          
          if (Object.keys(attributes).length > 0) {
            partAttributes[partNumber] = attributes;
          }
        });
        
        // Check if any attributes were provided
        if (Object.keys(partAttributes).length === 0) {
          this.addMessage({
            role: 'assistant',
            content: "No attribute values provided for update.",
            timestamp: new Date()
          });
          modal.remove();
          return;
        }
        
        // Disable button during update
        confirmBtn.disabled = true;
        confirmBtn.textContent = 'Updating...';
        
        // Process updates for each part
        const results = [];
        for (const partNumber of partNumbers) {
          if (partAttributes[partNumber]) {
            const partResult = await this.updateStockItem(partNumber, partAttributes[partNumber]);
            results.push(partResult);
          }
        }
        
        // Remove the modal
        modal.remove();
        
        // Show results
        const successCount = results.filter(r => r.success).length;
        
        if (successCount === partNumbers.length) {
          this.addMessage({
            role: 'assistant',
            content: `Successfully updated attributes for all ${partNumbers.length} part(s).`,
            timestamp: new Date()
          });
          
          // Update conversation context
          this.updateConversationContext('attributeUpdate', { 
            partNumbers: partNumbers,
            attributesUpdated: Object.keys(partAttributes).length
          });
        } else {
          const failedParts = results.filter(r => !r.success).map(r => r.partNumber);
          
          this.addMessage({
            role: 'assistant',
            content: `Updated attributes for ${successCount} out of ${partNumbers.length} part(s). Failed updates: ${failedParts.join(", ")}`,
            timestamp: new Date()
          });
        }
      });
    }
    
    // Close when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
        this.addMessage({
          role: 'assistant',
          content: "Stock item update cancelled.",
          timestamp: new Date()
        });
      }
    });
  }
  
  // Updated method to fix the update functionality
  async updateStockItem(partNumber, attributeUpdates) {
    try {
      const instance = connectionManager.connections.acumatica.instance;
      
      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          console.error("Error getting cookies:", cookieError);
        }
      }
      
      // Prepare update payload with the correct format
      const attributes = [];
      
      for (const [attrId, value] of Object.entries(attributeUpdates)) {
        if (value && value.trim()) {
          attributes.push({
            AttributeID: {
              value: attrId
            },
            Value: {
              value: value.trim()
            }
          });
        }
      }
      
      // Skip if no attributes provided
      if (attributes.length === 0) {
        console.warn(`No attributes to update for part ${partNumber}`);
        return { 
          partNumber, 
          success: false, 
          message: "No attributes provided for update" 
        };
      }
      
      // Create update payload
      const updatePayload = {
        Attributes: attributes
      };
      
      console.log(`Updating part ${partNumber} with payload:`, JSON.stringify(updatePayload));
      
      // Send update request using the correct URL format
      const updateUrl = `${instance}/entity/Default/20.200.001/StockItem?$filter=InventoryID eq '${partNumber}'&$select=InventoryID,Attributes&$expand=Attributes`;
      console.log(`Update URL: ${updateUrl}`);
      
      const updateResponse = await fetch(updateUrl, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        },
        body: JSON.stringify(updatePayload)
      });
      
      if (!updateResponse.ok) {
        const errorText = await updateResponse.text();
        throw new Error(`Update failed: ${updateResponse.status} - ${errorText}`);
      }
      
      const responseData = await updateResponse.json();
      console.log(`Update response:`, responseData);
      
      return { partNumber, success: true };
    } catch (error) {
      console.error(`Error updating part ${partNumber}:`, error);
      return { partNumber, success: false, error: error.message };
    }
  }

  generateGenericResponse(message) {
    // Try to understand the message better if AI is available
    if (this.aiInitialized) {
      // Check for specific patterns and provide better responses
      const lowerMessage = message.toLowerCase();
      
      // Respond to greetings
      if (/^(hi|hello|hey|greetings)/.test(lowerMessage)) {
        return "Hello! How can I help you today? You can ask me about parts, orders, or Monday.com data.";
      }
      
      // Respond to thanks
      if (/^(thanks|thank you|appreciate)/.test(lowerMessage)) {
        return "You're welcome! Is there anything else I can help with?";
      }
      
      // Respond to goodbyes
      if (/^(bye|goodbye|see you|until next time)/.test(lowerMessage)) {
        return "Goodbye! Feel free to return if you need assistance later.";
      }
    }
    
    // Default response if no pattern matches
    const responses = [
      "I'm not sure I understand. Could you try rephrasing your question?",
      "You can ask me about parts, order tracking, or Monday.com data.",
      "Try asking me to find parts, track an order, or search Monday.com.",
      "I can help you find parts, track orders, or answer questions about Envent products.",
      "Would you like to search for parts, track an order, or look up information in Monday.com?"
    ];
    
    return responses[Math.floor(Math.random() * responses.length)];
  }

  getPersonalizedGreeting() {
    let greeting = this.getRandomGreeting();
    
    // Add personalization if user data is available
    if (this.user && this.user["User Name"]) {
      greeting = greeting.replace("Hello!", `Hello ${this.user["User Name"]}!`);
      greeting = greeting.replace("Hi there!", `Hi ${this.user["User Name"]}!`);
      greeting = greeting.replace("Welcome!", `Welcome, ${this.user["User Name"]}!`);
      greeting = greeting.replace("Greetings!", `Greetings, ${this.user["User Name"]}!`);
    }
    
    return greeting;
  }

  getRandomGreeting() {
    return this.greetingMessages[Math.floor(Math.random() * this.greetingMessages.length)];
  }

  showFileUploadModal() {
    alert('File upload functionality would be implemented here.');
    // In a real implementation, you would show a modal for file selection
  }
  
  // Track conversation context for smarter responses
  updateConversationContext(action, data = {}) {
    this.conversationContext = {
      ...this.conversationContext,
      lastAction: action,
      timestamp: new Date(),
      ...data
    };
  }
}