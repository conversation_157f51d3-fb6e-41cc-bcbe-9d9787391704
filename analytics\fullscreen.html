<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shipping Analytics Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  
  <!-- Add Maplibre for map visualizations -->
  <link href="https://cdn.jsdelivr.net/npm/maplibre-gl@3.1.0/dist/maplibre-gl.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/maplibre-gl@3.1.0/dist/maplibre-gl.js"></script>
  <!-- Add XLSX.js for Excel export -->
  <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Inter', 'Segoe UI', sans-serif;
      background-color: #f9fafb;
      color: #1f2937;
    }
    .dashboard-container {
      max-width: 1500px;
      margin: 0 auto;
      padding: 20px;
    }
    .chart-container {
      min-height: 300px;
      background-color: white;
      transition: all 0.3s ease-in-out;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      margin-bottom: 1rem;
      overflow: hidden;
    }
    .chart-container:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }
    .kpi-card {
      transition: all 0.2s ease;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
      background: white;
      position: relative;
      overflow: hidden;
    }
    .kpi-card::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      height: 4px;
      width: 100%;
      background: linear-gradient(90deg, #3b82f6, #60a5fa);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    .kpi-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.08), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    .kpi-card:hover::after {
      opacity: 1;
    }
    .kpi-card.cost-card::after {
      background: linear-gradient(90deg, #10b981, #34d399);
    }
    .kpi-card.carrier-card::after {
      background: linear-gradient(90deg, #f59e0b, #fbbf24);
    }
    .kpi-card.destination-card::after {
      background: linear-gradient(90deg, #8b5cf6, #a78bfa);
    }
    .kpi-card.avg-card::after {
      background: linear-gradient(90deg, #ef4444, #f87171);
    }
    .bg-gradient {
      background: linear-gradient(135deg, #f3f4f6 0%, #f9fafb 100%);
    }
    /* For displaying error messages */
    .error-message {
      color: #ef4444;
      font-size: 14px;
      margin-top: 8px;
      padding: 8px;
      background-color: #fee2e2;
      border-radius: 4px;
      border: 1px solid #fca5a5;
    }
    .hidden {
      display: none;
    }
    /* Tab styles */
    .tab-button {
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
      color: #4B5563;
    }
    .tab-button.active {
      border-bottom: 2px solid #3b82f6;
      color: #3b82f6;
      font-weight: 600;
    }
    .tab-button:hover:not(.active) {
      border-bottom: 2px solid #93c5fd;
      color: #1d4ed8;
    }
    .tab-content {
      display: none;
      animation: fadeIn 0.5s ease;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .tab-content.active {
      display: block;
    }
    /* Loading spinner */
    .loading-spinner {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 3px solid #e5e7eb;
      border-top-color: #3b82f6;
      animation: spinner 1s linear infinite;
      margin: 20px auto;
    }
    @keyframes spinner {
      to {transform: rotate(360deg);}
    }
    /* Modern button styles */
    .btn {
      @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center justify-center;
    }
    .btn-blue {
      @apply bg-blue-500 text-white hover:bg-blue-600 active:bg-blue-700;
    }
    .btn-green {
      @apply bg-green-500 text-white hover:bg-green-600 active:bg-green-700;
    }
    .btn-purple {
      @apply bg-purple-500 text-white hover:bg-purple-600 active:bg-purple-700;
    }
    /* Data table styling */
    .data-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
    }
    .data-table th {
      background-color: #f9fafb;
      font-weight: 600;
      padding: 0.75rem 1rem;
      text-align: left;
      border-bottom: 2px solid #e5e7eb;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .data-table td {
      padding: 0.75rem 1rem;
      border-bottom: 1px solid #e5e7eb;
    }
    .data-table tr:nth-child(even) {
      background-color: #f9fafb;
    }
    .data-table tr:hover {
      background-color: #f3f4f6;
    }
    /* Filter bar */
    .filter-bar {
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    }
    /* Map container styles */
    .map-container {
      width: 100%;
      height: 300px;
      border-radius: 0.5rem;
      overflow: hidden;
      margin-top: 1rem;
    }
    /* Animated elements */
    .animate-fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }
    .animate-slide-up {
      animation: slideUp 0.5s ease-in-out;
    }
    @keyframes slideUp {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    /* Enhanced dropdowns */
    .tracking-dropdown {
      transition: opacity 0.2s ease, transform 0.2s ease;
    }
    /* Print styles */
    @media print {
      .no-print {
        display: none !important;
      }
      .chart-container {
        break-inside: avoid;
        page-break-inside: avoid;
        margin-bottom: 30px;
        box-shadow: none;
        border: 1px solid #e5e7eb;
        padding: 15px !important;
        max-height: none !important;
      }
      .kpi-card {
        box-shadow: none;
        border: 1px solid #e5e7eb;
      }
      .pagebreak {
        page-break-before: always;
      }
      body {
        background-color: white;
        color: black !important;
      }
      .print-row {
        break-inside: avoid;
        page-break-inside: avoid;
      }
      /* Force all tabs to display when printing */
      .tab-content:not(.active) {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
      }
      .tab-content {
        page-break-before: always;
        margin-bottom: 40px;
      }
      .tab-content:first-of-type {
        page-break-before: avoid;
      }
      /* Ensure charts are properly printed */
      .apexcharts-canvas {
        width: 100% !important;
        height: auto !important;
      }
      #dashboardContent {
        display: block !important;
      }
      @page {
        size: A4 landscape;
        margin: 15mm;
      }
      /* Better SVG print quality */
      svg {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
      }
      /* Table adjustments for printing */
      .data-table {
        font-size: 9pt;
        width: 100% !important;
        border-collapse: collapse;
      }
      .data-table th, .data-table td {
        padding: 4px 8px;
        border: 1px solid #e5e7eb;
      }
      /* Print title on each page */
      h1, h2, h3 {
        color: black !important;
      }
      /* Fix chart height issues during print */
      .tab-content .chart-container {
        height: auto !important; 
        min-height: 300px !important;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard-container">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">Shipping Analytics Dashboard</h1>
      <div id="dashboardControlButtons" class="flex space-x-2 no-print">
        <span id="lastUpdated" class="text-sm text-gray-500 mr-2">Loading data...</span>
        <button id="refreshData" class="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh Data
        </button>
        <button id="exportCSV" class="px-3 py-1.5 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
          </svg>
          Export Data
        </button>
        <button id="printReport" class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
          </svg>
          Print Report
        </button>
        <button id="dashboardSettingsBtn" class="px-3 py-1.5 bg-indigo-500 text-white text-sm rounded-md hover:bg-indigo-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Settings
        </button>
      </div>
    </div>

    <!-- Error/Status message area -->
    <div id="statusMessage" class="mb-4 hidden"></div>

    <!-- Initial loading indicator -->
    <div id="initialLoading" class="text-center py-12">
      <div class="loading-spinner"></div>
      <p class="mt-4 text-gray-600">Loading dashboard data...</p>
    </div>

    <!-- Main dashboard content - hidden initially -->
    <div id="dashboardContent" class="hidden">
      <!-- KPI Cards Row -->
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6 print-row">
        <!-- Shipment Volume KPI -->
        <div class="kpi-card p-5" id="kpi-shipments">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Total Shipments</p>
              <div class="flex items-baseline mt-2">
                <p class="text-2xl font-bold text-gray-800" id="totalShipmentsKPI">0</p>
                <span class="ml-2 text-xs font-medium text-gray-500">shipments</span>
              </div>
              <p class="text-xs font-medium mt-2" id="shipmentsChangeKPI">0% from last period</p>
            </div>
            <div class="p-3 bg-blue-50 rounded-xl flex-shrink-0">
              <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
              </svg>
            </div>
          </div>
          <div class="w-full mt-3" id="shipmentsMiniChart"></div>
        </div>
        
        <!-- Freight Cost KPI -->
        <div class="kpi-card cost-card p-5" id="kpi-cost">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Total Freight Cost</p>
              <div class="flex items-baseline mt-2">
                <p class="text-2xl font-bold text-gray-800" id="totalFreightKPI">$0.00</p>
              </div>
              <p class="text-xs font-medium mt-2" id="freightChangeKPI">0% from last period</p>
            </div>
            <div class="p-3 bg-green-50 rounded-xl flex-shrink-0">
              <svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="w-full mt-3" id="costMiniChart"></div>
        </div>
        
        <!-- Top Carrier KPI -->
        <div class="kpi-card carrier-card p-5" id="kpi-carrier">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Top Carrier</p>
              <div class="flex items-baseline mt-2">
                <p class="text-2xl font-bold text-gray-800" id="topCarrierKPI">-</p>
              </div>
              <p class="text-xs font-medium mt-2" id="carrierPercentKPI">0% of shipments</p>
            </div>
            <div class="p-3 bg-yellow-50 rounded-xl flex-shrink-0">
              <svg class="w-6 h-6 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
              </svg>
            </div>
          </div>
          <div class="w-full mt-3" id="carrierMiniChart"></div>
        </div>
        
        <!-- Top Destination KPI -->
        <div class="kpi-card destination-card p-5" id="kpi-destination">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Top Destination</p>
              <div class="flex items-baseline mt-2">
                <p class="text-2xl font-bold text-gray-800" id="topDestinationKPI">-</p>
              </div>
              <p class="text-xs font-medium mt-2" id="destinationPercentKPI">0% of shipments</p>
            </div>
            <div class="p-3 bg-purple-50 rounded-xl flex-shrink-0">
              <svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="w-full mt-3" id="destinationMiniChart"></div>
        </div>
        
        <!-- Avg Cost/Shipment KPI -->
        <div class="kpi-card avg-card p-5" id="kpi-weight">
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-500">Avg Cost/Shipment</p>
              <div class="flex items-baseline mt-2">
                <p class="text-2xl font-bold text-gray-800" id="avgCostKPI">$0.00</p>
              </div>
              <p class="text-xs font-medium mt-2" id="avgCostChangeKPI">0% from last period</p>
            </div>
            <div class="p-3 bg-red-50 rounded-xl flex-shrink-0">
              <svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3"></path>
              </svg>
            </div>
          </div>
          <div class="w-full mt-3" id="avgCostMiniChart"></div>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="border-b border-gray-200 mb-6 no-print">
        <ul class="flex flex-wrap -mb-px">
          <li class="mr-2">
            <button class="tab-button active" data-tab="overview">Overview</button>
          </li>
          <li class="mr-2">
            <button class="tab-button" data-tab="cost-analysis">Cost Analysis</button>
          </li>
          <li class="mr-2">
            <button class="tab-button" data-tab="carrier-performance">Carrier Performance</button>
          </li>
          <li class="mr-2">
            <button class="tab-button" data-tab="shipping-trends">Shipping Trends</button>
          </li>
          <li>
            <button class="tab-button" data-tab="shipping-data">Shipping Data</button>
          </li>
        </ul>
      </div>

      <!-- Tab Content: Overview -->
      <div id="overview-tab" class="tab-content active">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Shipping Overview</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Daily Shipment Cost Tracker -->
            <div id="freightCostContainer" class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Freight Cost Tracker</h3>
                <select id="freightCostFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="week" selected>Last 7 days</option>
                  <option value="month">Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="dailyFreightCostChart" class="w-full h-80 overflow-hidden"></div>
            </div>
            
            <!-- Shipment Volume Trends -->
            <div id="volumeContainer" class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipment Volume Trends</h3>
                <select id="shipmentVolumeFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="week" selected>Weekly</option>
                  <option value="month">Monthly</option>
                  <option value="quarter">Quarterly</option>
                </select>
              </div>
              <div id="shipmentVolumeChart" class="w-full h-80 overflow-hidden"></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Shipment by Weekday -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipments by Day of Week</h3>
                <select id="shipmentByDayFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="week" selected>Recent</option>
                  <option value="month">Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                </select>
              </div>
              <div id="shipmentByDayChart" class="w-full h-72 overflow-hidden"></div>
            </div>
            
            <!-- Shipment by Carrier -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipments by Carrier</h3>
                <select id="shipmentByCarrierFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="shipmentByCarrierChart" class="w-full h-72 overflow-hidden"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content: Cost Analysis -->
      <div id="cost-analysis-tab" class="tab-content">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Shipping Cost Analysis</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Cost Trends Over Time -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Cost Trends Over Time</h3>
                <select id="costTrendsFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Monthly</option>
                  <option value="quarter">Quarterly</option>
                  <option value="year">Yearly</option>
                </select>
              </div>
              <div id="costTrendsChart" class="w-full h-80 overflow-hidden"></div>
            </div>
            
            <!-- Average Cost By Destination -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Average Cost By Destination</h3>
                <select id="costByDestinationFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="costByDestinationChart" class="w-full h-80 overflow-hidden"></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Shipping Cost by Carrier -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Cost by Carrier</h3>
                <select id="carrierCostFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="carrierCostChart" class="w-full h-72 overflow-hidden"></div>
            </div>
            
            <!-- Cost by Package Type -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Cost by Package Type</h3>
                <select id="packageCostFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="packageCostChart" class="w-full h-72 overflow-hidden"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content: Carrier Performance -->
      <div id="carrier-performance-tab" class="tab-content">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Carrier Performance Analysis</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Carrier Utilization -->
            <div class="chart-container p-4">
              <h3 class="text-base font-semibold mb-4 text-gray-700">Carrier Utilization</h3>
              <div id="carrierUtilizationChart" class="w-full h-72 overflow-hidden"></div>
            </div>
            
            <!-- Carrier Performance Comparison -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Carrier Performance</h3>
                <select id="carrierPerformanceFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="carrierPerformanceChart" class="w-full h-72 overflow-hidden"></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Carrier Cost vs Volume -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Carrier Cost vs Volume</h3>
                <select id="carrierCostVolumeFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="carrierCostVolumeChart" class="w-full h-80 overflow-hidden"></div>
            </div>
            
            <!-- Carrier Usage by Destination -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Carrier Usage by Destination</h3>
                <select id="carrierDestinationFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="carrierDestinationChart" class="w-full h-80 overflow-hidden"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content: Shipping Trends -->
      <div id="shipping-trends-tab" class="tab-content">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Shipping Trends Analysis</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Top Products -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Top Shipped Products</h3>
                <select id="topProductsFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">All Time</option>
                </select>
              </div>
              <div id="topProductsChart" class="w-full h-72 overflow-hidden"></div>
            </div>
            
            <!-- Shipments per Customer -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipments by Customer</h3>
                <select id="customerShipmentsFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="customerShipmentsChart" class="w-full h-80 overflow-hidden"></div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6 print-row">
            <!-- Shipment Method Distribution -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipping Methods Used</h3>
                <select id="shippingMethodFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="shippingMethodChart" class="w-full h-72 overflow-hidden"></div>
            </div>
            
            <!-- Shipments by Country Distribution (Pie Chart) -->
            <div class="chart-container p-4">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-base font-semibold text-gray-700">Shipments by Country</h3>
                <select id="topDestinationFilter" class="text-sm bg-gray-50 border border-gray-300 rounded-md px-2 py-1 no-print">
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div id="topDestinationChart" class="w-full h-72 overflow-hidden"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content: Shipping Data -->
      <div id="shipping-data-tab" class="tab-content">
        <div class="mb-6">
          <h2 class="text-lg font-semibold text-gray-700 mb-4">Recent Shipping History</h2>
          
          <!-- Enhanced Filters for Shipping Data -->
          <div class="bg-gray-50 p-4 mb-4 rounded-lg no-print">
            <div class="flex flex-wrap gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
                <select id="historyDateFilter" class="text-sm bg-white border border-gray-300 rounded-md px-2 py-1">
                  <option value="week">Last 7 days</option>
                  <option value="month" selected>Last 30 days</option>
                  <option value="quarter">Last 90 days</option>
                  <option value="year">Last 365 days</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Carrier</label>
                <select id="historyCarrierFilter" class="text-sm bg-white border border-gray-300 rounded-md px-2 py-1">
                  <option value="all" selected>All Carriers</option>
                  <!-- Dynamically filled options -->
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                <select id="historyCountryFilter" class="text-sm bg-white border border-gray-300 rounded-md px-2 py-1">
                  <option value="all" selected>All Countries</option>
                  <!-- Dynamically filled options -->
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" id="historySearchInput" placeholder="Search shipments..." class="text-sm bg-white border border-gray-300 rounded-md px-2 py-1 w-60">
              </div>
            </div>
          </div>
          
          <!-- Enhanced Shipping History Table -->
          <div class="chart-container p-4 mb-6">
            <div id="historyTableContainer" class="w-full overflow-x-auto"></div>
            <div class="mt-4 text-right text-sm text-gray-500 no-print">
              <span id="historyTablePagination">Showing 1-20 of 0 shipments</span>
              <div class="mt-2 flex justify-end">
                <button id="prevPage" class="px-2 py-1 bg-gray-200 rounded-l-md disabled:opacity-50">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                  </svg>
                </button>
                <button id="nextPage" class="px-2 py-1 bg-gray-200 rounded-r-md disabled:opacity-50">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <!-- Export Options (CSV, Excel) -->
          <div class="chart-container p-4 mb-6 no-print">
            <h3 class="text-base font-semibold mb-4 text-gray-700">Export Options</h3>
            <div class="flex space-x-4">
              <button id="exportTableCSV" class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-150">
                Export Current View as CSV
              </button>
              <button id="exportTableExcel" class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-150">
                Export Current View as Excel
              </button>
              <button id="exportAllData" class="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors duration-150">
                Export All Shipping Data
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Global error handler -->
  <script>
    window.addEventListener('error', function(event) {
      console.error('Error caught:', event.error);
      document.getElementById('statusMessage').innerHTML = `<div class="error-message">${event.error?.message || 'Unknown error'}</div>`;
      document.getElementById('statusMessage').classList.remove('hidden');
      document.getElementById('initialLoading').classList.add('hidden');
    });
  </script>

  <!-- Tab handling -->
  <script>
    // This script has been disabled to avoid conflicts with analytics-fullscreen.js
    // Tab functionality is now managed by the analytics-fullscreen.js file
  </script>

  <!-- Load print enhancement module -->
  <script type="module">
    // Import PrintManager from external file
    import { PrintManager } from './analytics-fullscreen-print.js';

    // Make PrintManager globally available
    window.PrintManager = PrintManager;
    
    // Initialize PrintManager when DOM is ready
    function initPrintManager() {
      try {
        console.log("Creating PrintManager instance...");
        // Create new instance
        window.printManager = new PrintManager();
        
        // Initialize the PrintManager
        console.log("Initializing PrintManager...");
        window.printManager.init();
        
        console.log("PrintManager initialized successfully");
        console.log("Available methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(window.printManager)));
        
        // Debug info for print button
        const printButton = document.getElementById('printReport');
        console.log("Print button found:", printButton !== null);
        
        // Add explicit event listener for testing
        if (printButton) {
          printButton.setAttribute('data-print-hooked', 'true');
          printButton.addEventListener('click', function(e) {
            console.log("Print button clicked via direct HTML initialization");
            e.preventDefault();
            e.stopPropagation();
            
            if (window.printManager && typeof window.printManager.showPrintDialog === 'function') {
              console.log("Calling PrintManager.showPrintDialog() from HTML");
              window.printManager.showPrintDialog();
            } else {
              console.warn("PrintManager not available in direct HTML listener");
              window.print();
            }
            
            return false;
          });
        }
      } catch (error) {
        console.error('Error initializing PrintManager:', error);
        console.error('Error stack:', error.stack);
      }
    }

    // Initialize when document is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initPrintManager);
    } else {
      // If DOM is already loaded, initialize immediately
      initPrintManager();
    }
    
    // Also ensure initialization when window is fully loaded
    window.addEventListener('load', function() {
      console.log("Window loaded, ensuring PrintManager is initialized...");
      if (!window.printManager || typeof window.printManager.showPrintDialog !== 'function') {
        console.log("PrintManager not properly initialized, retrying...");
        initPrintManager();
      }
    });
  </script>

  <!-- Load other scripts -->
  <script src="./apexcharts.min.js"></script>
  <script src="simple-charts.js"></script>
  <script type="module" src="./modern-pie-charts.js"></script>
  <script type="module" src="./analytics-fullscreen.js"></script>
  <script type="module" src="./analytics-fullscreen-integration.js"></script>

  <!-- Settings Panel -->
  <div id="dashboardSettingsPanel" class="fixed right-0 top-0 h-full w-96 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out z-40 overflow-y-auto">
    <div class="p-4 bg-gradient-to-r from-indigo-500 to-blue-600 text-white">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-semibold">Dashboard Settings</h3>
        <button id="closeSettingsBtn" class="text-white hover:text-gray-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
    
    <!-- Print Settings Section -->
    <div id="printSettingsContainer" class="p-4 border-b border-gray-200">
      <h4 class="font-medium text-gray-800 mb-3">Print Settings</h4>
      <div class="mb-3">
        <label class="block text-sm font-medium text-gray-700 mb-1">Paper Size</label>
        <select id="printPaperSize" class="w-full border rounded-md px-3 py-2 text-sm">
          <option value="a4">A4</option>
          <option value="letter" selected>Letter</option>
          <option value="legal">Legal</option>
        </select>
      </div>
      <div class="mb-3">
        <label class="block text-sm font-medium text-gray-700 mb-1">Orientation</label>
        <select id="printOrientation" class="w-full border rounded-md px-3 py-2 text-sm">
          <option value="portrait">Portrait</option>
          <option value="landscape" selected>Landscape</option>
        </select>
      </div>
      <div class="mb-3">
        <div class="flex items-center mb-2">
          <input type="checkbox" id="printIncludeAll" class="form-checkbox" checked>
          <label class="ml-2 text-sm text-gray-700">Include all charts</label>
        </div>
        <div class="flex items-center">
          <input type="checkbox" id="printIncludeTable" class="form-checkbox">
          <label class="ml-2 text-sm text-gray-700">Include data table</label>
        </div>
      </div>
      <button id="printFromSettings" class="w-full px-3 py-2 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center justify-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
        Print Dashboard
      </button>
    </div>
  </div>

  <!-- Settings Panel Initialization Script -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Get the settings button, panel, and close button
      const settingsBtn = document.getElementById('dashboardSettingsBtn');
      const settingsPanel = document.getElementById('dashboardSettingsPanel');
      const closeSettingsBtn = document.getElementById('closeSettingsBtn');
      
      // Toggle panel when settings button is clicked
      if (settingsBtn && settingsPanel) {
        settingsBtn.addEventListener('click', () => {
          settingsPanel.classList.toggle('translate-x-full');
        });
      }
      
      // Close panel when close button is clicked
      if (closeSettingsBtn && settingsPanel) {
        closeSettingsBtn.addEventListener('click', () => {
          settingsPanel.classList.add('translate-x-full');
        });
      }
      
      // Set up settings actions
      const refreshBtn = document.getElementById('settingsRefreshBtn');
      const exportBtn = document.getElementById('settingsExportBtn');
      const printBtn = document.getElementById('settingsPrintBtn');
      const printFromSettingsBtn = document.getElementById('printFromSettings');
      
      // Refresh data
      if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
          const mainRefreshBtn = document.getElementById('refreshData');
          if (mainRefreshBtn) {
            mainRefreshBtn.click();
          }
        });
      }
      
      // Export data
      if (exportBtn) {
        exportBtn.addEventListener('click', () => {
          const mainExportBtn = document.getElementById('exportCSV');
          if (mainExportBtn) {
            mainExportBtn.click();
          }
        });
      }
      
      // Print from settings panel buttons
      const printReport = () => {
        const printManager = window.printManager;
        if (printManager && typeof printManager.showPrintDialog === 'function') {
          printManager.showPrintDialog();
          settingsPanel.classList.add('translate-x-full');
        } else {
          // Fallback to browser print
          window.print();
        }
      };
      
      if (printBtn) {
        printBtn.addEventListener('click', printReport);
      }
      
      if (printFromSettingsBtn) {
        printFromSettingsBtn.addEventListener('click', printReport);
      }
    });
  </script>
</body>
</html> 