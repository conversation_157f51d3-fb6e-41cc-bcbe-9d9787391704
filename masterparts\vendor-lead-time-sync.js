// vendor-lead-time-sync.js - Specialized component for vendor lead time synchronization
import { connectionManager } from "../core/connection.js";
import { NotificationSystem } from "../core/notifications.js";

export class VendorLeadTimeSync {
  constructor() {
    this.notificationSystem = new NotificationSystem();
    this.isProcessing = false;
    this.results = null;
  }

  /**
   * Sync vendor lead times to Acumatica based on historical PO data
   * @param {Array} poData - Array of purchase order data
   * @param {boolean} excludeWeekends - Whether to exclude weekends in lead time calculations
   * @returns {Promise<Object>} - Sync results including success status and counts
   */
  async syncLeadTimes(poData, excludeWeekends = true) {
    if (!poData || poData.length === 0) {
      this.notificationSystem.addNotification('No data available to sync', 'error');
      return { success: false, error: 'No data available' };
    }
    
    try {
      this.isProcessing = true;
      
      // Check Acumatica connection
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        this.notificationSystem.addNotification('Please connect to Acumatica first', 'error');
        return { success: false, error: 'Not connected to Acumatica' };
      }
      
      // Calculate vendor lead times
      const vendorLeadTimes = this.calculateVendorLeadTimes(poData, excludeWeekends);
      
      // Push to Acumatica (simulated for now)
      const updateResults = await this.updateAcumaticaLeadTimes(vendorLeadTimes);
      
      this.results = {
        success: true,
        totalVendors: Object.keys(vendorLeadTimes).length,
        updatedVendors: updateResults.updatedCount,
        failedVendors: updateResults.failedCount,
        timestamp: new Date().toISOString()
      };
      
      return this.results;
    } catch (error) {
      console.error('Error syncing lead times:', error);
      this.notificationSystem.addNotification(`Lead time sync error: ${error.message}`, 'error');
      return { success: false, error: error.message };
    } finally {
      this.isProcessing = false;
    }
  }
  
  /**
   * Calculate average lead times per vendor from PO data
   * @param {Array} poData - Purchase order data
   * @param {boolean} excludeWeekends - Whether to exclude weekends
   * @returns {Object} - Mapping of vendor IDs to calculated lead times
   */
  calculateVendorLeadTimes(poData, excludeWeekends) {
    const vendorLeadTimes = {};
    
    // Group by vendor and calculate lead times
    poData.forEach(po => {
      if (!po.VendorID?.value || !po.ActualDelivery || !po.Date?.value) return;
      
      const vendorId = po.VendorID.value;
      const orderDate = new Date(po.Date.value);
      const deliveryDate = new Date(po.ActualDelivery);
      
      let leadTime = Math.round((deliveryDate - orderDate) / (1000 * 60 * 60 * 24));
      
      // Exclude weekends if needed
      if (excludeWeekends) {
        leadTime = this.calculateBusinessDays(orderDate, deliveryDate);
      }
      
      if (!vendorLeadTimes[vendorId]) {
        vendorLeadTimes[vendorId] = {
          totalLeadTime: 0,
          orderCount: 0,
          recentOrders: []
        };
      }
      
      vendorLeadTimes[vendorId].totalLeadTime += leadTime;
      vendorLeadTimes[vendorId].orderCount++;
      
      // Keep track of the 5 most recent orders for this vendor
      vendorLeadTimes[vendorId].recentOrders.push({
        orderNumber: po.OrderNbr?.value,
        orderDate: orderDate,
        deliveryDate: deliveryDate,
        leadTime: leadTime
      });
      
      // Sort by date descending and keep only the 5 most recent
      vendorLeadTimes[vendorId].recentOrders.sort((a, b) => b.orderDate - a.orderDate);
      if (vendorLeadTimes[vendorId].recentOrders.length > 5) {
        vendorLeadTimes[vendorId].recentOrders.pop();
      }
    });
    
    // Calculate averages
    const vendorAverages = {};
    for (const [vendorId, data] of Object.entries(vendorLeadTimes)) {
      if (data.orderCount > 0) {
        // Only include vendors with at least 2 orders
        if (data.orderCount >= 2) {
          const avgLeadTime = Math.round(data.totalLeadTime / data.orderCount);
          
          // Set reasonable limits (between 1 and 90 days)
          vendorAverages[vendorId] = Math.max(1, Math.min(90, avgLeadTime));
        }
      }
    }
    
    return vendorAverages;
  }
  
  /**
   * Calculate business days between two dates (excluding weekends)
   * @param {Date} startDate - Start date
   * @param {Date} endDate - End date
   * @returns {number} - Number of business days
   */
  calculateBusinessDays(startDate, endDate) {
    // Clone dates to avoid modifying originals
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    // Set to beginning of day
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);
    
    // Calculate total days
    let days = Math.round((end - start) / (1000 * 60 * 60 * 24));
    
    // Count weekends
    let startDay = start.getDay(); // 0 = Sunday, 6 = Saturday
    let endDay = end.getDay();
    
    // Calculate full weeks
    const fullWeeks = Math.floor(days / 7);
    let weekendDays = fullWeeks * 2; // 2 weekend days per week
    
    // Handle remaining days
    const remainingDays = days % 7;
    if (remainingDays > 0) {
      // Start day after start date
      let currentDay = startDay;
      for (let i = 0; i < remainingDays; i++) {
        currentDay = (currentDay + 1) % 7;
        if (currentDay === 0 || currentDay === 6) {
          weekendDays++;
        }
      }
    }
    
    // Calculate business days
    return days - weekendDays;
  }
  
  /**
   * Update vendor lead times in Acumatica
   * @param {Object} vendorLeadTimes - Mapping of vendor IDs to lead times
   * @returns {Promise<Object>} - Update results
   */
  async updateAcumaticaLeadTimes(vendorLeadTimes) {
    // In a real implementation, this would make API calls to Acumatica
    // to update vendor records with the calculated lead times
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Simulate a mix of successes and failures
    const vendorIds = Object.keys(vendorLeadTimes);
    const updatedCount = Math.round(vendorIds.length * 0.9); // 90% success rate
    const failedCount = vendorIds.length - updatedCount;
    
    console.log('Would update these lead times in Acumatica:', vendorLeadTimes);
    
    return {
      updatedCount,
      failedCount
    };
  }
}

export default VendorLeadTimeSync; 