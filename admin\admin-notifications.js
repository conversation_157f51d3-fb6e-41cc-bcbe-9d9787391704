// admin-notifications.js - Handles admin messages and notification system

export class NotificationSystem {
  constructor() {
    this.SHEETDB_API_URL = "https://sheetdb.io/api/v1/lcon68jvxh23y";
    this.SHEETDB_API_TOKEN = "Bearer w5icozbrzltd65ilssma8sk2al9i9zu7xpjkr6cc";
    this.adminMessage = null;
    this.notificationCount = 0;
  }

  // Initialize notification system and load admin message
  async init() {
    try {
      await this.loadAdminMessage();
      return true;
    } catch (error) {
      console.error("Error initializing notification system:", error);
      return false;
    }
  }

  // Load admin message from storage or API
  async loadAdminMessage() {
    try {
      // First try to get from storage
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await new Promise(resolve => {
          chrome.storage.local.get(['user'], data => {
            resolve(data?.user || null);
          });
        });

        if (result && result["Adminmessage-Messge"]) {
          this.adminMessage = {
            name: result["Adminmessage-Name"] || "Admin",
            avatar: result["Adminmessage-Avatar"] || "default",
            message: result["Adminmessage-Messge"],
            timestamp: new Date().toISOString()
          };
          return this.adminMessage;
        }
      } else {
        // Try localStorage fallback
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          if (user && user["Adminmessage-Messge"]) {
            this.adminMessage = {
              name: user["Adminmessage-Name"] || "Admin",
              avatar: user["Adminmessage-Avatar"] || "default",
              message: user["Adminmessage-Messge"],
              timestamp: new Date().toISOString()
            };
            return this.adminMessage;
          }
        }
      }

      // If not found in storage, fetch from API
      return await this.fetchAdminMessageFromApi();
    } catch (error) {
      console.error("Error loading admin message:", error);
      throw error;
    }
  }
  
  // Refresh admin message directly from API, bypassing storage
  async refreshAdminMessage() {
    try {
      this.addNotification("Updating messages...", "info");
      return await this.fetchAdminMessageFromApi(true);
    } catch (error) {
      console.error("Error refreshing admin message:", error);
      this.addNotification("Update failed. Please try again later.", "error");
      throw error;
    }
  }

  // Fetch admin message directly from API
  async fetchAdminMessageFromApi(isRefresh = false) {
    try {
      // Get current user email from storage
      let userEmail = '';
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await new Promise(resolve => {
          chrome.storage.local.get(['user'], data => {
            resolve(data?.user || null);
          });
        });
        
        if (result && result.Email) {
          userEmail = result.Email;
        }
      } else {
        // Fallback to localStorage
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          if (user && user.Email) {
            userEmail = user.Email;
          }
        }
      }
      
      if (!userEmail) {
        throw new Error("User email not found in storage");
      }
      
      // Add cache busting for refresh requests
      const apiUrl = `${this.SHEETDB_API_URL}${isRefresh ? `?_cb=${Date.now()}` : ''}`;
      
      // Fetch all user data from API - using the main endpoint to avoid 405 errors
      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": this.SHEETDB_API_TOKEN,
          "Cache-Control": isRefresh ? "no-cache, no-store, must-revalidate" : "default",
          "Pragma": isRefresh ? "no-cache" : "default"
        },
      });

      if (!response.ok) {
        throw new Error(`Network request failed: ${response.status}`);
      }

      const allData = await response.json();
      
      // Find the user by email
      const userData = allData.find(user => user.Email === userEmail);
      
      if (userData) {
        // Get admin message
        const adminName = userData["Adminmessage-Name"];
        const adminAvatar = userData["Adminmessage-Avatar"];
        const adminMessage = userData["Adminmessage-Messge"];
        
        if (adminMessage) {
          this.adminMessage = {
            name: adminName || "Admin",
            avatar: adminAvatar || "default",
            message: adminMessage,
            timestamp: new Date().toISOString()
          };
          
          // Also update the user data in storage with fresh admin message
          if (isRefresh && typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.local.get(['user'], (result) => {
              if (result.user) {
                const updatedUser = {...result.user};
                updatedUser["Adminmessage-Name"] = adminName;
                updatedUser["Adminmessage-Avatar"] = adminAvatar;
                updatedUser["Adminmessage-Messge"] = adminMessage;
                
                chrome.storage.local.set({ user: updatedUser }, () => {
                  console.log("Updated user data with fresh admin message");
                });
              }
            });
          }
          
          if (isRefresh) {
            this.addNotification("Messages updated successfully", "success");
          }
          
          return this.adminMessage;
        }
      }
      
      throw new Error("Admin message not found");
    } catch (error) {
      console.error("Error fetching admin message from API:", error);
      
      if (!isRefresh) {
        // Return a default message in case of error, but only when not explicitly refreshing
        this.adminMessage = {
          name: "System",
          avatar: "default",
          message: "Welcome to Envent Bridge",
          timestamp: new Date().toISOString()
        };
        return this.adminMessage;
      }
      
      throw error;
    }
  }

  // Show notification popup
  showNotificationPopup(container) {
    if (!container) return;
    
    // Close any existing popup
    this.closeNotificationPopup(container);
    
    // Create notification popup
    const popupHTML = `
      <div id="notificationPopup" class="notification-popup">
        <div class="notification-header">
          <h3>Notifications</h3>
          <button id="closeNotificationBtn" class="close-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="notification-content">
          ${this.adminMessage ? this.renderAdminMessage() : 
            '<div class="empty-message">No notifications available</div>'}
        </div>
        <div class="notification-footer">
          <button id="refreshNotificationsBtn">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 inline-block">
              <path d="M23 4v6h-6"></path>
              <path d="M1 20v-6h6"></path>
              <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
            </svg>
            Update Messages
          </button>
        </div>
      </div>
      
      <style>
        .notification-popup {
          position: absolute;
          top: 60px;
          right: 16px;
          width: 300px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 1000;
          overflow: hidden;
        }
        
        .dark-mode .notification-popup {
          background: #2d2d2d;
          color: white;
          border: 1px solid #3d3d3d;
        }
        
        .notification-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e5e5e5;
        }
        
        .dark-mode .notification-header {
          border-color: #3d3d3d;
        }
        
        .notification-header h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }
        
        .close-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #6b7280;
        }
        
        .dark-mode .close-btn {
          color: #d1d5db;
        }
        
        .close-btn:hover {
          color: #374151;
        }
        
        .dark-mode .close-btn:hover {
          color: white;
        }
        
        .notification-content {
          max-height: 300px;
          overflow-y: auto;
          padding: 16px;
        }
        
        .notification-footer {
          display: flex;
          justify-content: center;
          padding: 8px 16px;
          border-top: 1px solid #e5e5e5;
        }
        
        .dark-mode .notification-footer {
          border-color: #3d3d3d;
        }
        
        .notification-footer button {
          background: #0066ff;
          color: white;
          border: none;
          padding: 6px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 12px;
        }
        
        .notification-footer button:hover {
          background: #0052cc;
        }
        
        .admin-message {
          display: flex;
          gap: 12px;
          margin-bottom: 16px;
          padding-bottom: 16px;
          border-bottom: 1px solid #e5e5e5;
        }
        
        .dark-mode .admin-message {
          border-color: #3d3d3d;
        }
        
        .admin-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
          border: 1px solid #e5e5e5;
        }
        
        .dark-mode .admin-avatar {
          border-color: #3d3d3d;
        }
        
        .message-content {
          flex: 1;
        }
        
        .admin-name {
          font-weight: 600;
          margin-bottom: 4px;
        }
        
        .message-text {
          color: #374151;
          font-size: 14px;
          margin-bottom: 8px;
        }
        
        .dark-mode .message-text {
          color: #d1d5db;
        }
        
        .message-time {
          color: #6b7280;
          font-size: 12px;
        }
        
        .dark-mode .message-time {
          color: #9ca3af;
        }
        
        .empty-message {
          text-align: center;
          color: #6b7280;
          padding: 24px 0;
        }
        
        .dark-mode .empty-message {
          color: #9ca3af;
        }
      </style>
    `;
    
    // Append popup to container
    container.insertAdjacentHTML('beforeend', popupHTML);
    
    // Add event listeners
    const closeBtn = container.querySelector('#closeNotificationBtn');
    const refreshBtn = container.querySelector('#refreshNotificationsBtn');
    
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeNotificationPopup(container);
      });
    }
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        // Update button to show loading state
        refreshBtn.innerHTML = `
          <svg class="animate-spin h-4 w-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Updating...
        `;
        refreshBtn.disabled = true;
        
        try {
          // Use the refresh method that directly fetches from API
          await this.refreshAdminMessage();
          
          // Update the notification content
          const contentArea = container.querySelector('.notification-content');
          if (contentArea) {
            contentArea.innerHTML = this.renderAdminMessage();
          }
        } catch (error) {
          console.error("Error refreshing notifications:", error);
        } finally {
          // Restore button state
          refreshBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1 inline-block">
              <path d="M23 4v6h-6"></path>
              <path d="M1 20v-6h6"></path>
              <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
            </svg>
            Update Messages
          `;
          refreshBtn.disabled = false;
        }
      });
    }
    
    // Close when clicking outside
    document.addEventListener('click', this.handleOutsideClick.bind(this, container));
  }
  
  // Close notification popup
  closeNotificationPopup(container) {
    const existingPopup = container.querySelector('#notificationPopup');
    if (existingPopup) {
      existingPopup.remove();
      document.removeEventListener('click', this.handleOutsideClick);
    }
  }
  
  // Handle clicks outside the popup
  handleOutsideClick(container, event) {
    const popup = container.querySelector('#notificationPopup');
    const notificationButton = document.getElementById('notificationButton');
    
    if (popup && !popup.contains(event.target) && !notificationButton.contains(event.target)) {
      this.closeNotificationPopup(container);
    }
  }
  
  // Render admin message HTML
  renderAdminMessage() {
    if (!this.adminMessage) return '';
    
    const timestamp = new Date(this.adminMessage.timestamp);
    const formattedTime = timestamp.toLocaleString();
    const avatarSrc = this.adminMessage.avatar && this.adminMessage.avatar !== 'default' 
      ? `images/avatars/avatar_${this.adminMessage.avatar}.jpg` 
      : 'images/avatars/default.jpg';
    
    return `
      <div class="admin-message">
        <img src="${avatarSrc}" alt="${this.adminMessage.name}" class="admin-avatar">
        <div class="message-content">
          <div class="admin-name">${this.adminMessage.name}</div>
          <div class="message-text">${this.adminMessage.message}</div>
          <div class="message-time">${formattedTime}</div>
        </div>
      </div>
    `;
  }
  
  // Display a temporary notification message (toast)
  addNotification(message, type = 'info') {
    const container = document.body;
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `temp-notification temp-notification-${type}`;
    
    // Set notification content
    notification.innerHTML = `
      <div class="notification-icon">
        ${this.getNotificationIcon(type)}
      </div>
      <div class="notification-message">${message}</div>
      <button class="notification-close">×</button>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .temp-notification {
        position: fixed;
        top: 16px;
        right: 16px;
        display: flex;
        align-items: center;
        padding: 12px 16px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        animation: slideIn 0.3s ease-out, fadeOut 0.5s ease-in 2.5s forwards;
        z-index: 1000;
        max-width: 320px;
      }
      
      .dark-mode .temp-notification {
        box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      }
      
      .temp-notification-info {
        background: #e1f0ff;
        border-left: 4px solid #0066ff;
        color: #0052cc;
      }
      
      .dark-mode .temp-notification-info {
        background: #1e3a5f;
        color: #90caf9;
      }
      
      .temp-notification-success {
        background: #e6f4ea;
        border-left: 4px solid #34a853;
        color: #1e7e34;
      }
      
      .dark-mode .temp-notification-success {
        background: #1e3b2c;
        color: #a5d6a7;
      }
      
      .temp-notification-warning {
        background: #fff8e6;
        border-left: 4px solid #fbbc05;
        color: #b06000;
      }
      
      .dark-mode .temp-notification-warning {
        background: #3d3223;
        color: #ffe082;
      }
      
      .temp-notification-error {
        background: #fdecea;
        border-left: 4px solid #ea4335;
        color: #c62828;
      }
      
      .dark-mode .temp-notification-error {
        background: #3e2827;
        color: #ef9a9a;
      }
      
      .notification-icon {
        margin-right: 12px;
        display: flex;
      }
      
      .notification-message {
        flex: 1;
        font-size: 14px;
      }
      
      .notification-close {
        background: none;
        border: none;
        color: inherit;
        font-size: 18px;
        cursor: pointer;
        padding: 0 0 0 12px;
        opacity: 0.7;
      }
      
      .notification-close:hover {
        opacity: 1;
      }
      
      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      
      @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; visibility: hidden; }
      }
    `;
    
    // Append to container
    container.appendChild(style);
    container.appendChild(notification);
    
    // Handle close button
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
      notification.remove();
      style.remove();
    });
    
    // Automatically remove after 3 seconds
    setTimeout(() => {
      notification.remove();
      style.remove();
    }, 3000);
  }
  
  // Get icon for notification based on type
  getNotificationIcon(type) {
    switch (type) {
      case 'info':
        return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>`;
      case 'success':
        return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
          <polyline points="22 4 12 14.01 9 11.01"></polyline>
        </svg>`;
      case 'warning':
        return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
          <line x1="12" y1="9" x2="12" y2="13"></line>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>`;
      case 'error':
        return `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>`;
      default:
        return '';
    }
  }
}