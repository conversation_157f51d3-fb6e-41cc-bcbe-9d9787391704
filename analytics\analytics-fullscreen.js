// analytics-fullscreen.js - Dedicated module for the fullscreen analytics dashboard

import { createM<PERSON>rn<PERSON><PERSON><PERSON><PERSON>, createModern<PERSON><PERSON><PERSON><PERSON>, createMixed<PERSON><PERSON>, createMapVisualization } from "./modern-charts.js";
import { createModernPie<PERSON><PERSON>, createModernDonut<PERSON>hart } from "./modern-pie-charts.js";
import { HistoryTable } from "./analytics-history.js";
import { ChartManager } from "./analytics-fullscreen-charts.js";
import { PrintManager } from "./analytics-fullscreen-print.js";

/**
 * FullscreenDashboard class - Handles the fullscreen analytics view
 * Separating this class into its own module improves maintainability and organization
 */
export class FullscreenDashboard {
  constructor() {
    this.historyData = [];
    this.timeFilter = 'month';
    this.costData = [];
    this.filters = {
      dailyFreightCost: 'month',
      shipmentVolume: 'week',
      shipmentByDay: 'month',
      shipmentByCarrier: 'month',
      carrierCost: 'month',
      topProducts: 'month',
      topDestination: 'month',
      customerShipments: 'month'
    };
    this.lastUpdate = new Date();
    this.historyTable = null;
    this.chartManager = null;
    this.printManager = null;
    
    // SheetDB API configuration
    this.sheetDbConfig = {
      url: 'https://sheetdb.io/api/v1/ygn268dcacru7',
      bearerToken: 'tgive5whsoypqz9f6lq7ggno3xkamhp1dhqhc9ed'
    };
  }

  /**
   * Initialize the dashboard and load data
   */
  async init() {
    try {
      console.log("Initializing enhanced fullscreen dashboard");
      
      // Show loading state
      document.getElementById('initialLoading').classList.remove('hidden');
      document.getElementById('dashboardContent').classList.add('hidden');
      document.getElementById('statusMessage').classList.add('hidden');
      
      // Load data with fallback to sample data
      await this.loadData();
      
      // Show dashboard content
      document.getElementById('initialLoading').classList.add('hidden');
      document.getElementById('dashboardContent').classList.remove('hidden');
      
      // Clean up header buttons - keep only essential ones
      this.organizeHeaderButtons();
      
      // Initialize the chart manager first
      this.initializeChartManager();
      
      // Initialize the print manager
      this.initializePrintManager();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Render all charts
      this.renderCharts();
      
      // Update KPI cards
      this.updateKPICards();
      
      // Initialize history table if container exists
      if (document.getElementById('historyTableContainer')) {
        this.historyTable = new HistoryTable(
          document.getElementById('historyTableContainer'),
          this.historyData.slice(0, 20)
        );
        this.historyTable.render();
      }
      
      // Update last updated text
      document.getElementById('lastUpdated').textContent = `Last updated: ${this.lastUpdate.toLocaleString()}`;
      
      // Process additional data for enhanced visualizations
      this.processShipmentData();
      
      // Set up data table filtering
      this.setupDataTableFiltering();
      
      console.log("Enhanced dashboard initialized successfully");
    } catch (error) {
      console.error("Enhanced initialization error:", error);
      document.getElementById('statusMessage').innerHTML = `<div class="error-message">Failed to initialize enhanced dashboard: ${error.message}</div>`;
      document.getElementById('statusMessage').classList.remove('hidden');
      document.getElementById('initialLoading').classList.add('hidden');
    }
  }
  
  /**
   * Organize header buttons to keep only essential ones
   */
  organizeHeaderButtons() {
    const controlButtons = document.getElementById('dashboardControlButtons');
    if (!controlButtons) return;
    
    // Get all buttons
    const refreshBtn = document.getElementById('refreshData');
    const exportBtn = document.getElementById('exportCSV');
    const printBtn = document.getElementById('printReport');
    const settingsBtn = document.getElementById('dashboardSettingsBtn');
    
    // Keep only print and settings buttons visible
    if (refreshBtn) refreshBtn.style.display = 'none';
    if (exportBtn) exportBtn.style.display = 'none';
    
    // Make sure settings panel has these buttons
    this.ensureSettingsPanelButtons();
  }
  
  /**
   * Ensure settings panel has required buttons
   */
  ensureSettingsPanelButtons() {
    const settingsPanel = document.getElementById('dashboardSettingsPanel');
    if (!settingsPanel) return;
    
    // Check if action buttons container exists
    let actionsContainer = settingsPanel.querySelector('.dashboard-actions');
    if (!actionsContainer) {
      // Get the first section (header)
      const header = settingsPanel.querySelector('.p-4.bg-gradient-to-r');
      if (!header) return;
      
      // Create actions section
      actionsContainer = document.createElement('div');
      actionsContainer.className = 'p-4 border-b border-gray-200 dashboard-actions';
      actionsContainer.innerHTML = `
        <h4 class="font-medium text-gray-800 mb-3">Dashboard Actions</h4>
        <div class="flex space-x-2">
          <button id="settingsRefreshBtn" class="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh Data
          </button>
          <button id="settingsExportBtn" class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Export Data
          </button>
          <button id="settingsPrintBtn" class="px-3 py-1.5 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print Report
          </button>
        </div>
      `;
      
      // Insert after header
      header.parentNode.insertBefore(actionsContainer, header.nextSibling);
    }
  }

  /**
   * Initialize the chart manager
   */
  initializeChartManager() {
    console.log("Initializing ChartManager");
    this.chartManager = new ChartManager();
    this.chartManager.init();
    
    // Make it globally available for debugging
    window.chartManager = this.chartManager;
  }
  
  /**
   * Initialize the print manager
   */
  initializePrintManager() {
    try {
      console.log("Initializing PrintManager");
      
      // Check if the PrintManager class is available
      if (typeof PrintManager === 'undefined') {
        console.error("PrintManager class is not defined. Make sure analytics-fullscreen-print.js is loaded");
        return;
      }
      
      // Create the PrintManager instance with the dashboard reference
      this.printManager = new PrintManager(this);
      
      // Check if initialization method exists before calling it
      if (typeof this.printManager.init === 'function') {
        this.printManager.init();
      } else {
        console.error("PrintManager missing init method");
      }
      
      // Connect PrintManager to ChartManager if both exist
      if (this.chartManager && this.printManager) {
        this.chartManager.printManager = this.printManager;
        console.log("Connected PrintManager to ChartManager");
      }
      
      // Make it globally available for debugging
      window.printManager = this.printManager;
      console.log("PrintManager initialized successfully");
    } catch (error) {
      console.error("Error initializing PrintManager:", error);
    }
  }

  /**
   * Load data from SheetDB API with robust fallbacks
   */
  async loadData() {
    return new Promise(async (resolve) => {
      console.log("Attempting to load data from SheetDB API");
      
      // Set a flag to track if we've loaded data
      let dataLoaded = false;
      
      const useTestData = () => {
        console.log("Using test data");
        this.createTestData();
        dataLoaded = true;
        resolve(true);
      };
      
      try {
        // First try fetching from SheetDB API
        const response = await fetch(this.sheetDbConfig.url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${this.sheetDbConfig.bearerToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          if (Array.isArray(data) && data.length > 0) {
            console.log(`Fetched ${data.length} records from SheetDB API`);
            this.historyData = data.map(item => {
              // Convert date strings to proper date objects if needed
              if (item.Date && typeof item.Date === 'string') {
                try {
                  // Keep the original date string if conversion fails
                  const dateParts = item.Date.split('-');
                  if (dateParts.length === 3) {
                    const parsedDate = new Date(
                      parseInt(dateParts[0]), 
                      parseInt(dateParts[1]) - 1, 
                      parseInt(dateParts[2])
                    );
                    if (!isNaN(parsedDate.getTime())) {
                      item.DateObj = parsedDate;
                    }
                  }
                } catch (e) {
                  console.error("Date parsing error:", e);
                }
              }
              
              // Process freight cost
              if (item["Freight Cost"] && typeof item["Freight Cost"] === 'string') {
                item["Freight Cost"] = parseFloat(item["Freight Cost"].replace(/[^0-9.-]+/g, "")) || 0;
              }
              
              return item;
            });
            
            // Set the last update time
            this.lastUpdate = new Date();
            
            // Extract cost data for charts
            this.processCostData();
            
            dataLoaded = true;
            resolve(true);
            return;
          }
        } else {
          console.error("API response not OK:", response.status, response.statusText);
        }
      } catch (e) {
        console.error("Error fetching from API:", e);
      }
      
      // Try localStorage if API failed
      if (!dataLoaded) {
        try {
          console.log("Trying localStorage");
          const storedData = localStorage.getItem('analyticsData');
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            if (parsedData && parsedData.shipmentData && parsedData.shipmentData.length > 0) {
              console.log(`Found ${parsedData.shipmentData.length} records in localStorage`);
              this.historyData = parsedData.shipmentData;
              this.filters = parsedData.timeFilter || this.filters;
              this.costData = parsedData.costData || [];
              dataLoaded = true;
              resolve(true);
              return;
            }
          }
          console.log("No valid data in localStorage");
        } catch (e) {
          console.error("Error reading from localStorage:", e);
        }
      }
      
      // Then try Chrome storage if still no data
      if (!dataLoaded && typeof chrome !== 'undefined' && chrome.storage) {
        console.log("Chrome storage API is available");
        
        try {
          chrome.storage.local.get(['analyticsData'], (result) => {
            if (chrome.runtime.lastError) {
              console.error('Chrome storage error:', chrome.runtime.lastError);
              if (!dataLoaded) useTestData();
              return;
            }
            
            console.log("Storage get result:", result);
            
            if (result && result.analyticsData && result.analyticsData.shipmentData && 
                result.analyticsData.shipmentData.length > 0) {
              console.log(`Found ${result.analyticsData.shipmentData.length} records in Chrome storage`);
              this.historyData = result.analyticsData.shipmentData;
              this.filters = result.analyticsData.timeFilter || this.filters;
              this.costData = result.analyticsData.costData || [];
              dataLoaded = true;
              resolve(true);
              return;
            }
            
            // If we get here and no data is loaded yet, use test data
            if (!dataLoaded) useTestData();
          });
        } catch (error) {
          console.error("Exception accessing Chrome storage:", error);
          if (!dataLoaded) useTestData();
        }
      } else {
        // No Chrome storage, use test data if we haven't loaded data yet
        console.log("Chrome storage not available");
        if (!dataLoaded) useTestData();
      }
      
      // Safety timeout - if after 3 seconds we still don't have data, use test data
      setTimeout(() => {
        if (!dataLoaded) {
          console.log("Timeout reached, forcing test data");
          useTestData();
        }
      }, 3000);
    });
  }

  /**
   * Process cost data from history data
   */
  processCostData() {
    this.costData = this.historyData
      .filter(item => item["Freight Cost"] && !isNaN(parseFloat(item["Freight Cost"])))
      .map(item => ({
        date: item.Date,
        cost: parseFloat(item["Freight Cost"])
      }));
  }

  /**
   * Create test data if needed
   */
  createTestData() {
    this.historyData = Array(40).fill(null).map((_, i) => ({
      Date: new Date(Date.now() - i * 86400000).toISOString().split('T')[0],
      Carrier: ['FedEx', 'UPS', 'DHL', 'USPS'][Math.floor(Math.random() * 4)],
      Country: ['USA', 'Canada', 'Mexico', 'UK', 'Germany'][Math.floor(Math.random() * 5)],
      "Freight Cost": (Math.random() * 500 + 50).toFixed(2),
      "Customer Name": `Customer ${Math.floor(Math.random() * 10) + 1}`,
      "Inventory ID": `PART-${1000 + Math.floor(Math.random() * 20)}`
    }));
    
    this.costData = this.historyData.map(item => ({
      date: item.Date,
      cost: parseFloat(item["Freight Cost"])
    }));
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Settings button and panel
    const settingsBtn = document.getElementById('dashboardSettingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        console.log('Settings button clicked');
        const settingsPanel = document.getElementById('dashboardSettingsPanel');
        if (settingsPanel) {
          settingsPanel.classList.toggle('translate-x-full');
        }
      });
    }
    
    // Settings panel close button
    const closeSettingsBtn = document.getElementById('closeSettingsBtn');
    if (closeSettingsBtn) {
      closeSettingsBtn.addEventListener('click', () => {
        console.log('Close settings button clicked');
        const settingsPanel = document.getElementById('dashboardSettingsPanel');
        if (settingsPanel) {
          settingsPanel.classList.add('translate-x-full');
        }
      });
    }
    
    // Setup tab navigation
    this.setupTabEventListeners();
    
    // Print report button
    const printBtn = document.getElementById('printReport');
    if (printBtn && this.printManager) {
      printBtn.addEventListener('click', () => {
        console.log('Print button clicked');
        if (typeof this.printManager.executePrint === 'function') {
          this.printManager.executePrint();
        } else {
          console.error('PrintManager.executePrint is not a function');
          window.print(); // Fallback
        }
      });
    }
    
    // Settings panel buttons within the panel
    const refreshBtn = document.getElementById('refreshData');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        console.log('Refresh button clicked');
        this.loadData();
      });
    }
    
    const exportBtn = document.getElementById('exportCSV');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        console.log('Export button clicked');
        this.exportAsCSV(this.historyData);
      });
    }
    
    // Connect print button in settings panel
    const settingsPrint = document.getElementById('printFromSettings');
    if (settingsPrint && this.printManager) {
      settingsPrint.addEventListener('click', () => {
        console.log('Print from settings button clicked');
        if (typeof this.printManager.executePrint === 'function') {
          this.printManager.executePrint();
        } else {
          console.error('PrintManager.executePrint is not a function');
          window.print(); // Fallback
        }
      });
    }
    
    // Select/deselect all charts buttons
    const selectAllBtn = document.getElementById('selectAllChartsPrintBtn');
    if (selectAllBtn && this.printManager) {
      selectAllBtn.addEventListener('click', () => {
        console.log('Select all charts button clicked');
        this.printManager.selectAllCharts(true);
      });
    }
    
    const deselectAllBtn = document.getElementById('deselectAllChartsPrintBtn');
    if (deselectAllBtn && this.printManager) {
      deselectAllBtn.addEventListener('click', () => {
        console.log('Deselect all charts button clicked');
        this.printManager.selectAllCharts(false);
      });
    }
    
    // Restore all charts button
    const restoreAllBtn = document.getElementById('restoreAllChartsBtn');
    if (restoreAllBtn && this.chartManager) {
      restoreAllBtn.addEventListener('click', () => {
        console.log('Restore all charts button clicked');
        this.chartManager.restoreAllCharts();
      });
    }
    
    // Chart size selector
    const chartSizeSelect = document.getElementById('chartSizeSelect');
    if (chartSizeSelect && this.chartManager) {
      chartSizeSelect.addEventListener('change', (e) => {
        console.log('Chart size changed to', e.target.value);
        this.chartManager.updateChartSize(e.target.value);
      });
    }
    
    // Color theme selector
    const colorThemeSelect = document.getElementById('colorThemeSelect');
    if (colorThemeSelect && this.chartManager) {
      colorThemeSelect.addEventListener('change', (e) => {
        console.log('Color theme changed to', e.target.value);
        this.chartManager.updateColorTheme(e.target.value);
      });
    }
    
    // Dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle && this.chartManager) {
      darkModeToggle.addEventListener('change', (e) => {
        console.log('Dark mode toggled', e.target.checked);
        document.body.classList.toggle('dark-mode', e.target.checked);
        this.chartManager.updateDarkMode(e.target.checked);
      });
    }
  }

  /**
   * Refresh data from API and update all tabs and charts
   */
  async refreshData() {
    try {
      console.log("Refreshing dashboard data");
      
      // Verify ApexCharts is available
      if (typeof ApexCharts === 'undefined') {
        throw new Error('ApexCharts is not defined');
      }
      
      // Show loading indicator for refresh
      const refreshButton = document.getElementById('refreshData');
      
      if (refreshButton) {
        const originalContent = refreshButton.innerHTML;
        refreshButton.innerHTML = `
          <svg class="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Refreshing...
        `;
        refreshButton.disabled = true;
      }
      
      // Show status message
      document.getElementById('statusMessage').innerHTML = `<div class="bg-blue-50 text-blue-700 p-2 rounded-md">Refreshing data...</div>`;
      document.getElementById('statusMessage').classList.remove('hidden');
      
      // Fetch fresh data from API
      const response = await fetch(this.sheetDbConfig.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${this.sheetDbConfig.bearerToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (!Array.isArray(data) || data.length === 0) {
        throw new Error('API returned empty or invalid data');
      }
      
      console.log(`Fetched ${data.length} records from SheetDB API`);
      
      // Process the data
      this.historyData = data.map(item => {
        // Format dates
        if (item.Date && typeof item.Date === 'string') {
          try {
            const dateParts = item.Date.split('-');
            if (dateParts.length === 3) {
              const parsedDate = new Date(
                parseInt(dateParts[0]), 
                parseInt(dateParts[1]) - 1, 
                parseInt(dateParts[2])
              );
              if (!isNaN(parsedDate.getTime())) {
                item.DateObj = parsedDate;
              }
            }
          } catch (e) {
            console.error("Date parsing error:", e);
          }
        }
        
        // Process freight cost
        if (item["Freight Cost"] && typeof item["Freight Cost"] === 'string') {
          item["Freight Cost"] = parseFloat(item["Freight Cost"].replace(/[^0-9.-]+/g, "")) || 0;
        }
        
        return item;
      });
      
      // Update the last refresh time
      this.lastUpdate = new Date();
      document.getElementById('lastUpdated').textContent = `Last updated: ${this.lastUpdate.toLocaleString()}`;
      
      // Process cost data for charts
      this.processCostData();
      
      // Re-process all data for visualizations
      this.processShipmentData();
      
      // Update KPI cards first (they're visible in all tabs)
      this.updateKPICards();
      
      // Make sure ApexCharts is still available before updating charts
      if (typeof ApexCharts === 'undefined') {
        throw new Error('ApexCharts is not defined');
      }
      
      // Update the mini charts in KPI cards - REMOVING THIS LINE TO AVOID DUPLICATES
      // this.initKPIMiniCharts();
      
      // Re-render all charts
      this.renderCharts();
      
      // Update the history table if it exists
      if (this.historyTable) {
        this.historyTable.updateData(this.historyData.slice(0, 20));
      }
      
      // Update filter dropdowns with new data
      this.populateFilterDropdowns();
      
      // Save to localStorage for offline access
      try {
        localStorage.setItem('analyticsData', JSON.stringify({
          shipmentData: this.historyData,
          timeFilter: this.filters,
          costData: this.costData,
          lastUpdate: this.lastUpdate
        }));
      } catch (e) {
        console.error("Error saving to localStorage:", e);
      }
      
      // Update Chrome storage if available
      if (typeof chrome !== 'undefined' && chrome.storage) {
        try {
          chrome.storage.local.set({
            'analyticsData': {
              shipmentData: this.historyData,
              timeFilter: this.filters,
              costData: this.costData,
              lastUpdate: this.lastUpdate
            }
          });
        } catch (e) {
          console.error("Error saving to Chrome storage:", e);
        }
      }
      
      // Display success message
      document.getElementById('statusMessage').innerHTML = `
        <div class="bg-green-50 text-green-700 p-2 rounded-md">
          Data refreshed successfully with ${this.historyData.length} records.
        </div>
      `;
      
      // Hide the message after 3 seconds
      setTimeout(() => {
        document.getElementById('statusMessage').classList.add('hidden');
      }, 3000);
      
      // Reset refresh button
      if (refreshButton) {
        refreshButton.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh Data
        `;
        refreshButton.disabled = false;
      }
      
      console.log("Data refresh complete");
      
    } catch (error) {
      console.error("Error during data refresh:", error);
      
      // Show error message
      document.getElementById('statusMessage').innerHTML = `
        <div class="error-message">
          Failed to refresh data: ${error.message}
        </div>
      `;
      
      // Reset refresh button
      const refreshButton = document.getElementById('refreshData');
      if (refreshButton) {
        refreshButton.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh Data
        `;
        refreshButton.disabled = false;
      }
    }
  }

  // KPI mini-charts
  initKPIMiniCharts() {
    console.log("Initializing KPI mini charts");
    
    // Create mini sparkline charts for KPI cards
    this.initShipmentsMiniChart();
    this.initCostMiniChart();
    this.initCarrierMiniChart();
    this.initDestinationMiniChart();
    this.initAvgCostMiniChart();
  }

  // Mini chart for shipments KPI
  initShipmentsMiniChart() {
    const dates = this.getLast7DatesArray();
    const data = this.getCountsPerDay(dates, 'shipments');
    
    const options = {
      series: [{
        name: 'Shipments',
        data: data
      }],
      chart: {
        height: 40,
        type: 'area',
        sparkline: {
          enabled: true
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      colors: ['#3b82f6'],
      tooltip: {
        fixed: {
          enabled: false
        },
        x: {
          show: false
        },
        y: {
          title: {
            formatter: function (seriesName) {
              return '';
            }
          }
        },
        marker: {
          show: false
        }
      }
    };

    const chartElement = document.getElementById('shipmentsMiniChart');
    if (chartElement) {
      // Clear previous chart if exists
      chartElement.innerHTML = '';
      
      // Check if instance already exists and destroy it
      if (this.shipmentsMiniChart) {
        this.shipmentsMiniChart.destroy();
      }
      
      this.shipmentsMiniChart = new ApexCharts(chartElement, options);
      this.shipmentsMiniChart.render();
    }
  }

  // Mini chart for cost KPI
  initCostMiniChart() {
    const dates = this.getLast7DatesArray();
    const data = this.getCountsPerDay(dates, 'cost');
    
    const options = {
      series: [{
        name: 'Cost',
        data: data
      }],
      chart: {
        height: 40,
        type: 'area',
        sparkline: {
          enabled: true
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      colors: ['#10b981'],
      tooltip: {
        fixed: {
          enabled: false
        },
        x: {
          show: false
        },
        y: {
          title: {
            formatter: function (seriesName) {
              return '';
            }
          },
          formatter: function(value) {
            return '$' + value.toFixed(2);
          }
        },
        marker: {
          show: false
        }
      }
    };

    const chartElement = document.getElementById('costMiniChart');
    if (chartElement) {
      // Clear previous chart if exists
      chartElement.innerHTML = '';
      
      // Check if instance already exists and destroy it
      if (this.costMiniChart) {
        this.costMiniChart.destroy();
      }
      
      this.costMiniChart = new ApexCharts(chartElement, options);
      this.costMiniChart.render();
    }
  }

  // Mini chart for carrier KPI
  initCarrierMiniChart() {
    // Create a simple donut chart showing carrier distribution
    const carrierCounts = this.getCarrierDistribution();
    
    const options = {
      series: carrierCounts.values,
      chart: {
        type: 'donut',
        height: 80,
        sparkline: {
          enabled: true
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#f59e0b', '#fb923c', '#fcd34d', '#fbbf24'],
      stroke: {
        width: 1
      },
      tooltip: {
        fixed: {
          enabled: false
        },
        y: {
          formatter: function(value, { seriesIndex, dataPointIndex, w }) {
            const label = carrierCounts.labels[seriesIndex];
            return label + ': ' + value;
          }
        }
      },
      legend: {
        show: false
      }
    };

    const chartElement = document.getElementById('carrierMiniChart');
    if (chartElement) {
      // Clear previous chart if exists
      chartElement.innerHTML = '';
      
      // Check if instance already exists and destroy it
      if (this.carrierMiniChart) {
        this.carrierMiniChart.destroy();
      }
      
      this.carrierMiniChart = new ApexCharts(chartElement, options);
      this.carrierMiniChart.render();
    }
  }

  // Mini chart for destination KPI
  initDestinationMiniChart() {
    // Create a simple donut chart showing destination distribution
    const destCounts = this.getDestinationDistribution();
    
    const options = {
      series: destCounts.values,
      chart: {
        type: 'donut',
        height: 80,
        sparkline: {
          enabled: true
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe'],
      stroke: {
        width: 1
      },
      tooltip: {
        fixed: {
          enabled: false
        },
        y: {
          formatter: function(value, { seriesIndex, dataPointIndex, w }) {
            const label = destCounts.labels[seriesIndex];
            return label + ': ' + value;
          }
        }
      },
      legend: {
        show: false
      }
    };

    const chartElement = document.getElementById('destinationMiniChart');
    if (chartElement) {
      // Clear previous chart if exists
      chartElement.innerHTML = '';
      
      // Check if instance already exists and destroy it
      if (this.destinationMiniChart) {
        this.destinationMiniChart.destroy();
      }
      
      this.destinationMiniChart = new ApexCharts(chartElement, options);
      this.destinationMiniChart.render();
    }
  }

  // Mini chart for avg cost KPI
  initAvgCostMiniChart() {
    // Create a gauge chart showing avg cost relative to target
    const avgCost = this.calculateAvgCost();
    const maxCost = Math.max(avgCost * 2, 200); // Dynamic range based on current avg
    
    const options = {
      series: [Math.round(avgCost)],
      chart: {
        height: 80,
        type: 'radialBar',
        sparkline: {
          enabled: true
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      colors: ['#ef4444'],
      plotOptions: {
        radialBar: {
          hollow: {
            size: '60%',
          },
          dataLabels: {
            show: false
          },
          track: {
            background: '#fee2e2',
          }
        }
      },
      stroke: {
        lineCap: 'round'
      }
    };

    const chartElement = document.getElementById('avgCostMiniChart');
    if (chartElement) {
      // Clear previous chart if exists
      chartElement.innerHTML = '';
      
      // Check if instance already exists and destroy it
      if (this.avgCostMiniChart) {
        this.avgCostMiniChart.destroy();
      }
      
      this.avgCostMiniChart = new ApexCharts(chartElement, options);
      this.avgCostMiniChart.render();
    }
  }

  // Helper function to get last 7 days as array of date strings
  getLast7DatesArray() {
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }
    return dates;
  }

  // Helper to count shipments or sum costs per day
  getCountsPerDay(dates, type) {
    const counts = [];
    
    dates.forEach(dateStr => {
      const dayData = this.historyData.filter(item => item.Date === dateStr);
      
      if (type === 'shipments') {
        counts.push(dayData.length);
      } else if (type === 'cost') {
        const total = dayData.reduce((sum, item) => sum + (parseFloat(item["Freight Cost"]) || 0), 0);
        counts.push(parseFloat(total.toFixed(2)));
      }
    });
    
    return counts;
  }

  // Helper to get carrier distribution
  getCarrierDistribution() {
    const carrierCounts = {};
    
    this.historyData.forEach(item => {
      const carrier = item.Carrier || 'Unknown';
      if (carrier !== 'Unknown' && carrier !== 'N/A') {
        carrierCounts[carrier] = (carrierCounts[carrier] || 0) + 1;
      }
    });
    
    // Sort and get top 4
    const topCarriers = Object.entries(carrierCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 4);
    
    return {
      labels: topCarriers.map(item => item[0]),
      values: topCarriers.map(item => item[1])
    };
  }

  // Helper to get destination distribution
  getDestinationDistribution() {
    const destCounts = {};
    
    this.historyData.forEach(item => {
      const dest = item.Country || 'Unknown';
      if (dest !== 'Unknown' && dest !== 'N/A') {
        destCounts[dest] = (destCounts[dest] || 0) + 1;
      }
    });
    
    // Sort and get top 4
    const topDests = Object.entries(destCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 4);
    
    return {
      labels: topDests.map(item => item[0]),
      values: topDests.map(item => item[1])
    };
  }

  // Helper to calculate average cost
  calculateAvgCost() {
    const totalCost = this.historyData.reduce((sum, item) => sum + (parseFloat(item["Freight Cost"]) || 0), 0);
    const count = this.historyData.length || 1;
    return totalCost / count;
  }

  /**
   * Update KPI cards with current data
   */
  updateKPICards() {
    try {
      // Calculate time periods
      const now = new Date();
      const thirtyDaysAgo = new Date(now);
      thirtyDaysAgo.setDate(now.getDate() - 30);
      const sixtyDaysAgo = new Date(now);
      sixtyDaysAgo.setDate(now.getDate() - 60);

      // Filter data for current and previous periods
      const currentPeriodData = this.historyData.filter(item => {
        const date = new Date(item.Date);
        return date >= thirtyDaysAgo && date <= now;
      });

      const previousPeriodData = this.historyData.filter(item => {
        const date = new Date(item.Date);
        return date >= sixtyDaysAgo && date < thirtyDaysAgo;
      });

      // Update shipment KPI
      const totalShipments = currentPeriodData.length;
      const previousTotalShipments = previousPeriodData.length;
      const shipmentChange = previousTotalShipments > 0 
        ? ((totalShipments - previousTotalShipments) / previousTotalShipments * 100).toFixed(1)
        : 0;
      
      document.getElementById('totalShipmentsKPI').textContent = totalShipments.toLocaleString();
      
      const shipmentsChangeEl = document.getElementById('shipmentsChangeKPI');
      shipmentsChangeEl.textContent = `${shipmentChange > 0 ? '+' : ''}${shipmentChange}% from last period`;
      shipmentsChangeEl.className = `text-xs font-medium mt-2 ${shipmentChange > 0 ? 'text-green-500' : shipmentChange < 0 ? 'text-red-500' : 'text-gray-500'}`;

      // Update cost KPI
      const totalFreight = currentPeriodData.reduce((sum, item) => sum + (parseFloat(item["Freight Cost"]) || 0), 0);
      const previousTotalFreight = previousPeriodData.reduce((sum, item) => sum + (parseFloat(item["Freight Cost"]) || 0), 0);
      const freightChange = previousTotalFreight > 0 
        ? ((totalFreight - previousTotalFreight) / previousTotalFreight * 100).toFixed(1) 
        : 0;
      
      document.getElementById('totalFreightKPI').textContent = `$${totalFreight.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
      
      const freightChangeEl = document.getElementById('freightChangeKPI');
      freightChangeEl.textContent = `${freightChange > 0 ? '+' : ''}${freightChange}% from last period`;
      freightChangeEl.className = `text-xs font-medium mt-2 ${freightChange > 0 ? 'text-red-500' : freightChange < 0 ? 'text-green-500' : 'text-gray-500'}`;

      // Update carrier KPI
      const carrierCounts = {};
      currentPeriodData.forEach(item => {
        const carrier = item.Carrier || 'Unknown';
        carrierCounts[carrier] = (carrierCounts[carrier] || 0) + 1;
      });
      
      let topCarrier = 'N/A';
      let topCarrierCount = 0;
      
      Object.entries(carrierCounts).forEach(([carrier, count]) => {
        if (count > topCarrierCount && carrier !== 'Unknown' && carrier !== 'N/A') {
          topCarrier = carrier;
          topCarrierCount = count;
        }
      });
      
      const topCarrierPercent = totalShipments > 0 
        ? ((topCarrierCount / totalShipments) * 100).toFixed(1) 
        : 0;
      
      document.getElementById('topCarrierKPI').textContent = topCarrier;
      document.getElementById('carrierPercentKPI').textContent = `${topCarrierPercent}% of shipments`;

      // Update destination KPI
      const destinationCounts = {};
      currentPeriodData.forEach(item => {
        const country = item.Country || 'Unknown';
        destinationCounts[country] = (destinationCounts[country] || 0) + 1;
      });
      
      let topDestination = 'N/A';
      let topDestinationCount = 0;
      
      Object.entries(destinationCounts).forEach(([country, count]) => {
        if (count > topDestinationCount && country !== 'Unknown' && country !== 'N/A') {
          topDestination = country;
          topDestinationCount = count;
        }
      });
      
      const topDestinationPercent = totalShipments > 0 
        ? ((topDestinationCount / totalShipments) * 100).toFixed(1) 
        : 0;
      
      document.getElementById('topDestinationKPI').textContent = topDestination;
      document.getElementById('destinationPercentKPI').textContent = `${topDestinationPercent}% of shipments`;
      
      // Update avg cost per shipment KPI
      const avgCost = totalShipments > 0 ? totalFreight / totalShipments : 0;
      const previousAvgCost = previousTotalShipments > 0 ? previousTotalFreight / previousTotalShipments : 0;
      const avgCostChange = previousAvgCost > 0 
        ? ((avgCost - previousAvgCost) / previousAvgCost * 100).toFixed(1) 
        : 0;
      
      document.getElementById('avgCostKPI').textContent = `$${avgCost.toFixed(2)}`;
      
      const avgCostChangeEl = document.getElementById('avgCostChangeKPI');
      avgCostChangeEl.textContent = `${avgCostChange > 0 ? '+' : ''}${avgCostChange}% from last period`;
      avgCostChangeEl.className = `text-xs font-medium mt-2 ${avgCostChange > 0 ? 'text-red-500' : avgCostChange < 0 ? 'text-green-500' : 'text-gray-500'}`;
      
      // Initialize mini charts
      this.initKPIMiniCharts();
      
    } catch (error) {
      console.error("Error updating KPI cards:", error);
    }
  }

  /**
   * Render all charts with modernized, animated ApexCharts
   */
  renderCharts() {
    console.log("Rendering all charts");
    
    // Render all charts regardless of which tab is active
    // Overview tab charts
      this.renderFreightCostChart();
      this.renderShipmentVolumeChart();
      this.renderShipmentByDayChart();
      this.renderShipmentByCarrierChart();
    
    // Cost Analysis tab charts
      this.renderCostTrendsChart();
      this.renderCostByDestinationChart();
      this.renderCarrierCostChart();
      this.renderPackageCostChart();
    
    // Carrier Performance tab charts
      this.renderCarrierUtilizationChart();
      this.renderCarrierPerformanceChart();
      this.renderCarrierCostVolumeChart();
      this.renderCarrierDestinationChart();
    
    // Shipping Trends tab charts
      this.renderTopProductsChart();
      this.renderTopDestinationChart();
      this.renderCustomerShipmentsChart();
      this.renderShippingMethodChart();
    
    console.log("All charts rendered");
  }

  /**
   * Render enhanced Freight Cost Chart with animations and improved layout
   */
  renderFreightCostChart() {
    const chartContainer = document.getElementById('dailyFreightCostChart');
    if (!chartContainer) return;
    
    // Get filter value
    const filterSelect = document.getElementById('freightCostFilter');
    const timeFilter = filterSelect ? filterSelect.value : 'month';
    
    // Update filter state
    this.filters.dailyFreightCost = timeFilter;
    
    // Get filtered data based on time
    const cutoffDate = this.getCutoffDate(timeFilter);
    
    // Process data for daily costs
    const costByDate = {};
    
    this.historyData
      .filter(item => {
        const itemDate = item.DateObj || (item.Date ? new Date(item.Date) : null);
        return itemDate && itemDate >= cutoffDate;
      })
      .forEach(item => {
        // Format the date to YYYY-MM-DD
        const itemDate = item.DateObj || new Date(item.Date);
        const dateStr = itemDate.toISOString().split('T')[0];
        
        if (!costByDate[dateStr]) {
          costByDate[dateStr] = {
            date: dateStr,
            cost: 0,
            count: 0
          };
        }
        
        // Add cost if available
        if (item["Freight Cost"] && !isNaN(parseFloat(item["Freight Cost"]))) {
          costByDate[dateStr].cost += parseFloat(item["Freight Cost"]);
        }
        
        costByDate[dateStr].count += 1;
      });
    
    // Convert to array and sort by date
    const costData = Object.values(costByDate).sort((a, b) => 
      new Date(a.date) - new Date(b.date)
    );
    
    // Create ApexCharts configuration for modern, animated chart
    const options = {
      series: [{
        name: 'Freight Cost',
        type: 'column',
        data: costData.map(d => parseFloat(d.cost.toFixed(2)))
      }, {
        name: 'Shipments',
        type: 'line',
        data: costData.map(d => d.count)
      }],
      chart: {
        height: 350,
        type: 'line',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      stroke: {
        width: [0, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '60%',
          borderRadius: 6
        }
      },
      fill: {
        opacity: [0.85, 1],
        type: ['gradient', 'solid'],
        gradient: {
          shade: 'light',
          type: "vertical",
          shadeIntensity: 0.25,
          gradientToColors: undefined,
          inverseColors: true,
          opacityFrom: 1,
          opacityTo: 0.7,
          stops: [0, 100]
        }
      },
      markers: {
        size: 4,
        strokeWidth: 0,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: costData.map(d => {
          const date = new Date(d.date);
          return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        }),
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      yaxis: [
        {
          title: {
            text: 'Freight Cost ($)',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            formatter: function(val) {
              return '$' + val.toFixed(0);
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        {
          opposite: true,
          title: {
            text: 'Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        }
      ],
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: function(y, { seriesIndex }) {
            if (seriesIndex === 0) {
              return '$' + y.toFixed(2);
            }
            return y + " shipments";
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        offsetY: 5
      },
      colors: ['#3b82f6', '#f59e0b']
    };

    // Clear previous chart
    chartContainer.innerHTML = '';
    
    // Create and render the chart
    const chart = new ApexCharts(chartContainer, options);
    chart.render();
  }

  /**
   * Render enhanced Shipment Volume Chart with animations and improved layout
   */
  renderShipmentVolumeChart() {
    const chartContainer = document.getElementById('shipmentVolumeChart');
    if (!chartContainer) return;
    
    // Get filter value
    const filterSelect = document.getElementById('shipmentVolumeFilter');
    const timeFilter = filterSelect ? filterSelect.value : 'week';
    
    // Update filter state
    this.filters.shipmentVolume = timeFilter;
    
    // Process data based on the selected time filter
    let volumeData = [];
    let categories = [];
    
    // Current date for calculations
    const currentDate = new Date();
    
    if (timeFilter === 'week') {
      // Process weekly data - last 12 weeks
      for (let i = 11; i >= 0; i--) {
        const weekStart = new Date(currentDate);
        weekStart.setDate(currentDate.getDate() - (7 * i + currentDate.getDay()));
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        const count = this.historyData.filter(item => {
          const itemDate = item.DateObj || (item.Date ? new Date(item.Date) : null);
          return itemDate && itemDate >= weekStart && itemDate <= weekEnd;
        }).length;
        
        const weekLabel = `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
        
        volumeData.push(count);
        categories.push(weekLabel);
      }
    } else if (timeFilter === 'month') {
      // Process monthly data - last 12 months
      for (let i = 11; i >= 0; i--) {
        const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 0);
        
        const count = this.historyData.filter(item => {
          const itemDate = item.DateObj || (item.Date ? new Date(item.Date) : null);
          return itemDate && itemDate >= monthStart && itemDate <= monthEnd;
        }).length;
        
        const monthLabel = monthStart.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
        
        volumeData.push(count);
        categories.push(monthLabel);
      }
    } else if (timeFilter === 'quarter') {
      // Process quarterly data - last 8 quarters
      for (let i = 7; i >= 0; i--) {
        const year = Math.floor(currentDate.getFullYear() - (i / 4));
        const quarter = 4 - (i % 4);
        const quarterStart = new Date(year, (quarter - 1) * 3, 1);
        const quarterEnd = new Date(year, quarter * 3, 0);
        
        const count = this.historyData.filter(item => {
          const itemDate = item.DateObj || (item.Date ? new Date(item.Date) : null);
          return itemDate && itemDate >= quarterStart && itemDate <= quarterEnd;
        }).length;
        
        const quarterLabel = `Q${quarter} ${year}`;
        
        volumeData.push(count);
        categories.push(quarterLabel);
      }
    }
    
    // Create ApexCharts configuration for modern, animated area chart
    const options = {
      series: [{
        name: 'Shipment Volume',
        data: volumeData
      }],
      chart: {
        height: 350,
        type: 'area',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 3
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 90, 100],
          colorStops: [
            {
              offset: 0,
              color: '#3b82f6',
              opacity: 0.7
            },
            {
              offset: 90,
              color: '#3b82f6',
              opacity: 0.2
            },
            {
              offset: 100,
              color: '#3b82f6',
              opacity: 0
            }
          ]
        }
      },
      markers: {
        size: 4,
        colors: ['#3b82f6'],
        strokeWidth: 0,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          },
          rotateAlways: timeFilter === 'week'
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Number of Shipments',
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: 0.5
        }
      },
      tooltip: {
        x: {
          format: 'dd MMM yyyy'
        }
      },
      colors: ['#3b82f6']
    };

    // Clear previous chart
    chartContainer.innerHTML = '';
    
    // Create and render the chart
    const chart = new ApexCharts(chartContainer, options);
    chart.render();
  }

  renderShipmentByDayChart() {
    const chartContainer = document.getElementById('shipmentByDayChart');
    if (!chartContainer) return;
    
    try {
      // Get time-filtered data
      const timeFilteredData = this.getTimeFilteredData(this.filters.shipmentByDay);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Count by day of week
      const dayCount = {
        'Monday': 0,
        'Tuesday': 0,
        'Wednesday': 0,
        'Thursday': 0,
        'Friday': 0,
        'Saturday': 0,
        'Sunday': 0
      };
      
      timeFilteredData.forEach(item => {
        const date = new Date(item.Date);
        const dayOfWeek = date.getDay(); // 0 = Sunday, 1 = Monday, ...
        const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        dayCount[dayNames[dayOfWeek]]++;
      });
      
      // Format for ApexCharts bar chart, keeping days in order
      const orderedDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      const values = orderedDays.map(day => dayCount[day]);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Configure ApexCharts
      const options = {
        series: [{
          name: 'Shipments',
          data: values
        }],
        chart: {
          type: 'bar',
          height: chartContainer.clientHeight || 300,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '60%',
            borderRadius: 4,
            distributed: true,
            dataLabels: {
              position: 'top'
            }
          }
        },
        colors: ['#A78BFA', '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6', '#4C1D95', '#4338CA'],
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return val;
          },
          offsetY: -20,
          style: {
            fontSize: '12px',
            colors: ['#666']
          }
        },
        grid: {
          borderColor: '#e5e7eb',
          row: {
            colors: ['transparent', 'transparent'],
            opacity: 0.5
          }
        },
        xaxis: {
          categories: orderedDays,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Day of Week',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          title: {
            text: 'Number of Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'light',
            type: "vertical",
            shadeIntensity: 0.2,
            gradientToColors: undefined,
            inverseColors: true,
            opacityFrom: 0.85,
            opacityTo: 1,
            stops: [0, 100]
          }
        },
        tooltip: {
          y: {
            formatter: function(val) {
              return val + " shipments";
            }
          }
        }
      };
      
      // Create ApexCharts instance
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
    } catch (error) {
      console.error("Error rendering shipment by day chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  renderShipmentByCarrierChart() {
    const chartContainer = document.getElementById('shipmentByCarrierChart');
    if (!chartContainer) return;
    
    try {
      // Get time-filtered data
      const timeFilteredData = this.getTimeFilteredData(this.filters.shipmentByCarrier);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Count by carrier
      const carrierCount = {};
      
      timeFilteredData.forEach(item => {
        const carrier = item.Carrier || 'Unknown';
        if (carrier !== 'Unknown' && carrier !== 'N/A') {
          carrierCount[carrier] = (carrierCount[carrier] || 0) + 1;
        }
      });
      
      // Format for ApexCharts
      const sortedCarriers = Object.entries(carrierCount)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 8); // Top 8 carriers
      
      const categories = sortedCarriers.map(([carrier]) => carrier);
      const values = sortedCarriers.map(([_, count]) => count);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Configure ApexCharts
      const options = {
        series: [{
          name: 'Shipments',
          data: values
        }],
        chart: {
          type: 'bar',
          height: chartContainer.clientHeight || 300,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: true,
            distributed: true,
            barHeight: '70%',
            borderRadius: 4,
            dataLabels: {
              position: 'middle'
            }
          }
        },
        colors: ['#F59E0B', '#EF4444', '#3B82F6', '#10B981', '#8B5CF6', '#EC4899', '#F97316', '#06B6D4'],
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return val;
          },
          textAnchor: 'start',
          style: {
            fontSize: '12px',
            colors: ['#fff']
          },
          offsetX: 10
        },
        grid: {
          borderColor: '#e5e7eb',
          row: {
            colors: ['transparent'],
            opacity: 0.5
          },
          xaxis: {
            lines: {
              show: false
            }
          }
        },
        xaxis: {
          categories: categories,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Carrier',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          title: {
            text: 'Number of Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        tooltip: {
          y: {
            formatter: function(val) {
              return val + " shipments";
            }
          }
        },
        legend: {
          show: false
        }
      };
      
      // Create ApexCharts instance
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
    } catch (error) {
      console.error("Error rendering shipment by carrier chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  renderCarrierCostChart() {
    const chartContainer = document.getElementById('carrierCostChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering carrier cost chart');
      
      const filter = document.getElementById('carrierCostFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by carrier
      const carrierData = {};
      
      timeFilteredData.forEach(item => {
        const carrier = item.Carrier || 'Unknown';
        if (carrier !== 'Unknown' && carrier !== 'N/A') {
          if (!carrierData[carrier]) {
            carrierData[carrier] = { total: 0, count: 0 };
          }
          
          carrierData[carrier].total += parseFloat(item["Freight Cost"]) || 0;
          carrierData[carrier].count++;
        }
      });
      
      // Format data for chart
      const sortedData = Object.entries(carrierData)
        .map(([carrier, data]) => ({
          carrier: carrier,
          total: parseFloat(data.total.toFixed(2)),
          count: data.count,
          avg: parseFloat((data.count > 0 ? data.total / data.count : 0).toFixed(2))
        }))
        .sort((a, b) => b.total - a.total);
      
      // Prepare data for ApexCharts
      const categories = sortedData.map(d => d.carrier);
      const costs = sortedData.map(d => d.total);
      const avgCosts = sortedData.map(d => d.avg);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts configuration
      const options = {
        series: [{
          name: 'Total Cost',
          data: costs
        }, {
          name: 'Avg Cost/Shipment',
          data: avgCosts
        }],
        chart: {
          type: 'bar',
          height: chartContainer.clientHeight || 350,
          stacked: false,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            borderRadius: 4,
            dataLabels: {
              position: 'top'
            }
          }
        },
        colors: ['#F59E0B', '#EF4444'],
        dataLabels: {
          enabled: false
        },
        stroke: {
          width: [0, 2],
          curve: 'smooth'
        },
        grid: {
          borderColor: '#e5e7eb',
          padding: {
            right: 30
          }
        },
        xaxis: {
          categories: categories,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Carrier',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: [{
          title: {
            text: 'Total Cost ($)',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            formatter: function(val) {
              return '$' + val.toFixed(0);
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        }, {
          opposite: true,
          title: {
            text: 'Avg Cost per Shipment ($)',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            formatter: function(val) {
              return '$' + val.toFixed(2);
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        }],
        tooltip: {
          shared: true,
          intersect: false,
          y: [{
            formatter: function(val) {
              return '$' + val.toFixed(2) + ' total';
            }
          }, {
            formatter: function(val) {
              return '$' + val.toFixed(2) + ' per shipment';
            }
          }]
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          markers: {
            width: 10,
            height: 10,
            radius: 3
          }
        }
      };
      
      // Create and render chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering carrier cost chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  renderCarrierUtilizationChart() {
    const chartContainer = document.getElementById('carrierUtilizationChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering carrier utilization chart');
      
      // Get pre-processed carrier data or create it if needed
      if (!this.carrierPerformanceData || this.carrierPerformanceData.length === 0) {
        // Process data if not already processed
        this.processCarrierData();
      }
      
      if (!this.carrierPerformanceData || this.carrierPerformanceData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No carrier data available</div>`;
        return;
      }
      
      // Get top 6 carriers by shipment volume
      const topCarriers = this.carrierPerformanceData
        .slice(0, 6);
      
      // Prepare data for chart
      const labels = topCarriers.map(c => c.carrier);
      const values = topCarriers.map(c => c.shipments);
      const costs = topCarriers.map(c => c.totalCost);
      
      // Calculate total shipments for percentage
      const totalShipments = values.reduce((sum, val) => sum + val, 0);
      
      // Create ApexCharts donut chart configuration
      const options = {
        series: values,
        chart: {
          type: 'donut',
          height: 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          },
          toolbar: {
            show: false
          }
        },
        labels: labels,
        colors: ['#3B82F6', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6', '#EC4899'],
        plotOptions: {
          pie: {
            donut: {
              size: '55%',
              labels: {
                show: true,
                name: {
                  show: true,
                  fontSize: '14px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 600,
                  color: '#1F2937'
                },
                value: {
                  show: true,
                  fontSize: '22px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 700,
                  color: '#1F2937'
                },
                total: {
                  show: true,
                  showAlways: true,
                  label: 'Total Shipments',
                  fontSize: '16px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 600,
                  color: '#6B7280',
                  formatter: function(w) {
                    return totalShipments;
                  }
                }
              }
            }
          }
        },
        stroke: {
          width: 2,
          colors: ['#fff']
        },
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return Math.round(val) + '%';
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif',
            fontWeight: 500,
            colors: ['#fff']
          },
          dropShadow: {
            enabled: true,
            top: 1,
            left: 1,
            blur: 2,
            opacity: 0.2
          }
        },
        legend: {
          position: 'bottom',
          horizontalAlign: 'center',
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          markers: {
            width: 10,
            height: 10,
            radius: 2
          }
        },
        responsive: [{
          breakpoint: 480,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom'
            }
          }
        }],
        tooltip: {
          custom: function({ series, seriesIndex, dataPointIndex, w }) {
            const carrier = labels[seriesIndex];
            const shipments = values[seriesIndex];
            const cost = costs[seriesIndex];
            const percent = Math.round((shipments / totalShipments) * 100);
            
            return `
              <div class="p-2 bg-white shadow-lg rounded-md border border-gray-200">
                <div class="font-medium">${carrier}</div>
                <div class="text-sm"><span class="font-semibold">${shipments}</span> shipments (${percent}%)</div>
                <div class="text-sm">Total cost: <span class="font-semibold">$${cost.toFixed(2)}</span></div>
              </div>
            `;
          }
        }
      };
      
      // Clear the container
      chartContainer.innerHTML = '';
      
      // Create and render the chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering carrier utilization chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  renderTopProductsChart() {
    const chartContainer = document.getElementById('topProductsChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering top products chart');
      
      const filter = document.getElementById('topProductsFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by product/inventory
      const productData = {};
      
      timeFilteredData.forEach(item => {
        const productId = item["Inventory ID"] || item["Product ID"] || 'Unknown';
        if (productId !== 'Unknown' && productId !== 'N/A') {
          if (!productData[productId]) {
            productData[productId] = { 
              count: 0, 
              cost: 0,
              name: item["Product Name"] || productId
            };
          }
          
          productData[productId].count++;
          productData[productId].cost += parseFloat(item["Freight Cost"]) || 0;
        }
      });
      
      // Sort and limit to top products for clarity
      const sortedProducts = Object.entries(productData)
        .map(([id, data]) => ({
          id: id,
          name: data.name,
          count: data.count,
          cost: parseFloat(data.cost.toFixed(2))
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
      
      // Simplify product names for display
      const labels = sortedProducts.map(p => {
        // Use just the product ID if it's short, otherwise truncate it
        let simplified = p.id;
        if (simplified.length > 12) {
          simplified = simplified.substring(0, 12) + '...';
        }
        return simplified;
      });
      
      const values = sortedProducts.map(p => p.count);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts configuration
      const options = {
        series: [{
          name: 'Shipments',
          data: values
        }],
        chart: {
          type: 'bar',
          height: 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: true,
            barHeight: '70%',
            borderRadius: 4,
            distributed: true,
            dataLabels: {
              position: 'top'
            }
          }
        },
        colors: ['#10B981', '#059669', '#047857', '#065F46', '#064E3B'],
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return val;
          },
          offsetX: 10,
          style: {
            fontSize: '12px',
            colors: ['#fff']
          }
        },
        stroke: {
          width: 1,
          colors: ['#fff']
        },
        grid: {
          borderColor: '#e5e7eb',
          row: {
            colors: ['transparent', 'transparent'],
            opacity: 0.5
          }
        },
        xaxis: {
          categories: labels,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Number of Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Product ID',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        tooltip: {
          y: {
            formatter: function(val, { dataPointIndex }) {
              const product = sortedProducts[dataPointIndex];
              return `${val} shipments (Product: ${product.id})`;
            }
          }
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'dark',
            type: 'horizontal',
            shadeIntensity: 0.2,
            gradientToColors: ['#34D399'],
            inverseColors: false,
            opacityFrom: 0.85,
            opacityTo: 0.85,
            stops: [0, 100]
          }
        },
        title: {
          text: 'Top 10 Shipped Products',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        },
        legend: {
          show: false
        }
      };
      
      // Create and render chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering top products chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  renderTopDestinationChart() {
    const chartContainer = document.getElementById('topDestinationChart');
    if (!chartContainer) return;
    
    // Get filter value
    const filterSelect = document.getElementById('topDestinationFilter');
    const timeFilter = filterSelect ? filterSelect.value : 'month';
    
    // Get filtered data
    const filteredData = this.getTimeFilteredData(timeFilter);
    
    // Get destination distribution
    const destinations = {};
    
    filteredData.forEach(item => {
      const country = item.Country || 'Unknown';
      if (!destinations[country]) {
        destinations[country] = {
          count: 0,
          country: country,
          countryCode: this.getCountryCode(country),
          cost: 0
        };
      }
      destinations[country].count += 1;
      
      // Add cost if available
      if (item["Freight Cost"] && !isNaN(parseFloat(item["Freight Cost"]))) {
        destinations[country].cost += parseFloat(item["Freight Cost"]);
      }
    });
    
    // Convert to array and sort by count
    const destinationsArray = Object.values(destinations).sort((a, b) => b.count - a.count);
    
    // Take top 10 for chart
    const topDestinations = destinationsArray.slice(0, 10);
    
    // Create ApexCharts configuration
    const options = {
      series: [{
        name: 'Shipments',
        data: topDestinations.map(d => d.count)
      }],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      colors: ['#8b5cf6', '#a78bfa'],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          barHeight: '70%',
          distributed: true,
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val;
        },
        offsetX: 20,
        style: {
          fontSize: '12px',
          colors: ['#fff']
        }
      },
      stroke: {
        width: 1,
        colors: ['#fff']
      },
      grid: {
        borderColor: '#f1f1f1',
        padding: {
          left: 0,
          right: 0
        }
      },
      xaxis: {
        categories: topDestinations.map(d => d.country),
        labels: {
          style: {
            colors: [],
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif',
            fontWeight: 400
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: [],
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif',
            fontWeight: 400
          }
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'horizontal',
          shadeIntensity: 0.2,
          gradientToColors: undefined,
          inverseColors: true,
          opacityFrom: 0.8,
          opacityTo: 1,
          stops: [0, 50, 100],
          colorStops: []
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + " shipments";
          }
        }
      }
    };

    // Create the chart
    const chart = new ApexCharts(chartContainer, options);
    chart.render();
    
    // Now let's add the map visualization as a separate tab chart
    // Instead of adding the map after the chart, let's use a tabbed interface
    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'flex mt-3 border-b border-gray-200';
    tabsContainer.innerHTML = `
      <button class="px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 chart-tab active" data-target="chart">Chart View</button>
      <button class="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 chart-tab" data-target="map">Map View</button>
    `;
    
    const contentContainer = document.createElement('div');
    contentContainer.className = 'mt-4';
    contentContainer.innerHTML = `
      <div id="chart-view" class="tab-content active"></div>
      <div id="map-view" class="tab-content hidden" style="height: 250px;"></div>
    `;
    
    // Add the tabs and content after the chart
    chartContainer.parentNode.appendChild(tabsContainer);
    chartContainer.parentNode.appendChild(contentContainer);
    
    // Set up tab switching
    const tabButtons = tabsContainer.querySelectorAll('.chart-tab');
    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        tabButtons.forEach(btn => {
          btn.classList.remove('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
          btn.classList.add('text-gray-500');
        });
        button.classList.add('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
        button.classList.remove('text-gray-500');
        
        const target = button.dataset.target;
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.add('hidden');
        });
        document.getElementById(`${target}-view`).classList.remove('hidden');
        
        // If switching to map, initialize it
        if (target === 'map' && !document.getElementById('map-view').hasChildNodes()) {
          this.renderWorldMap('map-view', destinationsArray);
        }
      });
    });
  }

  /**
   * Render a simplified SVG world map with highlighted destinations
   */
  renderWorldMap(containerId, destinations) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    // Prepare country data for heatmap visualization
    const countryData = destinations.reduce((acc, dest) => {
      const code = dest.countryCode || 'unknown';
      if (code !== 'unknown') {
        acc[code] = {
          count: dest.count,
          fillColor: this.getHeatmapColor(dest.count, Math.max(...destinations.map(d => d.count)))
        };
      }
      return acc;
    }, {});
    
    // Create SVG map
    const mapWidth = container.clientWidth;
    const mapHeight = container.clientHeight;
    
    // We'll use ApexCharts for the map visualization
    const options = {
      series: [{
        name: 'Shipments',
        data: destinations.map(d => ({
          x: d.country,
          y: d.count
        }))
      }],
      chart: {
        height: mapHeight,
        type: 'treemap',
        toolbar: {
          show: false
        }
      },
      title: {
        text: 'Shipping Destinations Heatmap',
        align: 'center',
        style: {
          fontSize: '14px',
          fontWeight: 500,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#333'
        }
      },
      dataLabels: {
        enabled: true,
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          fontWeight: 400,
          colors: ['#fff']
        },
        formatter: function(text, op) {
          return [text, op.value + ' shipments'];
        },
        offsetY: -4
      },
      plotOptions: {
        treemap: {
          distributed: true,
          enableShades: true,
          shadeIntensity: 0.5,
          reverseNegativeShade: true,
          colorScale: {
            ranges: [
              {
                from: 0,
                to: destinations[destinations.length - 1]?.count || 1,
                color: '#c7d2fe'
              },
              {
                from: (destinations[destinations.length - 1]?.count || 1) + 1,
                to: destinations[0]?.count || 10,
                color: '#8b5cf6'
              }
            ]
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(value) {
            return value + ' shipments';
          }
        }
      }
    };

    const chart = new ApexCharts(container, options);
    chart.render();
  }

  /**
   * Get a color for the heatmap based on value
   */
  getHeatmapColor(value, max) {
    // Generate a color from purple (low) to deep purple (high)
    const intensity = Math.min(value / max, 1);
    const r = Math.round(139 + (91 - 139) * intensity);
    const g = Math.round(92 + (33 - 92) * intensity);
    const b = Math.round(246 + (170 - 246) * intensity);
    return `rgb(${r}, ${g}, ${b})`;
  }

  /**
   * Get country code from country name
   */
  getCountryCode(countryName) {
    const countryMap = {
      'USA': 'US',
      'United States': 'US',
      'Canada': 'CA',
      'Mexico': 'MX',
      'UK': 'GB',
      'United Kingdom': 'GB',
      'Germany': 'DE',
      'France': 'FR',
      'Italy': 'IT',
      'Spain': 'ES',
      'China': 'CN',
      'Japan': 'JP',
      'Australia': 'AU',
      'Brazil': 'BR',
      'India': 'IN',
      'Russia': 'RU'
      // Add more mappings as needed
    };
    
    if (countryName) {
      // Check if the country name contains a country code in parentheses
      const match = countryName.match(/\b([A-Z]{2})\b/);
      if (match) return match[1];
      
      // Look up in map
      const nameOnly = countryName.split('-')[0].trim();
      return countryMap[nameOnly] || 'unknown';
    }
    
    return 'unknown';
  }

  renderCustomerShipmentsChart() {
    const chartContainer = document.getElementById('customerShipmentsChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering customer shipments chart');
      
      const filter = document.getElementById('customerShipmentsFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by customer
      const customerData = {};
      
      timeFilteredData.forEach(item => {
        const customer = item["Customer Name"] || item["Company Name"] || 'Unknown';
        if (customer !== 'Unknown' && customer !== 'N/A') {
          if (!customerData[customer]) {
            customerData[customer] = { 
              count: 0, 
              cost: 0 
            };
          }
          
          customerData[customer].count++;
          customerData[customer].cost += parseFloat(item["Freight Cost"]) || 0;
        }
      });
      
      // Sort and limit to top customers
      const sortedCustomers = Object.entries(customerData)
        .map(([customer, data]) => ({
          customer: customer,
          count: data.count,
          cost: parseFloat(data.cost.toFixed(2)),
          avgCost: data.count > 0 ? parseFloat((data.cost / data.count).toFixed(2)) : 0
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);
      
      const labels = sortedCustomers.map(c => c.customer);
      const values = sortedCustomers.map(c => c.count);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts configuration
      const options = {
        series: [{
          name: 'Shipments',
          data: values
        }],
        chart: {
          type: 'bar',
          height: 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        plotOptions: {
          bar: {
            horizontal: true,
            borderRadius: 4,
            barHeight: '70%',
            dataLabels: {
              position: 'top'
            }
          }
        },
        colors: ['#8B5CF6', '#6D28D9', '#5B21B6', '#4C1D95'],
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return val;
          },
          offsetX: 10,
          style: {
            fontSize: '12px',
            colors: ['#fff']
          }
        },
        stroke: {
          width: 1,
          colors: ['#fff']
        },
        grid: {
          borderColor: '#e5e7eb',
          xaxis: {
            lines: {
              show: false
            }
          }
        },
        xaxis: {
          categories: labels,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          title: {
            text: 'Number of Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            },
            formatter: function(val) {
              // Truncate long customer names
              if (val.length > 15) {
                return val.substring(0, 15) + '...';
              }
              return val;
            }
          },
          title: {
            text: 'Customer',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        tooltip: {
          y: {
            formatter: function(val, { dataPointIndex }) {
              const customer = sortedCustomers[dataPointIndex];
              return `${val} shipments<br>Total cost: $${customer.cost.toFixed(2)}<br>Avg cost: $${customer.avgCost.toFixed(2)}`;
            }
          }
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'dark',
            type: 'horizontal',
            shadeIntensity: 0.2,
            gradientToColors: ['#A78BFA'],
            inverseColors: false,
            opacityFrom: 0.85,
            opacityTo: 0.85,
            stops: [0, 100]
          }
        },
        title: {
          text: 'Top 10 Customers by Shipment Volume',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        }
      };
      
      // Create and render chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering customer shipments chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  /**
   * Filter data by time period
   */
  getTimeFilteredData(filter) {
    console.log(`Filtering data with filter: ${filter}`);
    
    // Check for empty data
    if (!this.historyData || this.historyData.length === 0) {
      console.warn("No data to filter");
      return [];
    }
    
    const now = new Date();
    let cutoffDate = new Date();
    
    switch (filter) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case 'quarter':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        cutoffDate.setDate(now.getDate() - 365);
        break;
      default:
        cutoffDate.setDate(now.getDate() - 30);
    }
    
    // Add more robust filtering that handles bad date formats
    const filtered = this.historyData.filter(item => {
      try {
        if (!item.Date) return false;
        
        // Handle different date formats
        let recordDate;
        if (typeof item.Date === 'string') {
          // Try different date formats
          if (item.Date.includes('-') || item.Date.includes('/')) {
            recordDate = new Date(item.Date);
          } else if (!isNaN(item.Date)) {
            // Handle numeric timestamp
            recordDate = new Date(parseInt(item.Date));
          } else {
            return false; // Unrecognized format
          }
        } else if (item.Date instanceof Date) {
          recordDate = item.Date;
        } else {
          return false;
        }
        
        // Check if date is valid
        if (isNaN(recordDate.getTime())) {
          return false;
        }
        
        return recordDate >= cutoffDate && recordDate <= now;
      } catch (e) {
        console.warn(`Error filtering date: ${item.Date}`, e);
        return false;
      }
    });
    
    console.log(`Filtered to ${filtered.length} records`);
    return filtered;
  }

  // Additional data processing methods
  processShipmentData() {
    console.log("Processing shipment data for enhanced visualizations");
    
    // Calculate additional metrics and prepare data for various chart types
    if (!this.historyData || this.historyData.length === 0) {
      console.warn("No data to process");
      return;
    }
    
    // Extract all unique carriers, destinations, package types, etc.
    this.uniqueCarriers = [...new Set(this.historyData.map(item => item.Carrier || 'Unknown').filter(c => c !== 'Unknown' && c !== 'N/A'))];
    this.uniqueCountries = [...new Set(this.historyData.map(item => item.Country || 'Unknown').filter(c => c !== 'Unknown' && c !== 'N/A'))];
    this.uniquePackageTypes = [...new Set(this.historyData.map(item => item["Package Type"] || 'Unknown').filter(c => c !== 'Unknown' && c !== 'N/A'))];
    this.uniqueShippingMethods = [...new Set(this.historyData.map(item => item["Shipping Method"] || 'Unknown').filter(c => c !== 'Unknown' && c !== 'N/A'))];
    this.uniqueCustomers = [...new Set(this.historyData.map(item => item["Customer Name"] || 'Unknown').filter(c => c !== 'Unknown' && c !== 'N/A'))];
    
    // Fill filter dropdowns on data tab
    this.populateFilterDropdowns();
    
    // Process cost data by various dimensions
    this.processFreightCostData();
    
    // Process carrier performance data
    this.processCarrierData();
    
    // Process shipping method data
    this.processShippingMethodData();
    
    console.log("Data processing completed");
  }

  populateFilterDropdowns() {
    // Populate carrier filter dropdown
    const carrierFilter = document.getElementById('historyCarrierFilter');
    if (carrierFilter) {
      // Clear existing options except the first one
      while (carrierFilter.options.length > 1) {
        carrierFilter.remove(1);
      }
      
      // Add carriers
      this.uniqueCarriers.forEach(carrier => {
        const option = document.createElement('option');
        option.value = carrier;
        option.textContent = carrier;
        carrierFilter.appendChild(option);
      });
    }
    
    // Populate country filter dropdown
    const countryFilter = document.getElementById('historyCountryFilter');
    if (countryFilter) {
      // Clear existing options except the first one
      while (countryFilter.options.length > 1) {
        countryFilter.remove(1);
      }
      
      // Add countries
      this.uniqueCountries.forEach(country => {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countryFilter.appendChild(option);
      });
    }
  }

  processFreightCostData() {
    // Process freight cost data for various visualizations
    console.log("Processing freight cost data");
    
    // Group cost data by month for trends
    const costByMonth = {};
    const costByDestination = {};
    const costByPackageType = {};
    
    this.historyData.forEach(item => {
      const dateObj = new Date(item.Date);
      const monthYear = dateObj.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      const destination = item.Country || 'Unknown';
      const packageType = item["Package Type"] || 'Unknown';
      const cost = parseFloat(item["Freight Cost"]) || 0;
      
      // Costs by month
      if (!costByMonth[monthYear]) {
        costByMonth[monthYear] = { total: 0, count: 0 };
      }
      costByMonth[monthYear].total += cost;
      costByMonth[monthYear].count++;
      
      // Costs by destination
      if (destination !== 'Unknown' && destination !== 'N/A') {
        if (!costByDestination[destination]) {
          costByDestination[destination] = { total: 0, count: 0 };
        }
        costByDestination[destination].total += cost;
        costByDestination[destination].count++;
      }
      
      // Costs by package type
      if (packageType !== 'Unknown' && packageType !== 'N/A') {
        if (!costByPackageType[packageType]) {
          costByPackageType[packageType] = { total: 0, count: 0 };
        }
        costByPackageType[packageType].total += cost;
        costByPackageType[packageType].count++;
      }
    });
    
    // Calculate monthly averages for trend analysis
    this.costTrendsData = Object.entries(costByMonth).map(([month, data]) => ({
      month: month,
      averageCost: data.count > 0 ? data.total / data.count : 0,
      totalCost: data.total,
      count: data.count
    })).sort((a, b) => {
      // Sort by date (assuming month format like "Jan 23", "Feb 23")
      const [aMonth, aYear] = a.month.split(' ');
      const [bMonth, bYear] = b.month.split(' ');
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const aMonthIndex = months.indexOf(aMonth);
      const bMonthIndex = months.indexOf(bMonth);
      
      if (aYear !== bYear) return aYear.localeCompare(bYear);
      return aMonthIndex - bMonthIndex;
    });
    
    // Calculate average cost by destination
    this.costByDestinationData = Object.entries(costByDestination).map(([destination, data]) => ({
      destination: destination,
      averageCost: data.count > 0 ? data.total / data.count : 0,
      totalCost: data.total,
      count: data.count
    })).sort((a, b) => b.averageCost - a.averageCost);
    
    // Calculate costs by package type
    this.costByPackageTypeData = Object.entries(costByPackageType).map(([packageType, data]) => ({
      packageType: packageType,
      averageCost: data.count > 0 ? data.total / data.count : 0,
      totalCost: data.total,
      count: data.count
    })).sort((a, b) => b.totalCost - a.totalCost);
    
    console.log("Freight cost data processed");
  }

  processCarrierData() {
    // Process carrier data for performance analysis
    console.log("Processing carrier performance data");
    
    const carrierData = {};
    const carrierDestinationData = {};
    
    this.historyData.forEach(item => {
      const carrier = item.Carrier || 'Unknown';
      const destination = item.Country || 'Unknown';
      const cost = parseFloat(item["Freight Cost"]) || 0;
      
      if (carrier !== 'Unknown' && carrier !== 'N/A') {
        // Carrier performance metrics
        if (!carrierData[carrier]) {
          carrierData[carrier] = { 
            shipments: 0, 
            totalCost: 0,
            destinations: new Set(),
            packageTypes: new Set()
          };
        }
        
        carrierData[carrier].shipments++;
        carrierData[carrier].totalCost += cost;
        
        if (destination !== 'Unknown' && destination !== 'N/A') {
          carrierData[carrier].destinations.add(destination);
          
          // Carrier by destination data
          const carrierDestKey = `${carrier}:${destination}`;
          if (!carrierDestinationData[carrierDestKey]) {
            carrierDestinationData[carrierDestKey] = { 
              carrier: carrier,
              destination: destination,
              shipments: 0, 
              totalCost: 0 
            };
          }
          
          carrierDestinationData[carrierDestKey].shipments++;
          carrierDestinationData[carrierDestKey].totalCost += cost;
        }
        
        const packageType = item["Package Type"] || 'Unknown';
        if (packageType !== 'Unknown' && packageType !== 'N/A') {
          carrierData[carrier].packageTypes.add(packageType);
        }
      }
    });
    
    // Calculate carrier performance metrics
    this.carrierPerformanceData = Object.entries(carrierData).map(([carrier, data]) => ({
      carrier: carrier,
      shipments: data.shipments,
      totalCost: data.totalCost,
      avgCostPerShipment: data.shipments > 0 ? data.totalCost / data.shipments : 0,
      destinationCount: data.destinations.size,
      packageTypeCount: data.packageTypes.size,
      // Add performance score metric (lower cost = better)
      performanceScore: data.shipments > 0 ? 100 - (data.totalCost / data.shipments / 10) : 0
    })).sort((a, b) => b.shipments - a.shipments);
    
    // Format carrier-destination data
    this.carrierDestinationData = Object.values(carrierDestinationData)
      .map(data => ({
        ...data,
        avgCostPerShipment: data.shipments > 0 ? data.totalCost / data.shipments : 0
      }))
      .sort((a, b) => b.shipments - a.shipments);
    
    // Create carrier cost vs volume data for scatter plot
    this.carrierCostVolumeData = this.carrierPerformanceData.map(carrier => ({
      carrier: carrier.carrier,
      shipments: carrier.shipments,
      avgCost: carrier.avgCostPerShipment,
      size: Math.sqrt(carrier.shipments) * 5 // Size circle based on volume
    }));
    
    console.log("Carrier performance data processed");
  }

  processShippingMethodData() {
    // Process shipping method data
    console.log("Processing shipping method data");
    
    const shippingMethodData = {};
    
    this.historyData.forEach(item => {
      const method = item["Shipping Method"] || 'Unknown';
      const terms = item["Shipping Terms"] || 'Unknown';
      const combinedMethod = method !== 'Unknown' && terms !== 'Unknown' ? 
        `${method} (${terms})` : (method !== 'Unknown' ? method : terms);
      
      if (combinedMethod !== 'Unknown') {
        if (!shippingMethodData[combinedMethod]) {
          shippingMethodData[combinedMethod] = { count: 0, cost: 0 };
        }
        
        shippingMethodData[combinedMethod].count++;
        shippingMethodData[combinedMethod].cost += parseFloat(item["Freight Cost"]) || 0;
      }
    });
    
    // Format shipping method data
    this.shippingMethodsData = Object.entries(shippingMethodData)
      .map(([method, data]) => ({
        method: method,
        count: data.count,
        totalCost: data.cost,
        avgCost: data.count > 0 ? data.cost / data.count : 0
      }))
      .sort((a, b) => b.count - a.count);
    
    console.log("Shipping method data processed");
  }

  // Enhanced table functionality
  setupDataTableFiltering() {
    console.log("Setting up data table filtering");
    
    const historyDateFilter = document.getElementById('historyDateFilter');
    const historyCarrierFilter = document.getElementById('historyCarrierFilter');
    const historyCountryFilter = document.getElementById('historyCountryFilter');
    const historySearchInput = document.getElementById('historySearchInput');
    const prevPageBtn = document.getElementById('prevPage');
    const nextPageBtn = document.getElementById('nextPage');
    
    // Populate carrier filter
    if (historyCarrierFilter) {
      // Get unique carriers
      const carriers = [...new Set(this.historyData
        .filter(item => item.Carrier && item.Carrier !== 'N/A')
        .map(item => item.Carrier))];
      
      // Clear existing options except 'All Carriers'
      while (historyCarrierFilter.options.length > 1) {
        historyCarrierFilter.remove(1);
      }
      
      // Add carrier options
      carriers.forEach(carrier => {
        const option = document.createElement('option');
        option.value = carrier;
        option.textContent = carrier;
        historyCarrierFilter.appendChild(option);
      });
    }
    
    // Populate country filter
    if (historyCountryFilter) {
      // Get unique countries
      const countries = [...new Set(this.historyData
        .filter(item => item.Country && item.Country !== 'N/A')
        .map(item => item.Country))];
      
      // Clear existing options except 'All Countries'
      while (historyCountryFilter.options.length > 1) {
        historyCountryFilter.remove(1);
      }
      
      // Add country options
      countries.forEach(country => {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        historyCountryFilter.appendChild(option);
      });
    }
    
    // Set up event listeners
    if (historyDateFilter) {
      historyDateFilter.addEventListener('change', () => this.updateHistoryTable());
    }
    
    if (historyCarrierFilter) {
      historyCarrierFilter.addEventListener('change', () => this.updateHistoryTable());
    }
    
    if (historyCountryFilter) {
      historyCountryFilter.addEventListener('change', () => this.updateHistoryTable());
    }
    
    if (historySearchInput) {
      historySearchInput.addEventListener('input', debounce(() => this.updateHistoryTable(), 300));
    }
    
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        const currentPage = parseInt(prevPageBtn.dataset.currentPage) || 1;
        if (currentPage > 1) {
          prevPageBtn.dataset.currentPage = currentPage - 1;
          nextPageBtn.dataset.currentPage = currentPage - 1;
          this.updateHistoryTable(currentPage - 1);
        }
      });
    }
    
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        const currentPage = parseInt(nextPageBtn.dataset.currentPage) || 1;
        const totalPages = parseInt(nextPageBtn.dataset.totalPages) || 1;
        if (currentPage < totalPages) {
          prevPageBtn.dataset.currentPage = currentPage + 1;
          nextPageBtn.dataset.currentPage = currentPage + 1;
          this.updateHistoryTable(currentPage + 1);
        }
      });
    }
    
    // Set up export buttons
    const exportTableCSV = document.getElementById('exportTableCSV');
    const exportTableExcel = document.getElementById('exportTableExcel');
    const exportAllData = document.getElementById('exportAllData');
    
    if (exportTableCSV) {
      exportTableCSV.addEventListener('click', () => this.exportTableAsCSV());
    }
    
    if (exportTableExcel) {
      exportTableExcel.addEventListener('click', () => this.exportAsExcel(this.getFilteredTableData()));
    }
    
    if (exportAllData) {
      exportAllData.addEventListener('click', () => this.exportAllDataAsCSV());
    }
    
    // Initial table update
    this.updateHistoryTable();
  }

  /**
   * Get filtered table data based on current filters
   */
  getFilteredTableData() {
    const historyDateFilter = document.getElementById('historyDateFilter');
    const historyCarrierFilter = document.getElementById('historyCarrierFilter');
    const historyCountryFilter = document.getElementById('historyCountryFilter');
    const historySearchInput = document.getElementById('historySearchInput');
    
    // Get filter values
    const dateFilter = historyDateFilter ? historyDateFilter.value : 'month';
    const carrierFilter = historyCarrierFilter ? historyCarrierFilter.value : 'all';
    const countryFilter = historyCountryFilter ? historyCountryFilter.value : 'all';
    const searchFilter = historySearchInput ? historySearchInput.value.toLowerCase() : '';
    
    // Filter based on date
    let cutoffDate = this.getCutoffDate(dateFilter);
    let filteredData = this.historyData.filter(item => {
      const itemDate = item.DateObj || (item.Date ? new Date(item.Date) : null);
      return itemDate && itemDate >= cutoffDate;
    });
    
    // Filter by carrier if not 'all'
    if (carrierFilter && carrierFilter !== 'all') {
      filteredData = filteredData.filter(item => item.Carrier === carrierFilter);
    }
    
    // Filter by country if not 'all'
    if (countryFilter && countryFilter !== 'all') {
      filteredData = filteredData.filter(item => item.Country === countryFilter);
    }
    
    // Filter by search term
    if (searchFilter) {
      filteredData = filteredData.filter(item => 
        (item["Reference Number"] && item["Reference Number"].toLowerCase().includes(searchFilter)) ||
        (item["Customer Name"] && item["Customer Name"].toLowerCase().includes(searchFilter)) ||
        (item["User Name"] && item["User Name"].toLowerCase().includes(searchFilter)) ||
        (item["Company Name"] && item["Company Name"].toLowerCase().includes(searchFilter)) ||
        (item["Carrier"] && item["Carrier"].toLowerCase().includes(searchFilter)) ||
        (item["Inventory ID"] && item["Inventory ID"].toLowerCase().includes(searchFilter)) ||
        (item["Order Number"] && item["Order Number"].toLowerCase().includes(searchFilter))
      );
    }
    
    return filteredData;
  }

  /**
   * Update the enhanced history table with current filters
   */
  updateHistoryTable(page = 1) {
    const historyTableContainer = document.getElementById('historyTableContainer');
    const historyTablePagination = document.getElementById('historyTablePagination');
    const prevPageBtn = document.getElementById('prevPage');
    const nextPageBtn = document.getElementById('nextPage');
    
    if (!historyTableContainer) return;
    
    const filteredData = this.getFilteredTableData();
    
    // Pagination settings
    const itemsPerPage = 20;
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredData.length);
    const visibleData = filteredData.slice(startIndex, endIndex);
    
    // Update pagination buttons and info
    if (prevPageBtn) {
      prevPageBtn.disabled = page <= 1;
      prevPageBtn.dataset.currentPage = page;
    }
    
    if (nextPageBtn) {
      nextPageBtn.disabled = page >= totalPages;
      nextPageBtn.dataset.currentPage = page;
      nextPageBtn.dataset.totalPages = totalPages;
    }
    
    if (historyTablePagination) {
      historyTablePagination.textContent = `Showing ${startIndex + 1}-${endIndex} of ${filteredData.length} shipments`;
    }
    
    // Generate enhanced table with user profiles and actions
    let html = `
      <table class="data-table w-full">
        <thead>
          <tr>
            <th>User Info</th>
            <th>Action</th>
            <th>Customer</th>
            <th>Reference</th>
            <th>Shipping</th>
            <th>Package</th>
            <th>Date</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
    `;
    
    if (visibleData.length === 0) {
      html += `
        <tr>
          <td colspan="8" class="text-center py-4 text-gray-500">No matching records found</td>
        </tr>
      `;
    } else {
      visibleData.forEach(item => {
        // Format user avatar
        const avatarSrc = item.Avatar && item.Avatar !== 'N/A' 
          ? `images/avatars/avatar_${item.Avatar}.jpg` 
          : 'https://ui-avatars.com/api/?name=' + encodeURIComponent(item["User Name"] || 'User');
        
        // Format date
        const dateObj = item.DateObj || (item.Date ? new Date(item.Date) : new Date());
        const formattedDate = dateObj.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric', 
          year: 'numeric' 
        });
        
        // Format action with icon
        let actionIcon = '';
        let actionColor = 'bg-blue-100 text-blue-800';
        
        if (item["Action Performed"] && item["Action Performed"].toLowerCase().includes('send')) {
          actionIcon = `<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>`;
          actionColor = 'bg-blue-100 text-blue-800';
        } else if (item["Action Performed"] && item["Action Performed"].toLowerCase().includes('create')) {
          actionIcon = `<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>`;
          actionColor = 'bg-green-100 text-green-800';
        } else if (item["Action Performed"] && item["Action Performed"].toLowerCase().includes('update')) {
          actionIcon = `<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>`;
          actionColor = 'bg-yellow-100 text-yellow-800';
        }
        
        // Format tracking info with dropdown
        const trackingNumber = item["Tracking Number"] || '';
        const trackingLink = item["Tracking Link"] || '';
        const carrier = item["Carrier"] || 'N/A';
        
        html += `
          <tr>
            <td>
              <div class="flex items-center">
                <img class="h-8 w-8 rounded-full mr-2" src="${avatarSrc}" alt="${item["User Name"] || 'User'}">
                <div>
                  <div class="font-medium">${item["User Name"] || 'Unknown'}</div>
                  <div class="text-xs text-gray-500">${item["Role"] || 'User'}</div>
                </div>
              </div>
            </td>
            <td>
              <span class="px-2 py-1 rounded-full text-xs flex items-center w-fit ${actionColor}">
                ${actionIcon}${item["Action Performed"] || 'Unknown'}
              </span>
            </td>
            <td>
              <div class="font-medium">${item["Customer Name"] || 'N/A'}</div>
              <div class="text-xs text-gray-500">${item["Company Name"] || ''}</div>
            </td>
            <td>
              <div>${item["Reference Number"] || 'N/A'}</div>
              <div class="text-xs text-gray-500">${item["Order Number"] ? `Order: ${item["Order Number"]}` : ''}</div>
            </td>
            <td>
              <div>${carrier}</div>
              <div class="text-xs text-gray-500 truncate">${item["Shipping Method"] || 'N/A'}</div>
            </td>
            <td>
              <div>${item["Package Type"] || 'N/A'}</div>
              <div class="text-xs text-gray-500">${item["Package Weight"] ? `Weight: ${item["Package Weight"]}` : ''}</div>
            </td>
            <td>${formattedDate}</td>
            <td>
              <div class="relative inline-block text-left" id="dropdown-${startIndex + visibleData.indexOf(item)}">
                <button type="button" class="tracking-btn inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md ${trackingNumber ? 'bg-blue-600 text-white hover:bg-blue-700' : 'bg-gray-200 text-gray-500 cursor-not-allowed'}" ${!trackingNumber ? 'disabled' : ''}>
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  Track
                </button>
                <div class="tracking-dropdown origin-top-right absolute right-0 mt-2 w-72 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10 hidden">
                  <div class="py-1">
                    <div class="px-4 py-3 border-b">
                      <h3 class="text-sm font-medium">Tracking Information</h3>
                    </div>
                    <div class="px-4 py-3">
                      <div class="mb-2">
                        <span class="text-xs font-medium text-gray-500">Carrier:</span>
                        <span class="ml-2 text-sm">${carrier}</span>
                      </div>
                      <div class="mb-2">
                        <span class="text-xs font-medium text-gray-500">Tracking Number:</span>
                        <span class="ml-2 text-sm">${trackingNumber || 'N/A'}</span>
                      </div>
                      <div class="mb-2">
                        <span class="text-xs font-medium text-gray-500">Package Type:</span>
                        <span class="ml-2 text-sm">${item["Package Type"] || 'N/A'}</span>
                      </div>
                      <div class="mb-2">
                        <span class="text-xs font-medium text-gray-500">Weight:</span>
                        <span class="ml-2 text-sm">${item["Package Weight"] || 'N/A'}</span>
                      </div>
                      <div class="mb-2">
                        <span class="text-xs font-medium text-gray-500">Dimensions:</span>
                        <span class="ml-2 text-sm">${item["Package Dimensions"] || 'N/A'}</span>
                      </div>
                      <div class="mt-3">
                        ${trackingLink ? 
                          `<a href="${trackingLink}" target="_blank" class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                            Visit Carrier Website
                          </a>` : 
                          '<span class="text-xs text-gray-500">No tracking link available</span>'
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        `;
      });
    }
    
    html += `
        </tbody>
      </table>
    `;
    
    // Render the table
    historyTableContainer.innerHTML = html;
    
    // Set up tracking button dropdowns
    visibleData.forEach((item, index) => {
      const dropdownId = `dropdown-${startIndex + index}`;
      const dropdown = document.getElementById(dropdownId);
      
      if (dropdown) {
        const trackingBtn = dropdown.querySelector('.tracking-btn');
        const trackingDropdown = dropdown.querySelector('.tracking-dropdown');
        
        if (trackingBtn && trackingDropdown && item["Tracking Number"]) {
          trackingBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            // Hide all other dropdowns first
            document.querySelectorAll('.tracking-dropdown').forEach(el => {
              if (el !== trackingDropdown) el.classList.add('hidden');
            });
            trackingDropdown.classList.toggle('hidden');
          });
        }
      }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', () => {
      document.querySelectorAll('.tracking-dropdown').forEach(el => {
        el.classList.add('hidden');
      });
    });
  }

  /**
   * Export data as CSV
   * @param {Array} data - The data to export
   */
  exportAsCSV(data) {
    if (!data || data.length === 0) {
      alert('No data to export');
      return;
    }
    
    console.log(`Exporting ${data.length} records as CSV`);
    
    try {
      // Get all unique headers from the data
      const headers = Array.from(
        new Set(
          data.flatMap(item => Object.keys(item))
        )
      );
      
      // Create CSV content
      let csvContent = headers.join(',') + '\n';
      
      data.forEach(item => {
        const row = headers.map(header => {
          let value = item[header] || '';
          // Escape quotes and wrap in quotes if contains comma
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            value = '"' + value.replace(/"/g, '""') + '"';
          }
          return value;
        });
        
        csvContent += row.join(',') + '\n';
      });
      
      // Create download link
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      // Create and click download link
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `shipping_data_${new Date().toISOString().slice(0,10)}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      console.log('CSV export complete');
    } catch (error) {
      console.error('Error exporting CSV:', error);
      alert(`Export failed: ${error.message}`);
    }
  }
  
  /**
   * Export table data as CSV
   */
  exportTableAsCSV() {
    this.exportAsCSV(this.filteredData);
  }
  
  /**
   * Export all data as CSV
   */
  exportAllDataAsCSV() {
    this.exportAsCSV(this.historyData);
  }

  exportAsExcel(data) {
    alert('Excel export functionality requires additional libraries. Please use CSV export for now.');
  }

  // New chart rendering methods for Cost Analysis tab
  renderCostTrendsChart() {
    const chartContainer = document.getElementById('costTrendsChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering cost trends chart');
      
      const filter = document.getElementById('costTrendsFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group data by month/quarter/year based on filter
      const periodData = {};
      let periodFormat;
      
      if (filter === 'month') {
        periodFormat = { day: 'numeric', month: 'short' };
      } else if (filter === 'quarter') {
        periodFormat = { month: 'short' };
      } else {
        periodFormat = { month: 'short', year: '2-digit' };
      }
      
      timeFilteredData.forEach(item => {
        const date = new Date(item.Date);
        const period = date.toLocaleDateString(undefined, periodFormat);
        const cost = parseFloat(item["Freight Cost"]) || 0;
        
        if (!periodData[period]) {
          periodData[period] = { total: 0, count: 0 };
        }
        
        periodData[period].total += cost;
        periodData[period].count++;
      });
      
      // Format for chart
      const chartData = Object.entries(periodData)
        .map(([period, data]) => ({
          label: period,
          value: data.total,
          avgValue: data.count > 0 ? data.total / data.count : 0,
          count: data.count
        }))
        .sort((a, b) => {
          // Simple alphanumeric sort for periods
          return a.label.localeCompare(b.label);
        });
      
      // Prepare data for dual axis chart - show total cost and average cost
      const totalCosts = chartData.map(d => d.value);
      const avgCosts = chartData.map(d => d.avgValue);
      const labels = chartData.map(d => d.label);
      
      // Create ApexCharts configuration for modern, animated chart
      const options = {
        series: [{
          name: 'Total Cost',
          type: 'column',
          data: totalCosts
        }, {
          name: 'Average Cost',
          type: 'line',
          data: avgCosts
        }],
        chart: {
          height: 350,
          type: 'line',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        stroke: {
          width: [0, 3],
          curve: 'smooth'
        },
        colors: ['#60a5fa', '#ef4444'],
        plotOptions: {
          bar: {
            columnWidth: '60%',
            borderRadius: 6
          }
        },
        fill: {
          opacity: [0.85, 1],
          type: ['gradient', 'solid'],
          gradient: {
            shade: 'light',
            type: "vertical",
            shadeIntensity: 0.25,
            gradientToColors: undefined,
            inverseColors: true,
            opacityFrom: 1,
            opacityTo: 0.7,
            stops: [0, 100]
          }
        },
        markers: {
          size: 4,
          strokeWidth: 0,
          hover: {
            size: 6
          }
        },
        xaxis: {
          categories: labels,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: [
          {
            title: {
              text: 'Total Cost ($)',
              style: {
                fontSize: '13px',
                fontFamily: 'Inter, Segoe UI, sans-serif'
              }
            },
            labels: {
              formatter: function(val) {
                return '$' + val.toFixed(0);
              },
              style: {
                fontSize: '12px',
                fontFamily: 'Inter, Segoe UI, sans-serif'
              }
            }
          },
          {
            opposite: true,
            title: {
              text: 'Average Cost ($)',
              style: {
                fontSize: '13px',
                fontFamily: 'Inter, Segoe UI, sans-serif'
              }
            },
            labels: {
              formatter: function(val) {
                return '$' + val.toFixed(2);
              },
              style: {
                fontSize: '12px',
                fontFamily: 'Inter, Segoe UI, sans-serif'
              }
            }
          }
        ],
        tooltip: {
          shared: true,
          intersect: false,
          y: {
            formatter: function(y, { seriesIndex }) {
              if (typeof y !== 'undefined') {
                return '$' + y.toFixed(seriesIndex === 0 ? 2 : 2) +
                  (seriesIndex === 0 ? ' (Total)' : ' (Average)');
              }
              return y;
            }
          }
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          offsetY: 5,
          itemMargin: {
            horizontal: 10
          }
        }
      };
      
      // Clear container and create chart
      chartContainer.innerHTML = '';
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering cost trends chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  renderCostByDestinationChart() {
    const chartContainer = document.getElementById('costByDestinationChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering cost by destination chart');
      
      const filter = document.getElementById('costByDestinationFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by destination
      const destinationData = {};
      
      timeFilteredData.forEach(item => {
        const destination = item.Country || 'Unknown';
        const cost = parseFloat(item["Freight Cost"]) || 0;
        
        if (destination !== 'Unknown' && destination !== 'N/A' && cost > 0) {
          if (!destinationData[destination]) {
            destinationData[destination] = { total: 0, count: 0 };
          }
          
          destinationData[destination].total += cost;
          destinationData[destination].count++;
        }
      });
      
      // Calculate average cost per shipment by destination
      let chartData = Object.entries(destinationData)
        .map(([destination, data]) => ({
          destination: destination,
          avgCost: data.count > 0 ? data.total / data.count : 0,
          count: data.count,
          total: data.total
        }))
        .filter(item => item.count >= 1) // Include all destinations with at least 1 shipment
        .sort((a, b) => b.avgCost - a.avgCost)
        .slice(0, 10); // Top 10 destinations by average cost
      
      // If we have no data after filtering, show a message
      if (chartData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No destination cost data available</div>`;
        return;
      }
      
      const categories = chartData.map(d => d.destination);
      const values = chartData.map(d => parseFloat(d.avgCost.toFixed(2)));
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts configuration with a simpler bar chart
      const options = {
        series: [{
          name: 'Average Cost',
          data: values
        }],
        chart: {
          type: 'bar',
          height: chartContainer.clientHeight || 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '70%',
            distributed: true,
            borderRadius: 4
          }
        },
        colors: ['#8B5CF6', '#6D28D9', '#5B21B6', '#4C1D95'],
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return '$' + val.toFixed(0);
          },
          style: {
            fontSize: '12px',
            colors: ['#fff'],
            fontWeight: 'bold'
          },
          offsetY: -20
        },
        grid: {
          borderColor: '#e5e7eb',
          xaxis: {
            lines: {
              show: false
            }
          }
        },
        title: {
          text: 'Average Shipping Cost by Country',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        },
        xaxis: {
          categories: categories,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            },
            rotate: -45,
            rotateAlways: true
            }
          },
        yaxis: {
          title: {
            text: 'Average Cost per Shipment ($)',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
          labels: {
            formatter: function(val) {
              return '$' + val.toFixed(0);
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        tooltip: {
          y: {
            formatter: function(val, { dataPointIndex }) {
              const data = chartData[dataPointIndex];
              return `$${val.toFixed(2)}<br>${data.count} shipments`;
            }
          }
        },
        fill: {
          type: 'gradient',
          gradient: {
            type: 'vertical',
            shadeIntensity: 0.3,
            opacityFrom: 0.9,
            opacityTo: 0.7,
            stops: [0, 100]
          }
        },
        legend: {
          show: false
        }
      };

      // Create and render the chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering cost by destination chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  renderPackageCostChart() {
    const chartContainer = document.getElementById('packageCostChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering package cost chart');
      
      const filter = document.getElementById('packageCostFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by package type
      const packageData = {};
      
      timeFilteredData.forEach(item => {
        const packageType = item["Package Type"] || 'Unknown';
        const cost = parseFloat(item["Freight Cost"]) || 0;
        
        if (packageType !== 'Unknown' && packageType !== 'N/A' && cost > 0) {
          if (!packageData[packageType]) {
            packageData[packageType] = { total: 0, count: 0 };
          }
          
          packageData[packageType].total += cost;
          packageData[packageType].count++;
        }
      });
      
      // Format data for chart
      let sortedData = Object.entries(packageData)
        .map(([packageType, data]) => ({
          packageType: packageType,
          total: parseFloat(data.total.toFixed(2)),
          count: data.count,
          avg: parseFloat((data.count > 0 ? data.total / data.count : 0).toFixed(2))
        }))
        .sort((a, b) => b.total - a.total);
      
      // If we have no data after filtering, show a message
      if (sortedData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No package cost data available</div>`;
        return;
      }
      
      // Prepare data for ApexCharts - using a pie chart for better visualization
      const labels = sortedData.slice(0, 6).map(d => d.packageType);
      const values = sortedData.slice(0, 6).map(d => d.total);
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts pie chart configuration
      const options = {
        series: values,
        chart: {
          type: 'pie',
          height: chartContainer.clientHeight || 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        labels: labels,
        colors: ['#10B981', '#059669', '#047857', '#065F46', '#064E3B', '#022C22'],
        title: {
          text: 'Shipping Cost by Package Type',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function(val, { seriesIndex, w }) {
            return '$' + values[seriesIndex].toFixed(0);
          },
          style: {
            fontSize: '12px',
            colors: ['#fff'],
            fontWeight: 'bold'
          },
          dropShadow: {
            enabled: true,
            top: 1,
            left: 1,
            blur: 2,
            opacity: 0.2
          }
        },
        legend: {
          position: 'bottom',
          horizontalAlign: 'center',
              fontSize: '12px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          markers: {
            width: 10,
            height: 10,
            radius: 2
          },
          itemMargin: {
            horizontal: 5,
            vertical: 2
          },
          formatter: function(seriesName, opts) {
            // Format legend to include cost information
            return seriesName;
          }
        },
        tooltip: {
          y: {
            formatter: function(val, { seriesIndex }) {
              const data = sortedData[seriesIndex];
              return `Total: $${data.total.toFixed(0)}<br>Avg: $${data.avg.toFixed(2)}<br>${data.count} shipments`;
            }
          }
        },
        stroke: {
          width: 2,
          colors: ['#fff']
        },
        responsive: [{
          breakpoint: 480,
          options: {
            chart: {
              height: 300
            },
            legend: {
              position: 'bottom'
            }
          }
        }]
      };

      // Create and render the chart
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering package cost chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  // New chart rendering methods for Carrier Performance tab
  renderCarrierPerformanceChart() {
    const chartContainer = document.getElementById('carrierPerformanceChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering carrier performance chart');
      
      const filter = document.getElementById('carrierPerformanceFilter')?.value || 'month';
      
      // Use the pre-processed carrier performance data
      if (!this.carrierPerformanceData || this.carrierPerformanceData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No carrier data available</div>`;
        return;
      }
      
      // Get top 6 carriers by shipment volume
      const topCarriers = this.carrierPerformanceData
        .slice(0, 6);
      
      // Format for ApexCharts
      const carriers = topCarriers.map(c => c.carrier);
      const volumes = topCarriers.map(c => c.shipments);
      const costs = topCarriers.map(c => c.avgCostPerShipment);
      const scores = topCarriers.map(c => c.performanceScore);
      
      // Create ApexCharts configuration
      const options = {
        series: [{
            name: 'Volume', 
          data: volumes
        }, {
            name: 'Avg Cost', 
          data: costs
        }, {
            name: 'Performance', 
          data: scores
        }],
        chart: {
          type: 'bar',
          height: 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          },
          stacked: false
        },
        colors: ['#3B82F6', '#EF4444', '#10B981'],
        plotOptions: {
          bar: {
            horizontal: true,
            columnWidth: '55%',
            borderRadius: 4,
            dataLabels: {
              position: 'top'
            }
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function(val, opts) {
            // Different formatting based on series
            if (opts.seriesIndex === 0) {
              return val + ' shipments';
            } else if (opts.seriesIndex === 1) {
              return '$' + val.toFixed(2);
            } else {
              return val.toFixed(0) + '/100';
            }
          },
          style: {
            fontSize: '10px',
            colors: ['#fff'],
            fontWeight: 'bold'
          },
          offsetX: 5
        },
        stroke: {
          width: 1,
          colors: ['#fff']
        },
        grid: {
          borderColor: '#e5e7eb',
          row: {
            colors: ['transparent', 'transparent']
          }
        },
        xaxis: {
          categories: carriers,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          title: {
            text: 'Carrier',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        tooltip: {
          shared: false,
          intersect: true,
          y: {
            formatter: function(val, { seriesIndex }) {
              if (seriesIndex === 0) {
                return val + ' shipments';
              } else if (seriesIndex === 1) {
                return '$' + val.toFixed(2) + ' avg/shipment';
              } else {
                return val.toFixed(0) + '/100 performance score';
              }
            }
          }
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          offsetY: 0
        }
      };
      
      // Clear container and create chart
      chartContainer.innerHTML = '';
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering carrier performance chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  renderCarrierCostVolumeChart() {
    const chartContainer = document.getElementById('carrierCostVolumeChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering carrier cost vs volume chart');
      
      const filter = document.getElementById('carrierCostVolumeFilter')?.value || 'month';
      
      // Use the pre-processed carrier cost volume data
      if (!this.carrierCostVolumeData || this.carrierCostVolumeData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No carrier data available</div>`;
        return;
      }
      
      // Prepare data for scatter chart
      const series = this.carrierCostVolumeData.map(carrier => ({
        name: carrier.carrier,
        data: [[carrier.shipments, carrier.avgCost]]
      }));
      
      // Calculate average values for quadrants
      const avgShipments = this.carrierCostVolumeData.reduce((sum, d) => sum + d.shipments, 0) / this.carrierCostVolumeData.length;
      const avgCost = this.carrierCostVolumeData.reduce((sum, d) => sum + d.avgCost, 0) / this.carrierCostVolumeData.length;
      
      // Create ApexCharts configuration
      const options = {
        series: series,
        chart: {
          height: 350,
          type: 'scatter',
          zoom: {
            type: 'xy',
            enabled: true
          },
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        colors: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899'],
        xaxis: {
          title: {
            text: 'Shipment Volume',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          tickAmount: 5,
          labels: {
            formatter: function(val) {
              return parseInt(val).toString();
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          title: {
            text: 'Avg Cost/Shipment ($)',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            formatter: function(val) {
              return '$' + val.toFixed(2);
            },
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        markers: {
          size: this.carrierCostVolumeData.map(d => Math.max(8, Math.min(20, d.size || 12))),
          hover: {
            size: 12,
            sizeOffset: 3
          }
        },
        tooltip: {
          y: {
            formatter: function(val) {
              return '$' + val.toFixed(2);
            }
          },
          x: {
            formatter: function(val) {
              return val + ' shipments';
            }
          }
        },
        grid: {
          borderColor: '#e5e7eb',
          xaxis: {
            lines: {
              show: true
            }
          },
          yaxis: {
            lines: {
              show: true
            }
          }
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          markers: {
            width: 12,
            height: 12,
            strokeWidth: 0,
            radius: 12
          }
        },
        annotations: {
          yaxis: [{
            y: avgCost,
            borderColor: '#6B7280',
            strokeDashArray: 5,
            label: {
              text: 'Avg Cost',
              position: 'left',
              offsetX: 10,
              style: {
                fontSize: '12px',
                color: '#6B7280',
                background: '#F3F4F6'
              }
            }
          }],
          xaxis: [{
            x: avgShipments,
            borderColor: '#6B7280',
            strokeDashArray: 5,
            label: {
              text: 'Avg Volume',
              position: 'top',
              offsetY: 10,
              style: {
                fontSize: '12px',
                color: '#6B7280',
                background: '#F3F4F6'
              }
            }
          }],
          points: [
            {
              x: avgShipments / 2,
              y: avgCost * 1.5,
              marker: { size: 0 },
              label: {
                text: 'Low Volume, High Cost',
                style: { color: '#6B7280', background: 'transparent' }
              }
            },
            {
              x: avgShipments * 1.5,
              y: avgCost * 1.5,
              marker: { size: 0 },
              label: {
                text: 'High Volume, High Cost',
                style: { color: '#6B7280', background: 'transparent' }
              }
            },
            {
              x: avgShipments / 2,
              y: avgCost / 2,
              marker: { size: 0 },
              label: {
                text: 'Low Volume, Low Cost',
                style: { color: '#6B7280', background: 'transparent' }
              }
            },
            {
              x: avgShipments * 1.5,
              y: avgCost / 2,
              marker: { size: 0 },
              label: {
                text: 'High Volume, Low Cost',
                style: { color: '#6B7280', background: 'transparent' }
              }
            }
          ]
        }
      };
      
      // Clear container and create chart
      chartContainer.innerHTML = '';
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering carrier cost vs volume chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }
  
  // Helper function to get the average value for annotations
  getAverageValue(values) {
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  renderCarrierDestinationChart() {
    const chartContainer = document.getElementById('carrierDestinationChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering carrier shipment trends chart');
      
      const filter = document.getElementById('carrierDestinationFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group shipments by carrier and month
      const carrierMonthlyData = {};
      
      // Get unique months in the data period
      const months = new Set();
      
      timeFilteredData.forEach(item => {
        const carrier = item.Carrier || 'Unknown';
        if (carrier === 'Unknown' || carrier === 'N/A') return;
        
        const date = new Date(item.Date);
        const monthYear = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        
        months.add(monthYear);
        
        if (!carrierMonthlyData[carrier]) {
          carrierMonthlyData[carrier] = {};
        }
        
        if (!carrierMonthlyData[carrier][monthYear]) {
          carrierMonthlyData[carrier][monthYear] = {
            count: 0,
            cost: 0
          };
        }
        
        carrierMonthlyData[carrier][monthYear].count++;
        carrierMonthlyData[carrier][monthYear].cost += parseFloat(item["Freight Cost"]) || 0;
      });
      
      // Convert to array and get top 5 carriers by volume
      const topCarriers = Object.entries(carrierMonthlyData)
        .map(([carrier, data]) => ({
          carrier,
          totalShipments: Object.values(data).reduce((sum, m) => sum + m.count, 0)
        }))
        .sort((a, b) => b.totalShipments - a.totalShipments)
        .slice(0, 5)
        .map(c => c.carrier);
      
      // Sort months chronologically
      const sortedMonths = Array.from(months).sort((a, b) => {
        const [aMonth, aYear] = a.split(' ');
        const [bMonth, bYear] = b.split(' ');
        const monthOrder = { 'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5, 
                             'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11 };
        
        if (aYear !== bYear) return aYear - bYear;
        return monthOrder[aMonth] - monthOrder[bMonth];
      });
      
      // Prepare series data for ApexCharts
      const series = topCarriers.map(carrier => {
        const monthlyData = sortedMonths.map(month => {
          return carrierMonthlyData[carrier][month] ? carrierMonthlyData[carrier][month].count : 0;
        });
        
        return {
          name: carrier,
          data: monthlyData
        };
      });
      
      // Create ApexCharts configuration
      const options = {
        series: series,
        chart: {
          height: 350,
          type: 'line',
          zoom: {
            enabled: false
          },
          dropShadow: {
            enabled: true,
            color: '#000',
            top: 18,
            left: 7,
            blur: 10,
            opacity: 0.2
          },
          fontFamily: 'Inter, Segoe UI, sans-serif',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800
          }
        },
        colors: ['#3B82F6', '#F59E0B', '#10B981', '#EF4444', '#8B5CF6'],
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth',
          width: 3
        },
        title: {
          text: 'Monthly Shipment Volume by Top Carriers',
          align: 'center',
          style: {
            fontSize: '16px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        },
        grid: {
          borderColor: '#e5e7eb',
          row: {
            colors: ['#f8fafc', 'transparent'],
            opacity: 0.5
          }
        },
        markers: {
          size: 4,
          hover: {
            size: 6
          }
        },
        xaxis: {
          categories: sortedMonths,
          title: {
            text: 'Month',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        yaxis: {
          title: {
            text: 'Number of Shipments',
            style: {
              fontSize: '13px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          },
          min: 0,
          labels: {
            style: {
              fontSize: '12px',
              fontFamily: 'Inter, Segoe UI, sans-serif'
            }
          }
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          floating: true,
          offsetY: -25,
          offsetX: -5,
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif'
        },
        tooltip: {
          shared: true,
          intersect: false,
          y: {
            formatter: function(val) {
              return val + " shipments";
            }
          }
        }
      };
      
      // Clear container and create chart
      chartContainer.innerHTML = '';
      const chart = new ApexCharts(chartContainer, options);
      chart.render();
      
    } catch (error) {
      console.error("Error rendering carrier shipment trends chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  // New chart rendering methods for Shipping Trends tab
  renderShippingMethodChart() {
    const chartContainer = document.getElementById('shippingMethodChart');
    if (!chartContainer) return;
    
    try {
      console.log('Rendering shipping method chart with ApexCharts');
      
      const filter = document.getElementById('shippingMethodFilter')?.value || 'month';
      const timeFilteredData = this.getTimeFilteredData(filter);
      
      if (timeFilteredData.length === 0) {
        chartContainer.innerHTML = `<div class="p-4 text-center text-gray-500">No data available for selected time period</div>`;
        return;
      }
      
      // Group by shipping method
      const methodData = {};
      
      timeFilteredData.forEach(item => {
        const method = item["Shipping Method"] || 'Unknown';
        const terms = item["Shipping Terms"] || 'Unknown';
        const combinedMethod = method !== 'Unknown' && terms !== 'Unknown' ? 
          `${method} (${terms})` : (method !== 'Unknown' ? method : terms);
        
        if (combinedMethod !== 'Unknown') {
          if (!methodData[combinedMethod]) {
            methodData[combinedMethod] = { count: 0, cost: 0 };
          }
          
          methodData[combinedMethod].count++;
          methodData[combinedMethod].cost += parseFloat(item["Freight Cost"]) || 0;
        }
      });
      
      // Sort and limit to top 8 methods for clarity
      const sortedMethods = Object.entries(methodData)
        .sort((a, b) => b[1].count - a[1].count)
        .slice(0, 8);
      
      const labels = sortedMethods.map(([method]) => method);
      const values = sortedMethods.map(([_, data]) => data.count);
      const costs = sortedMethods.map(([_, data]) => parseFloat(data.cost.toFixed(2)));
      
      // Clear previous chart
      chartContainer.innerHTML = '';
      
      // Create ApexCharts donut chart
      new ApexCharts(chartContainer, {
        series: values,
        chart: {
          type: 'donut',
          height: chartContainer.clientHeight || 350,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          animations: {
            enabled: true,
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        title: {
          text: "Shipping Methods Used",
          align: 'center',
          style: {
            fontSize: '18px',
            fontWeight: 600,
            fontFamily: 'Inter, Segoe UI, sans-serif',
            color: '#111827'
          }
        },
        labels: labels,
        colors: ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#14B8A6', '#F97316'],
        plotOptions: {
          pie: {
            donut: {
              size: '55%',
              labels: {
                show: true,
                name: {
                  show: true,
                  fontSize: '14px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 600,
                  color: '#1F2937'
                },
                value: {
                  show: true,
                  fontSize: '20px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 600,
                  color: '#1F2937'
                },
                total: {
                  show: true,
                  label: 'Total Shipments',
                  fontSize: '16px',
                  fontFamily: 'Inter, Segoe UI, sans-serif',
                  fontWeight: 500,
                  color: '#6B7280'
                }
              }
            }
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function(val) {
            return Math.round(val) + '%';
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif',
            fontWeight: 500,
            colors: ['#fff']
          },
          dropShadow: {
            enabled: true,
            top: 1,
            left: 1,
            blur: 2,
            opacity: 0.2
          }
        },
        legend: {
          position: 'right',
          fontSize: '12px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          offsetY: 20,
          itemMargin: {
            horizontal: 5,
            vertical: 0
          },
          formatter: function(seriesName, opts) {
            // Truncate long method names
            return seriesName.length > 15 ? seriesName.substr(0, 15) + '...' : seriesName;
          },
          markers: {
            width: 8,
            height: 8,
            radius: 2
          }
        },
        tooltip: {
          custom: function({ series, seriesIndex, dataPointIndex, w }) {
            const method = labels[seriesIndex];
            const count = values[seriesIndex];
            const cost = costs[seriesIndex];
            const percent = Math.round((count / values.reduce((a, b) => a + b, 0)) * 100);
            
            return `
              <div class="p-2 bg-white shadow-lg rounded-md border border-gray-200">
                <div class="font-medium">${method}</div>
                <div class="text-sm"><span class="font-semibold">${count}</span> shipments (${percent}%)</div>
                <div class="text-sm">Total cost: <span class="font-semibold">$${cost.toFixed(2)}</span></div>
              </div>
            `;
          }
        }
      }).render();
    } catch (error) {
      console.error("Error rendering shipping method chart:", error);
      chartContainer.innerHTML = `<div class="error-message p-4">Error rendering chart: ${error.message}</div>`;
    }
  }

  // Function to get cutoff date based on filter
  getCutoffDate(filter) {
    const now = new Date();
    let cutoffDate = new Date();
    
    switch (filter) {
      case 'week':
        cutoffDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        cutoffDate.setDate(now.getDate() - 30);
        break;
      case 'quarter':
        cutoffDate.setDate(now.getDate() - 90);
        break;
      case 'year':
        cutoffDate.setDate(now.getDate() - 365);
        break;
      default:
        cutoffDate.setDate(now.getDate() - 30);
    }
    
    return cutoffDate;
  }

  // Helper method to render modern donut charts using ApexCharts
  renderModernDonutChart(container, title, series, labels, colors, tooltipFormatter) {
    if (!container) return null;
    
    // Clear the container
    container.innerHTML = '';
    
    // Create chart options
    const options = {
      series: series,
      chart: {
        type: 'donut',
        height: container.clientHeight || 350,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        animations: {
          enabled: true,
          speed: 700,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        },
        toolbar: {
          show: false
        }
      },
      title: {
        text: title,
        align: 'center',
        style: {
          fontSize: '18px',
          fontWeight: 600,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#111827'
        }
      },
      labels: labels,
      colors: colors || ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#14B8A6', '#F97316'],
      plotOptions: {
        pie: {
          donut: {
            size: '55%',
            background: 'transparent',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '14px',
                fontFamily: 'Inter, Segoe UI, sans-serif',
                fontWeight: 600,
                color: '#1F2937',
                offsetY: -10
              },
              value: {
                show: true,
                fontSize: '26px',
                fontFamily: 'Inter, Segoe UI, sans-serif',
                fontWeight: 700,
                color: '#1F2937',
                offsetY: 5,
                formatter: (val) => val
              },
              total: {
                show: true,
                showAlways: true,
                label: 'Total',
                fontSize: '16px',
                fontFamily: 'Inter, Segoe UI, sans-serif',
                fontWeight: 600,
                color: '#6B7280',
                formatter: (w) => w.globals.seriesTotals.reduce((a, b) => a + b, 0)
              }
            }
          }
        }
      },
      stroke: {
        width: 0
      },
      dataLabels: {
        enabled: true,
        style: {
          fontSize: '13px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          fontWeight: 500,
          colors: ['#fff']
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.2
        }
      },
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        offsetY: 5,
        itemMargin: {
          horizontal: 8,
          vertical: 5
        },
        markers: {
          width: 10,
          height: 10,
          radius: 3
        }
      },
      tooltip: {
        enabled: true,
        custom: tooltipFormatter || undefined
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            height: 280
          },
          legend: {
            position: 'bottom',
            offsetY: 0
          }
        }
      }]
    };
    
    // Create and render the chart
    const chart = new ApexCharts(container, options);
    chart.render();
    
    return chart;
  }

  /**
   * Set up event listeners for tab navigation
   */
  setupTabEventListeners() {
    // Get tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    
    console.log('Setting up tab event listeners for', tabButtons.length, 'tabs');
    
    // Remove any existing event listeners by cloning and replacing
    tabButtons.forEach(button => {
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);
    });
    
    // Get fresh references after cloning
    const refreshedButtons = document.querySelectorAll('.tab-button');
    
    // Add click event listeners to the new buttons
    refreshedButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.getAttribute('data-tab');
        console.log('Tab clicked:', tabId);
        
        // Deactivate all tabs
        refreshedButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });
        
        // Activate the clicked tab
        button.classList.add('active');
        
        // Find the tab content - looking for tab ID format "{tabId}-tab"
        const tabContentId = `${tabId}-tab`;
        const tabContent = document.getElementById(tabContentId);
        
        if (tabContent) {
          tabContent.classList.add('active');
          console.log('Activated tab content:', tabContentId);
        } else {
          console.error(`Tab content not found for id: ${tabContentId}`);
        }
      });
    });
    
    // Set up filter event listeners
    document.querySelectorAll('select[id$="Filter"]').forEach(select => {
      select.addEventListener('change', () => {
        console.log('Filter changed:', select.id, 'to', select.value);
        this.renderCharts();
      });
    });
  }
}

/**
 * Initialize the dashboard when the DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
  console.log("DOM loaded, initializing dashboard");
  
  // Add a small delay to ensure external scripts are fully loaded
  setTimeout(() => {
    try {
      // Check for ApexCharts
      if (typeof ApexCharts === 'undefined') {
        throw new Error('ApexCharts library is not available. Please check if the script is loaded correctly.');
      }
      
      console.log("ApexCharts found, initializing dashboard");
      
      // Initialize dashboard
      const dashboard = new FullscreenDashboard();
      
      // Make dashboard instance available globally for extensions
      window.dashboardInstance = dashboard;
      
      dashboard.init().catch(error => {
        console.error("Dashboard initialization error:", error);
        document.getElementById('statusMessage').innerHTML = 
          `<div class="error-message">Dashboard initialization failed: ${error.message}</div>`;
        document.getElementById('statusMessage').classList.remove('hidden');
        document.getElementById('initialLoading').classList.add('hidden');
      });
    } catch (error) {
      console.error("Critical error creating dashboard:", error);
      document.getElementById('statusMessage').innerHTML = 
        `<div class="error-message">Critical error: ${error.message}</div>`;
      document.getElementById('statusMessage').classList.remove('hidden');
      document.getElementById('initialLoading').classList.add('hidden');
    }
  }, 500); // 500ms delay to ensure scripts are loaded
});

/**
 * Helper function to debounce function calls
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
