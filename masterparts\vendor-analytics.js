// vendor-analytics.js - Analytics and chart rendering for vendor metrics
import { loadApexCharts } from "../analytics/chart-loader.js";

export class VendorAnalyticsComponent {
  constructor(container, poData, dateRangeFilter, notificationSystem) {
    this.container = container;
    this.poData = poData;
    this.dateRangeFilter = dateRangeFilter;
    this.notificationSystem = notificationSystem;
  }
  
  // Main render method for analytics view content
  render() {
    if (!this.poData || this.poData.length === 0) {
      this.container.innerHTML = `
        <div class="text-center py-8 text-gray-500 dark:text-gray-400">
          <p class="text-xs">No data available for charts.</p>
        </div>
      `;
      return;
    }
    
    this.container.innerHTML = `
      <div>
        ${this.dateRangeFilter.enabled ? `
          <div class="mb-3 p-2 bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs rounded">
            <span class="font-medium">Date Filter Applied:</span>
            ${this.dateRangeFilter.startDate ? ` From ${this.formatDateSafely(this.dateRangeFilter.startDate)}` : ''}
            ${this.dateRangeFilter.endDate ? ` To ${this.formatDateSafely(this.dateRangeFilter.endDate)}` : ''}
            <button id="clearChartDateRangeFilterBtn" class="ml-2 text-blue-700 dark:text-blue-400 hover:underline">Clear</button>
          </div>
        ` : ''}
      
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Lead Time Distribution</h3>
            <div id="leadTimeChart" class="h-60"></div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Delivery Status</h3>
            <div id="deliveryStatusChart" class="h-60"></div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Vendor Performance</h3>
            <div id="vendorPerformanceChart" class="h-60"></div>
          </div>
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Monthly Volume</h3>
            <div id="monthlyVolumeChart" class="h-60"></div>
          </div>
        </div>
      </div>
    `;
    
    // Setup event handlers for the clear filter button
    const clearChartDateRangeFilterBtn = this.container.querySelector('#clearChartDateRangeFilterBtn');
    if (clearChartDateRangeFilterBtn) {
      clearChartDateRangeFilterBtn.addEventListener('click', () => {
        // Dispatch a custom event that the main component can listen for
        const event = new CustomEvent('clearDateRange');
        this.container.dispatchEvent(event);
      });
    }
  }
  
  // Helper method to format dates safely
  formatDateSafely(dateValue) {
    if (!dateValue) return 'N/A';
    
    try {
      const date = new Date(dateValue);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'N/A';
      }
      
      // Format the date as MM/DD/YYYY
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'N/A';
    }
  }
  
  // Calculate lead time between order and delivery
  calculateLeadTime(po) {
    if (!po.Date?.value || !po.LastModifiedDateTime?.value) {
      return null;
    }
    
    const orderDate = new Date(po.Date.value);
    const receivedDate = new Date(po.LastModifiedDateTime.value);
    
    // Calculate raw lead time in days
    return Math.round((receivedDate - orderDate) / (1000 * 60 * 60 * 24));
  }
  
  // Determine delivery status based on promised vs. actual dates
  getDeliveryStatus(po) {
    if (!po.PromisedOn?.value || !po.LastModifiedDateTime?.value) {
      return 'Unknown';
    }
    
    const promisedDate = new Date(po.PromisedOn.value);
    const receivedDate = new Date(po.LastModifiedDateTime.value);
    
    // Compare dates
    const timeDiff = Math.round((receivedDate - promisedDate) / (1000 * 60 * 60 * 24));
    
    if (timeDiff < 0) {
      return 'Early';
    } else if (timeDiff === 0) {
      return 'OnTime';
    } else {
      return 'Late';
    }
  }
  
  // Main method to initialize and render all charts
  async renderCharts() {
    try {
      if (!this.poData || this.poData.length === 0) {
        console.log("No data available for charts");
        return;
      }
      
      // Ensure ApexCharts is loaded before proceeding
      await loadApexCharts();
      
      if (!window.ApexCharts) {
        console.error('ApexCharts failed to load');
        this.addNotification('Failed to load charts library', 'error');
        return;
      }
      
      // Wait for DOM to be ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Render individual charts
      this.renderLeadTimeChart();
      this.renderDeliveryStatusChart();
      this.renderVendorPerformanceChart();
      this.renderMonthlyVolumeChart();
      
      // Add notification about chart status
      if (this.dateRangeFilter.enabled) {
        this.addNotification(`Charts showing data from ${this.poData.length} POs within date range`, 'info');
      }
    } catch (error) {
      console.error('Error rendering charts:', error);
      this.addNotification('Error rendering charts: ' + error.message, 'error');
    }
  }
  
  // Utility to add notifications via the provided notification system
  addNotification(message, type = 'info') {
    if (this.notificationSystem) {
      this.notificationSystem.addNotification(message, type);
    } else {
      console.log(`Notification (${type}): ${message}`);
    }
  }
  
  // Render the lead time distribution chart
  renderLeadTimeChart() {
    const leadTimeElement = document.getElementById('leadTimeChart');
    if (!leadTimeElement) return;
    
    // Remove any existing chart
    leadTimeElement.innerHTML = '';
    
    // Get lead times from delivered POs
    const leadTimes = this.poData
      .filter(po => po.LastModifiedDateTime?.value)
      .map(po => this.calculateLeadTime(po))
      .filter(leadTime => leadTime !== null)
      .sort((a, b) => a - b);
    
    if (leadTimes.length === 0) {
      leadTimeElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No lead time data available</p></div>';
      return;
    }
    
    // Determine appropriate bucket size based on data range and count
    const minLeadTime = leadTimes[0];
    const maxLeadTime = leadTimes[leadTimes.length - 1];
    const leadTimeRange = maxLeadTime - minLeadTime;
    
    // Use adaptive bucket size based on the range
    let bucketSize;
    let maxDisplayValue;
    
    if (leadTimeRange <= 50) {
      bucketSize = 5; // Small range: 5-day buckets
      maxDisplayValue = null; // No cap
    } else if (leadTimeRange <= 100) {
      bucketSize = 10; // Medium range: 10-day buckets
      maxDisplayValue = null; // No cap
    } else if (leadTimeRange <= 200) {
      bucketSize = 20; // Large range: 20-day buckets
      maxDisplayValue = 200; // Cap at 200
    } else {
      bucketSize = 30; // Very large range: 30-day buckets
      maxDisplayValue = 300; // Cap at 300
    }
    
    // Create buckets with adaptive sizing
    const buckets = {};
    let overflowCount = 0;
    
    leadTimes.forEach(lt => {
      if (maxDisplayValue && lt > maxDisplayValue) {
        // Count values above the max display value
        overflowCount++;
      } else {
        // Calculate bucket based on the bucket size
        const bucketIndex = Math.floor(lt / bucketSize) * bucketSize;
        const bucketLabel = `${bucketIndex}-${bucketIndex + bucketSize - 1}`;
        buckets[bucketLabel] = (buckets[bucketLabel] || 0) + 1;
      }
    });
    
    // If we have overflow values, add them as a final bucket
    if (overflowCount > 0) {
      buckets[`${maxDisplayValue}+`] = overflowCount;
    }
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'Number of POs',
        data: Object.values(buckets)
      }],
      chart: {
        type: 'bar',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      plotOptions: {
        bar: {
          distributed: true,
          borderRadius: 2,
          columnWidth: '90%'
        }
      },
      dataLabels: {
        enabled: false
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      xaxis: {
        categories: Object.keys(buckets),
        labels: {
          style: {
            fontSize: '11px'
          },
          rotate: -45,
          trim: true,
          // Only show a subset of labels when there are many buckets
          formatter: function(value, timestamp, opts) {
            const allCategories = opts.w.globals.labels;
            const categoryIndex = allCategories.indexOf(value);
            
            // Determine how many labels to show based on total number of buckets
            if (allCategories.length <= 10) {
              return value; // Show all labels when fewer buckets
            } else {
              // For many buckets, show only some labels to avoid overcrowding
              return categoryIndex % Math.ceil(allCategories.length / 10) === 0 ? value : '';
            }
          }
        },
        title: {
          text: 'Lead Time (Days)'
        }
      },
      yaxis: {
        title: {
          text: 'Number of POs'
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light',
        x: {
          formatter: function(value) {
            return `Lead Time: ${value} days`;
          }
        }
      },
      colors: ['#3b82f6', '#6366f1', '#8b5cf6', '#d946ef', '#ec4899', '#f43f5e']
    };
    
    new ApexCharts(leadTimeElement, options).render();
  }
  
  // Render the delivery status chart (Early/OnTime/Late)
  renderDeliveryStatusChart() {
    const element = document.getElementById('deliveryStatusChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    // Count on-time vs late deliveries
    const deliveredPOs = this.poData.filter(po => po.LastModifiedDateTime?.value && po.PromisedOn?.value);
    
    if (deliveredPOs.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No delivery status data available</p></div>';
      return;
    }
    
    let early = 0;
    let onTime = 0;
    let late = 0;
    
    deliveredPOs.forEach(po => {
      const status = this.getDeliveryStatus(po);
      if (status === 'Early') early++;
      else if (status === 'OnTime') onTime++;
      else if (status === 'Late') late++;
    });
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [early, onTime, late],
      chart: {
        type: 'donut',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      labels: ['Early', 'On-Time', 'Late'],
      colors: ['#10b981', '#3b82f6', '#ef4444'],
      legend: {
        position: 'bottom'
      },
      dataLabels: {
        enabled: true,
        formatter: function(val, opts) {
          return opts.w.config.series[opts.seriesIndex] + ' POs';
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light'
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 200
          },
          legend: {
            position: 'bottom'
          }
        }
      }]
    };
    
    new ApexCharts(element, options).render();
  }
  
  // Render the vendor performance chart
  renderVendorPerformanceChart() {
    const element = document.getElementById('vendorPerformanceChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    // Calculate vendor metrics and extract top vendors
    // We need to build a simplified version for the chart
    const vendorMap = new Map();
    
    // Process each PO and group by vendor
    this.poData.forEach(po => {
      const vendorId = po.VendorID?.value;
      if (!vendorId) return;
      
      const vendorName = po.VendorName?.value || vendorId;
      const status = this.getDeliveryStatus(po);
      
      if (!vendorMap.has(vendorId)) {
        vendorMap.set(vendorId, {
          id: vendorId,
          name: vendorName,
          orderCount: 0,
          earlyOrOnTimeCount: 0
        });
      }
      
      const vendor = vendorMap.get(vendorId);
      vendor.orderCount++;
      if (status === 'Early' || status === 'OnTime') {
        vendor.earlyOrOnTimeCount++;
      }
    });
    
    // Convert to array and calculate percentages
    let topVendors = Array.from(vendorMap.values())
      .filter(v => v.orderCount >= 3) // Only include vendors with at least 3 orders
      .map(v => ({
        ...v,
        onTimePercent: Math.round((v.earlyOrOnTimeCount / v.orderCount) * 100)
      }))
      .sort((a, b) => b.onTimePercent - a.onTimePercent)
      .slice(0, 5);  // Get top 5
      
    if (topVendors.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No vendor performance data available</p></div>';
      return;
    }
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'On-Time %',
        data: topVendors.map(v => v.onTimePercent)
      }],
      chart: {
        type: 'bar',
        height: 250,
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      plotOptions: {
        bar: {
          horizontal: true,
          barHeight: '70%',
          distributed: true,
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val + '%';
        },
        textAnchor: 'start',
        offsetX: 5,
        style: {
          fontSize: '12px',
          colors: ['#fff']
        }
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4
      },
      xaxis: {
        categories: topVendors.map(v => {
          // Truncate long names
          return v.name.length > 15 ? v.name.substring(0, 15) + '...' : v.name;
        }),
        labels: {
          style: {
            fontSize: '11px'
          }
        },
        max: 100
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '11px'
          }
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light',
        y: {
          title: {
            formatter: function(seriesName, opts) {
              return topVendors[opts.dataPointIndex].name + ' - ' + seriesName;
            }
          }
        }
      },
      colors: ['#3b82f6', '#6366f1', '#8b5cf6', '#d946ef', '#ec4899']
    };
    
    new ApexCharts(element, options).render();
  }
  
  // Render the monthly volume chart
  renderMonthlyVolumeChart() {
    const element = document.getElementById('monthlyVolumeChart');
    if (!element) return;
    
    // Remove any existing chart
    element.innerHTML = '';
    
    if (this.poData.length === 0) {
      element.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-gray-500 dark:text-gray-400 text-sm">No monthly volume data available</p></div>';
      return;
    }
    
    // Group POs by month
    const monthlyVolume = {};
    
    // Determine years to include based on data
    const years = new Set();
    this.poData.forEach(po => {
      if (po.Date?.value) {
        const orderDate = new Date(po.Date.value);
        years.add(orderDate.getFullYear());
      }
    });
    
    // Sort years
    const sortedYears = Array.from(years).sort();
    
    // If we have too many years, use just the most recent 2 years
    const yearsToUse = sortedYears.length > 2 ? sortedYears.slice(-2) : sortedYears;
    
    // Create labels for each month in each year
    const labels = [];
    yearsToUse.forEach(year => {
      for (let month = 0; month < 12; month++) {
        const date = new Date(year, month, 1);
        const monthName = date.toLocaleString('default', { month: 'short' });
        labels.push(`${monthName} ${year}`);
        monthlyVolume[`${monthName} ${year}`] = 0;
      }
    });
    
    // Count POs by month
    this.poData.forEach(po => {
      if (!po.Date?.value) return;
      
      const orderDate = new Date(po.Date.value);
      const year = orderDate.getFullYear();
      
      if (yearsToUse.includes(year)) {
        const monthName = orderDate.toLocaleString('default', { month: 'short' });
        const label = `${monthName} ${year}`;
        monthlyVolume[label] = (monthlyVolume[label] || 0) + 1;
      }
    });
    
    const isDarkMode = document.body.classList.contains('dark-mode');
    
    const options = {
      series: [{
        name: 'PO Count',
        data: labels.map(label => monthlyVolume[label] || 0)
      }],
      chart: {
        height: 250,
        type: 'line',
        fontFamily: 'inherit',
        foreColor: isDarkMode ? '#9ca3af' : '#4b5563',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800
        }
      },
      stroke: {
        curve: 'smooth',
        width: 3
      },
      grid: {
        borderColor: isDarkMode ? '#374151' : '#e5e7eb',
        strokeDashArray: 4
      },
      xaxis: {
        categories: labels,
        labels: {
          rotate: -45,
          style: {
            fontSize: '10px'
          }
        }
      },
      tooltip: {
        theme: isDarkMode ? 'dark' : 'light'
      },
      markers: {
        size: 5,
        colors: ['#3b82f6'],
        strokeWidth: 0
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'dark',
          gradientToColors: ['#8b5cf6'],
          shadeIntensity: 1,
          type: 'horizontal',
          opacityFrom: 1,
          opacityTo: 1
        }
      }
    };
    
    new ApexCharts(element, options).render();
  }
} 