# Modern Spreadsheet UI

A clean, modern Google Sheets-like UI built with HTML, CSS, and JavaScript.

## Features

- 🎨 Modern, clean design with attention to detail
- ⌨️ Complete keyboard navigation (arrows, tab, enter)
- 📊 Resizable rows and columns
- 📋 Cell selection and range operations
- 🧮 Formula support with cell references
- 📑 Multiple sheet tabs
- 📱 Responsive design for different screen sizes
- 🌓 Light/dark mode support
- 🗄️ IndexedDB integration for importing data
- 🔍 Interactive data preview
- 🎯 Context menu for common operations
- 🎚️ Formatting controls for text styling

## Usage

1. Open `index.html` in your browser
2. Click on cells to edit their content
3. Use arrow keys to navigate between cells
4. Enter formulas starting with '=' (e.g., =A1+B1)
5. Use Tab and Enter for quick navigation
6. Click and drag to select multiple cells
7. Click on column/row borders to resize
8. Use the toolbar buttons for formatting
9. Click the database icon to import data from IndexedDB

## IndexedDB Import

This feature allows you to import data from any IndexedDB database in your browser:

1. Click the database icon in the toolbar
2. Select a database from the dropdown
3. Select an object store from the dropdown
4. View the data preview
5. Configure import options:
   - Include field names as headers
   - Clear existing data before import
6. Click "Import Data" to add the data to your spreadsheet

## Formula Examples

- Simple arithmetic: `=2+3*4`
- Cell references: `=A1+B1`
- Mixed: `=A1*2+10`

## Keyboard Shortcuts

- Arrow keys: Navigate between cells
- Tab: Move to the next cell (right)
- Enter: Move to the cell below
- Escape: Cancel editing
- Delete/Backspace: Clear selected cells

## Technologies Used

- HTML5
- CSS3 with variables and modern features
- Vanilla JavaScript (ES6+)
- IndexedDB API
- Font Awesome icons
- Inter font family

## Browser Support

This UI is designed to work with modern browsers that support CSS Grid, CSS Variables, ES6 JavaScript features, and IndexedDB. Chrome is recommended for full IndexedDB support.

## Performance Optimizations

- Efficient DOM manipulation
- Optimized event handling
- Virtualization techniques for large data sets
- Responsive to different screen sizes
- Touch-friendly for mobile devices 