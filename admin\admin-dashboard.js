// Admin Dashboard Component
export class AdminDashboardComponent {
  constructor(container) {
    this.container = container;
  }

  async init() {
    try {
      this.render();
    } catch (error) {
      console.error('Error initializing admin dashboard:', error);
      this.showError('Failed to load admin dashboard. Please try again later.');
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="h-full flex flex-col">
        <!-- Header -->
        <div class="bg-white shadow px-6 py-4 flex justify-between items-center">
          <h1 class="text-2xl font-bold text-gray-800 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-2 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
            </svg>
            Admin Dashboard
          </h1>
        </div>
        
        <!-- Coming Soon Message -->
        <div class="flex-grow p-12 bg-gray-50 flex flex-col items-center justify-center text-center">
          <div class="bg-white rounded-lg shadow-lg p-8 max-w-2xl">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-blue-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            
            <h2 class="text-2xl font-bold text-gray-800 mb-4">Admin Dashboard Coming Soon</h2>
            
            <p class="text-gray-600 mb-6">
              We're working hard to build a comprehensive admin dashboard with user management, 
              system monitoring, and advanced analytics. This feature will be available in the next update.
            </p>
            
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <div class="flex">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 class="text-blue-700 font-medium">Administrator Access Granted</h3>
                  <p class="text-blue-600 text-sm mt-1">
                    You have administrator privileges. You'll be notified when the admin dashboard is ready.
                  </p>
                </div>
              </div>
            </div>
            
            <h3 class="text-lg font-semibold text-gray-800 mb-3">Upcoming Features:</h3>
            
            <ul class="text-left text-gray-600 space-y-2 mb-6">
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                User Management & Permissions
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                System Monitoring & Logs
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                Advanced Analytics & Reporting
              </li>
              <li class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
                System Configuration & Backup
              </li>
            </ul>
            
            <div class="pt-4 border-t border-gray-200">
              <p class="text-sm text-gray-500">
                If you have any suggestions for the admin dashboard, please contact the development team.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <style>
        .nav-tab {
          @apply py-4 px-6 text-gray-500 hover:text-gray-700 font-medium border-b-2 border-transparent flex items-center;
        }
        .nav-tab.active {
          @apply text-blue-600 border-blue-600;
        }
      </style>
    `;
  }

  showError(message) {
    this.container.innerHTML = `
      <div class="flex items-center justify-center h-full">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded max-w-md">
          <div class="flex">
            <div class="py-1 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div>
              <p class="font-bold">Error</p>
              <p class="text-sm">${message}</p>
            </div>
          </div>
          <div class="mt-3 flex justify-center">
            <button id="retryBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
              Retry
            </button>
          </div>
        </div>
      </div>`;
    
    const retryBtn = document.getElementById('retryBtn');
    if (retryBtn) {
      retryBtn.addEventListener('click', () => this.init());
    }
  }
}