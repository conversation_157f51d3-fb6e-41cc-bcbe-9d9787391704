// Enhanced SVG-based chart generation functions optimized for Chrome extension

// Utility function to get the maximum value in an array
function getMaxValue(data) {
  return Math.max(...data.map((item) => (typeof item === "object" ? item.value : item)))
}

// Create a simple bar chart with enhanced styling and horizontal option
export function createBarChart(data, width, height, colors = ["#4CAF50", "#2196F3", "#FFC107", "#F44336"], horizontal = false, compact = false) {
  const maxValue = getMaxValue(data);
  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Enhanced background with subtle gradient
  svg += `
    <defs>
      <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#f9fafb" />
        <stop offset="100%" stop-color="#f3f4f6" />
      </linearGradient>
    </defs>
    <rect x="0" y="0" width="${width}" height="${height}" fill="url(#bg-gradient)" rx="4" />
  `;
  
  // Adjust margins based on compact mode and chart type
  // Increased bottom margin to ensure labels are fully visible
  const margins = compact ? 
    { top: 10, right: 10, bottom: 40, left: horizontal ? 70 : 35 } : 
    { top: 20, right: 20, bottom: 50, left: horizontal ? 100 : 50 };
  
  // Chart dimensions
  const chartWidth = width - margins.left - margins.right;
  const chartHeight = height - margins.top - margins.bottom;
  
  // Draw scale lines with enhanced styling
  const gridLineCount = compact ? 3 : 4;
  for (let i = 0; i <= gridLineCount; i++) {
    const y = horizontal ? 
      margins.top + (i * chartHeight / gridLineCount) : 
      margins.top + chartHeight - (i * chartHeight / gridLineCount);
    
    svg += `<line x1="${margins.left}" y1="${y}" x2="${width - margins.right}" y2="${y}" stroke="#e5e7eb" stroke-width="1" stroke-dasharray="${i === 0 ? 0 : '3,3'}" />`;
    
    // Only show y-axis labels on vertical charts or all charts if horizontal
    if (!horizontal || i === 0 || i === gridLineCount) {
      const valueLabel = Math.round(maxValue * i / gridLineCount);
      svg += `<text x="${margins.left - 5}" y="${y + 3}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#6b7280" text-anchor="end">${valueLabel}</text>`;
    }
  }

  if (horizontal) {
    // Horizontal bar chart
    const barHeight = chartHeight / data.length;
    const barSpacing = compact ? 2 : 5;
    
    data.forEach((item, index) => {
      const value = typeof item === "object" ? item.value : item;
      const label = typeof item === "object" ? item.label : `Item ${index + 1}`;
      const tooltip = typeof item === "object" && item.tooltip ? item.tooltip : label;
      
      const barWidth = (value / maxValue) * chartWidth;
      const y = margins.top + index * barHeight;
      
      // Add gradient for each bar
      svg += `
        <defs>
          <linearGradient id="bar-gradient-${index}" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="${colors[index % colors.length]}" />
            <stop offset="100%" stop-color="${colorLuminance(colors[index % colors.length], 0.2)}" />
          </linearGradient>
        </defs>
      `;
      
      // Bar with rounded corners and gradient
      svg += `<rect x="${margins.left}" y="${y + barSpacing/2}" width="${barWidth}" height="${barHeight - barSpacing}" rx="2" fill="url(#bar-gradient-${index})" />`;
      
      // Label on left - ensure full text is visible
      svg += `<text x="${margins.left - 7}" y="${y + barHeight/2}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#374151" text-anchor="end" dominant-baseline="middle">${label}</text>`;
      
      // Value at end of bar
      if (barWidth > 30) {
        svg += `<text x="${margins.left + barWidth - 4}" y="${y + barHeight/2}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="white" text-anchor="end" dominant-baseline="middle" font-weight="bold">${value}</text>`;
      } else {
        svg += `<text x="${margins.left + barWidth + 4}" y="${y + barHeight/2}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#374151" dominant-baseline="middle">${value}</text>`;
      }
      
      // Add tooltip title
      svg += `<title>${tooltip}</title>`;
    });
  } else {
    // Vertical bar chart
    const barWidth = chartWidth / data.length;
    const maxBarWidth = compact ? 30 : 40;
    const effectiveBarWidth = Math.min(barWidth, maxBarWidth);
    
    data.forEach((item, index) => {
      const value = typeof item === "object" ? item.value : item;
      const label = typeof item === "object" ? item.label : `Item ${index + 1}`;
      const tooltip = typeof item === "object" && item.tooltip ? item.tooltip : label;
      
      const barHeight = (value / maxValue) * chartHeight;
      const x = margins.left + index * barWidth + (barWidth - effectiveBarWidth) / 2;
      const y = margins.top + chartHeight - barHeight;
      
      // Add gradient for each bar
      svg += `
        <defs>
          <linearGradient id="bar-gradient-${index}" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stop-color="${colorLuminance(colors[index % colors.length], 0.2)}" />
            <stop offset="100%" stop-color="${colors[index % colors.length]}" />
          </linearGradient>
        </defs>
      `;
      
      // Bar with rounded top corners and gradient
      svg += `<rect x="${x}" y="${y}" width="${effectiveBarWidth}" height="${barHeight}" rx="2" fill="url(#bar-gradient-${index})" />`;
      
      // Bottom label - ensure full visibility with angled text
      // Increased offset for labels and made them angled for better readability
      const labelX = x + effectiveBarWidth/2;
      const labelY = margins.top + chartHeight + 12;
      svg += `<text x="${labelX}" y="${labelY}" font-family="Arial" font-size="${compact ? 9 : 10}" fill="#374151" text-anchor="end" transform="rotate(-35 ${labelX},${labelY})">${label}</text>`;
      
      // Value on top of bar if there's room
      if (barHeight > 15) {
        svg += `<text x="${x + effectiveBarWidth/2}" y="${y + (compact ? 12 : 15)}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="white" text-anchor="middle" font-weight="bold">${value}</text>`;
      } else if (barHeight > 0) {
        svg += `<text x="${x + effectiveBarWidth/2}" y="${y - 5}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#374151" text-anchor="middle">${value}</text>`;
      }
      
      // Add tooltip title
      svg += `<title>${tooltip}</title>`;
    });
  }

  svg += "</svg>";
  return svg;
}

// Create an enhanced line chart with labels, smooth curve, and area fill
export function createLineChart(data, width, height, color = "#2196F3", labels = [], compact = false) {
  const maxValue = getMaxValue(data);
  
  // Adjust margins based on compact mode
  const margins = compact ? 
    { top: 15, right: 15, bottom: 40, left: 40 } : 
    { top: 25, right: 25, bottom: 50, left: 50 };
  
  // Chart dimensions
  const chartWidth = width - margins.left - margins.right;
  const chartHeight = height - margins.top - margins.bottom;
  
  // Calculate point spacing
  const xStep = chartWidth / (data.length - 1 || 1); // Avoid division by zero
  const yScale = chartHeight / (maxValue || 1); // Avoid division by zero

  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Enhanced background with gradient
  svg += `
    <defs>
      <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#f9fafb" />
        <stop offset="100%" stop-color="#f3f4f6" />
      </linearGradient>
      <linearGradient id="area-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="${color}40" />
        <stop offset="100%" stop-color="${color}05" />
      </linearGradient>
    </defs>
    <rect x="0" y="0" width="${width}" height="${height}" fill="url(#bg-gradient)" rx="4" />
  `;
  
  // Draw horizontal grid lines
  const gridLineCount = compact ? 3 : 4;
  for (let i = 0; i <= gridLineCount; i++) {
    const yPos = margins.top + chartHeight - (i * chartHeight / gridLineCount);
    svg += `<line x1="${margins.left}" y1="${yPos}" x2="${width - margins.right}" y2="${yPos}" stroke="#e5e7eb" stroke-width="1" stroke-dasharray="${i === 0 ? 0 : '3,3'}" />`;
    
    // Add value labels
    const valueLabel = Math.round(maxValue * i / gridLineCount);
    svg += `<text x="${margins.left - 5}" y="${yPos + 3}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#6b7280" text-anchor="end">${valueLabel}</text>`;
  }

  // Create path with bezier curves for smoother line
  if (data.length > 0) {
    let pathD = `M${margins.left},${margins.top + chartHeight - data[0] * yScale}`;
    
    for (let i = 1; i < data.length; i++) {
      const x0 = margins.left + (i - 1) * xStep;
      const y0 = margins.top + chartHeight - data[i - 1] * yScale;
      const x1 = margins.left + i * xStep;
      const y1 = margins.top + chartHeight - data[i] * yScale;
      
      // Control points for bezier curve
      const cx1 = x0 + xStep / 3;
      const cy1 = y0;
      const cx2 = x1 - xStep / 3;
      const cy2 = y1;
      
      pathD += ` C${cx1},${cy1} ${cx2},${cy2} ${x1},${y1}`;
    }
    
    // Add area under curve with gradient
    svg += `<path d="${pathD} L${margins.left + (data.length - 1) * xStep},${margins.top + chartHeight} L${margins.left},${margins.top + chartHeight} Z" fill="url(#area-gradient)" />`;
    
    // Draw a slightly wider path for glow effect
    svg += `<path d="${pathD}" fill="none" stroke="${color}33" stroke-width="${compact ? 4 : 6}" stroke-linecap="round" />`;
    // Draw the main path
    svg += `<path d="${pathD}" fill="none" stroke="${color}" stroke-width="${compact ? 2 : 3}" stroke-linecap="round" />`;
    
    // Add data points with enhanced hover effect - add fewer points if compact
    const pointInterval = compact && data.length > 10 ? Math.ceil(data.length / 10) : 1;
    
    data.forEach((value, index) => {
      // Skip points if we're in compact mode and have lots of data
      if (compact && index % pointInterval !== 0 && index !== data.length - 1) return;
      
      const x = margins.left + index * xStep;
      const y = margins.top + chartHeight - value * yScale;
      
      // Add tooltip
      const label = labels[index] || `Point ${index + 1}`;
      
      // Outer glow circle - smaller in compact mode
      svg += `<circle cx="${x}" cy="${y}" r="${compact ? 4 : 5}" fill="${color}33" />`;
      // Inner circle - smaller in compact mode
      svg += `<circle cx="${x}" cy="${y}" r="${compact ? 2.5 : 3.5}" fill="white" stroke="${color}" stroke-width="${compact ? 1.5 : 2}" />`;
      // Add tooltip
      svg += `<title>${label}: ${value}</title>`;
    });
    
    // X-axis labels - calculate how many to show based on space
    const labelInterval = Math.ceil(data.length / (compact ? 5 : 8));
    
    for (let i = 0; i < data.length; i += labelInterval) {
      const x = margins.left + i * xStep;
      const label = labels[i] || `Point ${i + 1}`;
      
      // Draw small tick mark
      svg += `<line x1="${x}" y1="${margins.top + chartHeight}" x2="${x}" y2="${margins.top + chartHeight + 3}" stroke="#9ca3af" stroke-width="1" />`;
      
      // Add label with rotation for better readability
      const labelX = x;
      const labelY = margins.top + chartHeight + 12;
      svg += `<text x="${labelX}" y="${labelY}" font-family="Arial" font-size="${compact ? 9 : 10}" fill="#6b7280" text-anchor="end" transform="rotate(-35 ${labelX},${labelY})">${label}</text>`;
    }
  }

  svg += "</svg>";
  return svg;
}

// Create an enhanced pie chart with better proportions, labels and 3D effect
export function createPieChart(data, width, height, colors = ["#4CAF50", "#2196F3", "#FFC107", "#F44336"], compact = false) {
  // Calculate optimal radius based on available space
  const radius = Math.min(width, height) / (compact ? 3 : 2.5);
  const centerX = width / 2;
  const centerY = height / 2;
  
  // Calculate total for percentage display
  const total = data.reduce((sum, item) => sum + item.value, 0);

  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Enhanced background with subtle gradient
  svg += `
    <defs>
      <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#f9fafb" />
        <stop offset="100%" stop-color="#f3f4f6" />
      </linearGradient>
    </defs>
    <rect x="0" y="0" width="${width}" height="${height}" fill="url(#bg-gradient)" rx="4" />
  `;
  
  let startAngle = 0;
  
  // Calculate legend position
  const legendX = centerX + radius * 0.8;
  const legendY = compact ? 25 : 35;
  const legendItemHeight = compact ? 15 : 22;

  // Add subtle drop shadow for the pie
  svg += `
    <defs>
      <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
        <feDropShadow dx="0" dy="1" stdDeviation="2" flood-opacity="0.1"/>
      </filter>
    </defs>
  `;
  
  // Create a group for the pie with shadow
  svg += `<g filter="url(#shadow)">`;

  // Draw pie slices with enhanced styling
  data.forEach((item, index) => {
    const sliceAngle = (item.value / total) * 2 * Math.PI;
    const endAngle = startAngle + sliceAngle;
    const percentage = Math.round((item.value / total) * 100);
    const tooltip = item.tooltip || `${item.label}: ${percentage}%`;

    const startX = centerX + radius * Math.cos(startAngle);
    const startY = centerY + radius * Math.sin(startAngle);
    const endX = centerX + radius * Math.cos(endAngle);
    const endY = centerY + radius * Math.sin(endAngle);

    const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;

    const pathData = [
      `M ${centerX},${centerY}`,
      `L ${startX},${startY}`,
      `A ${radius},${radius} 0 ${largeArcFlag},1 ${endX},${endY}`,
      "Z",
    ].join(" ");

    const color = colors[index % colors.length];
    
    // Add gradient for each slice
    svg += `
      <defs>
        <linearGradient id="slice-gradient-${index}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stop-color="${colorLuminance(color, 0.2)}" />
          <stop offset="100%" stop-color="${color}" />
        </linearGradient>
      </defs>
    `;
    
    // Add pie slice with gradient
    svg += `<path d="${pathData}" fill="url(#slice-gradient-${index})" stroke="white" stroke-width="1.5">
      <title>${tooltip}</title>
    </path>`;

    // Add percentage label if slice is big enough
    if (percentage >= (compact ? 8 : 5)) {
      const labelAngle = startAngle + sliceAngle / 2;
      const labelRadius = radius * 0.65;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      svg += `<text x="${labelX}" y="${labelY}" font-family="Arial" font-size="${compact ? 10 : 12}" font-weight="bold" fill="white" text-anchor="middle" dominant-baseline="middle">${percentage}%</text>`;
    }
    
    startAngle = endAngle;
  });
  
  // Close the shadow group
  svg += `</g>`;
  
  // Add legend with improved styling
  data.forEach((item, index) => {
    const color = colors[index % colors.length];
    const percentage = Math.round((item.value / total) * 100);
    
    svg += `<rect x="${legendX}" y="${legendY + index * legendItemHeight}" width="${compact ? 10 : 12}" height="${compact ? 10 : 12}" fill="${color}" rx="2" />`;
    svg += `<text x="${legendX + (compact ? 15 : 17)}" y="${legendY + index * legendItemHeight + (compact ? 8 : 9)}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="#374151" dominant-baseline="middle">${item.label} (${percentage}%)</text>`;
  });

  svg += "</svg>";
  return svg;
}

// Create a stats card grid for displaying KPI metrics
export function createStatsGrid(stats, width, height, compact = false) {
  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Background with gradient
  svg += `
    <defs>
      <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#f9fafb" />
        <stop offset="100%" stop-color="#f3f4f6" />
      </linearGradient>
    </defs>
    <rect x="0" y="0" width="${width}" height="${height}" fill="url(#bg-gradient)" rx="4" />
  `;
  
  // Calculate grid layout
  const columns = 3;
  const rows = Math.ceil(stats.length / columns);
  const cellWidth = width / columns;
  const cellHeight = height / rows;
  
  // Create the stat cards
  stats.forEach((stat, index) => {
    const row = Math.floor(index / columns);
    const col = index % columns;
    const x = col * cellWidth;
    const y = row * cellHeight;
    
    // Card background
    svg += `
      <rect x="${x + 5}" y="${y + 5}" width="${cellWidth - 10}" height="${cellHeight - 10}" rx="6" fill="white" 
        stroke="${stat.color || '#e5e7eb'}" stroke-width="1" filter="url(#shadow)" />
    `;
    
    // Add icon background
    svg += `
      <circle cx="${x + 25}" cy="${y + 25}" r="15" fill="${stat.color}15" />
    `;
    
    // Add icon (simplified)
    if (stat.icon === 'weight') {
      svg += `
        <path d="M${x + 18},${y + 25} L${x + 32},${y + 25} M${x + 25},${y + 18} L${x + 25},${y + 32}" 
          stroke="${stat.color}" stroke-width="2" stroke-linecap="round" />
      `;
    } else if (stat.icon === 'dollar') {
      svg += `
        <path d="M${x + 25},${y + 18} L${x + 25},${y + 32} M${x + 21},${y + 21} L${x + 29},${y + 21} M${x + 21},${y + 29} L${x + 29},${y + 29}" 
          stroke="${stat.color}" stroke-width="2" stroke-linecap="round" />
      `;
    } else if (stat.icon === 'box') {
      svg += `
        <rect x="${x + 20}" y="${y + 20}" width="10" height="10" rx="1" stroke="${stat.color}" stroke-width="2" fill="none" />
      `;
    } else {
      svg += `
        <circle cx="${x + 25}" cy="${y + 25}" r="7" fill="${stat.color}" />
      `;
    }
    
    // Title
    svg += `
      <text x="${x + 50}" y="${y + 20}" font-family="Arial" font-size="${compact ? 10 : 12}" fill="#6B7280" text-anchor="start">${stat.title}</text>
    `;
    
    // Value
    svg += `
      <text x="${x + 50}" y="${y + 40}" font-family="Arial" font-size="${compact ? 14 : 18}" font-weight="bold" fill="#111827" text-anchor="start">${stat.value}</text>
    `;
    
    // Additional info
    if (stat.change) {
      const changeColor = stat.change.startsWith('+') ? '#10B981' : stat.change.startsWith('-') ? '#EF4444' : '#6B7280';
      svg += `
        <text x="${x + 50}" y="${y + 60}" font-family="Arial" font-size="${compact ? 9 : 11}" fill="${changeColor}" text-anchor="start">${stat.change}</text>
      `;
    }
  });
  
  // Add shadow filter
  svg += `
    <defs>
      <filter id="shadow" x="-5%" y="-5%" width="110%" height="110%">
        <feDropShadow dx="0" dy="1" stdDeviation="1" flood-opacity="0.08"/>
      </filter>
    </defs>
  `;
  
  svg += `</svg>`;
  return svg;
}

// Create a horizontal stacked bar chart for customer shipments
export function createCustomerShipmentChart(data, width, height, colors = ["#3b82f6", "#10b981", "#f59e0b", "#ef4444"], compact = false) {
  let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">`;
  
  // Enhanced background with subtle gradient
  svg += `
    <defs>
      <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
        <stop offset="0%" stop-color="#f9fafb" />
        <stop offset="100%" stop-color="#f3f4f6" />
      </linearGradient>
    </defs>
    <rect x="0" y="0" width="${width}" height="${height}" fill="url(#bg-gradient)" rx="4" />
  `;
  
  // Adjust margins based on compact mode - increased left margin for customer names
  const margins = compact ? 
    { top: 15, right: 15, bottom: 15, left: 100 } : 
    { top: 25, right: 25, bottom: 25, left: 150 };
  
  // Chart dimensions
  const chartWidth = width - margins.left - margins.right;
  const chartHeight = height - margins.top - margins.bottom;
  
  // Calculate max value
  const maxValue = Math.max(...data.map(item => item.count));
  
  // Calculate bar height
  const barHeight = Math.min(30, (chartHeight / data.length) - 8);
  
  // Draw data
  data.forEach((item, index) => {
    const y = margins.top + index * (barHeight + 8);
    const barWidth = (item.count / maxValue) * chartWidth;
    const color = colors[index % colors.length];
    
    // Add gradient
    svg += `
      <defs>
        <linearGradient id="customer-gradient-${index}" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stop-color="${color}" />
          <stop offset="100%" stop-color="${colorLuminance(color, 0.2)}" />
        </linearGradient>
      </defs>
    `;
    
    // Add bar with shadow effect
    svg += `
      <rect x="${margins.left}" y="${y}" width="${barWidth}" height="${barHeight}" rx="4" fill="url(#customer-gradient-${index})" 
        filter="url(#barShadow)" />
    `;
    
    // Add customer name - with better overflow handling
    const customerName = item.customer.length > 20 ? item.customer.substring(0, 18) + '...' : item.customer;
    svg += `<text x="${margins.left - 8}" y="${y + barHeight/2 + 5}" font-family="Arial" font-size="${compact ? 10 : 12}" 
      fill="#374151" text-anchor="end" dominant-baseline="middle" title="${item.customer}">${customerName}</text>`;
    
    // Add count
    svg += `<text x="${margins.left + barWidth + 8}" y="${y + barHeight/2 + 5}" font-family="Arial" font-size="${compact ? 10 : 12}" 
      fill="#4b5563" dominant-baseline="middle">${item.count}</text>`;
    
    // Add tooltip with full customer name
    svg += `<title>${item.customer}: ${item.count} shipments</title>`;
  });
  
  // Add shadow filter for bars
  svg += `
    <defs>
      <filter id="barShadow" x="-5%" y="-10%" width="110%" height="120%">
        <feDropShadow dx="0" dy="1" stdDeviation="1" flood-opacity="0.1"/>
      </filter>
    </defs>
  `;
  
  svg += "</svg>";
  return svg;
}

// Utility function to adjust color luminance (brighten/darken)
function colorLuminance(hex, lum) {
  // Validate hex string
  hex = String(hex).replace(/[^0-9a-f]/gi, '');
  if (hex.length < 6) {
    hex = hex[0]+hex[0]+hex[1]+hex[1]+hex[2]+hex[2];
  }
  lum = lum || 0;

  // Convert to decimal and adjust luminance
  let rgb = "#", c, i;
  for (i = 0; i < 3; i++) {
    c = parseInt(hex.substr(i*2,2), 16);
    c = Math.round(Math.min(Math.max(0, c + (c * lum)), 255)).toString(16);
    rgb += ("00"+c).substr(c.length);
  }

  return rgb;
}