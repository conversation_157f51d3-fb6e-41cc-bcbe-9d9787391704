<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Sheet</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fa-solid fa-table"></i>
                </div>
                <div class="document-controls">
                    <input type="text" class="sheet-title" value="Untitled spreadsheet">
                    <div class="header-buttons">
                        <button class="header-btn" data-tooltip="Favorite"><i class="fa-regular fa-star"></i></button>
                        <button class="header-btn" data-tooltip="Move to folder"><i class="fa-regular fa-folder"></i></button>
                        <button class="header-btn" data-tooltip="Auto-saved"><i class="fa-solid fa-cloud-arrow-up"></i> Saved</button>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <button class="share-btn"><i class="fa-solid fa-share-nodes"></i> Share</button>
                <div class="user-avatar">
                    <img src="../images/avatars/user1.png" alt="User" onerror="this.src='https://ui-avatars.com/api/?name=User&background=random'">
                </div>
            </div>
        </header>
        
        <div class="toolbar">
            <div class="toolbar-group">
                <button class="toolbar-btn" data-action="undo" data-tooltip="Undo"><i class="fa-solid fa-undo"></i></button>
                <button class="toolbar-btn" data-action="redo" data-tooltip="Redo"><i class="fa-solid fa-redo"></i></button>
                <button class="toolbar-btn" data-action="print" data-tooltip="Print"><i class="fa-solid fa-print"></i></button>
                <button class="toolbar-btn" data-action="spell-check" data-tooltip="Spell check"><i class="fa-solid fa-spell-check"></i></button>
            </div>
            <div class="toolbar-group">
                <select class="format-select" data-action="format">
                    <option>Normal text</option>
                    <option>Title</option>
                    <option>Subtitle</option>
                    <option>Heading 1</option>
                    <option>Heading 2</option>
                </select>
                <button class="toolbar-btn" data-action="bold" data-tooltip="Bold"><i class="fa-solid fa-bold"></i></button>
                <button class="toolbar-btn" data-action="italic" data-tooltip="Italic"><i class="fa-solid fa-italic"></i></button>
                <button class="toolbar-btn" data-action="underline" data-tooltip="Underline"><i class="fa-solid fa-underline"></i></button>
                <button class="toolbar-btn" data-action="strikethrough" data-tooltip="Strikethrough"><i class="fa-solid fa-strikethrough"></i></button>
            </div>
            <div class="toolbar-group">
                <button class="toolbar-btn" data-action="left" data-tooltip="Align left"><i class="fa-solid fa-align-left"></i></button>
                <button class="toolbar-btn" data-action="center" data-tooltip="Align center"><i class="fa-solid fa-align-center"></i></button>
                <button class="toolbar-btn" data-action="right" data-tooltip="Align right"><i class="fa-solid fa-align-right"></i></button>
            </div>
            <div class="toolbar-group">
                <button class="toolbar-btn" data-action="fill-color" data-tooltip="Fill color"><i class="fa-solid fa-fill-drip"></i></button>
                <button class="toolbar-btn" data-action="text-color" data-tooltip="Text color"><i class="fa-solid fa-palette"></i></button>
                <button class="toolbar-btn" data-action="borders" data-tooltip="Borders"><i class="fa-solid fa-border-all"></i></button>
                <button class="toolbar-btn" data-action="functions" data-tooltip="Functions"><i class="fa-solid fa-function"></i></button>
            </div>
            <div class="toolbar-group">
                <button class="toolbar-btn" id="db-import-btn" data-tooltip="Import from IndexedDB"><i class="fa-solid fa-database"></i></button>
                <button class="toolbar-btn" data-action="save" data-tooltip="Save spreadsheet"><i class="fa-solid fa-save"></i></button>
                <button class="toolbar-btn" data-action="download" data-tooltip="Download as CSV"><i class="fa-solid fa-download"></i></button>
            </div>
        </div>
        
        <div class="sheet-tabs">
            <div class="tab active">Sheet1</div>
            <div class="tab">Sheet2</div>
            <div class="tab add-sheet"><i class="fa-solid fa-plus"></i></div>
        </div>
        
        <div class="spreadsheet-container">
            <div class="column-headers" id="column-headers"></div>
            <div class="row-headers" id="row-headers"></div>
            <div class="spreadsheet" id="spreadsheet"></div>
        </div>
        
        <div class="formula-bar">
            <div class="cell-reference">A1</div>
            <div class="formula-input-container">
                <span class="formula-fx">fx</span>
                <input type="text" class="formula-input" placeholder="Formula">
            </div>
        </div>
        
        <footer class="app-footer">
            <div class="footer-left">
                <button class="footer-btn"><i class="fa-solid fa-plus"></i> Sheet</button>
            </div>
            <div class="footer-right">
                <span class="sheet-info">100% <i class="fa-solid fa-angle-down"></i></span>
                <button class="footer-btn" data-tooltip="Help"><i class="fa-solid fa-question-circle"></i></button>
            </div>
        </footer>
    </div>
    
    <!-- Database Import Modal -->
    <div class="modal" id="db-import-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Import from IndexedDB</h2>
                <button class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <div class="db-selection">
                    <label for="db-name">Database Name:</label>
                    <select id="db-name" class="db-select">
                        <option value="">Select a database...</option>
                    </select>
                </div>
                <div class="store-selection">
                    <label for="store-name">Object Store:</label>
                    <select id="store-name" class="db-select" disabled>
                        <option value="">Select a store...</option>
                    </select>
                </div>
                <div class="import-options">
                    <label>Import Options:</label>
                    <div class="option">
                        <input type="checkbox" id="headers-included" checked>
                        <label for="headers-included">Include field names as headers</label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="clear-data" checked>
                        <label for="clear-data">Clear current data before import</label>
                    </div>
                </div>
                <div class="import-preview">
                    <h3>Preview:</h3>
                    <div class="preview-container">
                        <p class="no-preview">Select a database and store to preview data</p>
                        <table class="preview-table" style="display: none;"></table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="import-btn" class="primary-btn" disabled>Import Data</button>
                <button class="cancel-btn">Cancel</button>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html> 