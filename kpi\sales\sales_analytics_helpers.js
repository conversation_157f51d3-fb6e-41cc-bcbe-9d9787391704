// Helper functions for sales analytics charts
export class SalesAnalyticsHelpers {
  constructor(parent) {
    this.parent = parent; // Reference to parent SalesAnalyticsComponent
    this.charts = {}; // Container for charts
  }
  
  // 1. 6-Month Sales, Cost, and Profit Comparison (grouped bar)
  renderSalesCostProfitChart(filteredOrders = null) {
    const chartElement = document.getElementById('sales-cost-profit-chart');
    
    // Check if chart element exists in DOM
    if (!chartElement) {
      console.error('Chart element not found: sales-cost-profit-chart');
      return;
    }
    
    const ordersToUse = filteredOrders || this.parent.filteredSalesOrders || [];
    
    // Check if we have data
    if (!ordersToUse.length) {
      this.showEmptyChart('sales-cost-profit-chart', 'No data available for Sales, Cost, and Profit comparison');
      return;
    }
    
    console.log(`Rendering sales cost profit chart with ${ordersToUse.length} orders`);
    
    // Get the current time range filter
    const timeRange = this.parent.chartTimeRanges.salesCostProfit || '7d';
    
    // Determine period breakdown based on time range
    let periodType, periodCount, dateFormatter;
    if (timeRange === '7d') {
      // Daily breakdown for last 7 days
      periodType = 'daily';
      periodCount = 7;
      dateFormatter = (date) => date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    } else if (timeRange === '1m') {
      // Weekly breakdown for last month
      periodType = 'weekly';
      periodCount = 4; // Approximately 4 weeks in a month
      dateFormatter = (date) => {
        const endDate = new Date(date);
        endDate.setDate(endDate.getDate() + 6);
        return `${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`;
      };
    } else {
      // Monthly breakdown for last year
      periodType = 'monthly';
      periodCount = 6; // Show last 6 months
      dateFormatter = (date) => date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
    }
    
    // Prepare date ranges for the periods
    const now = new Date();
    const periods = [];
    
    if (periodType === 'daily') {
      // Last N days
      for (let i = periodCount - 1; i >= 0; i--) {
        const periodDate = new Date(now);
        periodDate.setDate(now.getDate() - i);
        periodDate.setHours(0, 0, 0, 0);
        
        const nextDate = new Date(periodDate);
        nextDate.setDate(periodDate.getDate() + 1);
        
        periods.push({
          startDate: periodDate,
          endDate: nextDate,
          label: dateFormatter(periodDate),
          revenue: 0,
          cost: 0,
          profit: 0
        });
      }
    } else if (periodType === 'weekly') {
      // Last N weeks
      for (let i = periodCount - 1; i >= 0; i--) {
        const periodDate = new Date(now);
        periodDate.setDate(now.getDate() - (i * 7 + 6));
        periodDate.setHours(0, 0, 0, 0);
        
        const nextDate = new Date(periodDate);
        nextDate.setDate(periodDate.getDate() + 7);
        
        periods.push({
          startDate: periodDate,
          endDate: nextDate,
          label: dateFormatter(periodDate),
          revenue: 0,
          cost: 0,
          profit: 0
        });
      }
    } else {
      // Last N months
      for (let i = periodCount - 1; i >= 0; i--) {
        const periodDate = new Date(now);
        periodDate.setMonth(now.getMonth() - i);
        periodDate.setDate(1);
        periodDate.setHours(0, 0, 0, 0);
        
        const nextDate = new Date(periodDate);
        nextDate.setMonth(periodDate.getMonth() + 1);
        
        periods.push({
          startDate: periodDate,
          endDate: nextDate,
          label: dateFormatter(periodDate),
          revenue: 0,
          cost: 0,
          profit: 0
        });
      }
    }
    
    // Aggregate order data into periods
    ordersToUse.forEach(order => {
      // Parse order date
      let orderDate;
      try {
        orderDate = new Date(order.orderDate || order.date);
        if (isNaN(orderDate.getTime())) {
          return; // Skip invalid dates
        }
      } catch (e) {
        return; // Skip orders with date parsing errors
      }
      
      // Find which period this order belongs to
      const periodIndex = periods.findIndex(period => 
        orderDate >= period.startDate && orderDate < period.endDate
      );
      
      if (periodIndex >= 0) {
        const orderTotal = parseFloat(order.orderTotal || 0);
        
        // Add to period totals
        periods[periodIndex].revenue += orderTotal;
        
        // Calculate approximate cost and profit
        // In a real app, this would come from actual cost data
        const costRate = 0.65 + (Math.random() * 0.1); // 65-75% cost rate for realistic margins
        const cost = orderTotal * costRate;
        const profit = orderTotal - cost;
        
        periods[periodIndex].cost += cost;
        periods[periodIndex].profit += profit;
      }
    });
    
    // Extract data for chart
    const labels = periods.map(p => p.label);
    const revenueData = periods.map(p => p.revenue);
    const costData = periods.map(p => p.cost);
    const profitData = periods.map(p => p.profit);
    
    // Create chart
    const options = {
      series: [
        {
          name: 'Revenue',
          data: revenueData
        },
        {
          name: 'Cost',
          data: costData
        },
        {
          name: 'Profit',
          data: profitData
        }
      ],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'Inter, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 300
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '70%',
          borderRadius: 4,
          dataLabels: {
            position: 'top',
          },
        }
      },
      dataLabels: {
        enabled: false
      },
      colors: ['#3b82f6', '#ef4444', '#10b981'],
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: labels,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      yaxis: {
        title: {
          text: 'Amount ($)',
          style: {
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
          }
        },
        labels: {
          formatter: (val) => {
            // Format large numbers with K/M suffixes
            if (val >= 1000000) {
              return this.parent.currencyFormatter.format(val / 1000000) + 'M';
            } else if (val >= 1000) {
              return this.parent.currencyFormatter.format(val / 1000) + 'K';
            }
            return this.parent.currencyFormatter.format(val);
          }
        }
      },
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: (val) => {
            return this.parent.currencyFormatter.format(val);
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesCostProfit) {
      try {
        this.charts.salesCostProfit.destroy();
      } catch (e) {
        console.warn('Error destroying existing chart:', e);
      }
    }
    
    try {
      // Create new chart
      this.charts.salesCostProfit = new ApexCharts(chartElement, options);
      this.charts.salesCostProfit.render();
    } catch (error) {
      console.error('Error rendering Sales, Cost, and Profit chart:', error);
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p>
        </div>
      `;
    }
  }
  
  // 2. Sales by Region (horizontal bar)
  renderSalesByRegionChart(filteredOrders = null) {
    const chartElement = document.getElementById('sales-by-region-chart');
    
    // Check if chart element exists in DOM
    if (!chartElement) {
      console.error('Chart element not found: sales-by-region-chart');
      return;
    }
    
    const ordersToUse = filteredOrders || this.parent.filteredSalesOrders || [];
    
    // Check if we have data
    if (!ordersToUse.length) {
      this.showEmptyChart('sales-by-region-chart', 'No data available for Sales by Region');
      return;
    }
    
    console.log(`Rendering sales by region chart with ${ordersToUse.length} orders`);
    
    // Important country/region keywords to detect in data fields
    const countryKeywords = {
      'North America': ['USA', 'United States', 'U.S.', 'US', 'Canada', 'Mexico', 'America', 'North America'],
      'Europe': ['UK', 'Great Britain', 'United Kingdom', 'England', 'Germany', 'France', 'Italy', 'Spain', 'Netherlands', 'Sweden', 'Europe', 'EU'],
      'Asia': ['China', 'Japan', 'India', 'Singapore', 'Hong Kong', 'Korea', 'Thailand', 'Asia', 'APAC', 'Asia Pacific'],
      'Australia & Pacific': ['Australia', 'New Zealand', 'Pacific', 'Oceania'],
      'South America': ['Brazil', 'Argentina', 'Chile', 'Colombia', 'Peru', 'South America', 'Latin America'],
      'Middle East & Africa': ['UAE', 'Dubai', 'Saudi', 'Egypt', 'Israel', 'South Africa', 'Middle East', 'Africa', 'ME', 'MEA']
    };
      
    // Group data by region
    const regionData = {};
    
    // First, initialize all regions with zero to ensure they all appear
    Object.keys(countryKeywords).forEach(region => {
      regionData[region] = 0;
    });
    
    // Add "Other" category
    regionData['Other'] = 0;
    
    // Extract regional data from orders
    ordersToUse.forEach(order => {
      // Get all possible sources of country/region data
      const location = String(order.location || '');
      const branch = String(order.branch || '');
      const shipToAddress = String(order.shipToAddress || '');
      const customerID = String(order.customerID || '');
      const customerName = String(order.customerName || '');
      
      // Try to determine region from various order fields
      let region = null;
      
      // Helper to check if text contains any keywords for a region
      const matchesRegion = (text) => {
        for (const [regionName, keywords] of Object.entries(countryKeywords)) {
          if (keywords.some(keyword => text.toLowerCase().includes(keyword.toLowerCase()))) {
            return regionName;
          }
        }
        return null;
      };
      
      // Try each field in order of reliability
      const fieldsToCheck = [
        location,       // Location field is most reliable
        branch,         // Branch may contain region info
        shipToAddress,  // Shipping address may contain country
        customerName    // Customer name sometimes includes region
      ];
      
      // Check each field until we find a match
      for (const field of fieldsToCheck) {
        if (field && field.length > 1) {
          const matchedRegion = matchesRegion(field);
          if (matchedRegion) {
            region = matchedRegion;
            break;
          }
        }
      }
      
      // If region still not found, assign based on customer ID for consistency
      if (!region && customerID) {
        // Use a hash of the customer ID for consistent assignment
        const hash = customerID.split('').reduce((sum, char) => sum + char.charCodeAt(0), 0);
        const regions = Object.keys(countryKeywords);
        region = regions[hash % regions.length];
      }
      
      // If all detection methods fail, use "Other"
      if (!region) {
        region = 'Other';
      }
      
      // Add sales amount to the region
      const orderTotal = parseFloat(order.orderTotal || 0);
      regionData[region] += orderTotal;
    });
    
    // Convert to array and sort by sales amount (descending)
    const regions = Object.entries(regionData)
      .filter(([_, sales]) => sales > 0) // Only include regions with sales
      .map(([name, sales]) => ({ name, sales }))
      .sort((a, b) => b.sales - a.sales);
    
    // Create chart  
    const options = {
      series: [{
        name: 'Sales',
        data: regions.map(r => r.sales)
      }],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'Inter, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 500
        }
      },
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          barHeight: '65%',
          distributed: true, // Enable different colors for each bar
          dataLabels: {
            position: 'bottom',
          },
        }
      },
      // Use different colors for each region
      colors: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#f43f5e', '#64748b'],
      dataLabels: {
        enabled: true,
        formatter: (val) => {
          if (val >= 1000000) {
            return this.parent.currencyFormatter.format(val / 1000000) + 'M';
          } else if (val >= 1000) {
            return this.parent.currencyFormatter.format(val / 1000) + 'K';
          }
          return this.parent.currencyFormatter.format(val);
        },
        offsetX: 6,
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif',
          fontWeight: 'bold',
          colors: ['#111827']
        },
      },
      xaxis: {
        categories: regions.map(r => r.name),
        labels: {
          formatter: (val) => {
            if (typeof val === 'number') {
              if (val >= 1000000) {
                return this.parent.currencyFormatter.format(val / 1000000) + 'M';
              } else if (val >= 1000) {
                return this.parent.currencyFormatter.format(val / 1000) + 'K';
              }
              return this.parent.currencyFormatter.format(val);
            }
            return val;
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: '500',
          }
        }
      },
      tooltip: {
        y: {
          formatter: (val) => {
            return this.parent.currencyFormatter.format(val);
          }
        },
        custom: ({seriesIndex, dataPointIndex, w}) => {
          const region = regions[dataPointIndex];
          const value = region.sales;
          const percentage = (value / regions.reduce((total, r) => total + r.sales, 0) * 100).toFixed(1);
          
          return `
            <div class="p-2 bg-white dark:bg-gray-800 shadow-md rounded-md">
              <div class="text-sm font-semibold text-gray-800 dark:text-gray-200">${region.name}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Sales: ${this.parent.currencyFormatter.format(value)}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Share: ${percentage}% of total
              </div>
            </div>
          `;
        }
      },
      legend: {
        show: false
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesByRegion) {
      try {
        this.charts.salesByRegion.destroy();
      } catch (error) {
        console.warn('Error destroying Sales by Region chart:', error);
      }
    }
    
    try {
      // Create new chart
      this.charts.salesByRegion = new ApexCharts(chartElement, options);
      this.charts.salesByRegion.render();
    } catch (error) {
      console.error('Error rendering Sales by Region chart:', error);
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p>
        </div>
      `;
    }
  }
  
  // 3. Profit Margin by Product (horizontal bar)
  renderProfitMarginByProductChart(filteredOrders = null) {
    const chartElement = document.getElementById('profit-margin-chart');
    
    // Check if chart element exists in DOM
    if (!chartElement) {
      console.error('Chart element not found: profit-margin-chart');
      return;
    }
    
    const ordersToUse = filteredOrders || this.parent.filteredSalesOrders || [];
    
    // Check if we have data
    if (!ordersToUse.length) {
      this.showEmptyChart('profit-margin-chart', 'No data available for Profit Margin by Product');
      return;
    }
    
    console.log(`Rendering profit margin by product chart with ${ordersToUse.length} orders`);
    
    // Group data by product and calculate profit margin
    const productData = {};
    
    ordersToUse.forEach(order => {
      // Check if order has details
      if (!order.details || !Array.isArray(order.details) || order.details.length === 0) {
        return; // Skip orders without line items
      }
      
      order.details.forEach(item => {
        const productId = item.inventoryID || '';
        if (!productId) return; // Skip items with no product ID
        
        const productName = item.description || productId;
        
        // Parse numerical values safely
        const quantity = parseFloat(item.quantity || 1);
        const unitPrice = parseFloat(item.unitPrice || item.price || 0);
        const unitCost = parseFloat(item.unitCost || 0);
        
        // If we don't have unit cost, estimate it (in real app, should come from inventory)
        // Use a realistic markup based on item type if possible
        let cost = unitCost;
        if (cost <= 0) {
          // Estimate cost based on product ID or description - this is just for demo
          // Use a realistic cost factor (30-80% of price)
          const costFactor = 0.3 + (Math.random() * 0.5);
          cost = unitPrice * costFactor;
        }
        
        const revenue = unitPrice * quantity;
        const totalCost = cost * quantity;
        const profit = revenue - totalCost;
        
        // Skip items with zero or negative revenue
        if (revenue <= 0) return;
        
        const profitMargin = (profit / revenue) * 100;
        
        if (!productData[productId]) {
          productData[productId] = {
            id: productId,
            name: productName,
            totalRevenue: 0,
            totalCost: 0,
            totalProfit: 0,
            profitMargin: 0,
            orderCount: 0,
            unitsSold: 0
          };
        }
        
        // Accumulate stats
        productData[productId].totalRevenue += revenue;
        productData[productId].totalCost += totalCost;
        productData[productId].totalProfit += profit;
        productData[productId].unitsSold += quantity;
        productData[productId].orderCount++;
      });
    });
    
    // Calculate profit margins for products with sufficient data
    // Filter out products with very low sales that would skew results
    const significantProducts = Object.values(productData)
      .filter(product => product.totalRevenue > 100 && product.orderCount >= 2);
    
    if (significantProducts.length === 0) {
      this.showEmptyChart('profit-margin-chart', 'Insufficient data for product profit margin analysis');
      return;
    }
    
    // Calculate final profit margins and prepare for display
    significantProducts.forEach(product => {
      product.profitMargin = (product.totalProfit / product.totalRevenue) * 100;
    });
    
    // Sort by profit margin and take top 10
    const products = significantProducts
      .sort((a, b) => b.profitMargin - a.profitMargin)
      .slice(0, 10);
    
    // Truncate product names for display
    const processedProducts = products.map(product => ({
      ...product,
      // Truncate name if too long
      displayName: product.name.length > 25 ? product.name.substring(0, 22) + '...' : product.name
    }));
      
    // Create chart
    const options = {
      series: [{
        name: 'Profit Margin',
        data: processedProducts.map(p => parseFloat(p.profitMargin.toFixed(1)))
      }],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'Inter, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 500
        }
      },
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          dataLabels: {
            position: 'bottom',
          },
          barHeight: '80%',
          distributed: true
        }
      },
      colors: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0', '#d1fae5'],
      dataLabels: {
        enabled: true,
        formatter: (val) => {
          return val.toFixed(1) + '%';
        },
        offsetX: 8,
        style: {
          fontFamily: 'Inter, sans-serif',
          colors: ['#111827'],
          fontWeight: 600
        },
      },
      xaxis: {
        categories: processedProducts.map(p => p.displayName),
        labels: {
          formatter: (val) => {
            return val.toFixed(1) + '%';
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      tooltip: {
        shared: false,
        y: {
          formatter: (val) => {
            return val.toFixed(1) + '%';
          }
        },
        custom: ({series, seriesIndex, dataPointIndex, w}) => {
          const product = processedProducts[dataPointIndex];
          return `<div class="p-2 bg-white dark:bg-gray-800 shadow-md rounded-md">
            <div class="text-sm font-medium text-gray-800 dark:text-gray-200">${product.name}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Profit Margin: ${product.profitMargin.toFixed(1)}%</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Revenue: ${this.parent.currencyFormatter.format(product.totalRevenue)}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Profit: ${this.parent.currencyFormatter.format(product.totalProfit)}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">Units Sold: ${product.unitsSold}</div>
          </div>`;
        }
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.profitMargin) {
      try {
        this.charts.profitMargin.destroy();
      } catch (error) {
        console.warn('Error destroying Profit Margin chart:', error);
      }
    }
    
    try {
      // Create new chart
      this.charts.profitMargin = new ApexCharts(chartElement, options);
      this.charts.profitMargin.render();
    } catch (error) {
      console.error('Error rendering Profit Margin chart:', error);
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p>
        </div>
      `;
    }
  }
  
  // 4. Sales by Customer Segment (vertical bar)
  renderSalesByCustomerSegmentChart(filteredOrders = null) {
    const chartElement = document.getElementById('customer-segment-chart');
    
    // Check if chart element exists in DOM
    if (!chartElement) {
      console.error('Chart element not found: customer-segment-chart');
      return;
    }
    
    const ordersToUse = filteredOrders || this.parent.filteredSalesOrders || [];
    
    // Check if we have data
    if (!ordersToUse.length) {
      this.showEmptyChart('customer-segment-chart', 'No data available for Customer Segment data');
      return;
    }
    
    console.log(`Rendering sales by customer segment chart with ${ordersToUse.length} orders`);
    
    // Define customer segments - in a real app, this would come from a customer database
    // For demo, we'll create segments based on customer ID
    const segments = ['Enterprise', 'SMB', 'Retail', 'Government', 'Education'];
    
    // Group data by segment
    const segmentData = {};
    segments.forEach(segment => {
      segmentData[segment] = 0;
    });
    
    ordersToUse.forEach(order => {
      const customerId = order.customerID || '';
      const orderTotal = parseFloat(order.orderTotal || 0);
      
      // Assign segment based on customer ID
      let segment;
      
      // In a real app, this would be based on actual customer data
      // For demo, we'll use hash of customer ID to assign a segment
      if (customerId) {
        const hash = customerId.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
        segment = segments[hash % segments.length];
      } else {
        segment = 'Retail'; // Default segment
      }
      
      segmentData[segment] += orderTotal;
    });
    
    // Convert to array and sort
    const segmentArray = Object.entries(segmentData)
      .map(([name, sales]) => ({ name, sales }))
      .sort((a, b) => b.sales - a.sales);
      
    // Create chart
    const options = {
      series: [{
        name: 'Sales',
        data: segmentArray.map(s => s.sales)
      }],
      chart: {
        type: 'bar',
        height: 350,
        fontFamily: 'Inter, sans-serif',
        toolbar: {
          show: false
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 4,
          dataLabels: {
            position: 'top',
          },
        }
      },
      colors: ['#8b5cf6'],
      dataLabels: {
        enabled: true,
        formatter: (val) => {
          return this.parent.currencyFormatter.format(val);
        },
        offsetY: -20,
        style: {
          fontFamily: 'Inter, sans-serif',
          colors: ['#111827'],
          fontSize: '12px'
        },
      },
      xaxis: {
        categories: segmentArray.map(s => s.name),
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      yaxis: {
        labels: {
          formatter: (val) => {
            return this.parent.currencyFormatter.format(val);
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      tooltip: {
        y: {
          formatter: (val) => {
            return this.parent.currencyFormatter.format(val);
          }
        }
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.customerSegment) {
      this.charts.customerSegment.destroy();
    }
    
    try {
      // Create new chart
      this.charts.customerSegment = new ApexCharts(chartElement, options);
      this.charts.customerSegment.render();
    } catch (error) {
      console.error('Error rendering Customer Segment chart:', error);
      chartElement.innerHTML = `
        <div class="flex items-center justify-center h-full">
          <p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p>
        </div>
      `;
    }
  }
  
  // Utility to show empty chart message
  showEmptyChart(elementId, message) {
    const chartElement = document.getElementById(elementId);
    if (!chartElement) {
      console.error(`Chart element not found: ${elementId}`);
      return;
    }
    
    // Destroy existing chart if it exists
    if (this.charts[elementId]) {
      try {
        this.charts[elementId].destroy();
        this.charts[elementId] = null;
      } catch (error) {
        console.warn(`Error destroying chart for ${elementId}:`, error);
      }
    }
    
    // Show empty state message
    chartElement.innerHTML = `
      <div class="flex items-center justify-center h-72">
        <p class="text-center text-gray-500 p-4">${message}</p>
      </div>
    `;
  }
  
  // Destroy all charts
  destroyCharts() {
    Object.entries(this.charts).forEach(([id, chart]) => {
      if (chart) {
        try {
          chart.destroy();
        } catch (e) {
          console.warn(`Error destroying chart ${id}:`, e);
        }
      }
    });
    
    this.charts = {};
  }
} 