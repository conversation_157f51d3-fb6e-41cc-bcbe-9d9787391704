// notifications.js - Improved notification system with duplicate prevention
let instance = null;

export class NotificationSystem {
  constructor(parent = document.body) {
    // Implement singleton pattern to prevent duplicate instances
    if (instance) {
      return instance;
    }
    
    this.notifications = []
    this.activeNotificationMessages = new Map() // Track active notifications by message
    this.container = null
    this.createContainer(parent)
    this.notificationPopup = null
    
    // SheetDB API credentials
    this.SHEETDB_API_URL = "https://sheetdb.io/api/v1/lcon68jvxh23y"
    this.SHEETDB_API_TOKEN = "Bearer w5icozbrzltd65ilssma8sk2al9i9zu7xpjkr6cc"
    
    // Store the instance
    instance = this;
  }

  createContainer(parent) {
    // Check if container already exists in the DOM
    const existingContainer = document.getElementById("notificationContainer");
    if (existingContainer) {
      this.container = existingContainer;
      return;
    }
    
    this.container = document.createElement("div")
    this.container.id = "notificationContainer"
    this.container.className = "fixed top-2 right-2 z-50 flex flex-col items-end space-y-1 max-w-xs"
    parent.appendChild(this.container)

    // Add global styles for notifications
    const style = document.createElement('style')
    style.textContent = `
      .notification {
        transform: translateX(0);
        opacity: 1;
        transition: all 0.3s ease-in-out;
      }
      .notification.hiding {
        transform: translateX(100%);
        opacity: 0;
      }
      .notification-count {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #e74c3c;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 11px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
      }
      .admin-notification-popup {
        position: fixed;
        top: 68px;
        right: 16px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        width: 280px;
        z-index: 1000;
        border: 1px solid rgba(0,0,0,0.1);
        overflow: hidden;
        animation: slideIn 0.3s ease-out;
      }
      @keyframes slideIn {
        from { transform: translateY(-20px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
      }
    `
    document.head.appendChild(style)
  }

  addNotification(message, type = "info", duration = 3000) {
    // Check if a notification with the same message and type already exists
    const key = `${type}:${message}`
    
    if (this.activeNotificationMessages.has(key)) {
      // Update existing notification instead of creating a new one
      const existingNotification = this.activeNotificationMessages.get(key)
      
      // Increment count
      let count = existingNotification.dataset.count ? parseInt(existingNotification.dataset.count) : 1
      count++
      existingNotification.dataset.count = count
      
      // Update count badge if it exists
      let countBadge = existingNotification.querySelector('.notification-count')
      if (!countBadge) {
        countBadge = document.createElement('div')
        countBadge.className = 'notification-count'
        existingNotification.appendChild(countBadge)
      }
      countBadge.textContent = count
      
      // Reset the timeout for this notification
      clearTimeout(existingNotification.dataset.timeoutId)
      existingNotification.dataset.timeoutId = setTimeout(() => {
        this.removeNotification(existingNotification)
      }, duration)
      
      return existingNotification
    }
    
    // Create new notification if no duplicate exists
    const notification = this.createNotificationElement(message, type)
    this.container.appendChild(notification)
    this.notifications.push(notification)
    
    // Store in active notifications map
    this.activeNotificationMessages.set(key, notification)
    
    // Set timeout to remove
    const timeoutId = setTimeout(() => {
      this.removeNotification(notification)
    }, duration)
    
    // Store timeout ID for potential resets
    notification.dataset.timeoutId = timeoutId
    notification.dataset.count = '1'

    return notification
  }

  createNotificationElement(message, type) {
    const notification = document.createElement("div")
    notification.className = `
      notification relative py-2 px-3 rounded shadow-md text-white text-sm
      flex items-center ${this.getTypeClass(type)}
    `
    notification.innerHTML = `
      <div class="mr-2 flex-shrink-0">
        ${this.getTypeIcon(type)}
      </div>
      <p class="mr-6 flex-grow">${message}</p>
      <button class="absolute top-1 right-1 text-white hover:text-gray-200">
        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    `

    notification.querySelector("button").addEventListener("click", () => {
      this.removeNotification(notification)
    })

    return notification
  }

  removeNotification(notification) {
    // Remove from active notifications map
    for (const [key, value] of this.activeNotificationMessages.entries()) {
      if (value === notification) {
        this.activeNotificationMessages.delete(key)
        break
      }
    }
    
    // Add hiding class for animation
    notification.classList.add("hiding")
    
    // Clear the timeout to prevent memory leaks
    if (notification.dataset.timeoutId) {
      clearTimeout(notification.dataset.timeoutId)
    }
    
    setTimeout(() => {
      if (notification.parentNode === this.container) {
        this.container.removeChild(notification)
      }
      this.notifications = this.notifications.filter((n) => n !== notification)
    }, 300)
  }

  getTypeClass(type) {
    switch (type) {
      case "success":
        return "bg-green-600"
      case "error":
      case "danger":
        return "bg-red-600"
      case "warning":
        return "bg-yellow-600"
      default:
        return "bg-blue-600"
    }
  }

  getTypeIcon(type) {
    const iconClass = "w-4 h-4"
    switch (type) {
      case "success":
        return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`
      case "error":
      case "danger":
        return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>`
      case "warning":
        return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path></svg>`
      default:
        return `<svg class="${iconClass}" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`
    }
  }

  clearAll() {
    this.notifications.forEach((notification) => {
      this.removeNotification(notification)
    })
    this.activeNotificationMessages.clear()
  }
  
  // Add initialization method
  async init() {
    console.log("Initializing notification system");
    return Promise.resolve(true);
  }
  
  // Show admin notification popup when notification icon is clicked
  showNotificationPopup() {
    console.log("Showing admin notification popup");
    
    // Remove existing popup if it exists
    if (this.notificationPopup) {
      document.body.removeChild(this.notificationPopup);
      this.notificationPopup = null;
      return;
    }
    
    // Get current user info
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(['user'], (result) => {
        if (result && result.user) {
          this.fetchAdminMessage(result.user);
        } else {
          this.addNotification("User data not found", "error");
        }
      });
    } else {
      this.addNotification("Chrome storage not available", "error");
    }
  }
  
  // Fetch admin message from SheetDB
  async fetchAdminMessage(userData) {
    try {
      console.log("Fetching admin message from SheetDB");
      
      const response = await fetch(this.SHEETDB_API_URL, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": this.SHEETDB_API_TOKEN
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch admin message: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Find admin message in the data
      // Assuming the first row contains the admin message
      if (data && data.length > 0) {
        const adminData = data[0];
        
        // Get admin message data
        const adminName = adminData["Adminmessage-Name"] || "Envent Admin";
        const adminMessage = adminData["Adminmessage-Messge"] || "Hello world!";
        
        // Get avatar ID
        let avatarId = adminData["Adminmessage-Avatar"] || "1";
        
        // Normalize avatar path
        let avatarPath = "";
        if (typeof avatarId === 'number' || !isNaN(parseInt(avatarId))) {
          avatarPath = `images/avatars/avatar_${avatarId}.jpg`;
        } else if (avatarId.includes('avatar_')) {
          if (avatarId.includes('/')) {
            avatarPath = avatarId;
          } else {
            avatarPath = `images/avatars/${avatarId}`;
          }
        } else if (avatarId.includes('/')) {
          avatarPath = avatarId;
        } else {
          avatarPath = `images/avatars/avatar_${avatarId}.jpg`;
        }
        
        console.log("Admin message found:", { adminName, adminMessage, avatarPath });
        
        // Create and show the popup
        this.createAdminNotificationPopup(adminName, avatarPath, adminMessage);
      } else {
        throw new Error("No admin message found");
      }
    } catch (error) {
      console.error("Error fetching admin message:", error);
      this.addNotification(`Error fetching admin message: ${error.message}`, "error");
    }
  }
  
  // Update admin message from SheetDB
  async updateAdminMessage() {
    try {
      console.log("Updating admin message from SheetDB");
      
      // Add loading state to the button
      const updateBtn = this.notificationPopup.querySelector('#updateMessagesBtn');
      const originalBtnText = updateBtn.innerHTML;
      updateBtn.innerHTML = `
        <svg class="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Updating...
      `;
      updateBtn.disabled = true;
      
      // Fetch latest data
      const response = await fetch(this.SHEETDB_API_URL, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": this.SHEETDB_API_TOKEN
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to update admin message: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Find admin message in the data
      if (data && data.length > 0) {
        const adminData = data[0];
        
        // Get admin message data
        const adminName = adminData["Adminmessage-Name"] || "Envent Admin";
        const adminMessage = adminData["Adminmessage-Messge"] || "Hello world!";
        
        // Get avatar ID
        let avatarId = adminData["Adminmessage-Avatar"] || "1";
        
        // Normalize avatar path
        let avatarPath = "";
        if (typeof avatarId === 'number' || !isNaN(parseInt(avatarId))) {
          avatarPath = `images/avatars/avatar_${avatarId}.jpg`;
        } else if (avatarId.includes('avatar_')) {
          if (avatarId.includes('/')) {
            avatarPath = avatarId;
          } else {
            avatarPath = `images/avatars/${avatarId}`;
          }
        } else if (avatarId.includes('/')) {
          avatarPath = avatarId;
        } else {
          avatarPath = `images/avatars/avatar_${avatarId}.jpg`;
        }
        
        console.log("Updated admin message:", { adminName, adminMessage, avatarPath });
        
        // Update user data in chrome storage
        chrome.storage.local.get(['user'], (result) => {
          if (result && result.user) {
            const updatedUser = { ...result.user };
            updatedUser["Adminmessage-Name"] = adminData["Adminmessage-Name"];
            updatedUser["Adminmessage-Avatar"] = adminData["Adminmessage-Avatar"];
            updatedUser["Adminmessage-Messge"] = adminData["Adminmessage-Messge"];
            
            chrome.storage.local.set({ user: updatedUser }, () => {
              console.log("User data updated with latest admin message");
              
              // Update the existing popup content
              const nameElement = this.notificationPopup.querySelector('.font-medium.text-gray-800');
              const messageElement = this.notificationPopup.querySelector('.text-sm.text-gray-700');
              const timestampElement = this.notificationPopup.querySelector('.text-xs.text-gray-500');
              const avatarElement = this.notificationPopup.querySelector('img');
              
              // Update the content
              if (nameElement) nameElement.textContent = adminName;
              if (messageElement) messageElement.textContent = adminMessage;
              if (timestampElement) timestampElement.textContent = new Date().toLocaleString();
              if (avatarElement) {
                avatarElement.src = avatarPath;
                avatarElement.alt = adminName;
              }
              
              // Restore the button
              updateBtn.innerHTML = originalBtnText;
              updateBtn.disabled = false;
            });
          }
        });
      } else {
        throw new Error("No admin message found");
      }
    } catch (error) {
      console.error("Error updating admin message:", error);
      this.addNotification(`Error updating messages: ${error.message}`, "error");
      
      // Restore the button even on error
      if (this.notificationPopup) {
        const updateBtn = this.notificationPopup.querySelector('#updateMessagesBtn');
        if (updateBtn) {
          updateBtn.innerHTML = `
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Update Messages
          `;
          updateBtn.disabled = false;
        }
      }
    }
  }
  
  createAdminNotificationPopup(adminName, avatarPath, message) {
    // Create popup element
    this.notificationPopup = document.createElement('div');
    this.notificationPopup.className = 'admin-notification-popup';
    
    // Get current timestamp
    const timestamp = new Date().toLocaleString();
    
    // Construct the popup HTML
    this.notificationPopup.innerHTML = `
      <div class="p-0">
        <div class="flex justify-between items-center p-3 border-b border-gray-200">
          <h3 class="text-gray-800 font-medium text-base">Notifications</h3>
          <button id="closePopupBtn" class="text-gray-400 hover:text-gray-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="p-4">
          <div class="flex items-start mb-1">
            <div class="flex-shrink-0 mr-3">
              <img 
                src="${avatarPath}" 
                alt="${adminName}" 
                class="w-10 h-10 rounded-full object-cover"
                onerror="this.onerror=null; this.src='images/avatars/avatar_1.jpg'; console.log('Error loading avatar, using fallback');"
              >
            </div>
            <div>
              <div class="font-medium text-gray-800">${adminName}</div>
              <div class="text-sm text-gray-700">${message}</div>
              <div class="text-xs text-gray-500 mt-1">${timestamp}</div>
            </div>
          </div>
        </div>
        
        <div class="border-t border-gray-200 p-3 flex justify-center">
          <button id="updateMessagesBtn" class="bg-blue-500 hover:bg-blue-600 text-white py-1 px-4 rounded flex items-center justify-center text-sm">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Update Messages
          </button>
        </div>
      </div>
    `;
    
    // Add to DOM
    document.body.appendChild(this.notificationPopup);
    
    // Add close button functionality
    const closeBtn = this.notificationPopup.querySelector('#closePopupBtn');
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(this.notificationPopup);
      this.notificationPopup = null;
    });
    
    // Add update messages button functionality
    const updateBtn = this.notificationPopup.querySelector('#updateMessagesBtn');
    updateBtn.addEventListener('click', () => {
      this.updateAdminMessage();
    });
    
    // Close when clicking outside
    document.addEventListener('click', (e) => {
      if (this.notificationPopup && 
          !this.notificationPopup.contains(e.target) && 
          !e.target.closest('#notificationButton')) {
        document.body.removeChild(this.notificationPopup);
        this.notificationPopup = null;
      }
    }, { once: true });
  }
}

