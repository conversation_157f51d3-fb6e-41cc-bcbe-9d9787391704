// Fullscreen map functionality
document.addEventListener('DOMContentLoaded', initializeFullscreenMap);

// Show loading spinner initially
const loadingContainer = document.getElementById('loadingContainer');

// Initialize the fullscreen map
function initializeFullscreenMap() {
  console.log('Initializing fullscreen map...');
  
  // Apply dark mode if preferred
  const isDarkMode = localStorage.getItem('enventbridge_darkMode') === 'true';
  if (isDarkMode) {
    document.body.classList.add('dark-mode');
  }
  
  // Handle errors with better user feedback
  window.onerror = function(message, source, lineno, colno, error) {
    showError(`Error: ${message}`);
    console.error('Error loading map:', error);
    return true;
  };
  
  // Check if mapboxgl is loaded
  if (!window.mapboxgl) {
    showError('MapboxGL failed to load. Please check your internet connection and try again.');
    return;
  }
  
  try {
    // Get the token from localStorage or use the default one
    const mapboxToken = localStorage.getItem('enventbridge_mapboxToken') || 
                       'pk.eyJ1IjoibWFoZGktZWJhZGkiLCJhIjoiY204YjViYW0xMHAwZTJqcHlxbnp0N2EzbiJ9.SSUsIDq3g5JMy9GYyKQTYg';
    
    // Set the token
    window.mapboxgl.accessToken = mapboxToken;
    
    console.log('Creating map with token:', mapboxToken);
    
    // Create the map instance
    const map = new window.mapboxgl.Map({
      container: 'map',
      style: 'mapbox://styles/mahdi-ebadi/cm8vkgjaz00yd01snc85y0bra',
      center: [-95.7129, 37.0902],
      zoom: 3,
      pitch: 45,
      bearing: -17.6,
      antialias: true
    });
    
    // Make map available globally for other functions
    window.mapInstance = map;
    
    // Add event handler for map errors
    map.on('error', function(e) {
      console.error('Mapbox error:', e);
      showError('Map error occurred. Please try refreshing the page.');
    });
    
    // Load project data from localStorage with better error handling
    let projectData = [];
    try {
      console.log('DEBUGGING: Attempting to load project data from localStorage');
      
      // Check all possible storage keys
      const possibleKeys = ['enventbridge_projectData', 'enventbridge_project_locations', 'projectData', 'projects'];
      let storedData = null;
      
      for (const key of possibleKeys) {
        console.log(`Checking localStorage key: ${key}`);
        const data = localStorage.getItem(key);
        if (data) {
          console.log(`Found data in key: ${key}`);
          storedData = data;
          break;
        }
      }
      
      // Log what we found
      console.log('Found project data in localStorage:', !!storedData);
      
      // Debug what's in localStorage
      console.log('localStorage keys:', Object.keys(localStorage));
      
      if (storedData) {
        try {
          projectData = JSON.parse(storedData);
          console.log(`Successfully loaded ${projectData.length} projects from localStorage`);
          
          // Log first project to help with debugging
          if (projectData.length > 0) {
            console.log('First project sample:', {
              id: projectData[0].id,
              name: projectData[0].projectName,
              coords: [projectData[0].longitude, projectData[0].latitude]
            });
          }
        } catch (parseError) {
          console.error('Error parsing project data JSON:', parseError);
          console.log('Raw data from localStorage:', storedData);
          showError('Invalid project data format. Please try again.');
        }
      } else {
        console.warn('No project data found in localStorage');
        showError('No project data found. Please go back and try again.');
      }
    } catch (error) {
      console.error('Error accessing localStorage:', error);
      showError('Failed to load project data from localStorage.');
    }
    
    // Set up map once it's fully loaded
    map.on('load', function() {
      // Hide loading spinner
      if (loadingContainer) {
        loadingContainer.style.display = 'none';
      }
      
      console.log('Map loaded successfully');
      
      // Add navigation controls
      map.addControl(new mapboxgl.NavigationControl());
      
      // Add fullscreen control
      map.addControl(new mapboxgl.FullscreenControl());
      
      try {
        // Add terrain source if supported by the map style
        if (map.getSource('mapbox-dem')) {
          console.log('Terrain source already exists');
        } else {
          console.log('Adding mapbox-dem terrain source...');
          try {
            // Add terrain source
            map.addSource('mapbox-dem', {
              'type': 'raster-dem',
              'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
              'tileSize': 512,
              'maxzoom': 14
            });
            
            // Set terrain
            map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': 1.5 });
          } catch (terrainError) {
            console.warn('Unable to add terrain source:', terrainError);
            // Continue without terrain
          }
        }
        
        // Try to add 3D buildings if the map style supports it
        try {
          add3DBuildings(map);
        } catch (buildingError) {
          console.warn('Unable to add 3D buildings:', buildingError);
        }
        
        // Add project markers
        if (projectData && projectData.length > 0) {
          console.log(`Adding ${projectData.length} markers to map...`);
          addProjectMarkers(map, projectData);
        } else {
          console.warn('No project data available to add markers');
          document.getElementById('project-list').innerHTML = 
            '<div class="p-3 bg-yellow-100 text-yellow-800 rounded">No project locations available. Please add some locations first.</div>';
        }
      } catch (error) {
        console.error('Error setting up map features:', error);
        // Continue with basic map functionality
      }
    });
    
    // Handle map load error
    map.on('styledata', function(e) {
      if (map.isStyleLoaded()) {
        console.log('Map style loaded successfully');
      }
    });
    
  } catch (error) {
    console.error('Error initializing map:', error);
    showError('Failed to initialize map. Please try again.');
  }
}

// Add 3D buildings to the map
function add3DBuildings(map) {
  try {
    // Skip if map style doesn't support 3D buildings
    if (!map.getLayer('building') && !map.getSource('composite')) {
      console.warn('Map style does not support 3D buildings');
      return;
    }
    
    // Find the first symbol layer
    const layers = map.getStyle().layers;
    const labelLayerId = layers.find(
      (layer) => layer.type === 'symbol' && layer.layout && layer.layout['text-field']
    )?.id;
    
    // Skip if no label layer is found
    if (!labelLayerId) {
      console.warn('No label layer found for 3D buildings');
      return;
    }
    
    // Add 3D building extrusions
    map.addLayer(
      {
        'id': 'add-3d-buildings',
        'source': 'composite',
        'source-layer': 'building',
        'filter': ['==', 'extrude', 'true'],
        'type': 'fill-extrusion',
        'minzoom': 15,
        'paint': {
          'fill-extrusion-color': '#aaa',
          'fill-extrusion-height': [
            'interpolate',
            ['linear'],
            ['zoom'],
            15,
            0,
            15.05,
            ['get', 'height']
          ],
          'fill-extrusion-base': [
            'interpolate',
            ['linear'],
            ['zoom'],
            15,
            0,
            15.05,
            ['get', 'min_height']
          ],
          'fill-extrusion-opacity': 0.6
        }
      },
      labelLayerId
    );
    
    console.log('3D buildings added successfully');
  } catch (error) {
    console.warn('Could not add 3D buildings:', error);
  }
}

// Add project markers to the map
function addProjectMarkers(map, projectData) {
  console.log('DEBUG: Starting to add markers to map', {
    projectDataExists: !!projectData,
    projectDataLength: projectData?.length,
    projectDataIsArray: Array.isArray(projectData)
  });
  
  // Get the project list element
  const projectList = document.getElementById('project-list');
  
  // Check if we have projects
  if (!projectData || projectData.length === 0) {
    if (projectList) {
      projectList.innerHTML = '<div class="text-center py-2">No project data available</div>';
    }
    return;
  }
  
  console.log(`Adding ${projectData.length} project markers to map`);
  
  // Add markers for each project
  projectData.forEach(project => {
    try {
      // Skip if longitude or latitude is missing or invalid
      if (!project.longitude || !project.latitude || 
          isNaN(parseFloat(project.longitude)) || isNaN(parseFloat(project.latitude))) {
        console.warn(`Skipping project ${project.projectName || 'unknown'} due to invalid coordinates:`, 
                     {longitude: project.longitude, latitude: project.latitude});
        return;
      }
      
      // Ensure longitude and latitude are numbers
      const longitude = parseFloat(project.longitude);
      const latitude = parseFloat(project.latitude);
      
      console.log(`Adding marker for project: ${project.projectName} at ${latitude}, ${longitude}`);
      
      // Create marker with color based on gas type
      const markerColor = getMarkerColorByGasType(project.gasType);
      const marker = new mapboxgl.Marker({ color: markerColor })
        .setLngLat([longitude, latitude])
        .addTo(map);
      
      // Create popup with project details
      const popupContent = `
        <div class="popup-content">
          <h3>${project.projectName || 'Unnamed Project'}</h3>
          <p>Customer: ${project.customerName || 'Unknown'}</p>
          ${project.photoUrl ? `<img src="${project.photoUrl}" alt="${project.projectName}">` : ''}
          <div>
            <p><strong>Gas Type:</strong> ${project.gasType || 'Not specified'}</p>
            <p><strong>Install Date:</strong> ${formatDate(project.installDate)}</p>
            ${project.poNumber ? `<p><strong>PO Number:</strong> ${project.poNumber}</p>` : ''}
            ${project.analyzerModel ? `<p><strong>Analyzer Model:</strong> ${project.analyzerModel}</p>` : ''}
          </div>
          ${project.notes ? `<p><strong>Notes:</strong> ${project.notes}</p>` : ''}
        </div>
      `;
      
      const popup = new mapboxgl.Popup({ offset: 25 })
        .setHTML(popupContent);
      
      marker.setPopup(popup);
      
      // Add to list if the element exists
      if (projectList) {
        const listItem = document.createElement('div');
        listItem.className = 'project-item';
        listItem.innerHTML = `<strong>${project.projectName || 'Unnamed Project'}</strong><br>${project.customerName || 'Unknown'}`;
        
        listItem.addEventListener('click', () => {
          map.flyTo({
            center: [longitude, latitude],
            zoom: 14,
            essential: true
          });
          marker.togglePopup();
        });
        
        projectList.appendChild(listItem);
      }
    } catch (projError) {
      console.error(`Error adding marker for project ${project.id || 'unknown'}:`, projError);
    }
  });
}

// Helper function to format date
function formatDate(dateString) {
  if (!dateString) return 'N/A';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (e) {
    return dateString || 'N/A';
  }
}

// Helper function to get marker color by gas type
function getMarkerColorByGasType(gasType) {
  const colors = {
    'Natural Gas': '#4285F4',
    'LPG': '#EA4335',
    'CNG': '#FBBC05',
    'Biogas': '#34A853',
    'Other': '#9334E6'
  };
  
  return colors[gasType] || '#000000';
}

// Show error message to user
function showError(message) {
  if (loadingContainer) {
    loadingContainer.innerHTML = `
      <div style="background: white; padding: 20px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.2); max-width: 80%;">
        <h3 style="color: red; margin-top: 0;">Error</h3>
        <p>${message}</p>
        <button onclick="window.location.reload()" style="background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px;">Reload Page</button>
        <button onclick="window.close()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 10px; margin-left: 8px;">Close Window</button>
      </div>
    `;
  } else {
    alert(message);
  }
} 