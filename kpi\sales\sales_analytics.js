// Sales Analytics component for KPI Dashboard
export class SalesAnalyticsComponent {
  constructor(container) {
    this.container = container;
    
    // Define chart instances
    this.charts = {
      salesTrend: null,
      orderCountTrend: null,
    };  // Added missing closing brace here
    this.timeRange = '7d'; // Default to 7 days
    this.currencyFormatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    });
    
    this.salesOrders = [];
    this.filteredSalesOrders = [];
    this.customers = [];
    this.customerLookup = {};
    
    // Time range filters for individual charts
    this.chartTimeRanges = {
      salesTrend: '7d',
      orderCountTrend: '7d',
      salesByStatus: '7d',
      topProducts: '7d',
      salesVsTarget: '7d',
      profitMargin: '7d',
      unitsByRegion: '7d',
      revenueGrowth: '7d',
      topCustomers: '7d',
      salesConversion: '7d',
      salesCostProfit: '7d',
      salesByRegion: '7d',
      customerSegment: '7d',
      mainKpi: 'monthly'
    };
    
    // Color schemes for charts
    this.chartColors = {
      primary: ['#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe'],
      secondary: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe', '#ede9fe'],
      success: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0', '#d1fae5'],
      danger: ['#ef4444', '#f87171', '#fca5a5', '#fecaca', '#fee2e2'],
      warning: ['#f59e0b', '#fbbf24', '#fcd34d', '#fde68a', '#fef3c7'],
      gray: ['#1f2937', '#4b5563', '#9ca3af', '#d1d5db', '#f3f4f6'],
      status: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#6b7280']
    };
    
    // Chart instances
    this.charts = {
      salesTrend: null,
      orderCountTrend: null,
      salesByStatus: null,
      topProducts: null,
      salesVsTarget: null,
      profitMargin: null,
      unitsByRegion: null,
      revenueGrowth: null,
      topCustomers: null,
      salesConversion: null,
      mainKpi: null
    };
    
    // Mini chart instances for KPI cards
    this.miniCharts = {
      orders: null,
      revenue: null,
      aov: null,
      topCustomer: null,
      topProduct: null
    };
  }

  async init() {
    // Initialize database properties
    this.dbName = 'salesKpiDb'; // Use the same DB name as sales_order.js
    this.salesOrdersStoreName = 'salesOrders';
    this.customersStoreName = 'customers';
    
    // Default time range
    this.timeRange = '7d';
    
    // Flag to control mock data fallback behavior
    this.useMockData = true; // Set to true to allow using mock data when real data not available
    
    // Initialize helpers
    try {
      const SalesAnalyticsHelpers = (await import('./sales_analytics_helpers.js')).SalesAnalyticsHelpers;
      this.helpers = new SalesAnalyticsHelpers(this);
    } catch (error) {
      console.error('Failed to load helpers module:', error);
      this.helpers = null;
    }
    
    // Render initial UI structure
    this.render();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Make sure the DOM is completely rendered before loading data and charts
    await new Promise(resolve => setTimeout(resolve, 50));
    
    // Load data
    await this.loadData();
  }

  render() {
    // Main container HTML
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-6">
        <!-- Top Filter Bar -->
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Sales Analytics</h2>
          <div class="flex space-x-2">
            <select id="main-time-filter" class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <option value="7d">Last 7 Days</option>
              <option value="1m">Last Month</option>
              <option value="1y">Last Year</option>
            </select>
            <button id="refresh-data" class="bg-blue-50 hover:bg-blue-100 text-blue-600 rounded-md px-3 py-2 text-sm flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
              </button>
          </div>
            </div>
            
        <!-- KPI Cards Row -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 mb-6">
          <!-- Orders Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <div class="flex justify-between">
              <div>
                <p class="text-xs text-gray-500 dark:text-gray-400">Total Orders</p>
                <h3 id="total-orders" class="text-xl font-bold text-gray-800 dark:text-white mt-1">0</h3>
                <div id="orders-trend" class="text-xs flex items-center mt-1">
                  <span class="text-green-500 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>0% vs previous</span>
                  </span>
          </div>
              </div>
              <div class="p-2 bg-blue-50 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              </div>
            </div>
            <div id="orders-sparkline" class="mt-2 h-10"></div>
        </div>

          <!-- Revenue Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <div class="flex justify-between">
              <div>
                <p class="text-xs text-gray-500 dark:text-gray-400">Total Revenue</p>
                <h3 id="total-revenue" class="text-xl font-bold text-gray-800 dark:text-white mt-1">$0</h3>
                <div id="revenue-trend" class="text-xs flex items-center mt-1">
                  <span class="text-green-500 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>0% vs previous</span>
                  </span>
              </div>
            </div>
              <div class="p-2 bg-green-50 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div id="revenue-sparkline" class="mt-2 h-10"></div>
          </div>

          <!-- AOV Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <div class="flex justify-between">
              <div>
                <p class="text-xs text-gray-500 dark:text-gray-400">Avg. Order Value</p>
                <h3 id="avg-order-value" class="text-xl font-bold text-gray-800 dark:text-white mt-1">$0</h3>
                <div id="aov-trend" class="text-xs flex items-center mt-1">
                  <span class="text-green-500 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>0% vs previous</span>
                  </span>
              </div>
            </div>
              <div class="p-2 bg-purple-50 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
            </div>
            <div id="aov-sparkline" class="mt-2 h-10"></div>
          </div>

          <!-- Top Customer Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <div class="flex justify-between">
              <div>
                <p class="text-xs text-gray-500 dark:text-gray-400">Top Customer</p>
                <h3 id="top-customer-name" class="text-xl font-bold text-gray-800 dark:text-white mt-1 truncate">-</h3>
                <div id="top-customer-percent" class="text-xs flex items-center mt-1">
                  <span class="text-gray-500"><span>0% of orders</span></span>
              </div>
            </div>
              <div class="p-2 bg-amber-50 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
              </div>
            </div>
            <div id="top-customer-chart" class="mt-2 h-10"></div>
          </div>

          <!-- Top Product Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <div class="flex justify-between">
              <div>
                <p class="text-xs text-gray-500 dark:text-gray-400">Top Product</p>
                <h3 id="top-product-name" class="text-xl font-bold text-gray-800 dark:text-white mt-1 truncate">-</h3>
                <div id="top-product-percent" class="text-xs flex items-center mt-1">
                  <span class="text-gray-500"><span>0% of revenue</span></span>
              </div>
            </div>
              <div class="p-2 bg-indigo-50 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
              </div>
            </div>
            <div id="top-product-chart" class="mt-2 h-10"></div>
          </div>
        </div>

        <!-- Main KPI Dashboard (New) -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4 mb-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales Performance Dashboard</h3>
            <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="main-kpi-chart">
              <option value="weekly">Weekly View</option>
              <option value="monthly">Monthly View</option>
              <option value="yearly">Yearly View</option>
            </select>
          </div>
          <div id="main-kpi-chart" class="h-96"></div>
        </div>
        
        <!-- Charts Row 1 -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mb-6">
          <!-- Sales Trend Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales Trend</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="sales-trend-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="sales-trend-chart" class="h-72"></div>
          </div>

          <!-- Order Count Trend Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Order Count Trend</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="order-count-trend-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="order-count-trend-chart" class="h-72"></div>
          </div>
        </div>
        
        <!-- Charts Row 2 -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mb-6">
          <!-- Sales by Status Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales by Status</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="sales-status-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
          </div>
            <div id="sales-status-chart" class="h-72"></div>
        </div>

          <!-- Top Products Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Top Products</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="top-products-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="top-products-chart" class="h-72"></div>
          </div>
        </div>
        
        <!-- Charts Row 3 -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
          <!-- Top Customers Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Top Customers</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="top-customers-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="top-customers-chart" class="h-72"></div>
          </div>

          <!-- Sales vs Target Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales vs Target</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="sales-vs-target-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="sales-vs-target-chart" class="h-72"></div>
          </div>
        </div>

        <!-- Charts Row 4 - Helper Charts -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mt-6">
          <!-- Sales Cost Profit Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales, Cost & Profit</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="sales-cost-profit-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="sales-cost-profit-chart" class="h-72"></div>
          </div>

          <!-- Sales by Region Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales by Region</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="sales-by-region-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="sales-by-region-chart" class="h-72"></div>
          </div>
        </div>

        <!-- Charts Row 5 - More Helper Charts -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-4 mt-6">
          <!-- Profit Margin Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Profit Margin by Product</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="profit-margin-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="profit-margin-chart" class="h-72"></div>
          </div>

          <!-- Customer Segment Chart -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-4">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-md font-semibold text-gray-800 dark:text-white">Sales by Customer Segment</h3>
              <select class="chart-filter bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500" data-chart-id="customer-segment-chart">
                <option value="7d">Last 7 Days</option>
                <option value="1m">Last Month</option>
                <option value="1y">Last Year</option>
              </select>
            </div>
            <div id="customer-segment-chart" class="h-72"></div>
          </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="hidden fixed inset-0 bg-gray-900 bg-opacity-50 z-50 flex items-center justify-center">
          <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="text-gray-700 dark:text-gray-300">Loading data...</p>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Set up main time filter
    const mainTimeFilter = document.getElementById('main-time-filter');
    if (mainTimeFilter) {
      mainTimeFilter.addEventListener('change', (e) => {
        const value = e.target.value;
        
        // Update all individual chart filters except the KPI chart 
        // which now uses granularity instead of time range
        const chartFilters = document.querySelectorAll('.chart-filter:not([data-chart-id="main-kpi-chart"])');
        chartFilters.forEach(filter => {
          // Only update if the option exists in this filter
          if (Array.from(filter.options).some(option => option.value === value)) {
            filter.value = value;
          }
        });
        
        // Apply the filter
        this.applyFilter(value);
      });
    }
    
    // Set up individual chart filters
    const chartFilters = document.querySelectorAll('.chart-filter');
    chartFilters.forEach(filter => {
      filter.addEventListener('change', (e) => {
        const chartId = e.target.getAttribute('data-chart-id');
        const value = e.target.value;
        
        // Apply filter to this specific chart
        this.applyFilterToChart(chartId, value);
      });
    });
    
    // Set up refresh data button
    const refreshBtn = document.getElementById('refresh-data');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        const filter = document.getElementById('main-time-filter')?.value || '7d';
        await this.loadData(true); // Force refresh
        this.applyFilter(filter);
      });
    }
  }
  
  // Helper method to update KPI filter buttons
  updateKpiFilterButtons(timeRange) {
    const kpiFilterButtons = document.querySelectorAll('.kpi-filter-btn');
    
    kpiFilterButtons.forEach(btn => {
      // Remove active classes from all buttons
      btn.classList.remove('bg-white', 'dark:bg-gray-600', 'shadow-sm', 'text-blue-600', 'dark:text-blue-400');
      btn.classList.add('text-gray-600', 'dark:text-gray-300');
      
      // Add active classes to the matching button
      if ((timeRange === '7d' && btn.id === 'kpi-filter-7d') || 
          (timeRange === '1m' && btn.id === 'kpi-filter-1m') || 
          (timeRange === '1y' && btn.id === 'kpi-filter-1y')) {
        btn.classList.remove('text-gray-600', 'dark:text-gray-300');
        btn.classList.add('bg-white', 'dark:bg-gray-600', 'shadow-sm', 'text-blue-600', 'dark:text-blue-400');
      }
    });
  }
  
  // Apply filter to specific chart
  applyFilterToChart(chartId, timeRange) {
    console.log(`Applying filter ${timeRange} to chart ${chartId}`);
    
    // Store the selected time range for this chart
    if (chartId === 'main-kpi-chart') {
      // For main KPI chart, handle the new granularity values
      this.chartTimeRanges.mainKpi = timeRange;
      // Render the chart with the new granularity
      this.renderMainKpiChart();
      return;
    }
    else if (chartId === 'sales-trend-chart') this.chartTimeRanges.salesTrend = timeRange;
    else if (chartId === 'order-count-trend-chart') this.chartTimeRanges.orderCountTrend = timeRange;
    else if (chartId === 'sales-status-chart') this.chartTimeRanges.salesByStatus = timeRange;
    else if (chartId === 'top-products-chart') this.chartTimeRanges.topProducts = timeRange;
    else if (chartId === 'top-customers-chart') this.chartTimeRanges.topCustomers = timeRange;
    else if (chartId === 'sales-vs-target-chart') this.chartTimeRanges.salesVsTarget = timeRange;
    else if (chartId === 'sales-cost-profit-chart') this.chartTimeRanges.salesCostProfit = timeRange;
    else if (chartId === 'sales-by-region-chart') this.chartTimeRanges.salesByRegion = timeRange;
    else if (chartId === 'profit-margin-chart') this.chartTimeRanges.profitMargin = timeRange;
    else if (chartId === 'customer-segment-chart') this.chartTimeRanges.customerSegment = timeRange;
    
    // Get filtered data for this time range
    const filteredData = this.getFilteredData(timeRange);
    
    try {
      // Render the specific chart based on chartId
      switch (chartId) {
        case 'main-kpi-chart':
          this.renderMainKpiChart(filteredData);
          break;
        case 'sales-trend-chart':
          this.renderSalesTrendChart(filteredData);
          break;
        case 'order-count-trend-chart':
          this.renderOrderCountTrendChart(filteredData);
          break;
        case 'sales-status-chart':
          this.renderSalesByStatusChart(filteredData);
          break;
        case 'top-products-chart':
          this.renderTopProductsChart(filteredData);
          break;
        case 'top-customers-chart':
          this.renderTopCustomersChart(filteredData);
          break;
        case 'sales-vs-target-chart':
          this.renderSalesVsTargetChart(filteredData);
          break;
        case 'sales-cost-profit-chart':
          if (this.helpers) {
            this.helpers.renderSalesCostProfitChart(filteredData);
          } else {
            console.error('Helpers module not loaded, cannot render sales-cost-profit-chart');
          }
          break;
        case 'sales-by-region-chart':
          if (this.helpers) {
            this.helpers.renderSalesByRegionChart(filteredData);
          } else {
            console.error('Helpers module not loaded, cannot render sales-by-region-chart');
          }
          break;
        case 'profit-margin-chart':
          if (this.helpers) {
            this.helpers.renderProfitMarginByProductChart(filteredData);
          } else {
            console.error('Helpers module not loaded, cannot render profit-margin-chart');
          }
          break;
        case 'customer-segment-chart':
          if (this.helpers) {
            this.helpers.renderSalesByCustomerSegmentChart(filteredData);
          } else {
            console.error('Helpers module not loaded, cannot render customer-segment-chart');
          }
          break;
        default:
          console.warn(`No handler for chart: ${chartId}`);
      }
      console.log(`Successfully updated chart ${chartId} with time range ${timeRange}`);
    } catch (error) {
      console.error(`Error updating chart ${chartId}:`, error);
      const chartElement = document.getElementById(chartId);
      if (chartElement) {
        chartElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p></div>`;
      }
    }
  }

  async loadData(forceRefresh = false) {
    // Reset data loaded flag if forcing refresh
    if (forceRefresh) {
      this.isDataLoaded = false;
      this.salesOrders = [];
      this.customers = [];
    }
    
    // Prevent reloading if data is already loaded and not forcing refresh
    if (this.isDataLoaded && !forceRefresh) {
        this.applyFilter(this.timeRange); // Re-apply filter which renders charts etc.
        return;
    }

    this.showLoading();
    
    try {
      // Initialize customer lookup table
      this.customerLookup = {};
      
      // Load sales orders data
      await this.loadSalesOrdersDataFromDB();
      
      // Process customer names right after loading sales orders
      await this.lookupCustomerNames();
      
      this.isDataLoaded = true;
      this.applyFilter(this.timeRange);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      // If loading from DB fails, fall back to mock data
      this.salesOrders = this.generateMockSalesOrderData();
      this.applyFilter(this.timeRange);
    } finally {
      this.hideLoading();
    }
  }

  async loadSalesOrdersDataFromDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 2);
      
      request.onerror = (event) => {
        console.error(`IndexedDB error for ${this.dbName}:`, event.target.error);
        // Fallback to mock data on error
        this.salesOrders = this.generateMockSalesOrderData();
        resolve(); // Resolve even on error to allow mock data usage
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(this.salesOrdersStoreName)) {
          console.warn(`Store ${this.salesOrdersStoreName} not found. Using mock sales data.`);
          this.salesOrders = this.generateMockSalesOrderData();
          resolve();
        return;
      }
      
        try {
          const transaction = db.transaction([this.salesOrdersStoreName], 'readonly');
          const store = transaction.objectStore(this.salesOrdersStoreName);
          const getAllRequest = store.getAll();

          getAllRequest.onsuccess = () => {
            if (getAllRequest.result && getAllRequest.result.length > 0) {
              this.salesOrders = getAllRequest.result;
              console.log(`Loaded ${this.salesOrders.length} sales orders from IndexedDB.`);
            } else {
              console.warn('No sales orders found in IndexedDB. Using mock sales data.');
              this.salesOrders = this.generateMockSalesOrderData();
            }
            resolve();
          };

          getAllRequest.onerror = (error) => {
            console.error('Error fetching sales orders from IndexedDB:', error);
            this.salesOrders = this.generateMockSalesOrderData();
            resolve(); // Resolve to allow mock data usage
          };
        } catch (transactionError) {
          console.error('Transaction error fetching sales orders:', transactionError);
          this.salesOrders = this.generateMockSalesOrderData();
          resolve(); // Resolve to allow mock data usage
        }
      };
    });
  }

  async loadCustomersDataFromDB() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 2);

      request.onerror = (event) => {
        console.error(`IndexedDB error for ${this.dbName}:`, event.target.error);
        // Fallback to mock data on error
        this.customers = this.generateMockCustomerData();
        resolve(); // Resolve even on error
      };

      request.onsuccess = (event) => {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(this.customersStoreName)) {
          console.warn(`Store ${this.customersStoreName} not found. Using mock customer data.`);
          this.customers = this.generateMockCustomerData();
          resolve();
        return;
      }
      
        try {
          const transaction = db.transaction([this.customersStoreName], 'readonly');
          const store = transaction.objectStore(this.customersStoreName);
          const getAllRequest = store.getAll();

          getAllRequest.onsuccess = () => {
            if (getAllRequest.result && getAllRequest.result.length > 0) {
              this.customers = getAllRequest.result;
              console.log(`Loaded ${this.customers.length} customers from IndexedDB.`);
              
              // Create lookup for customer names by ID
              this.customerLookup = {};
              this.customers.forEach(customer => {
                if (customer.id) {
                  this.customerLookup[customer.id] = customer.name || customer.displayName || customer.id;
                }
              });
            } else {
              console.warn('No customers found in IndexedDB. Using mock customer data.');
              this.customers = this.generateMockCustomerData();
            }
            resolve();
          };

          getAllRequest.onerror = (error) => {
            console.error('Error fetching customers from IndexedDB:', error);
            this.customers = this.generateMockCustomerData();
            resolve(); // Resolve to allow mock data usage
          };
        } catch (transactionError) {
          console.error('Transaction error fetching customers:', transactionError);
          this.customers = this.generateMockCustomerData();
          resolve(); // Resolve to allow mock data usage
        }
      };
    });
  }

  // Apply time filter to data and update charts
  applyFilter(timeRange) {
    // Update the current time range
    this.timeRange = timeRange;
    
    // Update dropdown UI to reflect current filter
    const mainTimeFilter = document.getElementById('main-time-filter');
    if (mainTimeFilter) {
      mainTimeFilter.value = timeRange;
    }
    
    // Filter data based on time range
    this.filteredSalesOrders = this.getFilteredData(timeRange);
    
    // Update KPI cards with filtered data
    this.updateKPICards();
    
    // Render all charts with filtered data
    this.renderCharts();
  }

  updateFilterButtons() {
    // Update filter button states
    ['weekly', 'monthly', 'yearly'].forEach(range => {
      const button = document.getElementById(`btn-${range}`);
      if (button) {
        if (range === this.timeRange) {
          button.classList.add('bg-blue-100', 'text-blue-600');
          button.classList.remove('bg-gray-100', 'text-gray-600');
        } else {
          button.classList.remove('bg-blue-100', 'text-blue-600');
          button.classList.add('bg-gray-100', 'text-gray-600');
        }
      }
    });
  }

  updateKPICards() {
    // Get references to KPI elements
    const totalOrdersEl = document.getElementById('total-orders');
    const totalRevenueEl = document.getElementById('total-revenue');
    const avgOrderValueEl = document.getElementById('avg-order-value');
    const topCustomerNameEl = document.getElementById('top-customer-name');
    const topCustomerPercentEl = document.getElementById('top-customer-percent');
    const topProductNameEl = document.getElementById('top-product-name');
    const topProductPercentEl = document.getElementById('top-product-percent');
    
    // Calculate metrics from filtered orders
    const totalOrders = this.filteredSalesOrders.length;
    let totalRevenue = 0;
    
    // Customer and product aggregation
    const ordersByCustomer = {};
    const salesByProduct = {};
    
    this.filteredSalesOrders.forEach(order => {
      // Add to total revenue
      const orderTotal = parseFloat(order.orderTotal || 0);
      totalRevenue += orderTotal;
      
      // Add to customer stats - track ORDER COUNT instead of sales amount
      const customerId = order.customerID || 'Unknown';
      const customerName = order.customerName || this.customerLookup[customerId] || `Customer ${customerId}`;
      
      if (!ordersByCustomer[customerId]) {
        ordersByCustomer[customerId] = {
          id: customerId,
          name: customerName,
          orderCount: 0,
          totalSales: 0
        };
      }
      
      ordersByCustomer[customerId].orderCount += 1;
      ordersByCustomer[customerId].totalSales += orderTotal;
      
      // Add to product stats if details are available
      if (order.details && Array.isArray(order.details)) {
        order.details.forEach(item => {
          const productId = item.inventoryID || item.description || 'Unknown Product';
          const productSales = parseFloat(item.extendedPrice || item.amount || 0);
          
          if (!salesByProduct[productId]) {
            salesByProduct[productId] = {
              id: productId,
              name: item.description || productId,
              sales: 0
            };
          }
          salesByProduct[productId].sales += productSales;
        });
      }
    });
    
    // Calculate average order value
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Find top customer by order count
    const topCustomer = Object.values(ordersByCustomer)
      .sort((a, b) => b.orderCount - a.orderCount)[0] || { name: '-', orderCount: 0 };
    
    const topCustomerPercent = totalOrders > 0 ? (topCustomer.orderCount / totalOrders) * 100 : 0;
    
    // Find top product
    const topProduct = Object.values(salesByProduct)
      .sort((a, b) => b.sales - a.sales)[0] || { name: '-', sales: 0 };
    
    // Truncate product name if it's too long
    const truncatedProductName = topProduct.name ? this.truncateProductName(topProduct.name) : '-';
    
    // Truncate customer name to first word
    const truncatedCustomerName = topCustomer.name ? this.truncateCustomerName(topCustomer.name) : '-';
    
    const topProductPercent = totalRevenue > 0 ? (topProduct.sales / totalRevenue) * 100 : 0;
    
    // Update KPI card values
    if (totalOrdersEl) totalOrdersEl.textContent = totalOrders.toLocaleString();
    if (totalRevenueEl) totalRevenueEl.textContent = this.currencyFormatter.format(totalRevenue);
    if (avgOrderValueEl) avgOrderValueEl.textContent = this.currencyFormatter.format(avgOrderValue);
    
    if (topCustomerNameEl) topCustomerNameEl.textContent = truncatedCustomerName;
    if (topCustomerPercentEl) {
      const percentText = topCustomerPercentEl.querySelector('span');
      if (percentText) percentText.textContent = `${topCustomerPercent.toFixed(1)}% of orders`;
    }
    
    if (topProductNameEl) topProductNameEl.textContent = truncatedProductName;
    if (topProductPercentEl) {
      const percentText = topProductPercentEl.querySelector('span');
      if (percentText) percentText.textContent = `${topProductPercent.toFixed(1)}% of revenue`;
    }
    
    // Update trends
    this.updateTrendIndicators();
    
    // Render mini sparkline charts in KPI cards
    this.renderSparklineCharts();
  }

  // Helper method to truncate product names to a reasonable length
  truncateProductName(name) {
    if (!name) return '-';
    
    // If the name contains model numbers followed by description, extract just the model number
    const modelMatch = name.match(/^([A-Z0-9]{3,8}[A-Z]?)/);
    if (modelMatch) {
      return modelMatch[1];
    }
    
    // Otherwise truncate after a certain length or at the first comma/hyphen
    const firstSeparator = name.indexOf(',');
    if (firstSeparator > 0 && firstSeparator < 30) {
      return name.substring(0, firstSeparator).trim();
    }
    
    const firstHyphen = name.indexOf(' - ');
    if (firstHyphen > 0 && firstHyphen < 30) {
      return name.substring(0, firstHyphen).trim();
    }
    
    // If all else fails, truncate to 20 characters + ellipsis
    if (name.length > 20) {
      return name.substring(0, 20) + '...';
    }
    
    return name;
  }

  // Helper method to truncate customer names to just the first word
  truncateCustomerName(name) {
    if (!name) return '-';
    
    // If it's a company name, just take the first word
    const firstWord = name.split(' ')[0];
    if (firstWord && firstWord.length > 3) {
      return firstWord;
    }
    
    // If the name is too long, truncate it
    if (name.length > 15) {
      return name.substring(0, 15) + '...';
    }
    
    return name;
  }

  renderTopProductChart() {
    // Calculate product sales percentages
    const salesByProduct = {};
    let totalSales = 0;
    
    this.filteredSalesOrders.forEach(order => {
      if (order.details && Array.isArray(order.details)) {
        order.details.forEach(item => {
          const productId = item.inventoryID || item.description || 'Unknown Product';
          const productSales = parseFloat(item.extendedPrice || item.amount || 0);
          totalSales += productSales;
          
          if (!salesByProduct[productId]) {
            salesByProduct[productId] = 0;
          }
          salesByProduct[productId] += productSales;
        });
      }
    });
    
    // Get top product plus "Others"
    const productData = Object.entries(salesByProduct)
      .map(([id, sales]) => ({ id, sales }))
      .sort((a, b) => b.sales - a.sales);
    
    const topProducts = productData.slice(0, 1);
    const otherSales = productData.slice(1).reduce((sum, product) => sum + product.sales, 0);
    
    const chartData = [
      ...topProducts.map(p => p.sales),
      otherSales
    ];
    
    const options = {
      series: chartData,
      chart: {
        type: 'donut',
        height: 48,
        sparkline: {
          enabled: true
        },
        background: 'transparent'
      },
      colors: ['#8b5cf6', '#d1d5db'],
      stroke: {
        width: 1
      },
      tooltip: {
        enabled: false
      },
      legend: {
        show: false
      },
      dataLabels: {
        enabled: false
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%'
          }
        }
      }
    };
    
    if (this.miniCharts.topProduct) {
      this.miniCharts.topProduct.destroy();
    }
    
    this.miniCharts.topProduct = new ApexCharts(document.getElementById('top-product-chart'), options);
    this.miniCharts.topProduct.render();
  }

  async lookupCustomerNames() {
    const db = await this.openDatabase();
    if (!db) {
      console.warn('Database not available for customer lookup');
      return;
    }
    
    try {
      // First check if customers object store exists
      if (!db.objectStoreNames.contains('customers')) {
        console.warn('Customers store does not exist in database');
        return;
      }
      
      const transaction = db.transaction(['customers'], 'readonly');
      const customerStore = transaction.objectStore('customers');
      const customers = await new Promise((resolve, reject) => {
        const request = customerStore.getAll();
        request.onsuccess = () => resolve(request.result || []);
        request.onerror = () => reject(request.error);
      });
      
      // Debug: Check what we found
      console.log(`Found ${customers.length} customers for lookup`);
      
      // Create lookup table
      customers.forEach(customer => {
        // Use various properties that might contain the ID
        const ids = [
          customer.id, 
          customer.customerID,
          customer.customerId
        ].filter(id => id != null && id !== '');
        
        // Get the customer name from various possible properties
        const name = customer.name || 
                     customer.displayName || 
                     customer.customerName || 
                     customer.companyName ||
                     ids[0] || 'Unknown Customer';
        
        // Add all possible IDs to the lookup table
        ids.forEach(id => {
          if (id) {
            this.customerLookup[id] = name;
          }
        });
      });
      
      // Apply customer names to orders
      this.salesOrders = this.salesOrders.map(order => {
        const customerId = order.customerID || '';
        const customerName = this.customerLookup[customerId] || `Customer ${customerId}`;
        
        return {
          ...order,
          customerName
        };
      });
      
      console.log('Customer name lookup completed successfully');
    } catch (error) {
      console.error('Error during customer name lookup:', error);
    }
  }

  // Update trend indicators with period-over-period comparisons
  updateTrendIndicators() {
    // Calculate trends based on previous period data
    const previousRange = this.getPreviousTimeRange(this.timeRange);
    const previousData = this.getFilteredData(previousRange);
    
    // Calculate previous period metrics
    let prevTotalOrders = 0;
    let prevTotalRevenue = 0;
    
    previousData.forEach(order => {
      prevTotalOrders++;
      prevTotalRevenue += parseFloat(order.orderTotal || 0);
    });
    
    const prevAvgOrderValue = prevTotalOrders > 0 ? prevTotalRevenue / prevTotalOrders : 0;
    
    // Calculate trends
    const ordersTrend = prevTotalOrders > 0 ? 
      ((this.filteredSalesOrders.length - prevTotalOrders) / prevTotalOrders) * 100 : 0;
      
    const revenueTrend = prevTotalRevenue > 0 ? 
      ((this.calculateTotalRevenue(this.filteredSalesOrders) - prevTotalRevenue) / prevTotalRevenue) * 100 : 0;
      
    const aovTrend = prevAvgOrderValue > 0 ?
      ((this.calculateAvgOrderValue(this.filteredSalesOrders) - prevAvgOrderValue) / prevAvgOrderValue) * 100 : 0;
    
    // Update trend elements
    this.updateTrendElement('orders-trend', ordersTrend);
    this.updateTrendElement('revenue-trend', revenueTrend);
    this.updateTrendElement('aov-trend', aovTrend);
  }
  
  // Update a specific trend element with formatted trend value
  updateTrendElement(elementId, trendValue) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const trendSpan = element.querySelector('span');
    if (!trendSpan) return;
    
    const formattedTrend = trendValue.toFixed(1);
    const trendClass = trendValue > 0 ? 'text-green-500' : trendValue < 0 ? 'text-red-500' : 'text-gray-500';
    const trendIcon = trendValue > 0 ? '<i class="fas fa-arrow-up mr-1"></i>' : 
                       trendValue < 0 ? '<i class="fas fa-arrow-down mr-1"></i>' : '';
    
    trendSpan.className = trendClass;
    trendSpan.innerHTML = `${trendIcon}${Math.abs(formattedTrend)}% from last period`;
  }
  
  // Get data for previous time period
  getPreviousTimeRange(currentRange) {
    switch(currentRange) {
      case '7d':
        return 'prev7d';
      case '1m':
        return 'prev1m';
      case '1y':
        return 'prev1y';
      default:
        return 'prev7d';
    }
  }
  
  // Calculate total revenue from orders
  calculateTotalRevenue(orders) {
    return orders.reduce((total, order) => total + parseFloat(order.orderTotal || 0), 0);
  }
  
  // Calculate average order value
  calculateAvgOrderValue(orders) {
    const totalRevenue = this.calculateTotalRevenue(orders);
    return orders.length > 0 ? totalRevenue / orders.length : 0;
  }
  
  // Render mini sparkline charts in KPI cards
  renderSparklineCharts() {
    this.renderOrdersSparkline();
    this.renderRevenueSparkline();
    this.renderAOVSparkline();
    this.renderTopCustomerChart();
    this.renderTopProductChart();
  }
  
  // Render mini sparkline chart for orders
  renderOrdersSparkline() {
    const sparklineData = this.prepareSparklineData(this.filteredSalesOrders, 'count');
    
    const options = {
      series: [{
        name: 'Orders',
        data: sparklineData.values
      }],
      chart: {
        type: 'line',
        height: 48,
        sparkline: {
          enabled: true
        },
        background: 'transparent'
      },
      colors: ['#3b82f6'],
      stroke: {
        curve: 'smooth',
        width: 2
      },
      tooltip: {
        fixed: {
          enabled: false
        },
        x: {
          show: false
        },
        y: {
          title: {
            formatter: function (seriesName) {
              return '';
            }
          }
        },
        marker: {
          show: false
        }
      }
    };
    
    if (this.miniCharts.orders) {
      this.miniCharts.orders.destroy();
    }
    
    this.miniCharts.orders = new ApexCharts(document.getElementById('orders-sparkline'), options);
    this.miniCharts.orders.render();
  }
  
  // Render mini sparkline chart for revenue
  renderRevenueSparkline() {
    const sparklineData = this.prepareSparklineData(this.filteredSalesOrders, 'revenue');
    
    const options = {
      series: [{
        name: 'Revenue',
        data: sparklineData.values
      }],
      chart: {
        type: 'area',
        height: 48,
        sparkline: {
          enabled: true
        },
        background: 'transparent'
      },
      colors: ['#10b981'],
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      tooltip: {
        fixed: {
          enabled: false
        },
        x: {
          show: false
        },
        y: {
          title: {
            formatter: function (seriesName) {
              return '';
            }
          },
          formatter: (value) => this.currencyFormatter.format(value)
        },
        marker: {
          show: false
        }
      }
    };
    
    if (this.miniCharts.revenue) {
      this.miniCharts.revenue.destroy();
    }
    
    this.miniCharts.revenue = new ApexCharts(document.getElementById('revenue-sparkline'), options);
    this.miniCharts.revenue.render();
  }
  
  // Render mini sparkline chart for AOV
  renderAOVSparkline() {
    const sparklineData = this.prepareSparklineData(this.filteredSalesOrders, 'aov');
    
    const options = {
      series: [{
        name: 'Avg Order Value',
        data: sparklineData.values
      }],
      chart: {
        type: 'line',
        height: 48,
        sparkline: {
          enabled: true
        },
        background: 'transparent'
      },
      colors: ['#ef4444'],
      stroke: {
        curve: 'straight',
        width: 2
      },
      tooltip: {
        fixed: {
          enabled: false
        },
        x: {
          show: false
        },
        y: {
          title: {
            formatter: function (seriesName) {
              return '';
            }
          },
          formatter: (value) => this.currencyFormatter.format(value)
        },
        marker: {
          show: false
        }
      }
    };
    
    if (this.miniCharts.aov) {
      this.miniCharts.aov.destroy();
    }
    
    this.miniCharts.aov = new ApexCharts(document.getElementById('aov-sparkline'), options);
    this.miniCharts.aov.render();
  }
  
  // Render mini donut chart for top customer
  renderTopCustomerChart() {
    // Calculate customer order percentages
    const ordersByCustomer = {};
    let totalOrders = 0;
    
    this.filteredSalesOrders.forEach(order => {
      const customerId = order.customerID || 'Unknown';
      totalOrders++;
      
      if (!ordersByCustomer[customerId]) {
        ordersByCustomer[customerId] = 0;
      }
      ordersByCustomer[customerId]++;
    });
    
    // Get top customer plus "Others"
    const customerData = Object.entries(ordersByCustomer)
      .map(([id, count]) => ({ id, count }))
      .sort((a, b) => b.count - a.count);
    
    const topCustomers = customerData.slice(0, 1);
    const otherOrders = customerData.slice(1).reduce((sum, customer) => sum + customer.count, 0);
    
    const chartData = [
      ...topCustomers.map(c => c.count),
      otherOrders
    ];
    
    const options = {
      series: chartData,
      chart: {
        type: 'donut',
        height: 48,
        sparkline: {
          enabled: true
        },
        background: 'transparent'
      },
      colors: ['#f59e0b', '#d1d5db'],
      stroke: {
        width: 1
      },
      tooltip: {
        enabled: false
      },
      legend: {
        show: false
      },
      dataLabels: {
        enabled: false
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%'
          }
        }
      }
    };
    
    if (this.miniCharts.topCustomer) {
      this.miniCharts.topCustomer.destroy();
    }
    
    this.miniCharts.topCustomer = new ApexCharts(document.getElementById('top-customer-chart'), options);
    this.miniCharts.topCustomer.render();
  }
  
  renderTopProductsChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders || [];
    
    // Calculate product sales from filtered sales orders
    const salesByProduct = {};
    
    ordersToUse.forEach(order => {
      // Ensure order.details exists and is an array
      if (order.details && Array.isArray(order.details)) {
        order.details.forEach(item => {
          // Use Inventory ID as the key, fall back to description if needed
          const productId = item.inventoryID || item.description || 'Unknown Product';
          const productSales = parseFloat(item.extendedPrice || item.amount || 0); // Use extended price or amount
          
          if (!salesByProduct[productId]) {
            salesByProduct[productId] = {
              id: productId,
              name: item.description || productId, // Use description for name, fall back to ID
              sales: 0
            };
          }
          salesByProduct[productId].sales += productSales;
        });
      }
    });
    
    // Sort and get top 10 products
    const topProducts = Object.values(salesByProduct)
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 10);
    
    // Handle case where there are no products
    if (topProducts.length === 0) {
      console.log("No product data to display in Top Products chart.");
      // Optionally display a message in the chart area
      const chartElement = document.getElementById('top-products-chart');
      if (chartElement) {
        // Destroy existing chart if it exists
        if (this.charts.topProducts) {
          this.charts.topProducts.destroy();
          this.charts.topProducts = null; // Clear reference
        }
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-center text-gray-500 p-4">No product sales data available for this period. Try selecting a different time range.</p></div>';
      }

      // Fall back to mock data if we allow it
      if (this.useMockData) {
        const mockProducts = this.generateMockProductData();
        if (mockProducts && mockProducts.length > 0) {
          console.log("Using mock product data for visualization");
          topProducts.push(...mockProducts);
        } else {
          return;
        }
      } else {
        return;
      }
    }
    
    const options = {
      series: [{
        name: 'Revenue',
        data: topProducts.map(product => product.sales)
      }],
      chart: {
        type: 'bar',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        }
      },
      colors: [this.chartColors.secondary[0]],
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          barHeight: '60%',
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        textAnchor: 'start',
        offsetX: 10,
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif',
          colors: ['#374151']
        },
        formatter: (val) => this.currencyFormatter.format(val)
      },
      xaxis: {
        categories: topProducts.map(product => product.name),
        labels: {
          formatter: (val) => this.currencyFormatter.format(val),
          style: {
            colors: '#6b7280',
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            colors: '#4b5563',
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: false
          }
        },
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => this.currencyFormatter.format(val),
          title: {
            formatter: (seriesName) => 'Revenue'
          }
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.topProducts) {
      this.charts.topProducts.destroy();
    }
    
    // Create new chart
    this.charts.topProducts = new ApexCharts(document.getElementById('top-products-chart'), options);
    this.charts.topProducts.render();
  }
  
  // Prepare data for mini sparkline charts
  prepareSparklineData(orders, metricType) {
    // Sort orders by date
    const sortedOrders = [...orders].sort((a, b) => {
      return new Date(a.orderDate || a.date) - new Date(b.orderDate || b.date);
    });
    
    // Group by day and calculate metrics
    const days = 7;
    const now = new Date();
    const result = { dates: [], values: [] };
    
    // Create array of last 7 days
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      date.setHours(0, 0, 0, 0);
      
      result.dates.push(date);
      result.values.push(0);
    }
    
    // Fill in values based on metric type
    sortedOrders.forEach(order => {
      const orderDate = new Date(order.orderDate || order.date);
      orderDate.setHours(0, 0, 0, 0);
      
      const dayIndex = result.dates.findIndex(date => 
        date.getFullYear() === orderDate.getFullYear() &&
        date.getMonth() === orderDate.getMonth() &&
        date.getDate() === orderDate.getDate()
      );
      
      if (dayIndex >= 0) {
        switch (metricType) {
          case 'count':
            result.values[dayIndex]++;
            break;
          case 'revenue':
            result.values[dayIndex] += parseFloat(order.orderTotal || 0);
            break;
          case 'aov':
            // We'll calculate AOV later
            result.values[dayIndex] += parseFloat(order.orderTotal || 0);
            if (!result.counts) result.counts = Array(days).fill(0);
            result.counts[dayIndex]++;
            break;
        }
      }
    });
    
    // Calculate AOV if needed
    if (metricType === 'aov' && result.counts) {
      result.values = result.values.map((value, index) => 
        result.counts[index] > 0 ? value / result.counts[index] : 0
      );
    }
    
    return result;
  }

  renderCharts() {
    if (!this.isDataLoaded) {
      console.log('Data not loaded yet, skipping chart rendering');
      return;
    }
    
    // Make sure ApexCharts is loaded
    if (typeof ApexCharts === 'undefined') {
      console.error('ApexCharts library not loaded');
      const errorMessage = 'Chart library not available. Please check your network connection and try again.';
      this.showErrorMessage(errorMessage);
      return;
    }
    
    console.log('Rendering sales analytics charts');
    
    // Render the charts based on their individual time filters
    const chartMethods = {
      'mainKpi': {
        method: this.renderMainKpiChart.bind(this),
        elementId: 'main-kpi-chart'
      },
      'salesTrend': {
        method: this.renderSalesTrendChart.bind(this),
        elementId: 'sales-trend-chart'
      },
      'orderCountTrend': {
        method: this.renderOrderCountTrendChart.bind(this),
        elementId: 'order-count-trend-chart'
      },
      'salesByStatus': {
        method: this.renderSalesByStatusChart.bind(this),
        elementId: 'sales-status-chart'
      },
      'topProducts': {
        method: this.renderTopProductsChart.bind(this),
        elementId: 'top-products-chart'
      },
      'topCustomers': {
        method: this.renderTopCustomersChart.bind(this),
        elementId: 'top-customers-chart'
      },
      'salesVsTarget': {
        method: this.renderSalesVsTargetChart.bind(this),
        elementId: 'sales-vs-target-chart'
      }
    };
    
    // Add helper charts
    if (this.helpers) {
      // Add all helper charts
      Object.assign(chartMethods, {
        'salesCostProfit': {
          method: this.helpers.renderSalesCostProfitChart.bind(this.helpers),
          elementId: 'sales-cost-profit-chart'
        },
        'salesByRegion': {
          method: this.helpers.renderSalesByRegionChart.bind(this.helpers),
          elementId: 'sales-by-region-chart'
        },
        'profitMargin': {
          method: this.helpers.renderProfitMarginByProductChart.bind(this.helpers),
          elementId: 'profit-margin-chart'
        },
        'customerSegment': {
          method: this.helpers.renderSalesByCustomerSegmentChart.bind(this.helpers),
          elementId: 'customer-segment-chart'
        }
      });
    }
    
    // Track successful chart renders
    let successfulRenders = 0;
    const totalCharts = Object.keys(chartMethods).length;
    
    // Render all charts
    Object.entries(chartMethods).forEach(([chartId, {method, elementId}]) => {
      try {
        const element = document.getElementById(elementId);
        if (element) {
          console.log(`Rendering chart: ${chartId}`);
          const timeRange = this.chartTimeRanges[chartId] || this.timeRange;
          
          // Make sure we have valid time range
          if (!['7d', '1m', '1y'].includes(timeRange) && 
              !['weekly', 'monthly', 'yearly'].includes(timeRange)) {
            console.warn(`Invalid time range ${timeRange} for chart ${chartId}, using default 7d`);
            this.chartTimeRanges[chartId] = '7d';
          }
          
          // Get filtered data for this time range
          const filteredData = this.getFilteredData(this.chartTimeRanges[chartId] || '7d');
          
          // Call the chart rendering method
          method(filteredData);
          successfulRenders++;
        } else {
          console.warn(`Chart element not found: ${elementId}`);
        }
      } catch (error) {
        console.error(`Error rendering ${chartId} chart:`, error);
        const element = document.getElementById(elementId);
        if (element) {
          element.innerHTML = `
            <div class="flex items-center justify-center h-full">
              <p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p>
            </div>
          `;
        }
      }
    });
    
    console.log(`Successfully rendered ${successfulRenders} of ${totalCharts} charts`);
    
    // Render mini-charts for KPI cards
    try {
      this.renderSparklineCharts();
    } catch (error) {
      console.error('Error rendering sparkline charts:', error);
    }
  }
  
  // Render the main KPI dashboard chart (full width)
  renderMainKpiChart(filteredOrders = null) {
    const chartElement = document.getElementById('main-kpi-chart');
    
    // Check if chart element exists in DOM
    if (!chartElement) {
      console.error('Chart element not found: main-kpi-chart');
      return;
    }
    
    // Force use of all sales orders for comprehensive view
    const ordersToUse = this.salesOrders || [];
    
    // Check if we have data
    if (!ordersToUse.length) {
      chartElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-center text-gray-500 p-4">No data available for KPI dashboard</p></div>`;
      return;
    }
    
    console.log(`Rendering main KPI chart with ${ordersToUse.length} orders`);
    
    // Get the selected time granularity - ensure it defaults to monthly even when null or undefined
    let timeGranularity = this.chartTimeRanges.mainKpi || 'monthly';
    
    // Validate granularity value
    if (!['weekly', 'monthly', 'yearly'].includes(timeGranularity)) {
      console.warn(`Invalid time granularity: ${timeGranularity}, defaulting to monthly`);
      timeGranularity = 'monthly';
      this.chartTimeRanges.mainKpi = 'monthly';
    }
    
    // Current date info
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentWeek = this.getWeekNumber(now);
    
    // Prepare data arrays for actual and forecast data
    let actualData = [];
    let forecastData = [];
    let xAxisLabels = [];
    let changeData = []; // Store period-over-period changes
    
    // Organize data based on time granularity
    if (timeGranularity === 'weekly') {
      // Weekly view - show 52 weeks of the year
      const weeklyData = this.prepareWeeklyDataForYear(ordersToUse, currentYear);
      xAxisLabels = weeklyData.map(d => `W${d.week}`);
      
      // Calculate week-over-week changes
      for (let i = 0; i < weeklyData.length; i++) {
        if (i > 0) {
          const currentRevenue = weeklyData[i].revenue;
          const prevRevenue = weeklyData[i-1].revenue;
          const change = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;
          changeData.push({
            week: weeklyData[i].week,
            change: change
          });
        } else {
          changeData.push({
            week: weeklyData[i].week,
            change: 0
          });
        }
      }
      
      // Separate actual data (past weeks)
      actualData = weeklyData.map((item, index) => {
        if (item.week <= currentWeek) {
          return item.revenue;
        }
        return null; // Will create a gap in the chart
      });
      
      // Calculate trend based on the last 12 weeks or available data
      const pastWeeksData = weeklyData.filter(d => d.week <= currentWeek);
      const trend = this.calculateTrend(pastWeeksData.map(d => d.revenue));
      
      // Forecast data (future weeks)
      forecastData = weeklyData.map((item, index) => {
        if (item.week < currentWeek) {
          return null; // Only show forecast for current & future
        }
        
        // For current week, use actual if available, otherwise forecast
        if (item.week === currentWeek) {
          const forecastValue = this.forecastValue(pastWeeksData, trend, 0);
          return item.revenue || forecastValue;
        }
        
        // Future weeks
        const weeksAhead = item.week - currentWeek;
        const forecastValue = this.forecastValue(pastWeeksData, trend, weeksAhead);
        return forecastValue;
      });
    } else if (timeGranularity === 'monthly') {
      // Monthly view - show 12 months of the year
      const monthlyData = this.prepareMonthlyDataForYear(ordersToUse, currentYear);
      xAxisLabels = monthlyData.map(d => this.getMonthName(d.month));
      
      // Calculate month-over-month changes
      for (let i = 0; i < monthlyData.length; i++) {
        if (i > 0) {
          const currentRevenue = monthlyData[i].revenue;
          const prevRevenue = monthlyData[i-1].revenue;
          const change = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;
          changeData.push({
            month: monthlyData[i].month,
            change: change
          });
        } else {
          changeData.push({
            month: monthlyData[i].month,
            change: 0
          });
        }
      }
      
      // Actual data (past months)
      actualData = monthlyData.map((item, index) => {
        if (item.month <= currentMonth) {
          return item.revenue;
        }
        return null; // Will create a gap in the chart
      });
      
      // Calculate trend based on the last 6 months or available data
      const pastMonthsData = monthlyData.filter(d => d.month <= currentMonth);
      const trend = this.calculateTrend(pastMonthsData.map(d => d.revenue));
      
      // Forecast data (future months + current)
      forecastData = monthlyData.map((item, index) => {
        if (item.month < currentMonth) {
          return null; // Only show forecast for current & future
        }
        
        // For current month, use actual if available, otherwise forecast
        if (item.month === currentMonth) {
          const forecastValue = this.forecastValue(pastMonthsData, trend, 0);
          return item.revenue || forecastValue;
        }
        
        // Future months
        const monthsAhead = item.month - currentMonth;
        const forecastValue = this.forecastValue(pastMonthsData, trend, monthsAhead);
        return forecastValue;
      });
    } else {
      // Yearly view - show multiple years
      const yearlyData = this.prepareYearlyData(ordersToUse, currentYear - 4, currentYear + 1);
      xAxisLabels = yearlyData.map(d => d.year.toString());
      
      // Calculate year-over-year changes
      for (let i = 0; i < yearlyData.length; i++) {
        if (i > 0) {
          const currentRevenue = yearlyData[i].revenue;
          const prevRevenue = yearlyData[i-1].revenue;
          const change = prevRevenue > 0 ? ((currentRevenue - prevRevenue) / prevRevenue) * 100 : 0;
          changeData.push({
            year: yearlyData[i].year,
            change: change
          });
        } else {
          changeData.push({
            year: yearlyData[i].year,
            change: 0
          });
        }
      }
      
      // All historical years are actual data
      actualData = yearlyData.map((item) => {
        if (item.year <= currentYear) {
          return item.revenue;
        }
        return null;
      });
      
      // Forecast is only for next year
      const pastYearsData = yearlyData.filter(d => d.year <= currentYear);
      const trend = this.calculateTrend(pastYearsData.map(d => d.revenue));
      
      forecastData = yearlyData.map((item, index) => {
        if (item.year <= currentYear) {
          return null;
        }
        const yearsAhead = item.year - currentYear;
        const forecastValue = this.forecastValue(pastYearsData, trend, yearsAhead);
        return forecastValue;
      });
    }
    
    // Calculate totals for KPI indicators
    const totalRevenue = ordersToUse
      .filter(order => {
        const orderDate = new Date(order.orderDate || order.date);
        return orderDate.getFullYear() === currentYear;
      })
      .reduce((sum, order) => sum + parseFloat(order.orderTotal || 0), 0);
    
    const totalOrders = ordersToUse
      .filter(order => {
        const orderDate = new Date(order.orderDate || order.date);
        return orderDate.getFullYear() === currentYear;
      }).length;
    
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Compare with previous year data
    const prevYearOrders = ordersToUse.filter(order => {
      const orderDate = new Date(order.orderDate || order.date);
      return orderDate.getFullYear() === currentYear - 1;
    });
    
    const prevYearRevenue = prevYearOrders.reduce((sum, order) => sum + parseFloat(order.orderTotal || 0), 0);
    const prevYearOrderCount = prevYearOrders.length;
    
    const revenueTrend = prevYearRevenue > 0 ? ((totalRevenue - prevYearRevenue) / prevYearRevenue) * 100 : 0;
    const ordersTrend = prevYearOrderCount > 0 ? ((totalOrders - prevYearOrderCount) / prevYearOrderCount) * 100 : 0;
    
    // Prepare annotations for changes if we are in weekly or monthly view
    const annotations = {
      yaxis: [],
      xaxis: [],
      points: [{
        x: '10%',
        y: '90%',
        marker: {
          size: 0
        },
        label: {
          borderColor: '#ffffff00',
          offsetY: 20,
          style: {
            color: '#111827',
            background: '#ffffff00',
            fontSize: '14px',
            fontWeight: 'bold'
          },
          text: `Total Revenue: ${this.currencyFormatter.format(totalRevenue)} (${revenueTrend >= 0 ? '+' : ''}${revenueTrend.toFixed(1)}% YoY) | Orders: ${totalOrders} (${ordersTrend >= 0 ? '+' : ''}${ordersTrend.toFixed(1)}% YoY) | AOV: ${this.currencyFormatter.format(avgOrderValue)}`
        }
      }]
    };
    
    // Calculate which index is the current period
    let currentPeriodIndex = 0;
    if (timeGranularity === 'weekly') {
      currentPeriodIndex = currentWeek - 1;
    } else if (timeGranularity === 'monthly') {
      currentPeriodIndex = currentMonth;
    } else {
      // For yearly, find the index of the current year
      currentPeriodIndex = xAxisLabels.findIndex(year => parseInt(year) === currentYear);
    }
    
    // Prepare the continuous line series
    const continuousLineData = actualData.map((value, index) => {
      if (index <= currentPeriodIndex) {
        return value; // Use actual data for past and current periods
      } else {
        return forecastData[index]; // Use forecast data for future periods
      }
    });
    
    // Prepare annotations for significant changes (more than 20%)
    changeData.forEach((item, index) => {
      if (Math.abs(item.change) >= 20 && index > 0 && actualData[index] !== null) {
        annotations.points.push({
          x: xAxisLabels[index],
          y: actualData[index],
          marker: {
            size: 6,
            fillColor: item.change >= 0 ? '#10b981' : '#ef4444',
            strokeColor: '#fff',
            radius: 2
          },
          label: {
            borderColor: item.change >= 0 ? '#10b981' : '#ef4444',
            style: {
              color: '#fff',
              background: item.change >= 0 ? '#10b981' : '#ef4444',
              fontSize: '12px',
              fontWeight: 'bold'
            },
            text: `${item.change >= 0 ? '+' : ''}${item.change.toFixed(1)}%`
          }
        });
      }
    });
    
    // Create chart with multiple visualizations
    const options = {
      series: [
        {
          name: 'Revenue',
          type: 'column',
          data: actualData
        },
        {
          name: 'Forecast',
          type: 'line',
          data: forecastData
        },
        {
          name: 'Trend',
          type: 'line',
          data: continuousLineData
        }
      ],
      chart: {
        height: 380,
        type: 'line',
        stacked: false,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF',
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        }
      },
      stroke: {
        width: [0, 3, 2],
        curve: 'smooth',
        dashArray: [0, 5, 0]
      },
      plotOptions: {
        bar: {
          columnWidth: '70%',
          borderRadius: 4
        }
      },
      colors: ['#3b82f6', '#94a3b8', '#60a5fa'],
      fill: {
        opacity: [1, 0.5, 0.1],
        gradient: {
          inverseColors: false,
          shade: 'light',
          type: "vertical",
          opacityFrom: 0.85,
          opacityTo: 0.55,
          stops: [0, 100]
        }
      },
      markers: {
        size: [0, 4, 0],
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: xAxisLabels,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      yaxis: {
        title: {
          text: 'Revenue',
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        },
        labels: {
          formatter: (val) => {
            return this.currencyFormatter.format(val);
          },
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
          }
        }
      },
      tooltip: {
        shared: true,
        intersect: false,
        y: {
          formatter: function(y, { seriesIndex, dataPointIndex, w }) {
            if (y === null || y === undefined) return '-';
            const formattedValue = this.currencyFormatter.format(y);
            
            // Add change percentage for actual data
            if (seriesIndex === 0 && dataPointIndex > 0) {
              const change = changeData[dataPointIndex]?.change;
              if (change !== undefined && !isNaN(change)) {
                return `${formattedValue} (${change >= 0 ? '+' : ''}${change.toFixed(1)}% vs prev)`;
              }
            }
            
            return formattedValue;
          }.bind(this)
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '14px',
        offsetY: -10
      },
      annotations: annotations
    };

    // Destroy existing chart if it exists
    if (this.charts.mainKpi) {
      try {
        this.charts.mainKpi.destroy();
      } catch (error) {
        console.warn('Error destroying main KPI chart:', error);
      }
    }
    
    try {
      // Create new chart
      this.charts.mainKpi = new ApexCharts(chartElement, options);
      this.charts.mainKpi.render();
    } catch (error) {
      console.error('Error rendering Main KPI chart:', error);
      chartElement.innerHTML = `<div class="flex items-center justify-center h-full"><p class="text-center text-red-500 p-4">Error rendering chart: ${error.message}</p></div>`;
    }
  }
  
  // Helper to get week number in year
  getWeekNumber(date) {
    const target = new Date(date.valueOf());
    const dayNr = (date.getDay() + 6) % 7;
    target.setDate(target.getDate() - dayNr + 3);
    const firstThursday = target.valueOf();
    target.setMonth(0, 1);
    if (target.getDay() !== 4) {
      target.setMonth(0, 1 + ((4 - target.getDay()) + 7) % 7);
    }
    return 1 + Math.ceil((firstThursday - target) / 604800000);
  }
  
  // Helper to get month name
  getMonthName(monthIndex) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[monthIndex];
  }
  
  // Prepare weekly data for a year
  prepareWeeklyDataForYear(orders, year) {
    // Create array for 52 weeks
    const weeklyData = Array.from({ length: 53 }, (_, i) => ({
      week: i + 1,
      revenue: 0,
      orderCount: 0
    }));
    
    // Filter orders for the specified year
    const yearOrders = orders.filter(order => {
      const orderDate = new Date(order.orderDate || order.date);
      return orderDate.getFullYear() === year;
    });
    
    // Group data by week
    yearOrders.forEach(order => {
      const orderDate = new Date(order.orderDate || order.date);
      const weekNumber = this.getWeekNumber(orderDate);
      
      if (weekNumber <= 53) {
        weeklyData[weekNumber - 1].revenue += parseFloat(order.orderTotal || 0);
        weeklyData[weekNumber - 1].orderCount += 1;
      }
    });
    
    return weeklyData;
  }
  
  // Prepare monthly data for a year
  prepareMonthlyDataForYear(orders, year) {
    // Create array for 12 months
    const monthlyData = Array.from({ length: 12 }, (_, i) => ({
      month: i,
      revenue: 0,
      orderCount: 0
    }));
    
    // Filter orders for the specified year
    const yearOrders = orders.filter(order => {
      const orderDate = new Date(order.orderDate || order.date);
      return orderDate.getFullYear() === year;
    });
    
    // Group data by month
    yearOrders.forEach(order => {
      const orderDate = new Date(order.orderDate || order.date);
      const monthIndex = orderDate.getMonth();
      
      monthlyData[monthIndex].revenue += parseFloat(order.orderTotal || 0);
      monthlyData[monthIndex].orderCount += 1;
    });
    
    return monthlyData;
  }
  
  // Prepare yearly data
  prepareYearlyData(orders, startYear, endYear) {
    // Create array for the year range
    const yearlyData = [];
    for (let year = startYear; year <= endYear; year++) {
      yearlyData.push({
        year,
        revenue: 0,
        orderCount: 0
      });
    }
    
    // Group data by year
    orders.forEach(order => {
      const orderDate = new Date(order.orderDate || order.date);
      const year = orderDate.getFullYear();
      
      if (year >= startYear && year <= endYear) {
        const yearIndex = year - startYear;
        yearlyData[yearIndex].revenue += parseFloat(order.orderTotal || 0);
        yearlyData[yearIndex].orderCount += 1;
      }
    });
    
    return yearlyData;
  }
  
  // Calculate trend for forecasting
  calculateTrend(data) {
    // Filter out null values
    const validData = data.filter(val => val !== null && val !== undefined);
    if (validData.length < 2) return 0;
    
    // Find the overall average
    const overallAvg = validData.reduce((sum, val) => sum + val, 0) / validData.length;
    
    // Calculate simple linear regression using only the last 8 periods (or fewer if not available)
    // This makes the forecast more responsive to recent data
    const recentData = validData.slice(-8);
    
    let sumX = 0;
    let sumY = 0;
    let sumXY = 0;
    let sumXX = 0;
    
    for (let i = 0; i < recentData.length; i++) {
      sumX += i;
      sumY += recentData[i];
      sumXY += i * recentData[i];
      sumXX += i * i;
    }
    
    const count = recentData.length;
    let slope = (count * sumXY - sumX * sumY) / (count * sumXX - sumX * sumX);
    
    // Calculate average of last 3 periods to determine if trend is reasonable
    const lastThreeAvg = recentData.slice(-3).reduce((sum, val) => sum + val, 0) / Math.min(recentData.length, 3);
    
    // Find the highest value in the dataset to use as a reference point
    const maxValue = Math.max(...validData);
    
    // If trend is negative, reduce its impact significantly
    if (slope < 0) {
      // Limit negative trend to at most 3% decrease per period
      const maxNegativeSlope = -0.03 * lastThreeAvg;
      slope = Math.max(slope, maxNegativeSlope);
      
      // Further dampen the negative trend (more aggressive damping than before)
      slope = slope * 0.2;
    } else {
      // For positive trends, boost them slightly for optimism
      slope = slope * 1.1;
    }
    
    // Find highest value in the first half of the data
    const firstHalfMax = Math.max(...validData.slice(0, Math.floor(validData.length / 2)));
    
    // If recent values are lower than historical highs, add recovery factor
    if (lastThreeAvg < firstHalfMax * 0.8 && firstHalfMax > 0) {
      // Add recovery factor to slope - pushes forecast to gradually return to historical highs
      const recoveryFactor = (firstHalfMax - lastThreeAvg) / (firstHalfMax * 10);
      slope += recoveryFactor;
    }
    
    return slope;
  }
  
  // Forecast value based on trend
  forecastValue(historicalData, trend, periodsAhead) {
    // Filter out null values
    const validData = historicalData
      .map(d => typeof d === 'object' ? d.revenue : d)
      .filter(val => val !== null && val !== undefined);
      
    if (validData.length === 0) return 0;
    
    // Calculate basic statistics for better forecasting
    const avg = validData.reduce((sum, val) => sum + val, 0) / validData.length;
    const max = Math.max(...validData);
    const min = Math.min(...validData);
    const recent = validData.slice(-Math.min(validData.length, 4));
    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    
    // Calculate seasonality factor if we have enough data
    let seasonalityFactor = 1;
    if (validData.length >= 12) {
      // Compare current period to same period last year
      const currentPeriodIndex = validData.length - 1;
      const lastYearPeriodIndex = currentPeriodIndex - 12;
      
      if (lastYearPeriodIndex >= 0 && validData[lastYearPeriodIndex] > 0) {
        seasonalityFactor = validData[currentPeriodIndex] / validData[lastYearPeriodIndex];
        // Limit extreme values
        seasonalityFactor = Math.max(0.8, Math.min(seasonalityFactor, 1.5));
      }
    }
    
    // Get a weighted base value that emphasizes recent data but includes the overall average
    // This helps to stabilize the forecast
    const baseValue = (recentAvg * 0.7) + (avg * 0.3);
    
    // Apply trend with seasonality adjustment for near-term forecast
    let forecast = baseValue + (trend * periodsAhead * seasonalityFactor);
    
    // For long-term forecasts (more than 10 periods ahead), gradually incorporate growth
    // This prevents long downtrends and introduces optimism for the future
    if (periodsAhead > 10) {
      // Calculate a growth factor that increases with distance
      const growthFactor = 0.01 * (periodsAhead - 10); // 1% per period beyond 10
      
      // Blend the trend-based forecast with a growth-based forecast
      // The further in the future, the more we rely on the growth model
      const blendingFactor = Math.min((periodsAhead - 10) / 20, 0.8); // Max 80% blending
      
      // Growth model starts from the 10-period forecast and adds growth
      const trendForecast10 = baseValue + (trend * 10 * seasonalityFactor);
      const growthForecast = trendForecast10 * Math.pow(1 + growthFactor, periodsAhead - 10);
      
      // Blend the forecasts
      forecast = (forecast * (1 - blendingFactor)) + (growthForecast * blendingFactor);
    }
    
    // For shorter forecasts, make sure we don't drop below minimum threshold
    // but for longer periods, we could potentially drop lower temporarily before recovery
    const minThreshold = Math.max(min, baseValue * 0.3);
    forecast = Math.max(forecast, minThreshold);
    
    // Set ceiling based on historical maximum (with room for growth)
    const ceiling = max * 1.5; // Allow up to 50% growth beyond historical maximum
    forecast = Math.min(forecast, ceiling);
    
    return forecast;
  }
  
  // Helper method to show error message
  showErrorMessage(message) {
    const contentContainer = document.getElementById('salesTabContent');
    if (contentContainer) {
      contentContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">
                ${message}
              </p>
            </div>
          </div>
        </div>
      `;
    }
  }

  renderSalesTrendChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Prepare data for sales trend chart
    const trendData = this.prepareTrendData(ordersToUse, this.chartTimeRanges.salesTrend);
    
    const options = {
      series: [
        {
          name: 'Revenue',
          data: trendData.map(item => item.total)
        }
      ],
      chart: {
        type: 'area',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        }
      },
      tooltip: {
        enabled: true,
        theme: 'light', // Always light theme
        x: {
          format: this.getDateFormat(this.chartTimeRanges.salesTrend)
        },
        y: {
          formatter: (val) => this.currencyFormatter.format(val)
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      markers: {
        size: 4,
        colors: undefined,
        strokeColors: '#fff',
        strokeWidth: 2,
        strokeOpacity: 0.9,
        strokeDashArray: 0,
        fillOpacity: 1,
        discrete: [],
        shape: "circle",
        radius: 2,
        offsetX: 0,
        offsetY: 0,
        showNullDataPoints: true,
        hover: {
          size: undefined,
          sizeOffset: 3
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.6,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      colors: this.chartColors.primary,
      xaxis: {
        categories: trendData.map(item => item.date),
        labels: {
          style: {
            colors: '#6b7280' // Consistent text color
          }
        },
        tooltip: {
          enabled: false
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          formatter: (val) => this.currencyFormatter.format(val),
          style: {
            colors: '#6b7280' // Consistent text color
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        },
        padding: {
          top: 5,
          right: 10,
          bottom: 5,
          left: 10
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesTrend) {
      this.charts.salesTrend.destroy();
    }
    
    // Create new chart
    this.charts.salesTrend = new ApexCharts(document.getElementById('sales-trend-chart'), options);
    this.charts.salesTrend.render();
  }

  renderSalesByStatusChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Group sales by status
    const salesByStatus = {};
    let totalSales = 0;
    
    ordersToUse.forEach(order => {
      const status = order.status || 'Unknown';
      const orderTotal = parseFloat(order.orderTotal) || 0;
      
      if (!salesByStatus[status]) {
        salesByStatus[status] = 0;
      }
      salesByStatus[status] += orderTotal;
      totalSales += orderTotal;
    });
    
    const statusLabels = Object.keys(salesByStatus);
    const statusValues = Object.values(salesByStatus);
    
    const options = {
      series: statusValues,
      chart: {
        type: 'donut',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        animations: {
          enabled: true
        }
      },
      labels: statusLabels,
      colors: this.chartColors.status,
      legend: {
        position: 'bottom',
        fontSize: '14px',
        fontFamily: 'Inter, sans-serif',
        fontWeight: 400,
        labels: {
          colors: '#4b5563'
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5
        },
      },
      dataLabels: {
        enabled: false
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => this.currencyFormatter.format(val),
          title: {
            formatter: (seriesName) => seriesName,
          },
        },
        marker: {
          show: true,
        },
        items: {
          display: 'flex',
        },
        fixed: {
          enabled: false,
          position: 'topRight',
          offsetX: 0,
          offsetY: 0,
        },
        custom: ({ series, seriesIndex, dataPointIndex, w }) => {
          const label = w.globals.labels[seriesIndex];
          const value = series[seriesIndex];
          const percentage = totalSales > 0 ? ((value / totalSales) * 100).toFixed(1) : 0;
          const formattedValue = this.currencyFormatter.format(value);
          
          return `<div class="bg-white text-gray-800 shadow-lg rounded-md p-2 text-sm">
                    <strong>${label}:</strong> 
                    <span class="text-black font-semibold">${formattedValue}</span> 
                    (${percentage}%)
                  </div>`;
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '75%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '18px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 600,
                color: '#6b7280',
                offsetY: -10,
              },
              value: {
                show: true,
                fontSize: '24px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 700,
                color: '#111827',
                offsetY: 10,
          formatter: (val) => this.currencyFormatter.format(val)
              },
              total: {
                show: true,
                showAlways: true,
                label: 'Total Revenue',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 500,
                color: '#6b7280',
                formatter: (w) => {
                  const total = w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                  return this.currencyFormatter.format(total);
                }
              }
            }
          }
        }
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            height: 320
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesStatus) {
      this.charts.salesStatus.destroy();
    }
    
    // Create new chart
    this.charts.salesStatus = new ApexCharts(document.getElementById('sales-status-chart'), options);
    this.charts.salesStatus.render();
  }

  renderTopCustomersChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders || [];
    
    // Calculate sales by customer
    const salesByCustomer = {};
    let totalSales = 0;
    
    // Process order to calculate sales by customer
    ordersToUse.forEach(order => {
      const customerId = order.customerID || 'Unknown';
      const customerName = order.customerName || this.customerLookup[customerId] || `Customer ${customerId}`;
      const orderTotal = parseFloat(order.orderTotal || 0);
      
      totalSales += orderTotal;
      
      if (!salesByCustomer[customerId]) {
        salesByCustomer[customerId] = {
          id: customerId,
          name: customerName,
          sales: 0,
          orderCount: 0
        };
      }
      
      salesByCustomer[customerId].sales += orderTotal;
      salesByCustomer[customerId].orderCount += 1;
    });
    
    // Convert to array and sort by sales
    const topCustomers = Object.values(salesByCustomer)
      .sort((a, b) => b.sales - a.sales)
      .slice(0, 10);
    
    // Handle case where there are no customers
    if (topCustomers.length === 0) {
      console.log("No customer data to display in Top Customers chart.");
      const chartElement = document.getElementById('top-customers-chart');
      if (chartElement) {
        // Destroy existing chart if it exists
        if (this.charts.topCustomers) {
          this.charts.topCustomers.destroy();
          this.charts.topCustomers = null; // Clear reference
        }
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-center text-gray-500 p-4">No customer data available for this period. Try selecting a different time range.</p></div>';
      }
      
      // Use mock data if enabled
      if (this.useMockData) {
        console.log("Using mock customer data as fallback.");
        // Generate some mock customers
        for (let i = 0; i < 5; i++) {
          topCustomers.push({
            id: `CUST${i+1}`,
            name: `Customer ${i+1}`,
            sales: Math.random() * 10000 + 5000,
            orderCount: Math.floor(Math.random() * 10) + 1
          });
        }
      } else {
        return;
      }
    }
    
    // Prepare chart data
    const customerNames = topCustomers.map(customer => customer.name);
    const salesValues = topCustomers.map(customer => customer.sales);
    
    const options = {
      series: [{
        name: 'Revenue',
        data: salesValues
      }],
      chart: {
        type: 'bar',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: true,
          borderRadius: 4,
          barHeight: '60%',
          dataLabels: {
            position: 'top'
          }
        }
      },
      colors: [this.chartColors.primary[0]],
      dataLabels: {
        enabled: true,
        textAnchor: 'start',
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, sans-serif',
          colors: ['#374151']
        },
        formatter: (val) => this.currencyFormatter.format(val),
        offsetX: 10
      },
      xaxis: {
        categories: customerNames,
        labels: {
          formatter: (val) => this.currencyFormatter.format(val),
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
            colors: '#4b5563'
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => this.currencyFormatter.format(val),
          title: {
            formatter: (seriesName) => 'Revenue'
          }
        },
        custom: ({ series, seriesIndex, dataPointIndex }) => {
          const customer = topCustomers[dataPointIndex];
          if (!customer) return ''; // Safety check
          
          return `
            <div class="p-2 bg-white dark:bg-gray-800 shadow-md rounded-md">
              <div class="text-sm font-medium text-gray-800 dark:text-gray-200">${customer.name}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Revenue: ${this.currencyFormatter.format(customer.sales)}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">Orders: ${customer.orderCount}</div>
            </div>
          `;
        }
      },
      theme: {
        mode: 'light'
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.topCustomers) {
      this.charts.topCustomers.destroy();
    }
    
    // Create new chart
    this.charts.topCustomers = new ApexCharts(document.getElementById('top-customers-chart'), options);
    this.charts.topCustomers.render();
  }

  renderOrderCountTrendChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Prepare trend data
    const trendData = this.prepareTrendData(ordersToUse, this.chartTimeRanges.orderCountTrend);
    
    const options = {
      series: [
        {
          name: 'Orders',
          data: trendData.map(item => item.count)
        }
      ],
      chart: {
        type: 'line',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        },
        animations: {
          enabled: true,
          easing: 'easeinout',
          speed: 800,
        }
      },
      tooltip: {
        enabled: true,
        theme: 'light', // Always light theme
        x: {
          format: this.getDateFormat(this.chartTimeRanges.orderCountTrend)
        },
        y: {
          formatter: (val) => `${Math.round(val)} orders`
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      markers: {
        size: 4,
        colors: undefined,
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      colors: this.chartColors.accent,
      xaxis: {
        categories: trendData.map(item => item.date),
        labels: {
          style: {
            colors: '#6b7280' // Consistent text color
          }
        },
        tooltip: {
          enabled: false
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          formatter: (val) => Math.floor(val),
          style: {
            colors: '#6b7280' // Consistent text color
          }
        },
        title: {
          text: 'Number of Orders',
          style: {
            color: '#6b7280',
            fontSize: '12px',
            fontWeight: 400,
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        },
        padding: {
          top: 5,
          right: 10,
          bottom: 5,
          left: 10
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.orderCountTrend) {
      this.charts.orderCountTrend.destroy();
    }
    
    // Create new chart
    this.charts.orderCountTrend = new ApexCharts(document.getElementById('order-count-trend-chart'), options);
    this.charts.orderCountTrend.render();
  }

  prepareTrendData(orders, timeRange) {
    // Handle empty or invalid orders
    if (!orders || !Array.isArray(orders) || orders.length === 0) {
      console.log("No orders data to prepare trend");
      return [];
    }
    
    const now = new Date();
    let intervalCount, intervalType, formatString;
    
    // Set interval and format based on time range
    switch (timeRange) {
      case '7d':
      case 'weekly':
        intervalCount = 7;
        intervalType = 'day';
        formatString = this.getDateFormat('7d');
        break;
      case '1m':
      case 'monthly':
        intervalCount = 30;
        intervalType = 'day';
        formatString = this.getDateFormat('1m');
        break;
      case '1y':
      case 'yearly':
        intervalCount = 12;
        intervalType = 'month';
        formatString = this.getDateFormat('1y');
        break;
      default:
        intervalCount = 7;
        intervalType = 'day';
        formatString = this.getDateFormat('7d');
    }
    
    // Generate array of dates for the intervals
    const intervals = [];
    let currentDate = new Date();
      
      if (intervalType === 'day') {
      // For daily intervals, go back intervalCount days and then iterate forward
      currentDate.setDate(currentDate.getDate() - intervalCount + 1);
      currentDate.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < intervalCount; i++) {
        intervals.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }
      } else if (intervalType === 'month') {
      // For monthly intervals, go back intervalCount months and then iterate forward
      currentDate.setMonth(currentDate.getMonth() - intervalCount + 1);
      currentDate.setDate(1);
      currentDate.setHours(0, 0, 0, 0);
      
      for (let i = 0; i < intervalCount; i++) {
        intervals.push(new Date(currentDate));
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }
    
    // Initialize result with dates and zero values
    const result = intervals.map(date => ({
        date: this.formatDate(date, formatString),
      total: 0,
      count: 0,
      units: 0,
      profit: 0
    }));
    
    // Sort orders by date
    const sortedOrders = [...orders].sort((a, b) => {
      return new Date(a.orderDate || a.date) - new Date(b.orderDate || b.date);
    });
    
    // Fill in sales data
    sortedOrders.forEach(order => {
      const orderDate = new Date(order.orderDate || order.date);
      
      // Find which interval this order belongs to
      let intervalIndex = -1;
      
      if (intervalType === 'day') {
        // For daily intervals, find the exact day
        intervalIndex = intervals.findIndex(interval => 
          this.isSameDay(interval, orderDate)
        );
      } else if (intervalType === 'month') {
        // For monthly intervals, find the month
        intervalIndex = intervals.findIndex(interval => 
          this.isSameMonth(interval, orderDate)
        );
      }
      
      // If the order fits within our date range
      if (intervalIndex >= 0) {
        const orderTotal = parseFloat(order.orderTotal || 0);
        const orderProfit = parseFloat(order.estimatedProfit || 0);
        
        // Add order data to the corresponding interval
        result[intervalIndex].total += orderTotal;
        result[intervalIndex].count += 1;
        
        // Add units and profit if available
        let units = 0;
        if (order.details && Array.isArray(order.details)) {
          order.details.forEach(detail => {
            units += parseFloat(detail.quantity || 1);
          });
        }
        
        result[intervalIndex].units += units;
        result[intervalIndex].profit += orderProfit;
      }
    });
    
    return result;
  }

  isSameDay(date1, date2) {
    return date1.getDate() === date2.getDate() && 
           date1.getMonth() === date2.getMonth() && 
           date1.getFullYear() === date2.getFullYear();
  }

  isSameMonth(date1, date2) {
    return date1.getMonth() === date2.getMonth() && 
           date1.getFullYear() === date2.getFullYear();
  }

  formatDate(date, format) {
    // Simple date formatter, you could use a full-featured library like date-fns in a real app
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return format
      .replace('MMM', month)
      .replace('dd', day)
      .replace('yyyy', year);
  }

  getDateFormat(timeRange) {
    switch (timeRange) {
      case 'weekly':
      case 'monthly':
        return 'MMM dd';
      case 'yearly':
        return 'MMM yyyy';
      default:
        return 'MMM dd';
    }
  }

  isDarkMode() {
    // Simple dark mode detection based on document class or media query
    return document.documentElement.classList.contains('dark') || 
           window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  // Mock data generators for testing
  generateMockSalesOrderData() {
    const mockOrders = [];
    const statuses = ['Open', 'Closed', 'Cancelled', 'On Hold', 'Shipped', 'Pending Payment'];
    const customers = ['CUST001', 'CUST002', 'CUST003', 'CUST004', 'CUST005', 'CUST006', 'CUST007', 'CUST008'];
    
    // Generate orders from past year to now with higher concentration in recent months
    const now = new Date();
    const oneYearAgo = new Date(now);
    oneYearAgo.setFullYear(now.getFullYear() - 1);
    
    for (let i = 0; i < 200; i++) {
      // Create a weighted random date to have more recent orders
      const orderDate = this.getRandomDate(oneYearAgo, now, 2);
      
      // Create random order total between $100 and $5000
      const orderTotal = (Math.random() * 4900 + 100).toFixed(2);
      
      mockOrders.push({
        id: `ORD${String(i+1).padStart(5, '0')}`,
        orderDate: orderDate.toISOString(),
        orderTotal: orderTotal,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        customerID: customers[Math.floor(Math.random() * customers.length)]
      });
    }
    
    return mockOrders;
  }

  generateMockCustomerData() {
    return [
      { id: 'CUST001', companyName: 'Acme Corporation', firstName: 'John', lastName: 'Doe' },
      { id: 'CUST002', companyName: 'Globex Solutions', firstName: 'Jane', lastName: 'Smith' },
      { id: 'CUST003', companyName: 'Umbrella Industries', firstName: 'Robert', lastName: 'Johnson' },
      { id: 'CUST004', companyName: 'Wayne Enterprises', firstName: 'Bruce', lastName: 'Wayne' },
      { id: 'CUST005', companyName: 'Stark Industries', firstName: 'Tony', lastName: 'Stark' },
      { id: 'CUST006', companyName: 'Cyberdyne Systems', firstName: 'Sarah', lastName: 'Connor' },
      { id: 'CUST007', companyName: 'Weyland-Yutani Corp', firstName: 'Ellen', lastName: 'Ripley' },
      { id: 'CUST008', companyName: 'Oscorp Industries', firstName: 'Norman', lastName: 'Osborn' }
    ];
  }

  generateMockProductData() {
    // This might still be useful if order details processing fails, but primary source is now order details
    console.warn("Using mock product data as fallback.")
    return [
      { id: 'PRD001', name: 'Premium Laptop', sales: 35750.00 },
      { id: 'PRD002', name: 'Ultra HD Monitor', sales: 28900.50 },
      { id: 'PRD003', name: 'Wireless Earbuds', sales: 23500.75 },
      { id: 'PRD004', name: 'Gaming Console', sales: 19250.25 },
      { id: 'PRD005', name: 'Smart Watch', sales: 17800.00 },
      { id: 'PRD006', name: 'Tablet Pro', sales: 15400.50 },
      { id: 'PRD007', name: 'Office Chair', sales: 12200.00 },
      { id: 'PRD008', name: 'Desk Lamp', sales: 9850.75 }
    ];
  }

  // Helper method to create a weighted random date
  getRandomDate(startDate, endDate, weight = 1) {
    // Weight > 1 makes dates closer to the end date more likely
    const randomFactor = Math.pow(Math.random(), weight);
    const timeDiff = endDate.getTime() - startDate.getTime();
    const weightedTime = timeDiff * randomFactor;
    return new Date(endDate.getTime() - weightedTime);
  }

  showLoading() {
    const loadingElement = document.getElementById('analytics-loading');
    if (loadingElement) {
      loadingElement.classList.remove('hidden');
    }
  }

  hideLoading() {
    const loadingElement = document.getElementById('analytics-loading');
    if (loadingElement) {
      loadingElement.classList.add('hidden');
    }
  }

  renderSalesVsTargetChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders || [];
    
    // If no orders, show empty state
    if (!ordersToUse.length) {
      console.log("No data to display in Sales vs Target chart.");
      // Optionally display a message in the chart area
      const chartElement = document.getElementById('sales-vs-target-chart');
      if (chartElement) {
        // Destroy existing chart if it exists
        if (this.charts.salesVsTarget) {
          this.charts.salesVsTarget.destroy();
          this.charts.salesVsTarget = null; // Clear reference
        }
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-center text-gray-500 p-4">No sales data available for this period. Try selecting a different time range.</p></div>';
      }
      return;
    }
    
    // Prepare trend data for sales vs target
    const timeRange = this.chartTimeRanges.salesVsTarget;
    const trendData = this.prepareTrendData(ordersToUse, timeRange);
    
    // Make sure we have at least one data point
    if (!trendData || !Array.isArray(trendData) || trendData.length === 0) {
      console.log("No trend data available for Sales vs Target chart.");
      const chartElement = document.getElementById('sales-vs-target-chart');
      if (chartElement) {
        if (this.charts.salesVsTarget) {
          this.charts.salesVsTarget.destroy();
          this.charts.salesVsTarget = null;
        }
        chartElement.innerHTML = '<div class="flex items-center justify-center h-full"><p class="text-center text-gray-500 p-4">Insufficient data for Sales vs Target chart.</p></div>';
      }
      return;
    }
    
    // Set realistic target based on time range
    // For example, target could be 20% higher than current sales for yearly data
    // and 30% higher for monthly and weekly data
    const multiplier = timeRange === '1y' ? 1.2 : 1.3;
    const minTargetYearly = 25000; // Minimum yearly target
    const minTargetMonthly = 2500; // Minimum monthly target
    const minTargetWeekly = 500;  // Minimum weekly target
    
    let minTarget;
    if (timeRange === '1y') {
      minTarget = minTargetYearly;
    } else if (timeRange === '1m') {
      minTarget = minTargetMonthly;
    } else {
      minTarget = minTargetWeekly;
    }
    
    // Create target data array
    const salesData = trendData.map(point => point.total);
    const targetData = salesData.map(sale => Math.max(sale * multiplier, minTarget));
    
    // Calculate chart dates
    const chartLabels = trendData.map(point => point.date);
    
    // Set up chart options
    const options = {
      series: [
        {
          name: 'Actual Sales',
          data: salesData
        },
        {
          name: 'Target Sales',
          data: targetData
        }
      ],
      chart: {
        type: 'bar',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        animations: {
          enabled: true
        },
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 4
        }
      },
      colors: ['#3b82f6', '#d1d5db'],
      dataLabels: {
        enabled: false
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '14px',
        markers: {
          width: 12,
          height: 12,
          radius: 12
        },
        itemMargin: {
          horizontal: 10
        }
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: chartLabels,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Revenue (USD)',
          style: {
            fontSize: '14px',
            fontFamily: 'Inter, sans-serif',
            fontWeight: 500,
            color: '#4b5563'
          }
        },
        labels: {
          formatter: (val) => this.currencyFormatter.format(val),
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif',
            colors: '#4b5563'
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => this.currencyFormatter.format(val)
        },
        marker: {
          show: false
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesVsTarget) {
      this.charts.salesVsTarget.destroy();
    }
    
    // Create new chart
    this.charts.salesVsTarget = new ApexCharts(document.getElementById('sales-vs-target-chart'), options);
    this.charts.salesVsTarget.render();
  }

  renderProfitMarginChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Prepare trend data
    const trendData = this.prepareTrendData(ordersToUse, this.chartTimeRanges.profitMargin);
    
    // Calculate profit margins as percentages
    const profitMargins = trendData.map(item => {
      // Avoid division by zero
      if (item.total === 0) return 0;
      // Return profit margin as a decimal (e.g., 0.30 for 30%)
      return item.profit / item.total;
    });
    
    const options = {
      series: [{
        name: 'Profit Margin',
        data: profitMargins
      }],
      chart: {
        type: 'area',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        },
        animations: {
          enabled: true
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        curve: 'smooth',
        width: 2
      },
      colors: [this.chartColors.secondary[0]],
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.6,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      markers: {
        size: 4,
        colors: undefined,
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: trendData.map(item => item.date),
        labels: {
          style: {
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        labels: {
          formatter: (val) => this.percentFormatter.format(val),
          style: {
            colors: '#6b7280'
          }
        },
        title: {
          text: 'Profit Margin (%)',
          style: {
            color: '#6b7280',
            fontSize: '12px'
          }
        },
        min: 0,
        max: profitMargins.length > 0 ? Math.max(...profitMargins) * 1.2 : 0.5
      },
      tooltip: {
        theme: 'light',
        x: {
          format: this.getDateFormat(this.chartTimeRanges.profitMargin)
        },
        y: {
          formatter: (val) => this.percentFormatter.format(val)
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        },
        padding: {
          top: 5,
          right: 10,
          bottom: 5,
          left: 10
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.profitMargin) {
      this.charts.profitMargin.destroy();
    }
    
    // Create new chart
    this.charts.profitMargin = new ApexCharts(document.getElementById('profit-margin-chart'), options);
    this.charts.profitMargin.render();
  }

  renderUnitsByRegionChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Extract regions from the orders
    // In a real application, these would come from actual order data
    // Here we're creating sample regions based on branch or location data
    const unitsByRegion = {};
    
    ordersToUse.forEach(order => {
      // Try to extract region from branch, location, or fallback to a default
      let region = 'Unknown';
      
      if (order.branch) {
        region = order.branch;
      } else if (order.location) {
        region = order.location;
      }
      
      // For demonstration, if we don't have real regions, create some based on customer ID
      if (region === 'Unknown' && order.customerID) {
        // Assign one of 4 regions based on the customerID
        const customerId = order.customerID.toString();
        const lastChar = customerId.charAt(customerId.length - 1);
        const charCode = lastChar.charCodeAt(0);
        
        if (charCode % 4 === 0) region = 'North';
        else if (charCode % 4 === 1) region = 'South';
        else if (charCode % 4 === 2) region = 'East';
        else region = 'West';
      }
      
      // Count units from order details
      let units = 0;
      if (order.details && Array.isArray(order.details)) {
        units = order.details.reduce((sum, item) => sum + (parseFloat(item.quantity) || 0), 0);
      } else {
        // If no details, estimate units based on order total
        units = Math.round((parseFloat(order.orderTotal) || 0) / 100);
      }
      
      if (!unitsByRegion[region]) {
        unitsByRegion[region] = 0;
      }
      
      unitsByRegion[region] += units;
    });
    
    // Prepare chart data
    const regions = Object.keys(unitsByRegion);
    const unitCounts = Object.values(unitsByRegion);
    
    const options = {
      series: [{
        name: 'Units Sold',
        data: unitCounts
      }],
      chart: {
        type: 'bar',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        },
        animations: {
          enabled: true
        }
      },
      plotOptions: {
        bar: {
          distributed: true, // Different colors for each bar
          borderRadius: 4,
          dataLabels: {
            position: 'top'
          }
        }
      },
      colors: this.chartColors.primary.concat(this.chartColors.secondary, this.chartColors.accent),
      dataLabels: {
        enabled: true,
        formatter: (val) => this.numberFormatter.format(val),
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#4b5563']
        }
      },
      xaxis: {
        categories: regions,
        position: 'bottom',
        labels: {
          style: {
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Units Sold',
          style: {
            color: '#6b7280',
            fontSize: '12px'
          }
        },
        labels: {
          formatter: (val) => this.numberFormatter.format(val),
          style: {
            colors: '#6b7280'
          }
        }
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => `${this.numberFormatter.format(val)} units`
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        yaxis: {
          lines: {
            show: true
          }
        },
        padding: {
          top: 20,
          right: 10,
          bottom: 5,
          left: 10
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.unitsByRegion) {
      this.charts.unitsByRegion.destroy();
    }
    
    // Create new chart
    this.charts.unitsByRegion = new ApexCharts(document.getElementById('units-by-region-chart'), options);
    this.charts.unitsByRegion.render();
  }

  renderRevenueGrowthChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Prepare trend data
    const trendData = this.prepareTrendData(ordersToUse, this.chartTimeRanges.revenueGrowth);
    
    // Calculate revenue growth percentages
    const growthData = [];
    for (let i = 1; i < trendData.length; i++) {
      const prevTotal = trendData[i-1].total;
      const currentTotal = trendData[i].total;
      
      let growthPercent = 0;
      if (prevTotal > 0) {
        growthPercent = ((currentTotal - prevTotal) / prevTotal);
      }
      
      growthData.push({
        date: trendData[i].date,
        growth: growthPercent
      });
    }
    
    const options = {
      series: [{
        name: 'Revenue Growth',
        data: growthData.map(item => item.growth)
      }],
      chart: {
        type: 'bar',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        toolbar: {
          show: false
        },
        animations: {
          enabled: true
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          columnWidth: '60%',
          colors: {
            ranges: [{
              from: -1,
              to: 0,
              color: '#ef4444' // Red for negative growth
            }]
          }
        }
      },
      colors: [this.chartColors.secondary[0]],
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: growthData.map(item => item.date),
        labels: {
          style: {
            colors: '#6b7280'
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Growth Rate',
          style: {
            color: '#6b7280',
            fontSize: '12px'
          }
        },
        labels: {
          formatter: (val) => this.percentFormatter.format(val),
          style: {
            colors: '#6b7280'
          }
        }
      },
      tooltip: {
        theme: 'light',
        y: {
          formatter: (val) => this.percentFormatter.format(val)
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        strokeDashArray: 4,
        yaxis: {
          lines: {
            show: true
          }
        },
        padding: {
          top: 5,
          right: 10,
          bottom: 5,
          left: 10
        }
      },
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.revenueGrowth) {
      this.charts.revenueGrowth.destroy();
    }
    
    // Create new chart
    this.charts.revenueGrowth = new ApexCharts(document.getElementById('revenue-growth-chart'), options);
    this.charts.revenueGrowth.render();
  }

  renderSalesConversionChart(filteredOrders = null) {
    // Use provided filtered orders or global filtered orders
    const ordersToUse = filteredOrders || this.filteredSalesOrders;
    
    // Group orders by status and calculate conversion rates
    // For this visualization, we'll create a donut chart showing the 
    // percentage of orders in each status (as a measure of conversion)
    
    const ordersByStatus = {};
    let totalOrders = 0;
    
    ordersToUse.forEach(order => {
      const status = order.status || 'Unknown';
      
      if (!ordersByStatus[status]) {
        ordersByStatus[status] = 0;
      }
      ordersByStatus[status] += 1;
      totalOrders += 1;
    });
    
    const statusLabels = Object.keys(ordersByStatus);
    const statusValues = Object.values(ordersByStatus);
    
    const options = {
      series: statusValues,
      chart: {
        type: 'donut',
        height: 288,
        fontFamily: 'Inter, sans-serif',
        background: '#FFFFFF', // White background
        animations: {
          enabled: true
        }
      },
      labels: statusLabels,
      colors: this.chartColors.status,
      legend: {
        position: 'bottom',
        fontSize: '14px',
        fontFamily: 'Inter, sans-serif',
        fontWeight: 400,
        labels: {
          colors: '#4b5563'
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5
        },
      },
      dataLabels: {
        enabled: true,
        formatter: (val, opt) => {
          return `${Math.round(val)}%`;
        }
      },
      tooltip: {
        theme: 'light',
        custom: ({ series, seriesIndex, dataPointIndex, w }) => {
          const label = w.globals.labels[seriesIndex];
          const count = series[seriesIndex];
          const percentage = ((count / totalOrders) * 100).toFixed(1);
          return `<div class="bg-white text-gray-800 shadow-lg rounded-md p-2 text-sm">
                    <strong>${label}:</strong> 
                    <span class="text-black font-semibold">${count} orders</span> 
                    (${percentage}%)
                  </div>`;
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 600,
                color: '#6b7280'
              },
              value: {
                show: true,
                fontSize: '18px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 700,
                color: '#111827',
                formatter: (val) => `${val} orders`
              },
              total: {
                show: true,
                showAlways: true,
                label: 'Total Orders',
                fontSize: '14px',
                fontFamily: 'Inter, sans-serif',
                fontWeight: 500,
                color: '#6b7280',
                formatter: (w) => totalOrders
              }
            }
          }
        }
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            height: 320
          },
          legend: {
            position: 'bottom'
          }
        }
      }],
      theme: {
        mode: 'light' // Always use light mode
      }
    };
    
    // Destroy existing chart if it exists
    if (this.charts.salesConversion) {
      this.charts.salesConversion.destroy();
    }
    
    // Create new chart
    this.charts.salesConversion = new ApexCharts(document.getElementById('sales-conversion-chart'), options);
    this.charts.salesConversion.render();
  }

  async getCustomersFromDB(db) {
    return new Promise((resolve, reject) => {
      try {
        if (!db) {
          console.warn('Database is null in getCustomersFromDB');
          resolve([]);
          return;
        }
        
        if (!db.objectStoreNames.contains('customers')) {
          console.warn('Customers store not found in DB');
          resolve([]);
          return;
        }
        
        const transaction = db.transaction(['customers'], 'readonly');
        const store = transaction.objectStore('customers');
        const request = store.getAll();
        
        request.onsuccess = () => {
          const customers = request.result || [];
          console.log(`Found ${customers.length} customers in database`);
          
          // Debug log to check customer data format
          if (customers.length > 0) {
            const sampleCustomer = customers[0];
            console.log('Sample customer data:', JSON.stringify({
              id: sampleCustomer.id,
              customerID: sampleCustomer.customerID,
              name: sampleCustomer.name,
              displayName: sampleCustomer.displayName,
              companyName: sampleCustomer.companyName,
              customerName: sampleCustomer.customerName
            }));
          }
          
          resolve(customers);
        };
        
        request.onerror = (error) => {
          console.error('Error in getCustomersFromDB request:', error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
      } catch (error) {
        console.error('Error in getCustomersFromDB:', error);
        resolve([]); // Resolve with empty array instead of rejecting
      }
    });
  }

  async openDatabase() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, 2);
      
      request.onerror = (event) => {
        console.error('Error opening database:', event.target.error);
        resolve(null);
      };
      
      request.onsuccess = (event) => {
        resolve(event.target.result);
      };
    });
  }

  // Get filtered data for a specific time range
  getFilteredData(timeRange) {
    console.log(`Filtering data for time range: ${timeRange}`);
    
    if (!this.salesOrders || !this.salesOrders.length) {
      console.warn('No sales orders data available for filtering');
      return [];
    }
    
    // Apply time filter to data
    const now = new Date();
    now.setHours(23, 59, 59, 999); // Set to end of current day
    
    let startDate = new Date(now);
    
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        startDate.setHours(0, 0, 0, 0); // Start of day 7 days ago
        break;
      case '1m':
        startDate.setMonth(now.getMonth() - 1);
        startDate.setHours(0, 0, 0, 0); // Start of day 1 month ago
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        startDate.setHours(0, 0, 0, 0); // Start of day 1 year ago
        break;
      default:
        startDate.setDate(now.getDate() - 7); // Default to 7 days
        startDate.setHours(0, 0, 0, 0); // Start of day
    }
    
    console.log(`Filtering orders from ${startDate.toISOString()} to ${now.toISOString()}`);
    
    // Filter sales orders data
    return this.salesOrders.filter(order => {
      if (!order.orderDate && !order.date) {
        console.warn('Order missing date:', order);
        return false;
      }
      
      let orderDate;
      try {
        orderDate = new Date(order.orderDate || order.date);
        // Skip invalid dates
        if (isNaN(orderDate.getTime())) {
          console.warn('Order has invalid date:', order);
          return false;
        }
      } catch (e) {
        console.warn('Error parsing order date:', e, order);
        return false;
      }
      
      return orderDate >= startDate && orderDate <= now;
    });
  }
  
  // Wrapper method for getFilteredData that's more explicit for chart filtering
  getDataForTimeRange(timeRange) {
    return this.getFilteredData(timeRange);
  }
  
  // Get data for previous time period (for comparison)
  getPreviousPeriodData(timeRange) {
    if (!this.salesOrders || !this.salesOrders.length) {
      console.warn('No sales orders data available for filtering previous period');
      return [];
    }
    
    const now = new Date();
    now.setHours(23, 59, 59, 999); // End of current day
    
    let startDate, endDate;
    
    switch (timeRange) {
      case '7d':
      case 'prev7d':
        // Previous 7 days
        endDate = new Date(now);
        endDate.setDate(now.getDate() - 7);
        endDate.setHours(23, 59, 59, 999); // End of day 7 days ago
        
        startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0); // Start of day 14 days ago
        break;
      case '1m':
      case 'prev1m':
        // Previous month
        endDate = new Date(now);
        endDate.setMonth(now.getMonth() - 1);
        endDate.setHours(23, 59, 59, 999); // End of day 1 month ago
        
        startDate = new Date(endDate);
        startDate.setMonth(endDate.getMonth() - 1);
        startDate.setHours(0, 0, 0, 0); // Start of day 2 months ago
        break;
      case '1y':
      case 'prev1y':
        // Previous year
        endDate = new Date(now);
        endDate.setFullYear(now.getFullYear() - 1);
        endDate.setHours(23, 59, 59, 999); // End of day 1 year ago
        
        startDate = new Date(endDate);
        startDate.setFullYear(endDate.getFullYear() - 1);
        startDate.setHours(0, 0, 0, 0); // Start of day 2 years ago
        break;
      default:
        // Default to previous 7 days
        endDate = new Date(now);
        endDate.setDate(now.getDate() - 7);
        endDate.setHours(23, 59, 59, 999); // End of day 7 days ago
        
        startDate = new Date(endDate);
        startDate.setDate(endDate.getDate() - 7);
        startDate.setHours(0, 0, 0, 0); // Start of day 14 days ago
    }
    
    console.log(`Filtering previous period orders from ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Filter sales orders data for previous period
    return this.salesOrders.filter(order => {
      if (!order.orderDate && !order.date) {
        return false;
      }
      
      let orderDate;
      try {
        orderDate = new Date(order.orderDate || order.date);
        // Skip invalid dates
        if (isNaN(orderDate.getTime())) {
          return false;
        }
      } catch (e) {
        return false;
      }
      
      return orderDate >= startDate && orderDate < endDate;
    });
  }
} 