// Helper functions for Purchase Analytics components
// Specifically for the Purchase By Vendors chart

/**
 * Adapts purchase order data for the Purchase By Vendors chart
 * Groups POs by vendor and date, allowing for bundled vertical bar display
 * @param {Array} data - Purchase order data
 * @param {String} timeRange - Time range filter (7d, 30d, 1y)
 * @return {Object} Formatted data for ApexCharts
 */
export function adaptDataForPurchaseByVendors(data, timeRange) {
  console.log(`Adapting Purchase By Vendors data with ${data.length} records for timeRange: ${timeRange}`);
  
  if (!data || data.length === 0) {
    return {
      series: [],
      dates: []
    };
  }

  // Sort data by createdAt date
  const sortedData = [...data].sort((a, b) => {
    return new Date(a.createdAt) - new Date(b.createdAt);
  });

  // Group POs by date and vendor
  const posByDateAndVendor = {};
  const vendorIds = new Set();
  
  // Process each PO
  sortedData.forEach(po => {
    if (!po.createdAt) return;
    
    const date = new Date(po.createdAt);
    date.setHours(0, 0, 0, 0);
    const dateStr = date.toISOString().split('T')[0];
    
    if (!posByDateAndVendor[dateStr]) {
      posByDateAndVendor[dateStr] = {};
    }
    
    const vendorId = po.vendorId || 'unknown';
    vendorIds.add(vendorId);
    
    if (!posByDateAndVendor[dateStr][vendorId]) {
      posByDateAndVendor[dateStr][vendorId] = {
        count: 0,
        total: 0,
        pos: []
      };
    }
    
    // Add this PO to the collection
    posByDateAndVendor[dateStr][vendorId].count += 1;
    const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
    posByDateAndVendor[dateStr][vendorId].total += amount;
    posByDateAndVendor[dateStr][vendorId].pos.push({
      id: po.id,
      amount: amount,
      number: po.number || 'N/A'
    });
  });

  // Filter dates based on timeRange
  const now = new Date();
  let cutoffDate;
  
  switch (timeRange) {
    case '7d':
      cutoffDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      break;
    case '30d':
      cutoffDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      break;
    case '1y':
      cutoffDate = new Date(now.getTime() - (365 * 24 * 60 * 60 * 1000));
      break;
    default:
      cutoffDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
  }
  
  cutoffDate.setHours(0, 0, 0, 0);
  
  // Filter and prepare data for chart
  const filteredDates = Object.keys(posByDateAndVendor)
    .filter(dateStr => new Date(dateStr) >= cutoffDate)
    .sort((a, b) => new Date(a) - new Date(b));
  
  // Convert vendor IDs to array for series
  const vendorIdsArray = Array.from(vendorIds);
  
  // Prepare series data
  const series = vendorIdsArray.map(vendorId => {
    return {
      name: vendorId,
      data: filteredDates.map(dateStr => {
        if (posByDateAndVendor[dateStr][vendorId]) {
          return {
            x: dateStr,
            y: posByDateAndVendor[dateStr][vendorId].count,
            poCount: posByDateAndVendor[dateStr][vendorId].count,
            total: posByDateAndVendor[dateStr][vendorId].total,
            pos: posByDateAndVendor[dateStr][vendorId].pos
          };
        }
        return {
          x: dateStr,
          y: 0,
          poCount: 0,
          total: 0,
          pos: []
        };
      })
    };
  });

  // Format dates for display
  const formattedDates = filteredDates.map(dateStr => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  });

  return {
    series: series,
    dates: formattedDates,
    rawDates: filteredDates
  };
}

/**
 * Resolves vendor names from IDs using the vendor lookup
 * @param {Array} series - Chart series data
 * @param {Object} vendorLookup - Lookup object mapping vendor IDs to names
 * @return {Array} Updated series with vendor names
 */
export function resolveVendorNames(series, vendorLookup) {
  return series.map(vendorSeries => {
    const vendorName = vendorLookup[vendorSeries.name] || `Vendor ${vendorSeries.name}`;
    return {
      ...vendorSeries,
      name: vendorName
    };
  });
}

/**
 * Generates a tooltip formatter function for the Purchase By Vendors chart
 * @param {Function} currencyFormatter - Function to format currency values
 * @return {Function} Tooltip formatter function for ApexCharts
 */
export function getPurchaseByVendorsTooltipFormatter(currencyFormatter) {
  return function(seriesData) {
    const dataPoint = seriesData.w.config.series[seriesData.seriesIndex].data[seriesData.dataPointIndex];
    
    if (!dataPoint) {
      return '';
    }
    
    const vendorName = seriesData.w.config.series[seriesData.seriesIndex].name;
    const dateStr = dataPoint.x;
    const poCount = dataPoint.poCount || 0;
    const total = dataPoint.total || 0;
    
    // Create list of POs if available
    let poList = '';
    if (dataPoint.pos && dataPoint.pos.length > 0) {
      poList = '<div class="mt-2"><ul class="list-disc pl-4">';
      dataPoint.pos.forEach(po => {
        poList += `<li>PO ${po.number || po.id || 'N/A'}: ${currencyFormatter(po.amount)}</li>`;
      });
      poList += '</ul></div>';
    }
    
    return `
      <div class="px-2 py-1">
        <div class="font-bold">${vendorName}</div>
        <div class="text-xs">${dateStr}</div>
        <div class="mt-1">
          <div>POs: <span class="font-semibold">${poCount}</span></div>
          <div>Total: <span class="font-semibold">${currencyFormatter(total)}</span></div>
        </div>
        ${poList}
      </div>
    `;
  };
}

// ----- NEW FUNCTIONS FOR VENDOR WEEKDAY BREAKDOWN CHART -----

/**
 * Adapts purchase order data for the Vendor Weekday Breakdown chart.
 * Filters data for the last 7 days (excluding weekends) and aggregates PO amounts by vendor and weekday.
 * @param {Array} data - Array of purchase order objects. Each object must have `date`, `vendorId`, `convertedTotal`, `orderNbr`.
 * @param {Object} vendorLookup - Object mapping vendorId to vendor name.
 * @param {String} timeRange - Time range ('7d' expected, others might default to '7d').
 * @returns {Object} Formatted data for ApexCharts: { series: [], categories: [] }.
 */
export function adaptDataForVendorWeekdayBreakdown(data, vendorLookup, timeRange = '7d') {
  console.log(`Adapting data for Vendor Weekday Breakdown (${timeRange}) with ${data.length} POs`);
  const output = { series: [], categories: [] };
  
  if (!data || data.length === 0) {
    console.warn("No data provided for Vendor Weekday Breakdown");
    return output;
  }

  // Helper: Checks if input is a valid UTC midnight Date object
  const isValidUTCDate = (d) => d instanceof Date && !isNaN(d.getTime());

  // Helper: Checks if a date is a weekend (Saturday or Sunday) using UTC day
  const isWeekend = (date) => {
    if (!isValidUTCDate(date)) return true; // Treat invalid dates as weekends to exclude
    const day = date.getUTCDay(); // 0 = Sunday, 6 = Saturday
    return day === 0 || day === 6;
  };

  // 1. Determine the date range (last 7 days ending 'today')
  const now = new Date();
  now.setUTCHours(0, 0, 0, 0); // Normalize 'now' to UTC midnight

  const endDate = new Date(now); // Today (UTC midnight)
  const startDate = new Date(now);
  startDate.setUTCDate(now.getUTCDate() - 6); // Start 7 days ago (inclusive of today)

  console.log(`Filtering for weekdays between ${startDate.toISOString()} and ${endDate.toISOString()}`);

  // 2. Filter data for the last 7 days and exclude weekends
  const filteredData = data.filter(po => {
    const poDate = po.date; // Assumes po.date is already a UTC midnight Date object
    
    if (!isValidUTCDate(poDate)) {
        console.warn(`Skipping PO with invalid date: ${po.id || po.orderNbr}`);
        return false;
    }

    const isInRange = poDate.getTime() >= startDate.getTime() && poDate.getTime() <= endDate.getTime();
    const isNotWeekend = !isWeekend(poDate);

    return isInRange && isNotWeekend;
  });

  console.log(`Found ${filteredData.length} POs within the last 7 weekdays.`);

  if (filteredData.length === 0) {
    return output; // No data to display
  }

  // 3. Aggregate data by Weekday and Vendor
  const aggregation = {}; // Structure: { 'YYYY-MM-DD': { 'vendorId': { total: 0, pos: [] } } }
  const allVendorIds = new Set();
  const allWeekdays = new Set(); // Store 'YYYY-MM-DD' strings

  filteredData.forEach(po => {
    const dateStr = po.date.toISOString().split('T')[0];
    const vendorId = po.vendorId || 'unknown';
    const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
    const poNumber = po.orderNbr || po.id || 'N/A'; // Use orderNbr, fallback to id

    allVendorIds.add(vendorId);
    allWeekdays.add(dateStr);

    if (!aggregation[dateStr]) {
      aggregation[dateStr] = {};
    }
    if (!aggregation[dateStr][vendorId]) {
      aggregation[dateStr][vendorId] = { total: 0, pos: [] };
    }

    aggregation[dateStr][vendorId].total += amount;
    aggregation[dateStr][vendorId].pos.push({ number: poNumber, amount: amount });
  });

  // 4. Prepare data for ApexCharts
  const sortedWeekdays = Array.from(allWeekdays).sort(); // Sort dates chronologically
  const sortedVendorIds = Array.from(allVendorIds).sort(); // Sort vendors alphabetically by ID

  output.categories = sortedWeekdays.map(dateStr => {
    // Format date as "Mon Mar 31"
    const date = new Date(`${dateStr}T00:00:00.000Z`);
    return date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', timeZone: 'UTC' });
  });

  output.series = sortedVendorIds.map(vendorId => {
    const vendorName = vendorLookup[vendorId]?.name || `Vendor ${vendorId}`; // Use lookup name or fallback
    
    const dataPoints = sortedWeekdays.map(dateStr => {
      const dayData = aggregation[dateStr];
      const vendorData = dayData ? dayData[vendorId] : null;
      
      return {
        x: dateStr, // Keep original date string for potential linking
        y: vendorData ? vendorData.total : 0,
        pos: vendorData ? vendorData.pos : [] // Include PO details for tooltip
      };
    });

    return {
      name: vendorName,
      data: dataPoints
    };
  });
  
  console.log("Adapted data for Vendor Weekday Breakdown:", output);
  return output;
}

/**
 * Creates a tooltip formatter for the Vendor Weekday Breakdown chart.
 * Shows Vendor Name, Date, Total Amount, and list of POs for that vendor/day.
 * @param {Function} currencyFormatter - Function to format currency values.
 * @returns {Function} Tooltip formatter function for ApexCharts.
 */
export function getVendorWeekdayTooltipFormatter(currencyFormatter) {
  // Use a cache for formatted currency values to improve performance
  const formattedValueCache = new Map();

  return ({ series, seriesIndex, dataPointIndex, w }) => {
    try {
      const category = w.globals.labels[dataPointIndex]; // e.g., "Mon Mar 31"
      const seriesName = w.globals.seriesNames[seriesIndex]; // Vendor Name
      const dataPoint = w.config.series[seriesIndex]?.data[dataPointIndex];

      if (!dataPoint || dataPoint.y === undefined || dataPoint.y === null) {
        return ''; // No data for this point
      }

      const totalAmount = dataPoint.y;
      const pos = dataPoint.pos || []; // Array of { number: 'PO-123', amount: 100 }

      // Format total amount using cache
      let formattedTotal;
      const cacheKeyTotal = `total_${totalAmount}`;
      if (formattedValueCache.has(cacheKeyTotal)) {
        formattedTotal = formattedValueCache.get(cacheKeyTotal);
      } else {
        formattedTotal = currencyFormatter(totalAmount);
        if (formattedValueCache.size < 100) { // Limit cache size
          formattedValueCache.set(cacheKeyTotal, formattedTotal);
        }
      }

      let poListHtml = '';
      if (pos.length > 0) {
        poListHtml = '<div class="mt-2"><ul class="list-disc pl-4 text-xs">';
        // Limit displayed POs for performance
        const maxPOsToShow = 5;
        pos.slice(0, maxPOsToShow).forEach(po => {
          // Format individual PO amount using cache
          let formattedPOAmount;
          const cacheKeyPO = `po_${po.amount}`;
          if (formattedValueCache.has(cacheKeyPO)) {
            formattedPOAmount = formattedValueCache.get(cacheKeyPO);
          } else {
            formattedPOAmount = currencyFormatter(po.amount);
            if (formattedValueCache.size < 100) { // Limit cache size
              formattedValueCache.set(cacheKeyPO, formattedPOAmount);
            }
          }
          poListHtml += `<li class="mb-1">PO ${po.number}: <span class="font-medium">${formattedPOAmount}</span></li>`;
        });
        if (pos.length > maxPOsToShow) {
          poListHtml += `<li class="text-gray-400">+ ${pos.length - maxPOsToShow} more POs</li>`;
        }
        poListHtml += '</ul></div>';
      }

      return `
        <div class="px-3 py-2 bg-gray-800 text-white rounded-md shadow-lg text-sm">
          <div class="font-bold text-sm border-b border-gray-700 pb-1 mb-1">${seriesName}</div>
          <div class="text-xs text-gray-300 mb-1">${category}</div>
          <div class="flex items-center">
            <span>Total:</span>
            <span class="font-semibold ml-2">${formattedTotal}</span>
          </div>
          ${pos.length > 0 ? 
            `<div class="text-xs mt-1">
              <span class="text-gray-300">Purchase Orders: <span class="text-white font-medium">${pos.length}</span></span>
            </div>` : ''}
          ${poListHtml}
        </div>
      `;
    } catch (error) {
      console.error("Error in Vendor Weekday tooltip formatter:", error);
      return '<div class="p-1 text-xs text-red-400">Error generating tooltip</div>';
    }
  };
}

/**
 * Creates chart options for the Vendor Weekday Breakdown chart.
 * Configures a grouped bar chart showing PO amounts per vendor per weekday.
 * @param {Object} chartData - Adapted data from adaptDataForVendorWeekdayBreakdown.
 * @param {Function} currencyFormatter - Function to format currency values.
 * @param {Array<String>} colors - Array of color strings for vendors.
 * @returns {Object} ApexCharts options object.
 */
export function createVendorWeekdayChartOptions(chartData, currencyFormatter, colors) {
  const defaultColors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#6b7280'];
  const chartColors = colors && colors.length > 0 ? colors : defaultColors;

  // Find min and max values for better scaling
  let minValue = Infinity;
  let maxValue = 0;
  const allValues = [];
  
  if (chartData.series && chartData.series.length > 0) {
    chartData.series.forEach(series => {
      series.data.forEach(point => {
        if (point.y > 0) {
          if (point.y < minValue) minValue = point.y;
          if (point.y > maxValue) maxValue = point.y;
          allValues.push(point.y);
        }
      });
    });
  }
  
  // If no valid data points, set reasonable defaults
  if (minValue === Infinity) minValue = 0;
  if (maxValue === 0) maxValue = 1000;
  
  // Calculate the ratio between max and min
  const ratio = maxValue / Math.max(minValue, 1);
  
  // Determine if we should use logarithmic scale
  const useLogarithmic = ratio > 100;
  
  // Find a good minimum y-axis value to ensure small values are visible
  // Different approach depending on scaling type
  let yAxisMin = 0;
  
  if (!useLogarithmic && ratio > 20) {
    // For linear scale with high ratio, ensure small values have minimum visibility
    // Ensure minimum bar height is at least ~10% of the chart
    yAxisMin = Math.max(0, minValue * 0.5);
  }
  
  console.log(`Chart scaling - Min: ${minValue}, Max: ${maxValue}, Ratio: ${ratio}, Using logarithmic: ${useLogarithmic}`);
  
  return {
    series: chartData.series || [],
    chart: {
      type: 'bar',
      height: 440,
      stacked: false,
      toolbar: {
        show: false
      },
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 450,
        dynamicAnimation: {
          enabled: true,
          speed: 350
        }
      },
      fontFamily: 'inherit',
      background: 'transparent',
      sparkline: {
        enabled: false
      },
      parentHeightOffset: 0,
      offsetX: 0,
      offsetY: 0,
      width: '100%',
      redrawOnParentResize: true,
      redrawOnWindowResize: true,
      margin: {
        left: 5,
        right: 5
      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%', // Even thinner columns for better spacing
        endingShape: 'rounded',
        borderRadius: 4,
        dataLabels: {
          position: 'top',
          maxItems: 100
        },
        distributed: false,
        rangeBarOverlap: false,
        rangeBarGroupRows: false
      },
    },
    dataLabels: {
      enabled: true,
      formatter: function (val) {
        if (val <= 0) return '';
        
        // Special handling for very small values
        if (val < 10) {
          return currencyFormatter(val);
        }
        else if (val >= 1000000) {
          return currencyFormatter(val / 1000000) + 'M';
        }
        else if (val >= 1000) {
          return currencyFormatter(val / 1000) + 'K';
        }
        return currencyFormatter(val);
      },
      offsetY: -28, // Move labels higher above bars
      style: {
        fontSize: '11px',
        fontWeight: '600',
        fontFamily: 'inherit',
        colors: ["#374151"]
      },
      background: {
        enabled: true, // Add background for clarity
        foreColor: '#374151',
        padding: 4,
        borderRadius: 2,
        borderWidth: 1,
        borderColor: 'transparent',
        opacity: 0.1,
        dropShadow: {
          enabled: false
        }
      },
      textAnchor: 'middle'
    },
    stroke: {
      show: true,
      width: 1,
      colors: ['transparent']
    },
    xaxis: {
      categories: chartData.categories || [],
      labels: {
        style: {
          fontSize: '11px',
          fontFamily: 'inherit',
          fontWeight: '400',
          cssClass: 'text-gray-600 dark:text-gray-400'
        },
        rotate: -45,
        offsetY: 4,
        hideOverlappingLabels: true,
        trim: false
      },
      title: {
        text: '',
        offsetY: 80,
        style: {
          fontSize: '13px',
          fontWeight: '600',
          fontFamily: 'inherit',
          color: '#64748b'
        }
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      },
      crosshairs: {
        show: true,
        width: 1,
        position: 'back',
        opacity: 0.2,
        stroke: {
          color: '#6b7280',
          width: 1,
          dashArray: 0
        }
      }
    },
    yaxis: {
      title: {
        text: 'Purchase Amount',
        style: {
          fontSize: '11px',
          fontWeight: '600',
          fontFamily: 'Helvetica, Arial, sans-serif',
          color: '#373d3f'
        },
        offsetX: -7,
        offsetY: 0,
        rotate: -90
      },
      min: yAxisMin,
      forceNiceScale: !useLogarithmic,
      labels: {
        formatter: function (val) {
          if (val === 0) return '0';
          if (useLogarithmic && val < 1) return currencyFormatter(val);
          // Use shorter formatting for large numbers
          if (val >= 1000) {
            return '$' + (val/1000).toFixed(1) + 'K';
          }
          return currencyFormatter(val);
        },
        style: {
          fontSize: '11px',
          fontFamily: 'inherit',
          fontWeight: '500',
          cssClass: 'text-gray-600 dark:text-gray-400'
        },
        offsetX: 0,
        minWidth: 0,
        maxWidth: 60
      },
      floating: false,
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: true,
        color: '#e2e8f0'
      },
      logarithmic: useLogarithmic,
      logBase: 10,
      tickAmount: 5
    },
    colors: chartColors,
    fill: {
      opacity: 0.9, // Slightly more opaque for better visibility
      type: 'solid'
    },
    legend: {
      position: 'bottom',
      horizontalAlign: 'center',
      fontSize: '10px',
      fontFamily: 'inherit',
      fontWeight: 'normal',
      labels: {
        colors: '#64748b',
        useSeriesColors: false
      },
      markers: {
        width: 10,
        height: 10,
        radius: 5,
        offsetX: 0
      },
      itemMargin: {
        horizontal: 8,
        vertical: 3
      },
      height: 60,
      width: '100%',
      formatter: function(legendName, opts) {
        // Truncate very long vendor names
        if (legendName.length > 25) {
          return legendName.substring(0, 22) + '...';
        }
        return legendName;
      },
      onItemClick: {
        toggleDataSeries: true
      }
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: true,
      custom: getVendorWeekdayTooltipFormatter(currencyFormatter),
      theme: 'dark',
      marker: {
        show: true
      }
    },
    grid: {
      show: true,
      borderColor: '#e2e8f0',
      strokeDashArray: 0, // Solid lines to match other charts
      position: 'back',
      xaxis: {
        lines: {
          show: false
        }
      },
      yaxis: {
        lines: {
          show: true
        }
      },
      row: {
        colors: undefined,
        opacity: 0.5
      },
      column: {
        colors: undefined,
        opacity: 0.5
      },
      padding: {
        top: 30, // For label space
        right: 8,
        bottom: 10,
        left: 0 // Completely eliminate left padding
      }
    },
    noData: {
      text: 'No purchase orders found for the selected period.',
      align: 'center',
      verticalAlign: 'middle',
      offsetY: 0,
      style: {
        color: '#64748b',
        fontSize: '14px',
        fontFamily: 'inherit'
      }
    },
    states: {
      hover: {
        filter: {
          type: 'darken',
          value: 0.08
        }
      },
      active: {
        filter: {
          type: 'darken',
          value: 0.12
        },
        allowMultipleDataPointsSelection: false
      }
    },
    responsive: [
      {
        breakpoint: 640,
        options: {
          chart: {
            height: 350
          },
          legend: {
            position: 'bottom',
            offsetY: 10
          }
        }
      }
    ]
  };
}

/**
 * Adapts purchase order data for the Purchase Orders by Top 10 Vendors chart
 * @param {Array} data - Purchase order data
 * @param {String} timeRange - Time range filter (7d, 30d, 1y)
 * @param {Object} vendorLookup - Object mapping vendor IDs to vendor names
 * @return {Object} Formatted data for ApexCharts
 */
export function adaptDataForTop10Vendors(data, timeRange, vendorLookup) {
  console.log(`Adapting Top 10 Vendors data with ${data.length} records for timeRange: ${timeRange}`);
  
  if (!data || data.length === 0) {
    return {
      series: [],
      categories: [],
      totalsByVendor: {}
    };
  }

  // Filter data by time range
  const now = new Date();
  let cutoffDate;
  
  switch (timeRange) {
    case '7d':
      cutoffDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      break;
    case '30d':
      cutoffDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
      break;
    case '1y':
      cutoffDate = new Date(now.getTime() - (365 * 24 * 60 * 60 * 1000));
      break;
    default:
      cutoffDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
  }
  
  cutoffDate.setHours(0, 0, 0, 0);
  
  // Filter data by time range
  const filteredData = data.filter(po => {
    const poDate = po.date;
    return poDate && poDate instanceof Date && !isNaN(poDate.getTime()) && poDate >= cutoffDate;
  });
  
  if (filteredData.length === 0) {
    return {
      series: [],
      categories: [],
      totalsByVendor: {}
    };
  }
  
  // Initialize vendor totals & PO collections
  const vendorData = {};
  
  // Calculate total for each vendor within the time range
  filteredData.forEach(po => {
    const vendorId = po.vendorId || 'unknown';
    const vendorName = vendorLookup[vendorId]?.name || `Vendor ${vendorId}`;
    const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
    const poNumber = po.orderNbr || po.id || 'N/A';
    
    if (!vendorData[vendorId]) {
      vendorData[vendorId] = {
        id: vendorId,
        name: vendorName,
        total: 0,
        pos: []
      };
    }
    
    vendorData[vendorId].total += amount;
    vendorData[vendorId].pos.push({
      number: poNumber,
      amount: amount,
      date: po.date
    });
  });
  
  // Get top 10 vendors by total amount
  const top10Vendors = Object.values(vendorData)
    .sort((a, b) => b.total - a.total)
    .slice(0, 10);
  
  // Create categories (vendor names) and series data
  const categories = top10Vendors.map(vendor => vendor.name);
  
  // Create a single series for all vendors
  const series = [{
    name: 'Purchase Amount',
    data: top10Vendors.map(vendor => ({
      x: vendor.name,
      y: vendor.total,
      id: vendor.id,
      pos: vendor.pos
    }))
  }];
  
  // Period label based on time range
  let periodLabel = '';
  switch (timeRange) {
    case '7d': periodLabel = 'Weekly'; break;
    case '30d': periodLabel = 'Monthly'; break;
    case '1y': periodLabel = 'Yearly'; break;
    default: periodLabel = 'Weekly';
  }
  
  return {
    series: series,
    categories: categories,
    totalsByVendor: top10Vendors.reduce((acc, vendor) => {
      acc[vendor.id] = vendor;
      return acc;
    }, {}),
    periodLabel: periodLabel
  };
}

/**
 * Generates a tooltip formatter function for the Top 10 Vendors chart
 * @param {Function} currencyFormatter - Function to format currency values
 * @return {Function} Tooltip formatter function for ApexCharts
 */
export function getTop10VendorsTooltipFormatter(currencyFormatter) {
  // Cache for formatted values
  const formattedValueCache = new Map();
  
  return ({ series, seriesIndex, dataPointIndex, w }) => {
    try {
      const vendorName = w.globals.labels[dataPointIndex]; // Vendor name from categories
      const dataPoint = w.config.series[seriesIndex]?.data[dataPointIndex];
      
      if (!dataPoint || dataPoint.y === 0) {
        return ''; // No data for this point
      }

      const totalAmount = dataPoint.y;
      const pos = dataPoint.pos || []; // Array of PO objects
      
      // Format amount using cache
      let formattedTotal;
      const cacheKey = `total_${totalAmount}`;
      if (formattedValueCache.has(cacheKey)) {
        formattedTotal = formattedValueCache.get(cacheKey);
      } else {
        formattedTotal = currencyFormatter(totalAmount);
        if (formattedValueCache.size < 100) {
          formattedValueCache.set(cacheKey, formattedTotal);
        }
      }
      
      // Format dates using a helper
      const formatDate = (date) => {
        if (!date) return '';
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      };
      
      // Create PO list
      let poListHtml = '';
      if (pos.length > 0) {
        poListHtml = '<div class="mt-2 max-h-32 overflow-y-auto"><ul class="list-disc pl-4 text-xs">';
        // Sort POs by amount (largest first)
        const sortedPos = [...pos].sort((a, b) => b.amount - a.amount);
        const maxToShow = 5;
        
        sortedPos.slice(0, maxToShow).forEach(po => {
          // Format PO amount using cache
          let formattedAmount;
          const amountCacheKey = `po_${po.amount}`;
          if (formattedValueCache.has(amountCacheKey)) {
            formattedAmount = formattedValueCache.get(amountCacheKey);
          } else {
            formattedAmount = currencyFormatter(po.amount);
            if (formattedValueCache.size < 100) {
              formattedValueCache.set(amountCacheKey, formattedAmount);
            }
          }
          
          const dateStr = formatDate(po.date);
          poListHtml += `<li class="mb-1">PO ${po.number}: <span class="font-medium">${formattedAmount}</span>${dateStr ? ` (${dateStr})` : ''}</li>`;
        });
        
        if (sortedPos.length > maxToShow) {
          poListHtml += `<li class="text-gray-400">+ ${sortedPos.length - maxToShow} more POs</li>`;
        }
        
        poListHtml += '</ul></div>';
      }
      
      return `
        <div class="px-3 py-2 bg-gray-800 text-white rounded-md shadow-lg text-sm">
          <div class="font-bold text-sm border-b border-gray-700 pb-1 mb-1">${vendorName}</div>
          <div class="flex items-center">
            <span>Total:</span>
            <span class="font-semibold ml-2">${formattedTotal}</span>
          </div>
          <div class="text-xs mt-1">
            <span class="text-gray-300">Purchase Orders: <span class="text-white font-medium">${pos.length}</span></span>
          </div>
          ${poListHtml}
        </div>
      `;
    } catch (error) {
      console.error("Error generating Top 10 Vendors tooltip:", error);
      return '<div class="p-1 text-xs text-red-400">Error generating tooltip</div>';
    }
  };
}

/**
 * Creates chart options for the Purchase Orders by Top 10 Vendors chart
 * @param {Object} chartData - Data from adaptDataForTop10Vendors
 * @param {Function} currencyFormatter - Function to format currency values
 * @return {Object} ApexCharts options
 */
export function createTop10VendorsChartOptions(chartData, currencyFormatter) {
  // Use blue color variations for the chart
  const blueColors = [
    '#3b82f6', // Base blue
    '#60a5fa', // Lighter blue
    '#93c5fd', // Even lighter blue
    '#2563eb', // Darker blue
    '#1d4ed8'  // Even darker blue
  ];
  
  return {
    series: chartData.series || [],
    chart: {
      type: 'bar',
      height: 400,
      stacked: false,
      toolbar: {
        show: false
      },
      animations: {
        enabled: true,
        easing: 'easeinout',
        speed: 500
      },
      fontFamily: 'inherit',
      background: 'transparent',
      parentHeightOffset: 0
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '60%',
        endingShape: 'rounded',
        borderRadius: 4,
        dataLabels: {
          position: 'top'
        },
        colors: {
          ranges: [{
            from: 0,
            to: Infinity,
            color: '#3b82f6' // Blue color for all bars
          }]
        }
      }
    },
    dataLabels: {
      enabled: true,
      formatter: function(val) {
        if (val <= 0) return '';
        if (val >= 1000000) return currencyFormatter(val / 1000000) + 'M';
        if (val >= 1000) return currencyFormatter(val / 1000) + 'K';
        return currencyFormatter(val);
      },
      offsetY: -20,
      style: {
        fontSize: '11px',
        fontWeight: '600',
        fontFamily: 'inherit',
        colors: ["#374151"]
      },
      background: {
        enabled: false
      }
    },
    colors: ['#3b82f6'], // Blue color for the bars
    stroke: {
      show: true,
      width: 1,
      colors: ['transparent']
    },
    xaxis: {
      categories: chartData.categories || [],
      labels: {
        style: {
          fontSize: '12px',
          fontFamily: 'inherit',
          fontWeight: '500'
        },
        rotate: -15, // Slight rotation to avoid text overlap
        trim: false, // Prevent trimming of labels
        hideOverlappingLabels: false, // Show all labels even if they overlap
        maxHeight: 120 // Allow more space for labels
      },
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    yaxis: {
      title: {
        text: 'Purchase Amount',
        style: {
          fontSize: '11px',
          fontWeight: '600',
          fontFamily: 'Helvetica, Arial, sans-serif',
          color: '#373d3f'
        },
        offsetX: -7,
        offsetY: 0,
        rotate: -90
      },
      labels: {
        formatter: function(val) {
          if (val === 0) return '0';
          if (val >= 1000) {
            return '$' + (val/1000).toFixed(1) + 'K';
          }
          return currencyFormatter(val);
        },
        style: {
          fontSize: '11px',
          fontFamily: 'inherit'
        }
      }
    },
    fill: {
      opacity: 0.9
    },
    legend: {
      show: false // Hide legend since we only have one series
    },
    tooltip: {
      enabled: true,
      shared: false,
      intersect: false, // Show tooltip on hover
      custom: getTop10VendorsTooltipFormatter(currencyFormatter),
      theme: 'dark'
    },
    grid: {
      show: true,
      borderColor: '#e2e8f0',
      strokeDashArray: 0,
      position: 'back',
      xaxis: {
        lines: {
          show: false
        }
      },
      yaxis: {
        lines: {
          show: true
        }
      },
      padding: {
        top: 30,
        right: 10,
        bottom: 40, // Increased bottom padding for labels
        left: 5
      }
    },
    noData: {
      text: 'No purchase data available for top vendors',
      align: 'center',
      verticalAlign: 'middle',
      style: {
        color: '#64748b',
        fontSize: '14px',
        fontFamily: 'inherit'
      }
    }
  };
} 