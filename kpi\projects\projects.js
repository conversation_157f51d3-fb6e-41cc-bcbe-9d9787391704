// Projects component for KPI Dashboard
export class ProjectsComponent {
  constructor(container) {
    this.container = container;
  }

  init() {
    this.render();
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <h1 class="text-2xl font-bold mb-6">Projects KPI Dashboard</h1>
        
        <div class="bg-teal-50 border-l-4 border-teal-500 p-4 mb-6 dark:bg-teal-900 dark:border-teal-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-teal-500 dark:text-teal-400">
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-teal-800 dark:text-teal-200">
                The Projects KPI Dashboard component is under development. Project metrics and performance indicators will be available here.
              </p>
            </div>
          </div>
        </div>
        
        <!-- Placeholder for future implementation -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Project Status Card -->
          <div class="kpi-card">
            <h3 class="font-semibold text-gray-700 mb-4">Project Status</h3>
            <div class="h-64 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
              <p class="text-gray-500 dark:text-gray-400">Chart coming soon</p>
            </div>
          </div>
          
          <!-- Project Timeline Card -->
          <div class="kpi-card">
            <h3 class="font-semibold text-gray-700 mb-4">Project Timeline</h3>
            <div class="h-64 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
              <p class="text-gray-500 dark:text-gray-400">Timeline coming soon</p>
            </div>
          </div>
        </div>
      </div>
    `;
  }
} 