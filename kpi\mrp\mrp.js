// MRP component for KPI Dashboard
export class MRPComponent {
  constructor(container) {
    this.container = container;
    this.activeTab = 'overview';
    this.tabs = [
      { id: 'overview', label: 'Overview', icon: 'chart-line' },
      { id: 'planning', label: 'Material Planning', icon: 'tasks' },
      { id: 'production', label: 'Production', icon: 'calendar-alt' },
      { id: 'sales_order', label: 'Sales Order', icon: 'file-invoice-dollar' },
      { id: 'inventory', label: 'Inventory Analysis', icon: 'boxes' },
      { id: 'purchasing', label: 'Purchasing', icon: 'shopping-cart' },
      { id: 'forecasting', label: 'Demand Forecasting', icon: 'chart-bar' },
      { id: 'suppliers', label: 'Suppliers', icon: 'truck' },
      { id: 'efficiency', label: 'Efficiency Metrics', icon: 'tachometer-alt' }
    ];
    this.visibleTabsStart = 0;
  }

  init() {
    this.render();
    this.setupEventListeners();
    this.switchTab('overview');
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <h1 class="text-2xl font-bold mb-6">MRP Dashboard</h1>
        
        <!-- Tab Navigation with Pagination -->
        <div class="flex items-center mb-6 border-b border-gray-200 dark:border-gray-700">
          <!-- Left Scroll Button -->
          <button id="tabScrollLeft" class="px-2 py-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-l-md">
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <!-- Tab Buttons Container -->
          <div class="flex overflow-hidden" style="max-width: calc(100% - 80px);">
            <div id="tabsContainer" class="flex transition-transform duration-300 ease-in-out">
              ${this.tabs.map(tab => `
                <button id="tab-${tab.id}" data-tab="${tab.id}" class="tab-button px-4 py-2 mr-1 flex items-center">
                  <i class="fas fa-${tab.icon} mr-2"></i>
                  <span>${tab.label}</span>
                </button>
              `).join('')}
            </div>
          </div>
          
          <!-- Right Scroll Button -->
          <button id="tabScrollRight" class="px-2 py-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-r-md">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        
        <!-- Tab Content Container -->
        <div id="mrpTabContent" class="tab-content">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Tab button click handlers
    this.tabs.forEach(tab => {
      const tabButton = document.getElementById(`tab-${tab.id}`);
      if (tabButton) {
        tabButton.addEventListener('click', () => this.switchTab(tab.id));
      }
    });

    // Tab scroll buttons
    const scrollLeftBtn = document.getElementById('tabScrollLeft');
    const scrollRightBtn = document.getElementById('tabScrollRight');
    
    if (scrollLeftBtn) {
      scrollLeftBtn.addEventListener('click', () => this.scrollTabs('left'));
    }
    
    if (scrollRightBtn) {
      scrollRightBtn.addEventListener('click', () => this.scrollTabs('right'));
    }
    
    // Update tab visibility on window resize
    window.addEventListener('resize', () => this.updateTabVisibility());
  }

  scrollTabs(direction) {
    const maxVisibleTabs = 4;
    const maxStartIndex = Math.max(0, this.tabs.length - maxVisibleTabs);
    
    if (direction === 'left') {
      this.visibleTabsStart = Math.max(0, this.visibleTabsStart - 1);
    } else if (direction === 'right') {
      this.visibleTabsStart = Math.min(maxStartIndex, this.visibleTabsStart + 1);
    }
    
    this.updateTabVisibility();
  }

  updateTabVisibility() {
    const tabsContainer = document.getElementById('tabsContainer');
    if (!tabsContainer) return;
    
    const tabWidth = 150; // Approximate width of each tab
    const translateX = -this.visibleTabsStart * tabWidth;
    tabsContainer.style.transform = `translateX(${translateX}px)`;
    
    // Update scroll button states
    const scrollLeftBtn = document.getElementById('tabScrollLeft');
    const scrollRightBtn = document.getElementById('tabScrollRight');
    
    if (scrollLeftBtn) {
      scrollLeftBtn.disabled = this.visibleTabsStart === 0;
      scrollLeftBtn.classList.toggle('opacity-50', this.visibleTabsStart === 0);
    }
    
    if (scrollRightBtn) {
      const maxVisibleTabs = 4;
      const maxStartIndex = Math.max(0, this.tabs.length - maxVisibleTabs);
      scrollRightBtn.disabled = this.visibleTabsStart >= maxStartIndex;
      scrollRightBtn.classList.toggle('opacity-50', this.visibleTabsStart >= maxStartIndex);
    }
  }

  async switchTab(tabId) {
    // Update active tab
    this.activeTab = tabId;
    
    // Update tab button styles
    this.tabs.forEach(tab => {
      const tabButton = document.getElementById(`tab-${tab.id}`);
      if (tabButton) {
        tabButton.classList.toggle('active', tab.id === tabId);
        if (tab.id === tabId) {
          tabButton.classList.add('border-b-2', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
          tabButton.classList.remove('text-gray-500', 'dark:text-gray-400');
        } else {
          tabButton.classList.remove('border-b-2', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
          tabButton.classList.add('text-gray-500', 'dark:text-gray-400');
        }
      }
    });
    
    // Get content container
    const contentContainer = document.getElementById('mrpTabContent');
    if (!contentContainer) return;
    
    // Show loading state
    contentContainer.innerHTML = `
      <div class="flex justify-center items-center p-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
      </div>
    `;
    
    try {
      // Load tab content based on tab ID
      switch (tabId) {
        case 'overview':
          this.renderOverviewTab(contentContainer);
          break;
        case 'planning':
          // Import and initialize the MaterialPlanning component
          try {
            const MaterialPlanningModule = await import('./material_planning.js');
            const materialPlanningComp = new MaterialPlanningModule.MaterialPlanningComponent(contentContainer);
            await materialPlanningComp.init();
          } catch (err) {
            console.warn('Material Planning module not fully implemented:', err);
            this.renderPlaceholderTab(contentContainer, 'Material Planning');
          }
          break;
        case 'production':
          // Import and initialize the Production component
          try {
            const ProductionModule = await import('./production.js');
            const productionComp = new ProductionModule.ProductionComponent(contentContainer);
            await productionComp.init();
          } catch (err) {
            console.warn('Production module not fully implemented:', err);
            this.renderPlaceholderTab(contentContainer, 'Production');
          }
          break;
        case 'sales_order':
          // Import and initialize the SalesOrder component
          try {
            const SalesOrderModule = await import('./sales_order.js');
            const salesOrderComp = new SalesOrderModule.SalesOrderComponent(contentContainer);
            await salesOrderComp.init();
          } catch (err) {
            console.warn('Sales Order module not fully implemented:', err);
            this.renderSalesOrderTab(contentContainer);
          }
          break;
        case 'inventory':
          // Import and initialize the Inventory component
          try {
            const InventoryModule = await import('./inventory_analysis.js');
            const inventoryComp = new InventoryModule.InventoryAnalysisComponent(contentContainer);
            await inventoryComp.init();
          } catch (err) {
            console.warn('Inventory Analysis module not fully implemented:', err);
            this.renderPlaceholderTab(contentContainer, 'Inventory Analysis');
          }
          break;
        case 'purchasing':
          // Import and initialize the Purchasing component
          try {
            const PurchasingModule = await import('./purchasing.js');
            const purchasingComp = new PurchasingModule.PurchasingComponent(contentContainer);
            await purchasingComp.init();
          } catch (err) {
            console.warn('Purchasing module not fully implemented:', err);
            this.renderPurchasingTab(contentContainer);
          }
          break;
        default:
          // For other tabs, show a placeholder
          this.renderPlaceholderTab(contentContainer, this.tabs.find(t => t.id === tabId)?.label || tabId);
      }
    } catch (error) {
      console.error(`Error loading tab ${tabId}:`, error);
      contentContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">
                Error loading tab content: ${error.message}
              </p>
            </div>
          </div>
        </div>
      `;
    }
  }

  renderOverviewTab(container) {
    container.innerHTML = `
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- MRP Overview Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">MRP Overview</h3>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Planned Orders</p>
              <p class="text-xl font-bold text-blue-600 dark:text-blue-400">248</p>
            </div>
            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Materials Required</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">1,432</p>
            </div>
          </div>
          <div class="h-48 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
            <p class="text-gray-500 dark:text-gray-400">Production Schedule Chart</p>
          </div>
        </div>
        
        <!-- Critical Materials Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Critical Materials</h3>
          <div class="h-64 overflow-y-auto">
            <table class="min-w-full">
              <thead>
                <tr class="bg-gray-100 dark:bg-gray-700">
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Material</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Required By</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">RM-1045</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Critical</span></td>
                  <td class="py-2 px-3 whitespace-nowrap">May 10, 2023</td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">RM-2367</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">Low Stock</span></td>
                  <td class="py-2 px-3 whitespace-nowrap">May 12, 2023</td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">RM-3578</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">On Track</span></td>
                  <td class="py-2 px-3 whitespace-nowrap">May 15, 2023</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderPurchasingTab(container) {
    container.innerHTML = `
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Purchase Orders Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Purchase Orders</h3>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Open Orders</p>
              <p class="text-xl font-bold text-blue-600 dark:text-blue-400">58</p>
            </div>
            <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Expected Deliveries</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">23</p>
            </div>
          </div>
          <div class="h-48 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
            <p class="text-gray-500 dark:text-gray-400">Purchase Orders Chart</p>
          </div>
        </div>
        
        <!-- Purchasing Status Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Purchasing Status</h3>
          <div class="h-64 overflow-y-auto">
            <table class="min-w-full">
              <thead>
                <tr class="bg-gray-100 dark:bg-gray-700">
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Order #</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Supplier</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Expected</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">PO-2023-142</td>
                  <td class="py-2 px-3 whitespace-nowrap">ABC Supplies</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 12, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">In Transit</span></td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">PO-2023-139</td>
                  <td class="py-2 px-3 whitespace-nowrap">XYZ Manufacturing</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 10, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Confirmed</span></td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">PO-2023-135</td>
                  <td class="py-2 px-3 whitespace-nowrap">Global Industries</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 18, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Delayed</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderSalesOrderTab(container) {
    container.innerHTML = `
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Sales Orders Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Sales Orders</h3>
          <div class="grid grid-cols-2 gap-4 mb-4">
            <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Open Orders</p>
              <p class="text-xl font-bold text-blue-600 dark:text-blue-400">87</p>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Ready to Ship</p>
              <p class="text-xl font-bold text-purple-600 dark:text-purple-400">34</p>
            </div>
          </div>
          <div class="h-48 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
            <p class="text-gray-500 dark:text-gray-400">Sales Orders Chart</p>
          </div>
        </div>
        
        <!-- Order Fulfillment Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Order Fulfillment</h3>
          <div class="h-64 overflow-y-auto">
            <table class="min-w-full">
              <thead>
                <tr class="bg-gray-100 dark:bg-gray-700">
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Order #</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Customer</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Due Date</th>
                  <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">SO-2023-267</td>
                  <td class="py-2 px-3 whitespace-nowrap">Acme Corp</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 15, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">Ready</span></td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">SO-2023-263</td>
                  <td class="py-2 px-3 whitespace-nowrap">Globex Industries</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 18, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">In Production</span></td>
                </tr>
                <tr>
                  <td class="py-2 px-3 whitespace-nowrap">SO-2023-259</td>
                  <td class="py-2 px-3 whitespace-nowrap">Initech LLC</td>
                  <td class="py-2 px-3 whitespace-nowrap">May 12, 2023</td>
                  <td class="py-2 px-3 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">Delayed</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    `;
  }

  renderPlaceholderTab(container, tabName) {
    container.innerHTML = `
      <div class="bg-blue-50 border-l-4 border-blue-500 p-4 dark:bg-blue-900 dark:border-blue-600">
        <div class="flex items-center">
          <div class="flex-shrink-0 text-blue-500 dark:text-blue-400">
            <i class="fas fa-info-circle"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm text-blue-800 dark:text-blue-200">
              The ${tabName} tab is under development.
            </p>
          </div>
        </div>
      </div>
    `;
  }
} 