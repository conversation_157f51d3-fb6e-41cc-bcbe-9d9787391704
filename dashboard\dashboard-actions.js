// dashboard-actions.js - Action functions

import { NotificationSystem } from "../core/notifications.js";

// Create a single instance of the notification system to use throughout the file
const notificationSystem = new NotificationSystem(document.body);

// Track last notification timestamps to prevent duplicates
const notificationTimestamps = new Map();
const NOTIFICATION_DEBOUNCE_TIME = 800; // ms

// Cache for master part data to avoid repeated fetches
let masterPartDataCache = null;

// Helper function to show notification with debounce
function showNotification(message, type) {
  const key = `${type}:${message}`;
  const now = Date.now();

  // Check if this exact notification was shown recently
  if (notificationTimestamps.has(key)) {
    const lastTime = notificationTimestamps.get(key);
    if (now - lastTime < NOTIFICATION_DEBOUNCE_TIME) {
      // Skip showing duplicate notification if shown recently
      return;
    }
  }

  // Update timestamp and show notification
  notificationTimestamps.set(key, now);
  notificationSystem.addNotification(message, type);

  // Clean up old timestamps after a while
  setTimeout(() => {
    notificationTimestamps.delete(key);
  }, NOTIFICATION_DEBOUNCE_TIME * 2);
}

// Helper function to show progress bar (placeholder)
function showProgressBar() {
  console.log("Showing progress bar");
  // Implement actual progress bar logic here
}

// Helper function to hide progress bar (placeholder)
function hideProgressBar() {
  console.log("Hiding progress bar");
  // Implement actual progress bar logic here
}

// Helper function to log audit trail (placeholder)
function logAuditTrail(message) {
  console.log("Audit Trail:", message);
  // Implement actual audit trail logging here
}

// Fetch master part data from JSON file
export async function fetchMasterPartData() {
  try {
    // Return cached data if available
    if (masterPartDataCache) {
      return masterPartDataCache;
    }

    // Check if we can use the fetch API (browser environment)
    if (typeof fetch === 'function') {
      const response = await fetch('json/masterpart.json');
      if (!response.ok) {
        throw new Error(`Failed to fetch master part data: ${response.status}`);
      }
      const data = await response.json();
      masterPartDataCache = data;
      console.log(`Loaded ${data.length} master parts from JSON`);
      return data;
    } 
    
    // If fetch is not available, try using chrome.runtime.getURL
    else if (typeof chrome !== 'undefined' && chrome.runtime) {
      return new Promise((resolve, reject) => {
        const jsonUrl = chrome.runtime.getURL('json/masterpart.json');
        const xhr = new XMLHttpRequest();
        xhr.open('GET', jsonUrl, true);
        xhr.responseType = 'json';
        xhr.onload = function() {
          if (xhr.status === 200) {
            masterPartDataCache = xhr.response;
            console.log(`Loaded ${xhr.response.length} master parts from JSON`);
            resolve(xhr.response);
          } else {
            reject(new Error(`XHR failed: ${xhr.statusText}`));
          }
        };
        xhr.onerror = function() {
          reject(new Error('Network error loading master part data'));
        };
        xhr.send();
      });
    }
    
    // Last resort, try using window.fs if available (for compatibility with the extension environment)
    else if (typeof window !== 'undefined' && window.fs) {
      const response = await window.fs.readFile('json/masterpart.json', { encoding: 'utf8' });
      const data = JSON.parse(response);
      masterPartDataCache = data;
      console.log(`Loaded ${data.length} master parts from JSON using window.fs`);
      return data;
    }
    
    else {
      throw new Error('No method available to load master part data');
    }
  } catch (error) {
    console.error('Error loading master part data:', error);
    return [];
  }
}

// Look up part details from master data
export async function lookupPartDetails(partId) {
  if (!partId) return null;
  
  try {
    // Normalize part ID for comparison (remove spaces, convert to string)
    const normalizedPartId = String(partId).trim();
    
    // Fetch master part data
    const masterParts = await fetchMasterPartData();
    
    // Find matching part by Part Number
    const matchedPart = masterParts.find(part => 
      String(part["Part Number"]).trim() === normalizedPartId
    );
    
    if (matchedPart) {
      console.log(`Found master data for part ${partId}`);
      return matchedPart;
    } else {
      console.log(`No master data found for part ${partId}`);
      return null;
    }
  } catch (error) {
    console.error(`Error looking up part ${partId}:`, error);
    return null;
  }
}

// Enhanced Part Lookup function - look up all parts in preview data
export async function partLookup(previewData = null) {
  showNotification("Looking up part details...", "info");

  try {
    // Use provided preview data or fetch it from storage if not provided
    let data = previewData;
    if (!data) {
      data = await getCurrentPreviewData();
    }
    
    if (!data || !data.parts || data.parts.length === 0) {
      throw new Error("No part data available to lookup");
    }

    // Fetch master part data
    const masterParts = await fetchMasterPartData();
    if (!masterParts || masterParts.length === 0) {
      throw new Error("Master part data not available");
    }

    // Look up each part in the preview data
    const partsWithDetails = await Promise.all(data.parts.map(async (part) => {
      const inventoryId = part.inventoryID;
      const masterPartData = await lookupPartDetails(inventoryId);
      
      // If found, merge the master part data with the part data
      if (masterPartData) {
        return {
          ...part,
          masterPartData
        };
      }
      
      // If not found, return the original part data
      return part;
    }));

    // Update the parts in the preview data
    const updatedData = {
      ...data,
      parts: partsWithDetails
    };

    showNotification("Part lookup completed successfully", "success");
    return updatedData;
  } catch (error) {
    console.error("Error in part lookup:", error);
    showNotification(`Error looking up parts: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// Fetch Data function
export async function fetchData() {
  showNotification("Fetching data...", "info");
  showProgressBar();

  return new Promise((resolve, reject) => {
    try {
      // Check if the chrome variable exists before using it
      if (typeof chrome !== "undefined" && chrome.tabs) {
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
          if (!tabs || tabs.length === 0) {
            hideProgressBar();
            showNotification("No active tab found.", "danger");
            reject(new Error("No active tab found."));
            return;
          }

          const activeTab = tabs[0];

          if (!chrome.scripting) {
            hideProgressBar();
            showNotification("Chrome API not available.", "danger");
            reject(new Error("Chrome scripting API not available."));
            return;
          }

          try {
            // First, execute script to get the main HTML content
            chrome.scripting.executeScript(
              {
                target: { tabId: activeTab.id },
                func: () => {
                  let fullSourceCode = "<!-- Main Page Source Code -->\n";
                  fullSourceCode += document.documentElement.outerHTML;

                  const iframes = document.querySelectorAll("iframe");
                  let iframeCount = 0;
                  let accessedIframeCount = 0;

                  iframes.forEach((iframe, idx) => {
                    iframeCount++;
                    try {
                      if (iframe.contentDocument) {
                        accessedIframeCount++;
                        fullSourceCode += `\n\n<!-- Iframe ${idx + 1} Source Code -->\n`;
                        fullSourceCode += iframe.contentDocument.documentElement.outerHTML;
                      }
                    } catch (e) {
                      console.warn(`Cannot access iframe ${idx + 1}: ${e.message}`);
                      fullSourceCode += `\n\n<!-- Iframe ${idx + 1} - Access Denied (${e.message}) -->\n`;
                      if (iframe.src) {
                        fullSourceCode += `<!-- Iframe source URL: ${iframe.src} -->\n`;
                      }
                    }
                  });

                  const frames = document.querySelectorAll("frame");
                  let frameCount = 0;
                  let accessedFrameCount = 0;

                  frames.forEach((frame, idx) => {
                    frameCount++;
                    try {
                      if (frame.contentDocument) {
                        accessedFrameCount++;
                        fullSourceCode += `\n\n<!-- Frame ${idx + 1} Source Code -->\n`;
                        fullSourceCode += frame.contentDocument.documentElement.outerHTML;
                      }
                    } catch (e) {
                      console.warn(`Cannot access frame ${idx + 1}: ${e.message}`);
                      fullSourceCode += `\n\n<!-- Frame ${idx + 1} - Access Denied (${e.message}) -->\n`;
                      if (frame.src) {
                        fullSourceCode += `<!-- Frame source URL: ${frame.src} -->\n`;
                      }
                    }
                  });

                  return {
                    sourceCode: fullSourceCode,
                    stats: {
                      pageTitle: document.title,
                      url: window.location.href,
                      totalIframes: iframeCount,
                      accessedIframes: accessedIframeCount,
                      totalFrames: frameCount,
                      accessedFrames: accessedFrameCount,
                      timestamp: new Date().toISOString(),
                    },
                  };
                },
              },
              (results) => {
                if (chrome.runtime.lastError) {
                  console.error("[dashboard.js] Fetch data error:", chrome.runtime.lastError.message);
                  hideProgressBar();
                  showNotification("Error fetching data.", "danger");
                  reject(new Error(chrome.runtime.lastError.message));
                  return;
                }

                if (!results || !results[0] || !results[0].result) {
                  hideProgressBar();
                  showNotification("Error: Unable to extract data.", "danger");
                  reject(new Error("Unexpected script execution result."));
                  return;
                }

                const result = results[0].result;
                const extractedSourceCode = result.sourceCode;
                const stats = result.stats;

                // Now execute script to scrape input fields from iframe - including both SH and PO fields
                chrome.scripting.executeScript(
                  {
                    target: { tabId: activeTab.id },
                    func: () => {
                      // SH process field IDs
                      const shInputIds = [
                        'ctl00_phG_tab_t3_formD_edFullName',          // Company name
                        'ctl00_phG_tab_t3_formD_edAttention',         // Attention
                        'ctl00_phG_tab_t3_formD_edPhone1',            // Phone
                        'ctl00_phG_tab_t3_formD_edEmail_text',        // Email
                        'ctl00_phG_tab_t3_formB_edAddressLine1',      // Address
                        'ctl00_phG_tab_t3_formB_edAddressLine2',      // Address 2nd
                        'ctl00_phG_tab_t3_formB_edCity',              // City
                        'ctl00_phG_tab_t3_formB_edCountryID_text',    // Country
                        'ctl00_phG_tab_t3_formB_edState_text',        // State
                        'ctl00_phG_tab_t3_formB_edPostalCode',        // Postal Code
                        'ctl00_phG_tab_t2_formF_edShipVia_text',      // Ship Via
                        'ctl00_phG_tab_t2_formF_edShipTermsID_text',  // Ship Terms
                        'ctl00_phF_form_t0_edShipmentDesc',           // Description
                        'ctl00_phF_form_t0_edShipDate_text'           // Date
                      ];
                      
                      // PO process field IDs
                      const poInputIds = [
                        'ctl00_phG_tab_t3_formVC_edFullName',        // Company Name
                        'ctl00_phG_tab_t3_formVC_edAttention',       // Attention
                        'ctl00_phG_tab_t3_formVC_edPhone1',          // Phone
                        'ctl00_phG_tab_t3_formVC_edEmail_text',      // Email
                        'ctl00_phG_tab_t3_formVA_edAddressLine1',    // Address Line 1
                        'ctl00_phG_tab_t3_formVA_edAddressLine2',    // Address Line 2
                        'ctl00_phG_tab_t3_formVA_edCity',            // City
                        'ctl00_phG_tab_t3_formVA_edCountryID_text',  // Country
                        'ctl00_phG_tab_t3_formVA_edState_text',      // State
                        'ctl00_phG_tab_t3_formVA_edPostalCode',      // Postal Code
                        'ctl00_phG_tab_t2_formSC_edPhone1',          // Contact Phone
                        'ctl00_phG_tab_t2_formSC_edEmail_text',      // Contact Email
                        'ctl00_phG_tab_t2_edShipVia_text',           // Shipping Method
                        'ctl00_phG_tab_t2_edSiteID_text',            // Shipping Terms
                        'ctl00_phF_form_t0_edOrderDesc',             // Shipment Description
                        'ctl00_phF_form_t0_edOrderDate'              // Shipment Date
                      ];

                      // Combine all IDs we're looking for
                      const allInputIds = [...new Set([...shInputIds, ...poInputIds])];

                      const scrapedData = {};
                      
                      // Function to scrape inputs from document or iframe
                      function scrapeInputsFromDocument(doc) {
                        const data = {};
                        
                        allInputIds.forEach(id => {
                          const inputElement = doc.getElementById(id);
                          data[id] = inputElement ? inputElement.value : null;
                        });
                        
                        return data;
                      }
                      
                      // First try to find elements in the main document
                      const mainDocData = scrapeInputsFromDocument(document);
                      
                      // Also look for the main iframe
                      const mainIframe = document.getElementById('main');
                      let iframeData = {};
                      
                      if (mainIframe) {
                        try {
                          const iframeDoc = mainIframe.contentDocument || mainIframe.contentWindow.document;
                          if (iframeDoc) {
                            iframeData = scrapeInputsFromDocument(iframeDoc);
                          }
                        } catch (e) {
                          console.warn('Cannot access iframe content:', e);
                          iframeData = { error: e.message };
                        }
                      }
                      
                      // Merge data (prefer iframe data if available)
                      allInputIds.forEach(id => {
                        if (iframeData[id] !== null && iframeData[id] !== undefined) {
                          scrapedData[id] = iframeData[id];
                        } else if (mainDocData[id] !== null && mainDocData[id] !== undefined) {
                          scrapedData[id] = mainDocData[id];
                        } else {
                          scrapedData[id] = null;
                        }
                      });
                      
                      // Process mappings for both SH and PO data
                      return {
                        // SH field mappings
                        sh: {
                          companyName: scrapedData['ctl00_phG_tab_t3_formD_edFullName'] || 'N/A',
                          attention: scrapedData['ctl00_phG_tab_t3_formD_edAttention'] || 'N/A',
                          phone: scrapedData['ctl00_phG_tab_t3_formD_edPhone1'] || 'N/A',
                          email: scrapedData['ctl00_phG_tab_t3_formD_edEmail_text'] || 'N/A',
                          addressLine1: scrapedData['ctl00_phG_tab_t3_formB_edAddressLine1'] || 'N/A',
                          addressLine2: scrapedData['ctl00_phG_tab_t3_formB_edAddressLine2'] || 'N/A',
                          city: scrapedData['ctl00_phG_tab_t3_formB_edCity'] || 'N/A',
                          country: scrapedData['ctl00_phG_tab_t3_formB_edCountryID_text'] || 'N/A',
                          state: scrapedData['ctl00_phG_tab_t3_formB_edState_text'] || 'N/A',
                          postalCode: scrapedData['ctl00_phG_tab_t3_formB_edPostalCode'] || 'N/A',
                          shippingMethod: scrapedData['ctl00_phG_tab_t2_formF_edShipVia_text'] || 'N/A',
                          shippingTerms: scrapedData['ctl00_phG_tab_t2_formF_edShipTermsID_text'] || 'N/A',
                          shipmentDescription: scrapedData['ctl00_phF_form_t0_edShipmentDesc'] || 'N/A',
                          shipmentDate: scrapedData['ctl00_phF_form_t0_edShipDate_text'] || 'N/A'
                        },
                        
                        // PO field mappings
                        po: {
                          companyName: scrapedData['ctl00_phG_tab_t3_formVC_edFullName'] || 'N/A',
                          attention: scrapedData['ctl00_phG_tab_t3_formVC_edAttention'] || 'N/A',
                          phone: scrapedData['ctl00_phG_tab_t3_formVC_edPhone1'] || 'N/A',
                          email: scrapedData['ctl00_phG_tab_t3_formVC_edEmail_text'] || 'N/A',
                          addressLine1: scrapedData['ctl00_phG_tab_t3_formVA_edAddressLine1'] || 'N/A',
                          addressLine2: scrapedData['ctl00_phG_tab_t3_formVA_edAddressLine2'] || 'N/A',
                          city: scrapedData['ctl00_phG_tab_t3_formVA_edCity'] || 'N/A',
                          country: scrapedData['ctl00_phG_tab_t3_formVA_edCountryID_text'] || 'N/A',
                          state: scrapedData['ctl00_phG_tab_t3_formVA_edState_text'] || 'N/A',
                          postalCode: scrapedData['ctl00_phG_tab_t3_formVA_edPostalCode'] || 'N/A',
                          contactPhone: scrapedData['ctl00_phG_tab_t2_formSC_edPhone1'] || 'N/A',
                          contactEmail: scrapedData['ctl00_phG_tab_t2_formSC_edEmail_text'] || 'N/A',
                          shippingMethod: scrapedData['ctl00_phG_tab_t2_edShipVia_text'] || 'N/A',
                          shippingTerms: scrapedData['ctl00_phG_tab_t2_edSiteID_text'] || 'N/A',
                          shipmentDescription: scrapedData['ctl00_phF_form_t0_edOrderDesc'] || 'N/A',
                          shipmentDate: scrapedData['ctl00_phF_form_t0_edOrderDate'] || 'N/A'
                        },
                        
                        // Raw data for debugging
                        raw: scrapedData
                      };
                    },
                  },
                  (inputResults) => {
                    hideProgressBar();
                    
                    if (chrome.runtime.lastError) {
                      console.error("[dashboard.js] Input scraping error:", chrome.runtime.lastError.message);
                      // Continue even if input scraping fails, we'll still have the main HTML
                      logAuditTrail(`Fetched data from ${stats.url} (without input fields)`);
                    }
                    
                    // Extract the scraped input data if available
                    const inputData = inputResults && inputResults[0] && inputResults[0].result ? 
                                      inputResults[0].result : { sh: {}, po: {} };
                    
                    // Store both the HTML source and the scraped input data
                    chrome.storage.local.set(
                      {
                        acumaticaSourceCode: extractedSourceCode,
                        acumaticaInputData: inputData,  // Store the scraped input fields
                        scrapeStats: stats,
                      },
                      () => {
                        if (chrome.runtime.lastError) {
                          console.error("Error saving to storage:", chrome.runtime.lastError.message);
                          showNotification("Error saving data.", "danger");
                          reject(new Error(chrome.runtime.lastError.message));
                        } else {
                          console.log("Source code, input data, and stats saved to storage");
                          logAuditTrail(`Fetched data from ${stats.url}`);
                          resolve("Data fetched successfully");
                        }
                      }
                    );
                  }
                );
              }
            );
          } catch (error) {
            hideProgressBar();
            console.error("[dashboard.js] executeScript error:", error.message);
            showNotification("Error executing script.", "danger");
            reject(error);
          }
        });
      } else {
        hideProgressBar();
        console.warn("Chrome API not available. Using test data.");

        setTimeout(() => {
          const testHtml = `
           <html>
             <head><title>Test Page</title></head>
             <body>
               <div id="usrCaption">SH12345 - Test Customer</div>
               <div id="ctl00_phG_tab_t0_grid">
                 <tr class="GridRow">
                   <td></td><td></td><td></td><td></td><td></td><td></td>
                   <td>SO</td>
                   <td>SO54321</td>
                   <td></td>
                   <td><a>PART-123</a></td>
                   <td></td><td></td><td></td><td></td><td></td><td></td><td></td>
                   <td>5</td>
                   <td></td><td></td><td></td><td></td><td></td><td></td><td></td>
                   <td>LOT123</td>
                   <td></td><td></td><td></td>
                   <td>Test Part Description</td>
                 </tr>
               </div>
             </body>
           </html>
         `;

          // Create test input data for development/testing
          const testInputData = {
            sh: {
              companyName: "ACME Corporation",
              attention: "John Doe",
              phone: "************",
              email: "<EMAIL>",
              addressLine1: "123 Main Street",
              addressLine2: "Suite 456",
              city: "Springfield",
              country: "United States",
              state: "IL",
              postalCode: "62701",
              shippingMethod: "UPS Ground",
              shippingTerms: "FOB",
              shipmentDescription: "Test Shipment",
              shipmentDate: "01/15/2024"
            },
            po: {
              companyName: "Vendor Company LLC",
              attention: "Supplier Contact",
              phone: "************",
              email: "<EMAIL>",
              addressLine1: "789 Vendor Street",
              addressLine2: "Building A",
              city: "Vendorville",
              country: "Canada",
              state: "ON",
              postalCode: "A1B 2C3",
              contactPhone: "************",
              contactEmail: "<EMAIL>",
              shippingMethod: "DHL Express",
              shippingTerms: "EXW",
              shipmentDescription: "Test PO Order",
              shipmentDate: "02/20/2024"
            }
          };

          localStorage.setItem("acumaticaSourceCode", testHtml);
          localStorage.setItem("acumaticaInputData", JSON.stringify(testInputData));
          localStorage.setItem(
            "scrapeStats",
            JSON.stringify({
              pageTitle: "Test Page",
              url: "https://test.acumatica.com",
              timestamp: new Date().toISOString(),
            })
          );

          resolve("Data fetched successfully");
        }, 1000);
      }
    } catch (error) {
      hideProgressBar();
      console.error("[dashboard.js] error:", error.message);
      showNotification("Error querying tabs.", "danger");
      reject(error);
    }
  });
}

// Helper function to get source code from storage
async function getSourceCode() {
  return new Promise((resolve) => {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["acumaticaSourceCode"], (result) => {
        resolve(result.acumaticaSourceCode);
      });
    } else {
      resolve(localStorage.getItem("acumaticaSourceCode"));
    }
  });
}

// Helper function to get input data from storage for SH Process
async function getInputData() {
  return new Promise((resolve) => {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["acumaticaInputData"], (result) => {
        const data = result.acumaticaInputData || {};
        resolve(data.sh || {});
      });
    } else {
      try {
        const inputDataStr = localStorage.getItem("acumaticaInputData");
        const inputData = inputDataStr ? JSON.parse(inputDataStr) : {};
        resolve(inputData.sh || {});
      } catch (e) {
        console.error("Error parsing input data from localStorage:", e);
        resolve({});
      }
    }
  });
}

// Helper function to get input data from storage for PO Process
async function getInputDataForPO() {
  return new Promise((resolve) => {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["acumaticaInputData"], (result) => {
        const data = result.acumaticaInputData || {};
        resolve(data.po || {});
      });
    } else {
      try {
        const inputDataStr = localStorage.getItem("acumaticaInputData");
        const inputData = inputDataStr ? JSON.parse(inputDataStr) : {};
        resolve(inputData.po || {});
      } catch (e) {
        console.error("Error parsing PO input data from localStorage:", e);
        resolve({});
      }
    }
  });
}

// SH Process function
export async function shProcess() {
  if (!(await getSourceCode())) {
    showNotification("No data available. Please fetch data first.", "warning");
    return Promise.reject(new Error("No data available"));
  }

  showNotification("Processing shipment data...", "info");

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(await getSourceCode(), "text/html");
    
    // Get the scraped input data for SH
    const inputData = await getInputData();

    const captionElement = doc.querySelector("#usrCaption");
    const captionText = captionElement ? captionElement.textContent.trim() : "";
    const [referenceNumber, ...customerNameParts] = captionText.split(" - ");

    const shipmentDetails = {
      type: "SH",
      userName: doc.querySelector(".user-info .user-name")?.textContent?.trim() || "N/A",
      customerName: customerNameParts.join(" - ").trim() || "N/A",
      shipmentNumber: referenceNumber.trim() || "N/A",
      reference: referenceNumber.trim() || "N/A",
      customer: customerNameParts.join(" - ").trim() || "N/A",
      orderType:
        doc.querySelector("#ctl00_phG_tab_t0_grid_dataT0 tbody > tr > td:nth-child(4)")?.textContent?.trim() || "N/A",
      orderNumber:
        doc.querySelector("#ctl00_phG_tab_t0_grid_dataT0 tbody > tr > td:nth-child(5)")?.textContent?.trim() || "N/A",
    };

    const parts = [];
    doc.querySelectorAll("#ctl00_phG_tab_t0_grid_dataT0 tbody > tr").forEach((row, index) => {
      parts.push({
        inventoryID: row.querySelector("td:nth-child(6) a")?.textContent?.trim() || "N/A",
        description: row.querySelector("td:nth-child(18)")?.textContent?.trim() || "N/A",
        shippedQuantity: row.querySelector("td:nth-child(11)")?.textContent?.trim() || "N/A",
        lotSerialNumber: row.querySelector("td:nth-child(20)")?.textContent?.trim() || "N/A",
      });
    });

    // Create the shipment info object from the input data
    const shipmentInfo = {
      companyName: inputData.companyName || "N/A",
      attention: inputData.attention || "N/A",
      phone: inputData.phone || "N/A",
      email: inputData.email || "N/A",
      addressLine1: inputData.addressLine1 || "N/A",
      addressLine2: inputData.addressLine2 || "N/A",
      city: inputData.city || "N/A",
      country: inputData.country || "N/A",
      state: inputData.state || "N/A",
      postalCode: inputData.postalCode || "N/A",
      shippingMethod: inputData.shippingMethod || "N/A",
      shippingTerms: inputData.shippingTerms || "N/A",
      shipmentDescription: inputData.shipmentDescription || "N/A",
      shipmentDate: inputData.shipmentDate || "N/A"
    };

    const processedData = {
      ...shipmentDetails,
      parts: parts,
      shipmentInfo: shipmentInfo, // Add the shipment info to the processed data
      timestamp: new Date().toISOString(),
      id: generateUniqueId() // Add a unique ID
    };

    // Perform part lookup automatically
    const dataWithPartDetails = await partLookup(processedData);

    showNotification("Shipment processed successfully.", "success");
    return dataWithPartDetails;
  } catch (error) {
    console.error("Error processing shipment:", error);
    showNotification(`Error processing shipment: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// PO Process function - Updated to use PO specific field IDs
export async function poProcess() {
  if (!(await getSourceCode())) {
    showNotification("No data available. Please fetch data first.", "warning");
    return Promise.reject(new Error("No data available"));
  }

  showNotification("Processing purchase order data...", "info");

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(await getSourceCode(), "text/html");
    
    // Get the scraped input data specifically for PO
    const inputData = await getInputDataForPO();

    // Extract PO number and vendor from the usrCaption
    const captionElement = doc.querySelector("#usrCaption");
    const captionText = captionElement ? captionElement.textContent.trim() : "";
    
    // Extract PO number and vendor name from caption
    let poNumber = "N/A";
    let vendorName = "N/A";
    
    if (captionText) {
      // The caption format is typically "Normal 029073 - Advanced Micro Instruments Inc"
      const captionParts = captionText.split(" - ");
      if (captionParts.length >= 1) {
        // Remove "Normal " prefix from PO number if present
        const poNumberWithPrefix = captionParts[0].trim();
        poNumber = poNumberWithPrefix.replace(/^Normal\s+/i, "");
      }
      if (captionParts.length >= 2) {
        vendorName = captionParts[1].trim();
      }
    }

    // Create PO details object
    const poDetails = {
      type: "PO",
      timestamp: new Date().toISOString(),
      reference: poNumber,
      poNumber: poNumber,
      vendor: vendorName,
      customer: vendorName, // Using vendor as customer for PO
      orderType: "PO", // Always PO for Purchase Orders
      orderNumber: poNumber,
      id: generateUniqueId()
    };

    // Find the parts table
    const poPartsTable = doc.querySelector("#ctl00_phG_tab_t0_grid_dataT0");
    if (!poPartsTable) {
      throw new Error("PO Parts table not found. Please verify the selector.");
    }

    // Get all parts rows
    const poPartsRows = poPartsTable.querySelectorAll("tbody > tr");
    if (!poPartsRows || poPartsRows.length === 0) {
      throw new Error("No purchase order items found");
    }

    const parts = [];

    poPartsRows.forEach((row, index) => {
      // Skip the "new row" if present
      if (row.id === "ctl00_phG_tab_t0_grid_newRow") {
        return;
      }

      const cells = row.querySelectorAll("td");
      if (cells.length < 11) { // Make sure row has enough cells
        console.warn(`Row ${index + 1} does not have enough columns. Skipping.`);
        return;
      }

      // Extract inventory ID from column 4 (inside anchor tag)
      const inventoryIDLink = cells[4].querySelector("a");
      const inventoryID = inventoryIDLink?.textContent.trim() || "N/A";
      
      if (inventoryID === "N/A") {
        console.warn(`Row ${index + 1} has invalid Part ID. Skipping.`);
        return;
      }

      // Extract description and quantity
      const description = cells[8].textContent.trim() || "N/A";
      const quantity = cells[9].textContent.trim() || "N/A";
      
      // Add to parts array
      parts.push({
        inventoryID: inventoryID,
        description: description,
        shippedQuantity: quantity, // Using shipped quantity for consistency
        lotSerialNumber: "N/A" // Not applicable for PO
      });
    });

    if (parts.length === 0) {
      throw new Error("No valid parts found in the purchase order");
    }

    // Create the shipment info object from the PO input data
    const shipmentInfo = {
      companyName: inputData.companyName || "N/A",
      attention: inputData.attention || "N/A",
      phone: inputData.phone || "N/A",
      email: inputData.email || "N/A",
      addressLine1: inputData.addressLine1 || "N/A",
      addressLine2: inputData.addressLine2 || "N/A",
      city: inputData.city || "N/A",
      country: inputData.country || "N/A",
      state: inputData.state || "N/A",
      postalCode: inputData.postalCode || "N/A",
      // Use contact info as additional properties
      contactPhone: inputData.contactPhone || "N/A",
      contactEmail: inputData.contactEmail || "N/A",
      shippingMethod: inputData.shippingMethod || "N/A",
      shippingTerms: inputData.shippingTerms || "N/A",
      shipmentDescription: inputData.shipmentDescription || "N/A",
      shipmentDate: inputData.shipmentDate || "N/A"
    };

    // Create the processed data object
    const processedData = {
      ...poDetails,
      parts: parts,
      shipmentInfo: shipmentInfo, // Add the PO-specific shipment info
      id: generateUniqueId() // Add a unique ID
    };

    // Perform part lookup automatically
    const dataWithPartDetails = await partLookup(processedData);

    showNotification("Purchase order processed successfully.", "success");
    return dataWithPartDetails;
  } catch (error) {
    console.error("Error processing purchase order:", error);
    showNotification(`Error processing purchase order: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// Enhanced Send to Monday function with proper field mappings and subitem support
export async function sendToMonday(previewData = null) {
  showNotification("Sending to Monday...", "info");
  try {
    // Use provided preview data or fetch it from storage if not provided
    let data = previewData;
    if (!data) {
      data = await getCurrentPreviewData();
    }
    
    if (!data) {
      throw new Error("No preview data available");
    }

    // IMPORTANT FIX: Collect all current input values from the UI before sending
    data = collectFormInputValues(data);

    // Get the user configuration from storage
    const userConfig = await getUserConfig();
    if (!userConfig) {
      throw new Error("User configuration not found. Please log in again.");
    }

    // Validate Monday API Key
    if (!userConfig["Monday API Key"]) {
      throw new Error("Monday.com API Key not found in user configuration");
    }

    // Determine data type (SH, PO, SO) and get appropriate Monday configuration
    const dataType = data.type || "Unknown";
    const mondayConfig = getMondayConfigForType(userConfig, dataType);
    
    if (!mondayConfig || !mondayConfig.boardId || !mondayConfig.groupId) {
      throw new Error(`Monday.com configuration for ${dataType} not found or incomplete.`);
    }
    
    // Step 1: Send data to Monday.com
    showNotification("Sending to Monday.com...", "info");
    const mondayResponse = await sendDataToMondayWithSubitems(data, userConfig, mondayConfig);
    
    // Step 2: Log to Google Sheet for history log
    showNotification("Logging to database...", "info");
    let sheetResponse;
    try {
      sheetResponse = await logToHistorySheet(data, userConfig);
    } catch (sheetError) {
      console.error("Error logging to history sheet:", sheetError);
      sheetResponse = { 
        success: false,
        id: "error-" + Date.now(),
        message: sheetError.message
      };
      // Continue with the process even if logging fails
    }
    
    // Create processed result object
    const processedData = {
      type: data.type || "Monday",
      action: "Sent to Monday",
      status: "Passed",
      timestamp: new Date().toISOString(),
      customer: data.customer || "N/A",
      reference: data.reference || "N/A",
      orderType: data.orderType || "N/A",
      orderNumber: data.orderNumber || "N/A",
      parts: data.parts || [],
      // Keep a reference to the original preview data
      previewData: data,
      // Store API response references
      mondayItemId: mondayResponse.id,
      mondaySubItemIds: mondayResponse.subItemIds || [],
      sheetLogId: sheetResponse.id,
      // Add a unique ID for the history entry
      id: generateUniqueId()
    };

    // Update history only once - FIX for double entries
    await updateHistoryInStorage(processedData);

    // Show notification based on whether both operations succeeded
    if (sheetResponse.success === false) {
      showNotification("Sent to Monday successfully, but there was an issue logging to database.", "warning");
    } else {
      showNotification("Sent to Monday successfully. Activity logged to database.", "success");
    }
    
    return processedData;
  } catch (error) {
    console.error("Error sending to Monday:", error);
    showNotification(`Error sending to Monday: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// NEW FUNCTION: Collect values from form inputs and update the data object
function collectFormInputValues(data) {
  console.log("Collecting form input values before sending to Monday");
  
  // Create a deep copy of the data object to avoid mutation
  const updatedData = JSON.parse(JSON.stringify(data));
  
  // Ensure packageInfo exists
  if (!updatedData.packageInfo) {
    updatedData.packageInfo = {};
  }
  
  // Get all relevant form inputs
  const shipmentMethod = document.getElementById("shipmentMethod");
  const packageQty = document.getElementById("packageQty");
  const packageType = document.getElementById("packageType");
  const packageDims = document.getElementById("packageDims");
  const packageWeight = document.getElementById("packageWeight");
  const packageCost = document.getElementById("packageCost");
  const carrier = document.getElementById("carrier");
  const trackingNo = document.getElementById("trackingNo");
  const packageNoteText = document.getElementById("packageNoteText");
  
  // Update the packageInfo object with form values
  if (shipmentMethod) updatedData.packageInfo.shipmentMethod = shipmentMethod.value;
  if (packageQty) updatedData.packageInfo.quantity = packageQty.value;
  if (packageType) updatedData.packageInfo.type = packageType.value;
  if (packageDims) updatedData.packageInfo.dimensions = packageDims.value || "N/A";
  if (packageWeight) updatedData.packageInfo.weight = packageWeight.value || "N/A";
  if (packageCost) updatedData.packageInfo.cost = packageCost.value || "N/A";
  if (carrier) updatedData.packageInfo.carrier = carrier.value;
  if (trackingNo) updatedData.packageInfo.trackingNumber = trackingNo.value;
  if (packageNoteText) updatedData.packageInfo.note = packageNoteText.value;
  
  // Preserve the custom tracking URL if it exists
  const trackingUrl = updatedData.packageInfo.trackingUrl; 
  
  // Store these values in _processedPackageData for use in Monday and SheetDB
  updatedData._processedPackageData = {
    packageQty: updatedData.packageInfo.quantity || "1",
    packageType: updatedData.packageInfo.type || "Box",
    packageDims: updatedData.packageInfo.dimensions || "N/A",
    packageWeight: updatedData.packageInfo.weight || "N/A",
    freightCost: updatedData.packageInfo.cost || "N/A",
    carrier: updatedData.packageInfo.carrier || "N/A",
    trackingNumber: updatedData.packageInfo.trackingNumber || "N/A",
    // Use the preserved trackingUrl property if it exists, otherwise generate it
    trackingLink: trackingUrl || updatedData.packageInfo.trackingUrl || generateTrackingLink(updatedData.packageInfo.carrier, updatedData.packageInfo.trackingNumber),
    packageNote: updatedData.packageInfo.note || "N/A",
    shipmentMethod: updatedData.packageInfo.shipmentMethod || "N/A"
  };
  
  console.log("Updated data with form values:", updatedData._processedPackageData);
  return updatedData;
}

// Get Monday.com configuration based on data type with full field mappings
function getMondayConfigForType(userConfig, dataType) {
  const config = {
    boardId: null,
    groupId: null,
    columnMappings: {}
  };
  
  // Extract configuration based on data type
  if (dataType === "SH") {
    config.boardId = userConfig["SH- Board ID"];
    config.groupId = userConfig["SH-Group ID"];
    
    // Map column IDs for SH
    config.columnMappings = {
      // Document fields
      orderNumber: userConfig["SH-Order Number"],
      orderType: userConfig["SH-Order Type"],
      customerName: userConfig["SH-Customer Name"],
      referenceNumber: userConfig["SH-Reference Number"],
      
      // Shipment info fields
      country: userConfig["SH-Country"],
      shipmentDescription: userConfig["SH-Shipment Description"],
      shipmentMethod: userConfig["SH-Shipment Method"],
      date: userConfig["SH-Date"],
      
      // Package fields
      packageQty: userConfig["SH-Package Qty"],
      packageType: userConfig["SH-Package Type"],
      dims: userConfig["SH-Dims"],
      weight: userConfig["SH-Weight"],
      freightCost: userConfig["SH-Freight Cost"],
      carrier: userConfig["SH-Carrier"],
      trackingNumber: userConfig["SH-Tracking Number"],
      trackingLink: userConfig["SH-Tracking Link"],
      packageNote: userConfig["SH_Package note"],
      
      // Part fields
      inventoryID: userConfig["SH-Inventory ID"],
      description: userConfig["SH-Part Description"],
      shippedQty: userConfig["SH-Shipped Qty"],
      serialNumber: userConfig["SH-Serial Nbr"],
      hsCode: userConfig["SH-HS Code"],
      countryOfOrigin: userConfig["SH-Country of Origin"]
    };
  } 
  else if (dataType === "PO") {
    config.boardId = userConfig["PO- Board ID"];
    config.groupId = userConfig["PO-Group ID"];
    
    // Map column IDs for PO with corrected field mappings
    config.columnMappings = {
      // Document fields - note that orderNumber and referenceNumber both map to PO-Reference Number
      orderNumber: userConfig["PO-Reference Number"],
      referenceNumber: userConfig["PO-Reference Number"],
      orderType: userConfig["PO-Order Type"],
      customerName: userConfig["PO-Customer Name"],
      
      // Shipment info fields
      country: userConfig["PO-Country"],
      shipmentDescription: userConfig["PO-Shipment Description"],
      shipmentMethod: userConfig["PO-Shipment Method"],
      
      // Package fields - all properly mapped to PO fields
      packageQty: userConfig["PO-Package Qty"],
      packageType: userConfig["PO-Package Type"],
      dims: userConfig["PO-Dims"],
      weight: userConfig["PO-Weight"],
      freightCost: userConfig["PO-Freight Cost"],
      carrier: userConfig["PO-Carrier"],
      // Special tracking number field name for PO
      trackingNumber: userConfig["PO-Tracking No"],
      trackingNo: userConfig["PO-Tracking No"],
      trackingLink: userConfig["PO-Tracking Link"],
      packageNote: userConfig["PO-Package Note"],
      
      // Part fields
      inventoryID: userConfig["PO-Inventory ID"],
      description: userConfig["PO-Part Description"],
      shippedQty: userConfig["PO-Part Qty"],
      serialNumber: userConfig["PO-Serial Nbr"],
      supplierPN: userConfig["PO-Supplier P/N"],
      hsCode: userConfig["PO-HS Code"],
      countryOfOrigin: userConfig["PO-COO"]
    };
  } 
  else if (dataType === "SO") {
    config.boardId = userConfig["SO- Board ID"];
    config.groupId = userConfig["SO-Group ID"];
    
    // Map column IDs for SO
    config.columnMappings = {
      customerName: userConfig["SO-Customer Name"],
      // Order number for SO uses Sales Order Number field
      orderNumber: userConfig["SO-Sales Order Number"],
      orderType: userConfig["SO-Order Type"],
      inventoryID: userConfig["SO-Inventory ID"],
      description: userConfig["SO-Description"],
      shippedQty: userConfig["SO-Order Qty"],
      productionOrderType: userConfig["SO-Production Order Type"],
      productionNumber: userConfig["SO-Production Number"]
    };
  }
  
  return config;
}

// Enhanced function to send data to Monday including subitems for parts
async function sendDataToMondayWithSubitems(data, userConfig, mondayConfig) {
  try {
    // Extract Monday.com API key from user config
    const apiKey = userConfig["Monday API Key"];
    
    // Extract board ID and group ID
    const { boardId, groupId, columnMappings } = mondayConfig;
    
    // Format the item name based on data type and sanitize
    const safeDataType = sanitizeForMondayAPI(data.type || "SH");
    const safeReference = sanitizeForMondayAPI(data.reference || data.orderNumber || "N/A");
    let itemName = `${safeDataType} - ${safeReference}`;
    
    // Build column values based on data type and columnMappings
    const columnValues = {};
    
    // For SH documents, ensure order number and reference number are differentiated
    if (data.type === "SH") {
      // For SH, order number should be SO number, and reference number is SH number
      const orderNum = data.orderNumber || data.soNumber || 
                       (data.parts && data.parts.length > 0 ? data.parts[0].orderNumber : "N/A");
      const refNum = data.reference || data.shipmentNumber || "N/A";
      
      if (columnMappings.orderNumber) {
        columnValues[columnMappings.orderNumber] = sanitizeForMondayAPI(orderNum);
      }
      
      if (columnMappings.referenceNumber) {
        columnValues[columnMappings.referenceNumber] = sanitizeForMondayAPI(refNum);
      }
    } else {
      // For other document types, map as usual
      if (columnMappings.orderNumber && data.orderNumber) {
        columnValues[columnMappings.orderNumber] = sanitizeForMondayAPI(data.orderNumber);
      }
      
      if (columnMappings.referenceNumber && data.reference) {
        columnValues[columnMappings.referenceNumber] = sanitizeForMondayAPI(data.reference);
      }
    }
    
    // Map customer name
    if (columnMappings.customerName && data.customer) {
      columnValues[columnMappings.customerName] = sanitizeForMondayAPI(data.customer);
    }
    
    // Map order type
    if (columnMappings.orderType && data.orderType) {
      columnValues[columnMappings.orderType] = sanitizeForMondayAPI(data.orderType);
    }
    
    // Extract and map shipment info fields - handle both shipmentInfo and direct properties
    const shipmentInfo = data.shipmentInfo || {};
    
    // Map shipment description - check multiple locations
    const shipmentDescription = 
      shipmentInfo.shipmentDescription || 
      data.shipmentDescription || 
      data.description || 
      "Package Shipment"; // Default value
    
    if (columnMappings.shipmentDescription) {
      columnValues[columnMappings.shipmentDescription] = sanitizeForMondayAPI(shipmentDescription);
    }
    
    // Map other shipment info fields with sanitization
    if (columnMappings.country && shipmentInfo.country) {
      columnValues[columnMappings.country] = sanitizeForMondayAPI(shipmentInfo.country);
    }
    
    // Map shipping method from form input
    if (columnMappings.shipmentMethod) {
      const shippingMethod = data.packageInfo && data.packageInfo.shipmentMethod ? 
                            data.packageInfo.shipmentMethod : 
                            (shipmentInfo.shippingMethod || "N/A");
      columnValues[columnMappings.shipmentMethod] = sanitizeForMondayAPI(shippingMethod);
    }
    
    if (columnMappings.date && shipmentInfo.shipmentDate) {
      columnValues[columnMappings.date] = sanitizeForMondayAPI(shipmentInfo.shipmentDate);
    }
    
    // Use the processed package data (collected from form inputs)
    const pd = data._processedPackageData || {};
    
    // Handle all package fields with sanitization
    const packageQty = pd.packageQty || "1"; // Default to 1 instead of N/A
    const packageType = pd.packageType || "Box"; // Default to Box 
    const packageDims = pd.packageDims || "N/A";
    const packageWeight = pd.packageWeight || "N/A";
    const freightCost = pd.freightCost || "N/A";
    const carrier = pd.carrier || "N/A";
    const trackingNumber = pd.trackingNumber || "N/A";
    const packageNote = pd.packageNote || "N/A";
    
    // Generate tracking link if carrier and tracking number are available
    let trackingLink = pd.trackingLink || "N/A";
    if ((trackingLink === "N/A") && carrier !== "N/A" && trackingNumber !== "N/A") {
      // First check if a custom tracking URL was already generated and saved
      if (data.packageInfo && data.packageInfo.trackingUrl) {
        trackingLink = data.packageInfo.trackingUrl;
      } else {
        // Fall back to generating a standard tracking link
        trackingLink = generateTrackingLink(carrier, trackingNumber);
      }
    }
    
    // Map all these values as text to Monday with sanitization
    if (columnMappings.packageQty) {
      columnValues[columnMappings.packageQty] = sanitizeForMondayAPI(packageQty);
    }
    
    if (columnMappings.packageType) {
      columnValues[columnMappings.packageType] = sanitizeForMondayAPI(packageType);
    }
    
    if (columnMappings.dims) {
      columnValues[columnMappings.dims] = sanitizeForMondayAPI(packageDims);
    }
    
    if (columnMappings.weight) {
      columnValues[columnMappings.weight] = sanitizeForMondayAPI(packageWeight);
    }
    
    if (columnMappings.freightCost) {
      columnValues[columnMappings.freightCost] = sanitizeForMondayAPI(freightCost);
    }
    
    if (columnMappings.carrier) {
      columnValues[columnMappings.carrier] = sanitizeForMondayAPI(carrier);
    }
    
    // Handle special field naming for PO vs SH
    const trackingFieldName = data.type === "PO" ? 
      columnMappings.trackingNumber || columnMappings.trackingNo : 
      columnMappings.trackingNumber;
    
    if (trackingFieldName) {
      columnValues[trackingFieldName] = sanitizeForMondayAPI(trackingNumber);
    }
    
    if (columnMappings.trackingLink) {
      // Send tracking link as plain text as requested
      columnValues[columnMappings.trackingLink] = sanitizeForMondayAPI(trackingLink);
    }
    
    if (columnMappings.packageNote) {
      columnValues[columnMappings.packageNote] = sanitizeForMondayAPI(packageNote);
    }
    
    // Create the main item mutation
    const createItemMutation = `
      mutation {
        create_item (
          board_id: ${boardId},
          group_id: "${groupId}",
          item_name: "${itemName}",
          column_values: ${JSON.stringify(JSON.stringify(columnValues))}
        ) {
          id
        }
      }
    `;
    
    console.log("Creating main Monday.com item");
    
    // Make the API call to create the main item
    const response = await fetch("https://api.monday.com/v2", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": apiKey
      },
      body: JSON.stringify({ query: createItemMutation })
    });
    
    if (!response.ok) {
      throw new Error(`Monday.com API error: ${response.status} ${response.statusText}`);
    }
    
    const responseData = await response.json();
    if (responseData.errors) {
      throw new Error(`Monday.com API error: ${responseData.errors[0].message}`);
    }
    
    const parentItemId = responseData.data.create_item.id;
    console.log(`Created main item with ID: ${parentItemId}`);
    
    // Step 2: Create subitems for each part
    const subItemIds = [];
    
    if (data.parts && data.parts.length > 0) {
      console.log(`Creating ${data.parts.length} subitems for parts`);
      
      for (const part of data.parts) {
        // Skip parts with no inventory ID
        if (!part.inventoryID) {
          console.warn("Skipping part with no inventory ID");
          continue;
        }
        
        // Create sanitized subitem name from part ID and description
        const safeInventoryID = sanitizeForMondayAPI(part.inventoryID);
        const safeDescription = sanitizeForMondayAPI(part.description || 'N/A');
        const subitemName = `${safeInventoryID} - ${safeDescription}`;
        
        // Build subitem column values - ALL AS TEXT with sanitization
        const subitemColumnValues = {};
        
        // Map part-specific fields
        if (columnMappings.inventoryID) {
          subitemColumnValues[columnMappings.inventoryID] = sanitizeForMondayAPI(part.inventoryID || "N/A");
        }
        
        if (columnMappings.description) {
          subitemColumnValues[columnMappings.description] = sanitizeForMondayAPI(part.description || "N/A");
        }
        
        if (columnMappings.shippedQty) {
          subitemColumnValues[columnMappings.shippedQty] = sanitizeForMondayAPI(part.shippedQuantity || "N/A");
        }
        
        if (columnMappings.serialNumber) {
          subitemColumnValues[columnMappings.serialNumber] = sanitizeForMondayAPI(part.lotSerialNumber || "N/A");
        }
        
        // Add master part data if available - ALL AS TEXT with sanitization
        if (part.masterPartData) {
          const mpd = part.masterPartData;
          
          if (columnMappings.hsCode && mpd["HS Code"]) {
            subitemColumnValues[columnMappings.hsCode] = sanitizeForMondayAPI(mpd["HS Code"]);
          }
          
          if (columnMappings.countryOfOrigin && mpd["Country of Origin"]) {
            subitemColumnValues[columnMappings.countryOfOrigin] = sanitizeForMondayAPI(mpd["Country of Origin"]);
          }
          
          // Map additional fields if they exist in the mappings
          if (data.type === "PO" && columnMappings.supplierPN && mpd["Supplier P/N"]) {
            subitemColumnValues[columnMappings.supplierPN] = sanitizeForMondayAPI(mpd["Supplier P/N"]);
          }
        }
        
        // Create the subitem mutation
        const createSubitemMutation = `
          mutation {
            create_subitem (
              parent_item_id: ${parentItemId},
              item_name: "${subitemName}",
              column_values: ${JSON.stringify(JSON.stringify(subitemColumnValues))}
            ) {
              id
            }
          }
        `;
        
        try {
          // Make the API call to create the subitem
          const subitemResponse = await fetch("https://api.monday.com/v2", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": apiKey
            },
            body: JSON.stringify({ query: createSubitemMutation })
          });
          
          if (!subitemResponse.ok) {
            console.error(`Error creating subitem: ${subitemResponse.status} ${subitemResponse.statusText}`);
            continue;
          }
          
          const subitemResponseData = await subitemResponse.json();
          if (subitemResponseData.errors) {
            console.error(`Error creating subitem: ${subitemResponseData.errors[0].message}`);
            continue;
          }
          
          const subitemId = subitemResponseData.data.create_subitem.id;
          subItemIds.push(subitemId);
          console.log(`Created subitem with ID: ${subitemId} for part ${part.inventoryID}`);
          
        } catch (subitemError) {
          console.error(`Error creating subitem for part ${part.inventoryID}:`, subitemError);
          // Continue with next part even if this one fails
        }
      }
    }
    
    return {
      id: parentItemId,
      subItemIds: subItemIds
    };
    
  } catch (error) {
    console.error("Error sending to Monday.com:", error);
    throw new Error(`Monday.com API error: ${error.message}`);
  }
}

/**
 * Sanitizes string values to safely use them in JSON and GraphQL queries
 * Removes or replaces problematic characters like double quotes
 * @param {string} str - The string to sanitize
 * @return {string} - The sanitized string
 */
function sanitizeForMondayAPI(str) {
  if (str === null || str === undefined) {
    return '';
  }
  
  // Convert to string if not already
  str = String(str);
  
  // Replace double quotes with single quotes to avoid breaking JSON
  str = str.replace(/"/g, "'");
  
  // Remove any characters that could cause issues with GraphQL queries
  str = str.replace(/[\u0000-\u001F\u007F-\u009F\\]/g, '');
  
  return str;
}

// Log data to History Log Sheet via SheetDB.io
async function logToHistorySheet(data, userConfig) {
  try {
    // SheetDB API configuration for history log
    const apiEndpoint = "https://sheetdb.io/api/v1/ygn268dcacru7";
    
    // Fix the authorization header format
    const apiKey = userConfig["SheetDB API Key"] || "tgive5whsoypqz9f6lq7ggno3xkamhp1dhqhc9ed";
    const headers = {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`
    };
    
    // Use package data from Monday processing if available
    const pd = data._processedPackageData || {};
    
    // Ensure we have package data from form inputs
    let packageQty = pd.packageQty || "1";
    let packageType = pd.packageType || "Box"; 
    let packageDims = pd.packageDims || "N/A";
    let packageWeight = pd.packageWeight || "N/A";
    let freightCost = pd.freightCost || "N/A";
    let carrier = pd.carrier || "N/A";
    let trackingNumber = pd.trackingNumber || "N/A";
    let trackingLink = pd.trackingLink || "N/A";
    let packageNote = pd.packageNote || "N/A";
    let shipmentMethod = pd.shipmentMethod || "N/A";
    
    // Generate tracking link if carrier and tracking number are available
    if ((trackingLink === "N/A") && carrier !== "N/A" && trackingNumber !== "N/A") {
      // First check if a custom tracking URL was already generated and saved
      if (data.packageInfo && data.packageInfo.trackingUrl) {
        trackingLink = data.packageInfo.trackingUrl;
      } else {
        // Fall back to generating a standard tracking link
        trackingLink = generateTrackingLink(carrier, trackingNumber);
      }
    }
    
    // Shipment Description - check multiple locations
    let shipmentDescription = pd.shipmentDescription || "N/A";
    if (!shipmentDescription || shipmentDescription === "N/A") {
      if (data.shipmentInfo && data.shipmentInfo.shipmentDescription) {
        shipmentDescription = data.shipmentInfo.shipmentDescription;
      } else if (data.shipmentDescription) {
        shipmentDescription = data.shipmentDescription;
      } else if (data.description && typeof data.description === 'string') {
        shipmentDescription = data.description;
      } else {
        shipmentDescription = "Package Shipment"; // Default
      }
    }
    
    // Differentiate order number and reference number for SH
    let orderNumber = data.orderNumber || "N/A";
    let referenceNumber = data.reference || "N/A";
    
    if (data.type === "SH") {
      // For SH, order number should be SO number, and reference number is SH number
      orderNumber = data.orderNumber || data.soNumber || 
                   (data.parts && data.parts.length > 0 ? data.parts[0].orderNumber : "N/A");
      referenceNumber = data.reference || data.shipmentNumber || "N/A";
    }
    
    // Collect all parts info into concatenated strings instead of just first part
    let allInventoryIDs = "";
    let allPartDescriptions = "";
    let allPartQuantities = "";
    let allSerialNumbers = "";
    
    if (data.parts && data.parts.length > 0) {
      // Collect all parts information into concatenated strings
      allInventoryIDs = data.parts.map(part => part.inventoryID || "N/A").join(", ");
      allPartDescriptions = data.parts.map(part => part.description || "N/A").join(", ");
      allPartQuantities = data.parts.map(part => part.shippedQuantity || "N/A").join(", ");
      allSerialNumbers = data.parts.map(part => part.lotSerialNumber || "N/A").join(", ");
    }
    
    // Create the date in YYYY-MM-DD format
    const today = new Date().toISOString().split('T')[0];
    
    // Format data for the history log sheet - EXACT field names to match structure
    const sheetData = {
      "User Email": userConfig.Email || "N/A",
      "Role": userConfig.Role || "N/A",
      "User Name": userConfig["User Name"] || "N/A",
      "Avatar": userConfig.Avatar || "N/A",
      "Action Performed": "Send to Monday",
      "Action Details": `${data.type} data sent to Monday.com`,
      "Customer Name": data.customer || "N/A",
      "Reference Number": referenceNumber,
      "Order Type": data.orderType || "N/A",
      "Order Number": orderNumber,
      "Company Name": data.shipmentInfo?.companyName || "N/A",
      "Attention": data.shipmentInfo?.attention || "N/A",
      "Phone": data.shipmentInfo?.phone || "N/A",
      "Email": data.shipmentInfo?.email || "N/A",
      "Address": data.shipmentInfo?.addressLine1 || "N/A",
      "City": data.shipmentInfo?.city || "N/A",
      "State": data.shipmentInfo?.state || "N/A",
      "Postal Code": data.shipmentInfo?.postalCode || "N/A",
      "Country": data.shipmentInfo?.country || "N/A",
      "Shipping Method": shipmentMethod,
      "Shipping Terms": data.shipmentInfo?.shippingTerms || "N/A",
      "Description": shipmentDescription,
      "Package Qty": packageQty,
      "Package Type": packageType,
      "Package Dimensions": packageDims,
      "Package Weight": packageWeight,
      "Freight Cost": freightCost,
      "Carrier": carrier,
      "Tracking Number": trackingNumber,
      "Tracking Link": trackingLink,
      "Package Note": packageNote,
      "Photo Taken": "N/A", // For future implementation
      "Photo Taken By": "N/A", // For future implementation
      "Inventory ID": allInventoryIDs,
      "Part Description": allPartDescriptions,
      "Part Quantity": allPartQuantities,
      "Serial Nbr": allSerialNumbers,
      "Date": today
    };
    
    console.log("Sending to History Log Sheet via SheetDB:", sheetData);
    
    // Try different API endpoints or authentication methods if the main one fails
    let response;
    let attempt = 1;
    
    // First attempt with Bearer token
    try {
      console.log(`SheetDB API attempt ${attempt} with Bearer token`);
      response = await fetch(apiEndpoint, {
        method: "POST",
        headers: headers,
        body: JSON.stringify(sheetData)
      });
      
      if (!response.ok) {
        console.warn(`SheetDB API attempt ${attempt} failed with status: ${response.status}`);
        throw new Error(`Status: ${response.status}`);
      }
    } catch (error) {
      console.warn(`SheetDB API attempt ${attempt} failed:`, error);
      
      // Second attempt with Basic auth
      attempt++;
      console.log(`Trying SheetDB API attempt ${attempt} with Basic auth`);
      
      try {
        response = await fetch(apiEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Basic ${apiKey}`
          },
          body: JSON.stringify(sheetData)
        });
        
        if (!response.ok) {
          console.warn(`SheetDB API attempt ${attempt} failed with status: ${response.status}`);
          throw new Error(`Status: ${response.status}`);
        }
      } catch (error) {
        console.warn(`SheetDB API attempt ${attempt} failed:`, error);
        
        // Third attempt with API key as URL parameter
        attempt++;
        console.log(`Trying SheetDB API attempt ${attempt} with API key as URL parameter`);
        
        response = await fetch(`${apiEndpoint}?api_key=${apiKey}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(sheetData)
        });
        
        if (!response.ok) {
          console.warn(`SheetDB API attempt ${attempt} failed with status: ${response.status}`);
          throw new Error(`Status: ${response.status}`);
        }
      }
    }
    
    // If we get here, one of the attempts succeeded
    const responseData = await response.json();
    console.log("SheetDB API response:", responseData);
    
    return {
      success: true,
      id: responseData.created || responseData.id || Date.now().toString(),
      message: "Successfully logged to history sheet"
    };
    
  } catch (error) {
    console.error("Error logging to History Sheet:", error);
    
    // Even if SheetDB fails, don't fail the entire operation
    return {
      success: false,
      id: Date.now().toString(),
      message: `Error logging to history: ${error.message}`
    };
  }
}

// Generate tracking link based on carrier and tracking number
function generateTrackingLink(carrier, trackingNumber) {
  if (!carrier || !trackingNumber) {
    return "";
  }
  
  // Standard tracking URLs for common carriers
  const trackingUrls = {
    "FedEx": `https://www.fedex.com/apps/fedextrack/?tracknumbers=${trackingNumber}`,
    "UPS": `https://www.ups.com/track?tracknum=${trackingNumber}`,
    "USPS": `https://tools.usps.com/go/TrackConfirmAction?tLabels=${trackingNumber}`,
    "DHL": `https://www.dhl.com/en/express/tracking.html?AWB=${trackingNumber}`,
    "Purolator": `https://www.purolator.com/en/shipping/track/details?pin=${trackingNumber}`,
    "Loomis": `https://www.ship24.com/tracking?p=${trackingNumber}`,
    "Manitoulin": `https://www.mtdirect.ca/MANITOULIN/pages/PROBILL?output=5&probill=${trackingNumber}`,
    "Rosenau": `https://www.rosenau.ca/track-shipment?trackingNumber=${trackingNumber}`,
    "HiWay9": `https://www.hi-way9.com/tracking?trackingNumber=${trackingNumber}`,
    "JazooExpress": `http://jazoocourier.com/tracking?num=${trackingNumber}`,
    "Kindersley": `https://www.kindersleytransport.com/tracking?code=${trackingNumber}`,
    "BREckels": `http://trace.breckels.com/ImagingDB/top/interface-degama/webTraceRoot/index.php?track=${trackingNumber}`,
    "Grimshaw": `https://www.grimshaw-trucking.com/track/v2/index.jsp?id=${trackingNumber}`,
    // Default to a generic tracking site
    "default": `https://www.ship24.com/tracking?p=${trackingNumber}`
  };
  
  // Add custom carriers from localStorage
  try {
    const customTrackingUrls = JSON.parse(localStorage.getItem('customTrackingUrls') || '{}');
    // Merge the custom tracking URLs with the standard ones
    Object.assign(trackingUrls, customTrackingUrls);
  } catch (e) {
    console.error("Error loading custom tracking URLs:", e);
  }
  
  // Get the base URL for this carrier
  let baseUrl = trackingUrls[carrier];
  
  // If no URL found, use the default
  if (!baseUrl) {
    console.log(`No tracking URL found for carrier ${carrier}, using default`);
    return trackingUrls["default"];
  }
  
  // Create the complete tracking URL
  let finalUrl;
  
  // Handle different URL formats
  if (baseUrl.includes("{tracking}")) {
    // Replace placeholder with tracking number
    finalUrl = baseUrl.replace("{tracking}", trackingNumber);
  } else if (baseUrl.includes("?") && !baseUrl.endsWith("?")) {
    // URL already has parameters
    finalUrl = baseUrl + (baseUrl.includes("=") ? "&" : "") + "tracking=" + trackingNumber;
  } else if (baseUrl.endsWith("?") || baseUrl.endsWith("/")) {
    // URL ends with ? or / - append tracking directly
    finalUrl = baseUrl + trackingNumber;
  } else {
    // Otherwise add as a path segment
    finalUrl = baseUrl + "/" + trackingNumber;
  }
  
  console.log(`Generated tracking URL for ${carrier}: ${finalUrl}`);
  return finalUrl;
}

// Helper function to map a field as a string if both mapping key and value exist
function mapStringFieldIfExists(mappingKey, value, targetObject) {
  if (mappingKey && (value !== undefined && value !== null)) {
    targetObject[mappingKey] = String(value);
  }
}

// Retrieve user configuration from storage
async function getUserConfig() {
  return new Promise((resolve) => {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["user"], (result) => {
        resolve(result.user);
      });
    } else {
      try {
        const userStr = localStorage.getItem("user");
        if (userStr) {
          resolve(JSON.parse(userStr));
        } else {
          resolve(null);
        }
      } catch (e) {
        console.error("Error parsing user data:", e);
        resolve(null);
      }
    }
  });
}

// Helper function to get current preview data
async function getCurrentPreviewData() {
  return new Promise((resolve) => {
    if (typeof chrome !== "undefined" && chrome.storage) {
      chrome.storage.local.get(["currentPreviewData"], (result) => {
        resolve(result.currentPreviewData);
      });
    } else {
      try {
        const data = JSON.parse(localStorage.getItem("currentPreviewData"));
        resolve(data);
      } catch (e) {
        console.error("Error parsing preview data:", e);
        resolve(null);
      }
    }
  });
}

// Generate a unique ID for history entries
function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
}

// Helper function to update history in storage
async function updateHistoryInStorage(historyEntry) {
  return new Promise((resolve, reject) => {
    try {
      if (typeof chrome !== "undefined" && chrome.storage) {
        chrome.storage.local.get(["processHistory"], (result) => {
          let history = result.processHistory || [];
          history.unshift(historyEntry);
          
          // Limit history size
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          
          chrome.storage.local.set({ processHistory: history }, () => {
            if (chrome.runtime.lastError) {
              console.error("Error saving to history:", chrome.runtime.lastError);
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              console.log("History updated successfully");
              resolve();
            }
          });
        });
      } else {
        // For localStorage
        try {
          let history = JSON.parse(localStorage.getItem("processHistory")) || [];
          history.unshift(historyEntry);
          
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          
          localStorage.setItem("processHistory", JSON.stringify(history));
          console.log("History updated in localStorage");
          resolve();
        } catch (e) {
          console.error("Error updating history in localStorage:", e);
          reject(e);
        }
      }
    } catch (error) {
      console.error("Error in updateHistoryInStorage:", error);
      reject(error);
    }
  });
}

// Save Process function - Updated to accept preview data
export async function saveProcess(previewData = null) {
  showNotification("Saving process data...", "info");

  try {
    // Use provided preview data or fetch it from storage if not provided
    let data = previewData;
    if (!data) {
      data = await getCurrentPreviewData();
    }
    
    if (!data) {
      throw new Error("No preview data available");
    }

    const processedData = {
      type: data.type || "SaveProcess",
      action: "saveProcess",
      status: "Saved",
      timestamp: new Date().toISOString(),
      customer: data.customer || "N/A",
      reference: data.reference || "N/A",
      orderType: data.orderType || "N/A",
      orderNumber: data.orderNumber || "N/A",
      parts: data.parts || [],
      // Keep a reference to the original preview data
      previewData: data,
      // Add a unique ID for the history entry
      id: generateUniqueId()
    };

    // Removed internal saveToHistory call

    showNotification("Process data saved successfully.", "success");
    return processedData;
  } catch (error) {
    console.error("Error saving process:", error);
    showNotification(`Error saving process: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// Schedule Process function - This now prepares the data to be scheduled by dashboard-scheduling.js
export async function scheduleProcess(previewData = null) {
  try {
    // Use provided preview data or fetch it from storage if not provided
    let data = previewData;
    if (!data) {
      data = await getCurrentPreviewData();
    }
    
    if (!data) {
      throw new Error("No preview data available");
    }

    // We return the scheduling data which will be handled by the scheduling system
    return {
      type: data.type || "Schedule",
      action: "scheduleProcess",
      status: "Pending",
      timestamp: new Date().toISOString(),
      customer: data.customer || "N/A",
      reference: data.reference || "N/A",
      orderType: data.orderType || "N/A",
      orderNumber: data.orderNumber || "N/A",
      parts: data.parts || [],
      // Keep a reference to the original preview data
      previewData: data,
      // Add a unique ID for the history entry
      id: generateUniqueId()
    };
  } catch (error) {
    console.error("Error preparing schedule process:", error);
    showNotification(`Error preparing schedule: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// Tools tab actions
export async function clearHistory() {
  showNotification("Clearing history...", "info");

  try {
    if (typeof chrome !== "undefined" && chrome.storage) {
      await new Promise((resolve, reject) => {
        chrome.storage.local.remove(["processHistory"], () => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve();
          }
        });
      });
    } else {
      localStorage.removeItem("processHistory");
    }

    showNotification("History cleared successfully.", "success");
    return "History cleared successfully";
  } catch (error) {
    console.error("Error clearing history:", error);
    showNotification("Error clearing history.", "danger");
    throw error;
  }
}

export async function exportData() {
  showNotification("Exporting data...", "info");
  await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulating export process
  showNotification("Data exported successfully.", "success");
  return "Data exported";
}

// Validate Data function
export async function validateData() {
  if (!(await getSourceCode())) {
    showNotification("No data available. Please fetch data first.", "warning");
    return Promise.reject(new Error("No data available"));
  }

  showNotification("Validating data...", "info");

  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(await getSourceCode(), "text/html");

    // Perform validation checks (this is a simplified example, adjust according to your specific validation needs)
    const errors = [];

    // Check if essential elements exist
    if (!doc.querySelector("#usrCaption")) {
      errors.push("Missing user caption");
    }

    if (!doc.querySelector("#ctl00_phG_tab_t0_grid_dataT0")) {
      errors.push("Missing data grid");
    }

    // Add more validation checks as needed

    const validationResult = {
      timestamp: new Date().toISOString(),
      isValid: errors.length === 0,
      errors: errors,
      message: errors.length === 0 ? "Validation successful" : `Found ${errors.length} error(s)`
    };

    if (validationResult.isValid) {
      showNotification("Data validation completed successfully.", "success");
    } else {
      showNotification(`Data validation failed. ${errors.length} error(s) found.`, "warning");
    }

    return validationResult;
  } catch (error) {
    console.error("Error validating data:", error);
    showNotification(`Error validating data: ${error.message}`, "danger");
    return Promise.reject(error);
  }
}

// Get Process History function
export async function getProcessHistory() {
  return new Promise((resolve, reject) => {
    try {
      if (typeof chrome !== "undefined" && chrome.storage) {
        chrome.storage.local.get(["processHistory", "deletedHistoryIds"], (result) => {
          if (chrome.runtime.lastError) {
            console.error("Error getting history:", chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          
          // Get the deleted IDs
          const deletedIds = (result.deletedHistoryIds || []).map(id => normalizeId(id));
          
          // Filter out deleted entries
          let history = result.processHistory || [];
          history = history.filter(item => !item.id || !deletedIds.includes(normalizeId(item.id)));
          
          resolve(history);
        });
      } else {
        try {
          // Get deleted IDs from localStorage
          let deletedIds = [];
          try {
            const deletedIdsStr = localStorage.getItem("deletedHistoryIds");
            if (deletedIdsStr) {
              deletedIds = JSON.parse(deletedIdsStr).map(id => normalizeId(id));
            }
          } catch (e) {
            console.warn("Error reading deleted IDs:", e);
          }
          
          // Get and filter history
          const historyStr = localStorage.getItem("processHistory");
          let history = historyStr ? JSON.parse(historyStr) : [];
          
          // Filter out deleted entries
          history = history.filter(item => !item.id || !deletedIds.includes(normalizeId(item.id)));
          
          resolve(history);
        } catch (e) {
          console.error("Error parsing history:", e);
          resolve([]);
        }
      }
    } catch (error) {
      console.error("Error in getProcessHistory:", error);
      reject(error);
    }
  });
}

// Helper function to normalize ID for consistent comparison
function normalizeId(id) {
  return String(id).trim();
}

// Empty placeholder functions for incomplete actions
export async function updateAcumatica() {
  showNotification("Update Acumatica feature is not implemented yet.", "info");
  return "Update Acumatica feature is not implemented yet";
}

export async function shipOrder() {
  // Implementation moved to dashboard-shiporder.js
  // This function remains for backward compatibility
  return; // Return nothing to prevent notification
}

export async function bulkActions() {
  showNotification("Bulk Actions feature is not implemented yet.", "info");
  return "Bulk Actions feature is not implemented yet";
}