// Home component for KPI Dashboard
import { HomeSettings, CardTemplates } from "./home-setting.js";

export class HomeComponent {
  constructor(container) {
    this.container = container;
    this.cardPositions = this.loadCardPositions() || {};
    this.isEditMode = false;
    this.homeSettings = new HomeSettings();
  }

  init() {
    this.render();
    this.setupEventListeners();
    
    // Apply any saved settings
    const settings = this.homeSettings.loadSettings();
    if (settings) {
      this.applySettings(settings);
    }
    
    // Add custom CSS for toggle switches and panels
    this.addCustomStyles();
    
    this.initMotion();
  }

  // Load saved card positions from localStorage
  loadCardPositions() {
    try {
      const savedPositions = localStorage.getItem('kpiCardPositions');
      return savedPositions ? JSON.parse(savedPositions) : null;
    } catch (e) {
      console.error('Error loading card positions:', e);
      return null;
    }
  }

  // Save card positions to localStorage
  saveCardPositions() {
    try {
      localStorage.setItem('kpiCardPositions', JSON.stringify(this.cardPositions));
    } catch (e) {
      console.error('Error saving card positions:', e);
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold">KPI Dashboard Overview</h1>
          <div class="flex gap-2 items-center">
            <button id="manageCardsBtn" class="border border-purple-500 dark:border-purple-500 rounded-md px-3 py-1.5 bg-purple-600 dark:bg-purple-600 focus:outline-none focus:ring-purple-500 focus:border-purple-500 hover:bg-purple-700 dark:hover:bg-purple-700 text-white dark:text-white" style="height: 36px;">
              <i class="fas fa-th-large mr-1"></i> Manage cards
            </button>
            <button id="settingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-gray-100 dark:bg-gray-700 focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-500 dark:text-gray-400 flex items-center justify-center" style="height: 36px; width: 36px;">
              <i class="fas fa-cog"></i>
            </button>
            <button id="saveLayoutBtn" class="border border-purple-500 dark:border-purple-500 rounded-md px-3 py-1.5 bg-purple-600 dark:bg-purple-600 focus:outline-none focus:ring-purple-500 focus:border-purple-500 hover:bg-purple-700 dark:hover:bg-purple-700 text-white dark:text-white hidden" style="height: 36px;">
              <i class="fas fa-save mr-1"></i> Save Layout
            </button>
            <button id="resetLayoutBtn" class="border border-purple-500 dark:border-purple-500 rounded-md px-3 py-1.5 bg-purple-600 dark:bg-purple-600 focus:outline-none focus:ring-purple-500 focus:border-purple-500 hover:bg-purple-700 dark:hover:bg-purple-700 text-white dark:text-white hidden" style="height: 36px;">
              <i class="fas fa-undo mr-1"></i> Reset
            </button>
          </div>
        </div>
        
        <div class="kpi-grid" id="kpiGrid">
          <!-- Sales KPI Card -->
          <div class="kpi-card" data-card-id="sales">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">Total Sales</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">30 Days</span>
            </div>
            <div class="kpi-value text-blue-600">$1,245,600</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-green-500">
                <path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>
              </svg>
              <span class="text-green-600">+12.5%</span> vs last period
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="sales">View Details →</button>
            </div>
          </div>
          
          <!-- Finance KPI Card -->
          <div class="kpi-card" data-card-id="finance">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">Gross Profit</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Monthly</span>
            </div>
            <div class="kpi-value text-green-600">$458,320</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-green-500">
                <path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>
              </svg>
              <span class="text-green-600">+8.3%</span> vs last month
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="finance">View Details →</button>
            </div>
          </div>
          
          <!-- Purchasing KPI Card -->
          <div class="kpi-card" data-card-id="purchasing">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">Purchase Orders</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Weekly</span>
            </div>
            <div class="kpi-value text-indigo-600">142</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-red-500">
                <path fill="currentColor" d="M7 10l5 5 5-5H7z"></path>
              </svg>
              <span class="text-red-600">-3.2%</span> vs last week
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="purchasing">View Details →</button>
            </div>
          </div>
          
          <!-- Inventory KPI Card -->
          <div class="kpi-card" data-card-id="inventory">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">Inventory Value</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Current</span>
            </div>
            <div class="kpi-value text-yellow-600">$3,782,950</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-green-500">
                <path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>
              </svg>
              <span class="text-green-600">+5.7%</span> since last month
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="inventory">View Details →</button>
            </div>
          </div>
          
          <!-- Logistics KPI Card -->
          <div class="kpi-card" data-card-id="logistics">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">On-Time Delivery</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">30 Days</span>
            </div>
            <div class="kpi-value text-purple-600">94.2%</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-green-500">
                <path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>
              </svg>
              <span class="text-green-600">+1.8%</span> vs previous period
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="logistics">View Details →</button>
            </div>
          </div>
          
          <!-- Projects KPI Card -->
          <div class="kpi-card" data-card-id="projects">
            <div class="drag-handle">
              <i class="fas fa-grip-horizontal"></i>
            </div>
            <div class="resize-handle"></div>
            <div class="flex justify-between items-center">
              <h3 class="font-semibold text-gray-700">Active Projects</h3>
              <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Current</span>
            </div>
            <div class="kpi-value text-teal-600">38</div>
            <div class="text-sm text-gray-500 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="w-4 h-4 mr-1 text-green-500">
                <path fill="currentColor" d="M7 14l5-5 5 5H7z"></path>
              </svg>
              <span class="text-green-600">+4</span> new projects this month
            </div>
            <div class="mt-3 text-right">
              <button class="text-blue-600 hover:text-blue-800 text-sm" data-component="projects">View Details →</button>
            </div>
          </div>
          
          <!-- Add Card Element -->
          <div class="kpi-card add-card cursor-pointer flex flex-col items-center justify-center" data-card-id="add-card">
            <div class="w-24 h-24 mb-2">
              <img src="../images/kpi/add_card.svg" alt="Add Card" class="w-full h-full">
            </div>
            <h3 class="font-semibold text-gray-700 dark:text-gray-300">Add New KPI</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 text-center mt-1">Create a custom metric</p>
          </div>
        </div>
        
        <!-- Recent Activity Section -->
        <div class="mt-6">
          <h2 class="text-xl font-semibold mb-4">Recent Activity</h2>
          <div class="bg-white rounded-lg shadow-sm p-4 dark:bg-gray-800">
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
              <li class="py-3 flex justify-between items-center">
                <div>
                  <span class="text-sm font-medium">Sales target achieved</span>
                  <p class="text-xs text-gray-500">Monthly sales target exceeded by 12%</p>
                </div>
                <span class="text-xs text-gray-500">2 hours ago</span>
              </li>
              <li class="py-3 flex justify-between items-center">
                <div>
                  <span class="text-sm font-medium">Inventory alert</span>
                  <p class="text-xs text-gray-500">Low stock warning for 5 items</p>
                </div>
                <span class="text-xs text-gray-500">Yesterday</span>
              </li>
              <li class="py-3 flex justify-between items-center">
                <div>
                  <span class="text-sm font-medium">New project started</span>
                  <p class="text-xs text-gray-500">Project #1234 has been initiated</p>
                </div>
                <span class="text-xs text-gray-500">2 days ago</span>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- Data Connection Status -->
        <div class="mt-6">
          <h2 class="text-xl font-semibold mb-4">Data Connection Status</h2>
          <div class="bg-white rounded-lg shadow-sm p-4 dark:bg-gray-800">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-sm">Acumatica ERP: Connected</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm">Monday.com: Disconnected</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                <span class="text-sm">ShipWave: Connection Issue</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span class="text-sm">Freight Sample: Connected</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Manage Cards Panel (hidden by default) -->
      <div id="manageCardsPanel" class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 dark:bg-gray-800 overflow-y-auto">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 class="text-lg font-semibold flex items-center">
            <i class="fas fa-th-large mr-2 text-gray-500"></i>
            Manage Cards
          </h3>
          <button id="closeManageCards" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md p-1 focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700" style="height: 36px; width: 36px;">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-4">
          <div class="mb-4">
            <h4 class="font-medium mb-2 flex items-center">
              <i class="fas fa-list-ul mr-2 text-gray-500"></i>
              Available Cards
            </h4>
            <div class="space-y-2">
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Sales KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="sales" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Finance KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="finance" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Purchasing KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="purchasing" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Inventory KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="inventory" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Logistics KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="logistics" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
              <div class="flex items-center justify-between p-2 bg-gray-50 rounded dark:bg-gray-700">
                <span>Projects KPI</span>
                <div>
                  <button class="text-green-600 hover:text-green-800 border border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md px-2 py-1 focus:outline-none focus:ring-green-500 focus:border-green-500 hover:bg-green-100" data-card="projects" data-action="add" style="height: 32px; width: 32px;">
                    <i class="fas fa-plus-circle"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <h4 class="font-medium mb-2 flex items-center">
              <i class="fas fa-cog mr-2 text-gray-500"></i>
              Actions
            </h4>
            <div class="space-y-2">
              <button id="removeAllCards" class="w-full py-2 px-4 border border-red-300 bg-red-50 text-red-700 rounded hover:bg-red-100 dark:border-red-600 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50" style="height: 40px;">
                <i class="fas fa-trash-alt mr-2"></i> Remove All Cards
              </button>
              <button id="resetDefaultCards" class="w-full py-2 px-4 border border-purple-500 bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50" style="height: 40px;">
                <i class="fas fa-undo mr-2"></i> Reset to Default
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Settings Panel (hidden by default) -->
      <div id="settingsPanel" class="fixed top-0 right-0 h-full w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 dark:bg-gray-800 overflow-y-auto">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
          <h3 class="text-lg font-semibold flex items-center">
            <i class="fas fa-cog mr-2 text-gray-500"></i>
            Home Settings
          </h3>
          <button id="closeSettings" class="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md p-1 focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700" style="height: 36px; width: 36px;">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="p-4">
          <div class="mb-4">
            <h4 class="font-medium mb-2 flex items-center">
              <i class="fas fa-desktop mr-2 text-gray-500"></i>
              Display Options
            </h4>
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <span>Show Recent Activity</span>
                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                  <input type="checkbox" id="showRecentActivity" checked class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                  <label for="showRecentActivity" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span>Show Connection Status</span>
                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                  <input type="checkbox" id="showConnectionStatus" checked class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                  <label for="showConnectionStatus" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span>Auto Refresh Data</span>
                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                  <input type="checkbox" id="autoRefreshData" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"/>
                  <label for="autoRefreshData" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                </div>
              </div>
            </div>
          </div>
          <div class="mb-4">
            <h4 class="font-medium mb-2 flex items-center">
              <i class="fas fa-th mr-2 text-gray-500"></i>
              Layout
            </h4>
            <div class="space-y-2">
              <div>
                <label class="block text-sm mb-1">Cards Per Row</label>
                <select id="cardsPerRow" class="w-full p-2 border border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                  <option value="2">2 Cards</option>
                  <option value="3" selected>3 Cards</option>
                  <option value="4">4 Cards</option>
                </select>
              </div>
              <div>
                <label class="block text-sm mb-1">Card Size</label>
                <select id="cardSize" class="w-full p-2 border border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600">
                  <option value="small">Small</option>
                  <option value="medium" selected>Medium</option>
                  <option value="large">Large</option>
                </select>
              </div>
            </div>
          </div>
          <div class="mt-6">
            <button id="saveSettings" class="w-full py-2 px-4 border border-purple-500 bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50" style="height: 40px;">
              <i class="fas fa-save mr-2"></i> Save Settings
            </button>
          </div>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Add event listeners for "View Details" buttons
    const detailButtons = this.container.querySelectorAll('button[data-component]');
    detailButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const componentName = e.target.getAttribute('data-component');
        if (componentName) {
          // Find the nav item with the matching component name and trigger a click
          const navItem = document.querySelector(`.nav-item[data-component="${componentName}"]`);
          if (navItem) {
            navItem.click();
          }
        }
      });
    });
    
    // Add event listener for the "Add Card" element
    const addCardElement = this.container.querySelector('.add-card');
    if (addCardElement) {
      addCardElement.addEventListener('click', () => {
        // This is just a placeholder for the actual functionality
        console.log('Add new KPI card clicked');
        // Display a more informative message to the user
        alert('Add Custom KPI Feature\n\nThis feature will allow you to create custom KPI cards with your own metrics and data sources. Coming soon in the next update!');
      });
    }

    // Add event listener for the Save Layout button
    const saveLayoutBtn = this.container.querySelector('#saveLayoutBtn');
    if (saveLayoutBtn) {
      saveLayoutBtn.addEventListener('click', () => {
        this.saveCardPositions();
        alert('Dashboard layout saved successfully!');
        this.toggleEditMode();
      });
    }

    // Add event listener for the Reset Layout button
    const resetLayoutBtn = this.container.querySelector('#resetLayoutBtn');
    if (resetLayoutBtn) {
      resetLayoutBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to reset the dashboard layout to default?')) {
          this.cardPositions = {};
          localStorage.removeItem('kpiCardPositions');
          this.init();
          this.toggleEditMode();
        }
      });
    }

    // Add event listener for the Edit Mode toggle in the sidebar
    const editModeToggle = document.querySelector('#toggleEditMode');
    if (editModeToggle) {
      editModeToggle.addEventListener('click', () => {
        this.toggleEditMode();
      });
    }
    
    // Add event listener for the Manage Cards button
    const manageCardsBtn = this.container.querySelector('#manageCardsBtn');
    if (manageCardsBtn) {
      manageCardsBtn.addEventListener('click', () => {
        this.toggleManageCardsPanel();
      });
    }
    
    // Add event listener for the close Manage Cards button
    const closeManageCards = this.container.querySelector('#closeManageCards');
    if (closeManageCards) {
      closeManageCards.addEventListener('click', () => {
        this.toggleManageCardsPanel(false);
      });
    }
    
    // Add event listener for the Settings button
    const settingsBtn = this.container.querySelector('#settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.toggleSettingsPanel();
      });
    }
    
    // Add event listener for the close Settings button
    const closeSettings = this.container.querySelector('#closeSettings');
    if (closeSettings) {
      closeSettings.addEventListener('click', () => {
        this.toggleSettingsPanel(false);
      });
    }
    
    // Add event listener for the Remove All Cards button
    const removeAllCards = this.container.querySelector('#removeAllCards');
    if (removeAllCards) {
      removeAllCards.addEventListener('click', () => {
        if (confirm('Are you sure you want to remove all cards from the dashboard?')) {
          this.removeAllCards();
        }
      });
    }
    
    // Add event listener for the Reset to Default button
    const resetDefaultCards = this.container.querySelector('#resetDefaultCards');
    if (resetDefaultCards) {
      resetDefaultCards.addEventListener('click', () => {
        if (confirm('Are you sure you want to reset all cards to their default state?')) {
          this.resetDefaultCards();
        }
      });
    }
    
    // Add event listener for the Save Settings button
    const saveSettings = this.container.querySelector('#saveSettings');
    if (saveSettings) {
      saveSettings.addEventListener('click', () => {
        this.saveSettings();
      });
    }
    
    // Add event listeners for the card management actions
    const cardActionButtons = this.container.querySelectorAll('[data-action]');
    cardActionButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = e.target.closest('[data-action]').getAttribute('data-action');
        const cardId = e.target.closest('[data-card]').getAttribute('data-card');
        
        if (action === 'add') {
          this.addCard(cardId);
        }
      });
    });
  }

  // Toggle the Manage Cards panel
  toggleManageCardsPanel(show) {
    const panel = this.container.querySelector('#manageCardsPanel');
    if (!panel) return;
    
    const isCurrentlyShown = !panel.classList.contains('translate-x-full');
    
    if (show === undefined) {
      // Toggle based on current state
      panel.classList.toggle('translate-x-full');
    } else if (show) {
      // Show panel
      panel.classList.remove('translate-x-full');
    } else if (isCurrentlyShown) {
      // Only hide if it's currently shown
      panel.classList.add('translate-x-full');
    } else {
      // Already hidden, do nothing
      return;
    }
    
    // Close settings panel if open and we're opening this panel
    if (show !== false) {
      const settingsPanel = this.container.querySelector('#settingsPanel');
      if (settingsPanel && !settingsPanel.classList.contains('translate-x-full')) {
        settingsPanel.classList.add('translate-x-full');
      }
    }
    
    // Add overlay when panel is shown
    this.togglePanelOverlay(!panel.classList.contains('translate-x-full'));
  }
  
  // Toggle the Settings panel
  toggleSettingsPanel(show) {
    const panel = this.container.querySelector('#settingsPanel');
    if (!panel) return;
    
    const isCurrentlyShown = !panel.classList.contains('translate-x-full');
    
    if (show === undefined) {
      // Toggle based on current state
      panel.classList.toggle('translate-x-full');
    } else if (show) {
      // Show panel
      panel.classList.remove('translate-x-full');
    } else if (isCurrentlyShown) {
      // Only hide if it's currently shown
      panel.classList.add('translate-x-full');
    } else {
      // Already hidden, do nothing
      return;
    }
    
    // Close manage cards panel if open and we're opening this panel
    if (show !== false) {
      const manageCardsPanel = this.container.querySelector('#manageCardsPanel');
      if (manageCardsPanel && !manageCardsPanel.classList.contains('translate-x-full')) {
        manageCardsPanel.classList.add('translate-x-full');
      }
    }
    
    // Add overlay when panel is shown
    this.togglePanelOverlay(!panel.classList.contains('translate-x-full'));
  }
  
  // Toggle panel overlay
  togglePanelOverlay(show) {
    let overlay = document.getElementById('panelOverlay');
    
    if (!overlay && show) {
      overlay = document.createElement('div');
      overlay.id = 'panelOverlay';
      overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity';
      overlay.addEventListener('click', () => {
        this.toggleManageCardsPanel(false);
        this.toggleSettingsPanel(false);
      });
      document.body.appendChild(overlay);
      
      // Fade in
      setTimeout(() => {
        overlay.classList.add('opacity-100');
      }, 10);
    } else if (overlay && !show) {
      // Fade out
      overlay.classList.remove('opacity-100');
      overlay.classList.add('opacity-0');
      
      // Remove after transition
      setTimeout(() => {
        if (overlay && overlay.parentNode) {
          overlay.parentNode.removeChild(overlay);
        }
      }, 300);
    }
  }
  
  // Handle adding a card
  addCard(cardId) {
    // Get the KPI grid element
    const grid = this.container.querySelector('#kpiGrid');
    if (!grid) {
      console.error('KPI grid not found');
      return;
    }
    
    // Check if card already exists
    const existingCard = this.container.querySelector(`.kpi-card[data-card-id="${cardId}"]`);
    if (existingCard) {
      if (existingCard.style.display === 'none') {
        // If card exists but is hidden, show it
        existingCard.style.display = '';
        this.showToast(`Card ${cardId} restored to dashboard`);
      } else {
        this.showToast(`Card ${cardId} is already on the dashboard`);
        return;
      }
    } else {
      // Card doesn't exist, create a new one
      const cardData = this.getCardData(cardId);
      if (!cardData) {
        console.error(`Card data not found for ${cardId}`);
        return;
      }
      
      // Create new card HTML
      const newCardHTML = window.CardTemplates.standardKPI(
        cardId,
        cardData.title,
        cardData.value || '$0',
        cardData.period || '30 Days',
        cardData.change || 0,
        cardId
      );
      
      // Create temporary container to convert HTML string to DOM element
      const tempContainer = document.createElement('div');
      tempContainer.innerHTML = newCardHTML.trim();
      const newCard = tempContainer.firstChild;
      
      // Insert before the "Add Card" element
      const addCardElement = this.container.querySelector('.add-card');
      if (addCardElement) {
        grid.insertBefore(newCard, addCardElement);
      } else {
        grid.appendChild(newCard);
      }
      
      // Setup event listeners for the new card
      this.setupCardEventListeners(newCard);
    }
    
    // Save card configuration
    this.homeSettings.addCard(cardId);
    
    // Show success message
    this.showToast(`Card ${cardId} added to dashboard`);
    
    // Close the panel after adding a card
    setTimeout(() => {
      this.toggleManageCardsPanel(false);
    }, 500);
  }
  
  // Handle removing a specific card
  removeCard(cardId) {
    const card = this.container.querySelector(`.kpi-card[data-card-id="${cardId}"]`);
    if (card) {
      // Hide the card instead of removing it
      card.style.display = 'none';
      
      // Update settings
      this.homeSettings.removeCard(cardId);
      
      this.showToast(`Card ${cardId} removed from dashboard`);
    }
  }
  
  // Setup event listeners for a card
  setupCardEventListeners(card) {
    // Setup View Details button
    const detailButton = card.querySelector('button[data-component]');
    if (detailButton) {
      detailButton.addEventListener('click', (e) => {
        const componentName = e.target.getAttribute('data-component');
        if (componentName) {
          // Find the nav item with the matching component name and trigger a click
          const navItem = document.querySelector(`.nav-item[data-component="${componentName}"]`);
          if (navItem) {
            navItem.click();
          }
        }
      });
    }
    
    // Setup drag and resize functionality
    const dragHandle = card.querySelector('.drag-handle');
    const resizeHandle = card.querySelector('.resize-handle');
    
    if (dragHandle) {
      dragHandle.addEventListener('mousedown', (e) => {
        if (!this.isEditMode) return;
        
        // Implement dragging logic here (already in initMotion)
      });
    }
    
    if (resizeHandle) {
      // Resize logic is handled in handleResize method
    }
  }
  
  // Get card data for a given card ID
  getCardData(cardId) {
    // Default data for all cards
    const defaultCardData = {
      sales: {
        title: 'Total Sales',
        value: '$1,245,600',
        period: '30 Days',
        change: 12.5
      },
      finance: {
        title: 'Gross Profit',
        value: '$458,320',
        period: 'Monthly',
        change: 8.3
      },
      purchasing: {
        title: 'Purchase Orders',
        value: '142',
        period: 'Weekly',
        change: -3.2
      },
      inventory: {
        title: 'Inventory Value',
        value: '$3,782,950',
        period: 'Current',
        change: 5.7
      },
      logistics: {
        title: 'On-Time Delivery',
        value: '94.2%',
        period: '30 Days',
        change: 1.8
      },
      projects: {
        title: 'Active Projects',
        value: '38',
        period: 'Current',
        change: 4
      },
      customers: {
        title: 'Active Customers',
        value: '1,276',
        period: 'Current',
        change: 2.3
      },
      quotes: {
        title: 'Open Quotes',
        value: '54',
        period: 'Current',
        change: -1.5
      }
    };
    
    return defaultCardData[cardId];
  }
  
  // Handle removing all cards
  removeAllCards() {
    const grid = this.container.querySelector('#kpiGrid');
    const cards = grid.querySelectorAll('.kpi-card:not(.add-card)');
    
    // Hide all cards except the add card
    cards.forEach(card => {
      card.style.display = 'none';
    });
    
    this.showToast('All cards removed from dashboard');
    this.toggleManageCardsPanel(false);
  }
  
  // Reset to default cards
  resetDefaultCards() {
    // Simply reload the component to restore default state
    this.init();
    this.showToast('Dashboard reset to default cards');
    this.toggleManageCardsPanel(false);
  }
  
  // Save settings
  saveSettings() {
    // Get settings values
    const showRecentActivity = this.container.querySelector('#showRecentActivity').checked;
    const showConnectionStatus = this.container.querySelector('#showConnectionStatus').checked;
    const autoRefreshData = this.container.querySelector('#autoRefreshData').checked;
    const cardsPerRow = this.container.querySelector('#cardsPerRow').value;
    const cardSize = this.container.querySelector('#cardSize').value;
    
    // Store settings in localStorage
    const settings = {
      showRecentActivity,
      showConnectionStatus,
      autoRefreshData,
      cardsPerRow,
      cardSize
    };
    
    localStorage.setItem('homeSettings', JSON.stringify(settings));
    
    // Apply settings
    this.applySettings(settings);
    
    this.showToast('Settings saved successfully');
    this.toggleSettingsPanel(false);
  }
  
  // Apply settings to the dashboard
  applySettings(settings) {
    const recentActivitySection = this.container.querySelector('.mt-6:nth-of-type(1)');
    const connectionStatusSection = this.container.querySelector('.mt-6:nth-of-type(2)');
    const grid = this.container.querySelector('#kpiGrid');
    
    // Show/hide recent activity
    if (recentActivitySection) {
      recentActivitySection.style.display = settings.showRecentActivity ? 'block' : 'none';
    }
    
    // Show/hide connection status
    if (connectionStatusSection) {
      connectionStatusSection.style.display = settings.showConnectionStatus ? 'block' : 'none';
    }
    
    // Set cards per row
    if (grid) {
      grid.style.gridTemplateColumns = `repeat(${settings.cardsPerRow}, 1fr)`;
    }
    
    // Set card size
    const cards = this.container.querySelectorAll('.kpi-card');
    let cardHeight = '150px'; // medium (default)
    
    if (settings.cardSize === 'small') {
      cardHeight = '120px';
    } else if (settings.cardSize === 'large') {
      cardHeight = '180px';
    }
    
    cards.forEach(card => {
      card.style.minHeight = cardHeight;
    });
    
    // Handle auto refresh if enabled
    if (settings.autoRefreshData && !this._refreshInterval) {
      this._refreshInterval = setInterval(() => {
        console.log('Auto refreshing data...');
        // In a real implementation, you would update the card data here
      }, 60000); // Refresh every minute
    } else if (!settings.autoRefreshData && this._refreshInterval) {
      clearInterval(this._refreshInterval);
      this._refreshInterval = null;
    }
  }

  // Initialize motion.js for drag and resize functionality
  initMotion() {
    const initDraggableCards = () => {
      try {
        if (typeof window.Motion === 'undefined') {
          console.error('Motion library not found!');
          return;
        }

        const grid = this.container.querySelector('#kpiGrid');
        const cards = this.container.querySelectorAll('.kpi-card');

        // Apply saved positions if available
        if (this.cardPositions && Object.keys(this.cardPositions).length > 0) {
          cards.forEach(card => {
            const cardId = card.getAttribute('data-card-id');
            if (cardId && this.cardPositions[cardId]) {
              const pos = this.cardPositions[cardId];
              if (pos.column && pos.columnSpan) {
                card.style.gridColumn = `${pos.column} / span ${pos.columnSpan}`;
              }
              if (pos.row && pos.rowSpan) {
                card.style.gridRow = `${pos.row} / span ${pos.rowSpan}`;
              }
            }
          });
        }

        const gridRect = grid.getBoundingClientRect();
        const gridWidth = gridRect.width;
        const gridHeight = gridRect.height;
        const colWidth = gridWidth / 3; // Assuming 3 columns
        const rowHeight = 150; // Approximate row height

        // Setup draggable functionality
        cards.forEach(card => {
          const dragHandle = card.querySelector('.drag-handle');
          const resizeHandle = card.querySelector('.resize-handle');
          
          if (!dragHandle) return;

          // Store original position for later use
          card._originalGridColumn = card.style.gridColumn;
          card._originalGridRow = card.style.gridRow;
          card._originalTransform = card.style.transform;

          // Setup drag functionality using Motion.animate
          dragHandle.addEventListener('mousedown', (e) => {
            if (!this.isEditMode) return;
            
            e.preventDefault();
            
            // Mark the card as being dragged
            card.classList.add('dragging');
            
            // Get initial positions
            const cardRect = card.getBoundingClientRect();
            const startX = e.clientX;
            const startY = e.clientY;
            const startLeft = cardRect.left - gridRect.left;
            const startTop = cardRect.top - gridRect.top;
            
            // Calculate grid position
            const calculateGridPosition = (x, y) => {
              const col = Math.max(1, Math.ceil(x / colWidth));
              const row = Math.max(1, Math.ceil(y / rowHeight));
              return { col, row };
            };
            
            // Apply grid position
            const applyGridPosition = (col, row) => {
              const cardId = card.getAttribute('data-card-id');
              if (!cardId) return;
              
              if (!this.cardPositions[cardId]) {
                this.cardPositions[cardId] = {};
              }
              
              this.cardPositions[cardId].column = col;
              this.cardPositions[cardId].row = row;
              
              // Set the columnSpan and rowSpan if they don't exist
              this.cardPositions[cardId].columnSpan = this.cardPositions[cardId].columnSpan || 1;
              this.cardPositions[cardId].rowSpan = this.cardPositions[cardId].rowSpan || 1;
              
              // Apply the position
              card.style.gridColumn = `${col} / span ${this.cardPositions[cardId].columnSpan}`;
              card.style.gridRow = `${row} / span ${this.cardPositions[cardId].rowSpan}`;
              card.style.transform = 'none';
            };
            
            const onMouseMove = (moveEvent) => {
              const deltaX = moveEvent.clientX - startX;
              const deltaY = moveEvent.clientY - startY;
              
              // Update transform for visual feedback during drag
              card.style.transform = `translate(${deltaX}px, ${deltaY}px)`;
            };
            
            const onMouseUp = (upEvent) => {
              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
              
              // Remove dragging class
              card.classList.remove('dragging');
              
              // Calculate final position
              const deltaX = upEvent.clientX - startX;
              const deltaY = upEvent.clientY - startY;
              const finalLeft = startLeft + deltaX;
              const finalTop = startTop + deltaY;
              
              // Calculate and apply grid position
              const { col, row } = calculateGridPosition(finalLeft, finalTop);
              applyGridPosition(col, row);
            };
            
            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          });

          // Setup resize functionality
          if (resizeHandle) {
            resizeHandle.addEventListener('mousedown', this.handleResize.bind(this, card, grid, colWidth, rowHeight));
          }
        });
        
        console.log('Draggable cards initialized successfully');
      } catch (error) {
        console.error('Error initializing draggable cards:', error);
      }
    };

    // Try to initialize now if motion.js is already loaded
    if (typeof window.Motion !== 'undefined' || typeof window.motion !== 'undefined') {
      initDraggableCards();
    } else {
      // Listen for the custom event or try again after a delay
      document.addEventListener('motionJsLoaded', initDraggableCards, { once: true });
      console.log('Waiting for motion.js to load...');
      
      // Fallback: try again after a short delay in case the event isn't fired
      setTimeout(initDraggableCards, 1000);
    }
  }

  // Handle card resizing
  handleResize(card, grid, colWidth, rowHeight, e) {
    // Only allow resizing in edit mode
    if (!this.isEditMode) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = card.offsetWidth;
    const startHeight = card.offsetHeight;
    
    const originalZIndex = card.style.zIndex;
    card.style.zIndex = "99";
    
    // Add a resize indicator to the card
    card.classList.add('resizing');
    
    const onMouseMove = (moveEvent) => {
      const deltaX = moveEvent.clientX - startX;
      const deltaY = moveEvent.clientY - startY;
      
      // Calculate how many columns/rows to span
      const colSpan = Math.max(1, Math.round((startWidth + deltaX) / colWidth));
      const rowSpan = Math.max(1, Math.round((startHeight + deltaY) / rowHeight));
      
      // Apply to card
      card.style.gridColumnEnd = `span ${colSpan}`;
      card.style.gridRowEnd = `span ${rowSpan}`;
      
      // Update tracking object
      const cardId = card.getAttribute('data-card-id');
      if (cardId && this.cardPositions[cardId]) {
        this.cardPositions[cardId].columnSpan = colSpan;
        this.cardPositions[cardId].rowSpan = rowSpan;
      }
    };
    
    const onMouseUp = () => {
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
      card.style.zIndex = originalZIndex;
      card.classList.remove('resizing');
      
      // Save the layout
      this.saveCardPositions();
    };
    
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
  }

  // Toggle edit mode on/off
  toggleEditMode() {
    this.isEditMode = !this.isEditMode;
    
    // Toggle edit mode class on grid
    const grid = this.container.querySelector('#kpiGrid');
    if (grid) {
      grid.classList.toggle('edit-mode', this.isEditMode);
    }
    
    // Toggle visibility of edit buttons
    const saveBtn = this.container.querySelector('#saveLayoutBtn');
    const resetBtn = this.container.querySelector('#resetLayoutBtn');
    if (saveBtn) saveBtn.classList.toggle('hidden', !this.isEditMode);
    if (resetBtn) resetBtn.classList.toggle('hidden', !this.isEditMode);
    
    // Update the cursor style on all cards
    const cards = this.container.querySelectorAll('.kpi-card');
    cards.forEach(card => {
      if (this.isEditMode) {
        card.style.cursor = 'move';
        // Show drag and resize handles more prominently when in edit mode
        const dragHandle = card.querySelector('.drag-handle');
        const resizeHandle = card.querySelector('.resize-handle');
        if (dragHandle) dragHandle.style.opacity = '1';
        if (resizeHandle) resizeHandle.style.opacity = '1';
      } else {
        card.style.cursor = '';
        // Hide handles when not in edit mode
        const dragHandle = card.querySelector('.drag-handle');
        const resizeHandle = card.querySelector('.resize-handle');
        if (dragHandle) dragHandle.style.opacity = '';
        if (resizeHandle) resizeHandle.style.opacity = '';
        
        // Auto-save when exiting edit mode
        this.saveCardPositions();
      }
    });
    
    // Update sidebar button to indicate state
    const editModeToggle = document.querySelector('#toggleEditMode');
    if (editModeToggle) {
      editModeToggle.classList.toggle('active', this.isEditMode);
      const statusText = editModeToggle.querySelector('span');
      if (statusText) {
        statusText.textContent = this.isEditMode ? 'Exit Edit Mode' : 'Edit Dashboard';
      }
    }
    
    // Show a toast notification
    if (this.isEditMode) {
      this.showToast('Edit mode enabled. Drag cards to reposition them, use the resize handle to change size.');
    } else {
      this.showToast('Layout saved successfully!');
    }
  }
  
  // Simple toast notification helper
  showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded shadow-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.classList.add('opacity-0');
      toast.style.transition = 'opacity 0.5s ease';
      setTimeout(() => toast.remove(), 500);
    }, 3000);
  }

  // Add custom CSS styles for toggle switches and panels
  addCustomStyles() {
    const styleId = 'kpi-dashboard-styles';
    
    // Only add if not already present
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        /* Toggle Switch Styles */
        .toggle-checkbox:checked {
          right: 0;
          border-color: #3B82F6;
        }
        .toggle-checkbox:checked + .toggle-label {
          background-color: #3B82F6;
        }
        
        /* Panel transition improvements */
        #manageCardsPanel, #settingsPanel {
          box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        }
        
        #panelOverlay {
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        /* Card hover styles in edit mode */
        .kpi-grid.edit-mode .kpi-card:hover {
          box-shadow: 0 0 0 2px #3B82F6;
        }
        
        /* Improve resize handle visibility */
        .resize-handle {
          opacity: 0;
          transition: opacity 0.2s ease;
        }
        .kpi-card:hover .resize-handle {
          opacity: 0.5;
        }
        .edit-mode .kpi-card:hover .resize-handle {
          opacity: 1;
        }
      `;
      document.head.appendChild(style);
    }
  }
} 