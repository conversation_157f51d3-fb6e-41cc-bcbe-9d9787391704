// Import components from their respective folders
import { HomeComponent } from "./home/<USER>";
import { SalesComponent } from "./sales/sales.js";
import { FinanceComponent } from "./finance/finance.js";
import { PurchasingComponent } from "./purchasing/purchasing.js";
import { InventoryComponent } from "./inventory/inventory.js";
import { LogisticsComponent } from "./logistics/logistics.js";
import { ProjectsComponent } from "./projects/projects.js";
import { MRPComponent } from "./mrp/mrp.js";
import { toggleDarkMode, loadDarkModePreference } from "../core/settings.js";
import { NotificationSystem } from "../core/notifications.js";
import { connectionUI } from "../core/connection-ui.js";

// Make motion.js available globally
window.addEventListener('load', () => {
  // Create a function to load motion.js
  const loadMotionJs = () => {
    return new Promise((resolve, reject) => {
      // If motion.js is already loaded, resolve immediately
      if (typeof window.Motion !== 'undefined') {
        console.log('Motion.js already loaded');
        resolve(window.Motion);
        return;
      }
      
      console.warn('Loading motion.js for draggable cards');
      
      // Create script element to load motion.js
      const scriptElement = document.createElement('script');
      scriptElement.src = '../kpi/library/motion.js';
      scriptElement.async = true;
      
      scriptElement.onload = () => {
        console.log('motion.js loaded successfully');
        
        // Ensure Motion is available globally
        if (typeof window.Motion === 'undefined' && typeof window.motion !== 'undefined') {
          window.Motion = window.motion;
        }
        
        // Make sure Motion is available
        if (typeof window.Motion === 'undefined') {
          console.warn('Motion library loaded but Motion object not found in window');
          
          // Create a simple fallback for dragging
          window.Motion = {
            animate: (target, keyframes, options) => {
              console.log('Using fallback animation');
              // Basic functionality to ensure something works
              return {
                finished: Promise.resolve(),
                stop: () => {}
              };
            }
          };
        }
        
        // Trigger a custom event that components can listen for
        document.dispatchEvent(new CustomEvent('motionJsLoaded'));
        resolve(window.Motion);
      };
      
      scriptElement.onerror = (err) => {
        console.error('Failed to load motion.js', err);
        
        // Create a simple fallback for dragging even if load fails
        window.Motion = {
          animate: (target, keyframes, options) => {
            console.log('Using fallback animation due to load error');
            return {
              finished: Promise.resolve(),
              stop: () => {}
            };
          }
        };
        
        // Still trigger the event so components can proceed with fallback
        document.dispatchEvent(new CustomEvent('motionJsLoaded'));
        reject(err);
      };
      
      // Add the script to the document
      document.head.appendChild(scriptElement);
    });
  };

  // Try to load motion.js
  loadMotionJs().catch(err => {
    console.error('Error initializing motion.js:', err);
  });
});

// Declare chrome variable if it's not defined globally
const chrome = window.chrome || {};

document.addEventListener("DOMContentLoaded", () => {
  // Get DOM elements
  const userAvatar = document.getElementById("userAvatar");
  const userName = document.getElementById("userName");
  const logoutButton = document.getElementById("logoutButton");
  const notificationButton = document.getElementById("notificationButton");
  const searchInput = document.querySelector(".search-input");
  const searchButton = document.querySelector(".search-button");
  const dynamicContent = document.getElementById("dynamicContent");
  const navItems = document.querySelectorAll(".nav-item");
  let currentComponent = null;
  
  // Initialize notification and connection systems
  const notificationSystem = new NotificationSystem();
  
  // Register all components
  const components = {
    home: HomeComponent,
    sales: SalesComponent,
    finance: FinanceComponent,
    purchasing: PurchasingComponent,
    inventory: InventoryComponent,
    logistics: LogisticsComponent,
    projects: ProjectsComponent,
    mrp: MRPComponent
  };

  // Store the current user role
  let currentUserRole = null;

  // Load user data
  loadUserData();
  
  // Initialize notification system
  initializeNotificationSystem();
  
  // Initialize connection system
  initializeConnectionSystem();

  // Event Handlers
  logoutButton.addEventListener("click", handleLogout);
  notificationButton.addEventListener("click", handleNotifications);

  // Navigation items
  navItems.forEach((item) => {
    item.addEventListener("click", function (event) {
      // Ignore clicks on the collapse sidebar button
      if (this.id === "collapseSidebar") return;
      
      event.preventDefault();
      const componentName = this.getAttribute("data-component");
      
      if (componentName) {
        switchContent(componentName);
        navItems.forEach((i) => i.classList.remove("active"));
        this.classList.add("active");

        // Add and remove 'flash' class for animation
        this.classList.add("flash");
        setTimeout(() => {
          this.classList.remove("flash");
        }, 300);
      }
    });
  });
  
  // Initialize the notification system
  async function initializeNotificationSystem() {
    try {
      await notificationSystem.init();
      console.log("Notification system initialized");
    } catch (error) {
      console.error("Error initializing notification system:", error);
    }
  }
  
  // Initialize the connection system
  async function initializeConnectionSystem() {
    try {
      await connectionUI.init();
      console.log("Connection system initialized");
    } catch (error) {
      console.error("Error initializing connection system:", error);
      notificationSystem.addNotification("Failed to initialize connection system. Some features may not work properly.", "error");
    }
  }
  
  // Handle notifications button click
  function handleNotifications() {
    notificationSystem.showNotificationPopup(document.body);
  }
  
  // Load user data from storage
  function loadUserData() {
    // Check if chrome is defined
    let chromeStorageAvailable = false;
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chromeStorageAvailable = true;
    }

    if (chromeStorageAvailable) {
      chrome.storage.local.get(["user"], (result) => {
        if (result.user) {
          userName.textContent = result.user["User Name"];
          userAvatar.src = `../images/avatars/avatar_${result.user.Avatar || "default"}.jpg`;
          
          // Store user role
          currentUserRole = result.user.Role;
        } else {
          console.warn("User data not found in storage");
          // Set default values
          userName.textContent = "Guest User";
          userAvatar.src = "../images/avatars/default.jpg";
        }
      });
    } else {
      console.warn("Chrome storage API not available. Running in a non-extension environment?");
      // Provide fallback behavior for non-extension environments
      let user = localStorage.getItem("user");
      if (user) {
        user = JSON.parse(user);
        userName.textContent = user["User Name"];
        userAvatar.src = `../images/avatars/avatar_${user.Avatar || "default"}.jpg`;
        
        // Store user role
        currentUserRole = user.Role;
      } else {
        // For demo purposes, create a temporary user
        const demoUser = {
          "User Name": "Demo User",
          "Avatar": "default",
          "Role": "User"
        };
        userName.textContent = demoUser["User Name"];
        userAvatar.src = `../images/avatars/default.jpg`;
      }
    }
  }

  // Handle logout
  function handleLogout() {
    let chromeStorageAvailable = false;
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chromeStorageAvailable = true;
    }

    if (chromeStorageAvailable) {
      chrome.storage.local.remove(["loggedIn", "user"], () => {
        chrome.action.setPopup({ popup: "login.html" }, () => {
          window.location.href = "../login.html";
        });
      });
    } else {
      // Fallback for non-extension environments
      localStorage.removeItem("loggedIn");
      localStorage.removeItem("user");
      window.location.href = "../login.html";
    }
  }

  // Dark Mode Functionality
  const darkModeButton = document.getElementById("darkModeButton");
  darkModeButton.addEventListener("click", toggleDarkMode);

  // Load dark mode preference
  loadDarkModePreference();

  // Sidebar Toggle Functionality
  const sidebar = document.getElementById("sidebar");
  const collapseSidebarButton = document.getElementById("collapseSidebar");
  const dashboardContainer = document.querySelector(".dashboard-container");

  collapseSidebarButton.addEventListener("click", (e) => {
    sidebar.classList.toggle("collapsed");
    
    // Save sidebar state
    const isCollapsed = sidebar.classList.contains("collapsed");
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chrome.storage.local.set({ sidebarCollapsed: isCollapsed });
    } else {
      localStorage.setItem("sidebarCollapsed", isCollapsed);
    }
  });

  // Load sidebar state
  function loadSidebarState() {
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chrome.storage.local.get(["sidebarCollapsed"], (result) => {
        if (result.sidebarCollapsed) {
          sidebar.classList.add("collapsed");
        }
      });
    } else {
      const isCollapsed = localStorage.getItem("sidebarCollapsed") === "true";
      if (isCollapsed) {
        sidebar.classList.add("collapsed");
      }
    }
  }

  // Show loading indicator in dynamic content area
  function showLoading() {
    dynamicContent.innerHTML = `
      <div class="component-loading">
        <div class="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    `;
  }

  // Show error message in dynamic content area
  function showError(message) {
    dynamicContent.innerHTML = `
      <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline">${message}</span>
      </div>
    `;
  }

  // Switch content based on component name
  function switchContent(componentName) {
    showLoading();

    setTimeout(() => {
      try {
        if (components[componentName]) {
          if (currentComponent) {
            // Clean up previous component if needed
          }
          
          // Initialize the component
          currentComponent = new components[componentName](dynamicContent);
          
          // Check if the component has an init method and call it
          if (typeof currentComponent.init === 'function') {
            currentComponent.init();
          } else if (typeof currentComponent.render === 'function') {
            // Fallback to render if init doesn't exist
            currentComponent.render();
          } else {
            throw new Error(`Component ${componentName} has no init or render method`);
          }
        } else {
          throw new Error(`Component ${componentName} not found`);
        }
      } catch (error) {
        console.error("Error rendering component:", error);
        showError(`Failed to load ${componentName} content. ${error.message}`);
      }
    }, 300);
  }

  // Initialize the KPI dashboard with the home component
  loadSidebarState();
  switchContent("home");
}); 