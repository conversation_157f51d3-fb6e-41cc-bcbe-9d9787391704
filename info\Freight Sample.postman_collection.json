{"info": {"_postman_id": "73be8884-616d-4b84-804e-35f0c1ae3a4d", "name": "Freight Sam<PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "39289533"}, "item": [{"name": "Shipment confrimation last step", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.freightsimpledemo.com/v1/shipments/get-shipment?shipmentId=dee5ffee-01e6-43ed-a48e-915a3b5eed6d", "protocol": "https", "host": ["api", "freightsimpledemo", "com"], "path": ["v1", "shipments", "get-shipment"], "query": [{"key": "shipmentId", "value": "dee5ffee-01e6-43ed-a48e-915a3b5eed6d"}]}}, "response": []}, {"name": "Shipment booking", "event": [{"listen": "test", "script": {"exec": ["var template = `\r", "<canvas id=\"myChart\" height=\"75\"></canvas>\r", "\r", "<script src=\"https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.5.0/Chart.min.js\"></script> \r", "\r", "<script>\r", "    var ctx = document.getElementById(\"myChart\");\r", "\r", "    var myChart = new Chart(ctx, {\r", "        type: \"pie\",\r", "        data: {\r", "            labels: [\"Error Code\", \"Error Message\"],\r", "            datasets: [{\r", "                data: [0, 0],\r", "                \r", "                // Change these colours to customize the chart\r", "                backgroundColor: [\"#003f5c\", \"#58508d\"],\r", "            }]\r", "        },\r", "        options: {\r", "            title: {\r", "                display: true,\r", "                text: '<PERSON><PERSON><PERSON>'\r", "            }\r", "        }\r", "\r", "    });\r", "\r", "    // Access the data passed to pm.visualizer.set() from the JavaScript\r", "    // code of the Visualizer template\r", "    pm.getData(function (err, value) {\r", "        myChart.data.datasets[0].data = [value.response.errorCode, value.response.errorMessage];\r", "        myChart.update();\r", "    });\r", "\r", "</script>`;\r", "\r", "function constructVisualizerPayload() {\r", "    var res = pm.response.json();\r", "\r", "    var visualizerData = {\r", "        // Error details\r", "        errorCode: res.errorCode,\r", "        errorMessage: res.errorMessage\r", "    };\r", "\r", "    return {response: visualizerData};\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"quoteId\": \"d9b72b0f-9ea5-4a3b-9fa8-3da241bebfc2\",\r\n  \"pickupDate\": \"2025-03-29\"\r\n}\r\n", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.freightsimpledemo.com/v1/shipments/start-booking", "protocol": "https", "host": ["api", "freightsimpledemo", "com"], "path": ["v1", "shipments", "start-booking"]}}, "response": []}, {"name": "shipment quoted", "event": [{"listen": "test", "script": {"exec": ["var template = `\r", "<style type=\"text/css\">\r", "    .tftable {font-size:14px;color:#333333;width:100%;border-width: 1px;border-color: #87ceeb;border-collapse: collapse;}\r", "    .tftable th {font-size:18px;background-color:#87ceeb;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;text-align:left;}\r", "    .tftable tr {background-color:#ffffff;}\r", "    .tftable td {font-size:14px;border-width: 1px;padding: 8px;border-style: solid;border-color: #87ceeb;}\r", "    .tftable tr:hover {background-color:#e0ffff;}\r", "</style>\r", "\r", "<table class=\"tftable\" border=\"1\">\r", "    <tr>\r", "        <th>Shipment ID</th>\r", "        <th>Shipment Status</th>\r", "        <th>Pickup Date</th>\r", "        <th>Booked At</th>\r", "        <th>Pickup Location</th>\r", "        <th>Delivery Location</th>\r", "    </tr>\r", "    <tr>\r", "        <td>{{response.shipmentId}}</td>\r", "        <td>{{response.shipmentStatus}}</td>\r", "        <td>{{response.pickupDate}}</td>\r", "        <td>{{response.bookedAt}}</td>\r", "        <td>{{response.pickupLocation.address.addressLine}}, {{response.pickupLocation.address.city}}, {{response.pickupLocation.address.stateOrProvinceCode}}, {{response.pickupLocation.address.countryCode}}</td>\r", "        <td>{{response.deliveryLocation.address.addressLine}}, {{response.deliveryLocation.address.city}}, {{response.deliveryLocation.address.stateOrProvinceCode}}, {{response.deliveryLocation.address.countryCode}}</td>\r", "    </tr>\r", "</table>\r", "`;\r", "\r", "function constructVisualizerPayload() {\r", "    return { response: pm.response.json() }\r", "}\r", "\r", "pm.visualizer.set(template, constructVisualizerPayload());"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://api.freightsimpledemo.com/v1/shipments/get-shipment?shipmentId=dee5ffee-01e6-43ed-a48e-915a3b5eed6d", "protocol": "https", "host": ["api", "freightsimpledemo", "com"], "path": ["v1", "shipments", "get-shipment"], "query": [{"key": "shipmentId", "value": "dee5ffee-01e6-43ed-a48e-915a3b5eed6d"}]}}, "response": []}, {"name": "Start quoting", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"pickupDate\": \"2025-03-31\",\r\n  \"pickup\": {\r\n    \"locationType\": \"Warehouse\",\r\n    \"businessName\": \"Pickup Business\",\r\n    \"addressLine\": \"2721 Hopewell Place NE\",\r\n    \"addressLine2\": \"N/A\",\r\n    \"city\": \"Calgary\",\r\n    \"stateOrProvinceCode\": \"AB\",\r\n    \"postalCode\": \"T1Y7J7\",\r\n    \"countryCode\": \"Canada\",\r\n    \"openFrom\": \"09:00\",\r\n    \"openUntil\": \"17:00\",\r\n    \"notes\": \"Notes...\",\r\n    \"contactName\": \"John <PERSON>\",\r\n    \"contactPhoneNumber\": \"9999999999\",\r\n    \"specialServiceInsideRequired\": false,\r\n    \"specialServiceLiftGateRequired\": false\r\n  },\r\n  \"delivery\": {\r\n    \"locationType\": \"Warehouse\",\r\n    \"businessName\": \"Delivery Business\",\r\n    \"addressLine\": \"10306 - 118 Street\",\r\n    \"addressLine2\": \"N/A\",\r\n    \"city\": \"Grand Prairie\",\r\n    \"stateOrProvinceCode\": \"AB\",\r\n    \"postalCode\": \"T8V3X9\",\r\n    \"countryCode\": \"Canada\",\r\n    \"openFrom\": \"09:00\",\r\n    \"openUntil\": \"17:00\",\r\n    \"notes\": \"Notes...\",\r\n    \"contactName\": \"<PERSON> Doe\",\r\n    \"contactPhoneNumber\": \"1234567890\",\r\n    \"specialServiceInsideRequired\": false,\r\n    \"specialServiceLiftGateRequired\": false,\r\n    \"specialServiceAppointmentRequired\": false\r\n  },\r\n  \"lineItems\": [\r\n    {\r\n      \"handlingUnitType\": \"Pallet\",\r\n      \"numberHandlingUnits\": 1,\r\n      \"description\": \"Line Item Description\",\r\n      \"lengthInches\": 30,\r\n      \"widthInches\": 30,\r\n      \"heightInches\": 30,\r\n      \"weightPerHandlingUnitPounds\": 200,\r\n      \"temperatureHandling\": \"NoSpecialHandling\",\r\n      \"isDangerous\": false,\r\n      \"isStackable\": false\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api.freightsimpledemo.com/v1/shipments/start-quoting", "protocol": "https", "host": ["api", "freightsimpledemo", "com"], "path": ["v1", "shipments", "start-quoting"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}