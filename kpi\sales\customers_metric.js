// Customers Metric component for Sales KPI Dashboard
export class CustomersMetricComponent {
  constructor(container) {
    this.container = container;
    this.customers = [];
    this.filteredCustomers = [];
    this.currentPage = 1;
    this.pageSize = 10;
    this.sortField = 'customerName';
    this.sortDirection = 'asc';
    this.searchTerm = '';
    this.isLoading = false;
    this.dbName = 'salesKpiDb';
    this.storeName = 'customers';
    this.viewMode = 'table'; // New property for tracking current view mode (table or map)
    this.mapComponent = null; // Will hold the map component instance
    
    // Import the ConnectionManager
    try {
      import('../../core/connection.js').then(module => {
        this.connectionManager = module.connectionManager;
      }).catch(error => {
        console.error('Error importing ConnectionManager:', error);
      });
    } catch (error) {
      console.error('Error importing ConnectionManager:', error);
    }
  }

  async init() {
    try {
      // Initialize the database
      await this.initDatabase();
      
      // Render the initial UI
      this.render();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load customer data
      await this.loadCustomerData();
    } catch (error) {
      console.error('Error initializing Customers Metric component:', error);
      this.showError('Failed to initialize the Customer Metrics component. Please try again later.');
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName, 1);
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create customers object store if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.log('Creating customers store in IndexedDB');
            const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
            store.createIndex('customerName', 'customerName', { unique: false });
            store.createIndex('customerID', 'customerID', { unique: true });
            store.createIndex('status', 'status', { unique: false });
          } else {
            console.log('Customers store already exists in IndexedDB');
          }
        };
        
        request.onsuccess = (event) => {
          console.log('IndexedDB connection established successfully');
          resolve(event.target.result);
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB:', event.target.error);
          // Instead of rejecting, resolve with null to allow fallback to mock data
          console.log('Falling back to mock data due to IndexedDB error');
          resolve(null);
        };
      } catch (error) {
        console.error('Exception in initDatabase:', error);
        // Resolve with null instead of rejecting to allow component to continue
        resolve(null);
      }
    });
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold">Customer Metrics</h2>
          <div class="flex items-center space-x-2">
            <div class="relative">
              <input 
                type="text" 
                id="customerSearch" 
                placeholder="Search customers..." 
                class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
              <div class="absolute left-3 top-2.5 text-gray-400">
                <i class="fas fa-search"></i>
              </div>
              <button id="clearSearchBtn" class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hidden">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button id="dateRangeBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Date Range">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button id="refreshCustomersBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Refresh Data">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>
            <button id="mapViewBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Map View">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6-3l-6-3m12 6l5.447 2.724A1 1 0 0021 16.382V5.618a1 1 0 00-1.447-.894L15 7m-6 3l6 3"></path>
              </svg>
            </button>
            <button id="exportCustomersBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Export Data">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
            </button>
            <button id="settingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Settings">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Table View -->
        <div id="tableView" class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="customerID">
                    <div class="flex items-center">
                      ID
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="customerName">
                    <div class="flex items-center">
                      Customer Name
                      <span class="sort-icon ml-1"><i class="fas fa-sort-up"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="status">
                    <div class="flex items-center">
                      Status
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="totalSales">
                    <div class="flex items-center">
                      Total Sales
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="lastOrder">
                    <div class="flex items-center">
                      Last Order
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderCount">
                    <div class="flex items-center">
                      Orders
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody id="customersTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Table content will be dynamically loaded -->
              </tbody>
            </table>
          </div>
          
          <!-- Loading Indicator -->
          <div id="customersLoadingIndicator" class="flex justify-center items-center p-10 hidden">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
          </div>
          
          <!-- Empty State -->
          <div id="customersEmptyState" class="flex flex-col justify-center items-center p-10 hidden">
            <i class="fas fa-users text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
            <p class="text-gray-500 dark:text-gray-400">No customer data available</p>
            <button id="loadCustomersBtn" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Load Customers
            </button>
          </div>
          
          <!-- Error State -->
          <div id="customersErrorState" class="hidden p-6">
            <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
              <div class="flex items-center">
                <div class="flex-shrink-0 text-red-500 dark:text-red-400">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ml-3">
                  <p id="errorMessage" class="text-sm text-red-800 dark:text-red-200">
                    Error loading customer data.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button id="mobilePagePrev" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Previous
              </button>
              <button id="mobilePageNext" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Showing
                  <span id="paginationStart" class="font-medium">1</span>
                  to
                  <span id="paginationEnd" class="font-medium">10</span>
                  of
                  <span id="paginationTotal" class="font-medium">0</span>
                  results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button id="pageFirst" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">First</span>
                    <i class="fas fa-angle-double-left"></i>
                  </button>
                  <button id="pagePrev" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Previous</span>
                    <i class="fas fa-angle-left"></i>
                  </button>
                  <span id="pageNumbers" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                    1 of 1
                  </span>
                  <button id="pageNext" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Next</span>
                    <i class="fas fa-angle-right"></i>
                  </button>
                  <button id="pageLast" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Last</span>
                    <i class="fas fa-angle-double-right"></i>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Map View -->
        <div id="mapView" class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden hidden h-[600px]">
          <div class="flex flex-col h-full">
            <!-- Map loading indicator -->
            <div id="mapLoadingIndicator" class="flex justify-center items-center p-10 absolute top-0 left-0 right-0 z-10 bg-white bg-opacity-80 dark:bg-gray-800 dark:bg-opacity-80">
              <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mr-3"></div>
              <p class="text-gray-700 dark:text-gray-300">Loading map...</p>
            </div>
            
            <!-- Map container - explicitly set height -->
            <div id="customerMapContainer" class="w-full h-[550px] relative"></div>
            
            <!-- Back to table button -->
            <div class="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
              <button id="backToTableBtn" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 flex items-center">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Table View
              </button>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                <span id="mapCustomerCount">0</span> customers displayed on map
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Add custom CSS for map markers
    const style = document.createElement('style');
    style.textContent = `
      /* Customer map markers */
      .customer-marker {
        width: 30px;
        height: 30px;
        cursor: pointer;
      }
      
      .marker-inner {
        width: 20px;
        height: 20px;
        background: #3182CE;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
        position: relative;
      }
      
      .marker-inner::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 8px solid white;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
      }
      
      .marker-green {
        background: #48BB78;
      }
      
      .marker-red {
        background: #F56565;
      }
      
      .marker-yellow {
        background: #ECC94B;
      }
      
      .marker-gray {
        background: #A0AEC0;
      }
      
      /* Customer popup */
      .customer-popup {
        max-width: 300px;
      }
      
      .customer-popup .mapboxgl-popup-content {
        padding: 15px;
        border-radius: 8px;
      }
      
      .dark .mapboxgl-popup-content {
        background-color: #2D3748;
        color: #E2E8F0;
      }
      
      .dark .mapboxgl-popup-tip {
        border-top-color: #2D3748;
        border-bottom-color: #2D3748;
      }
    `;
    document.head.appendChild(style);
    
    // Show loading indicator initially
    this.showLoading();
  }

  setupEventListeners() {
    // Table sorting
    const tableHeaders = this.container.querySelectorAll('th[data-sort]');
    tableHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        this.sortTable(field);
      });
    });
    
    // Search input
    const searchInput = this.container.querySelector('#customerSearch');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.searchTerm = searchInput.value;
        this.updateClearButton();
        this.applySearch();
      });
    }
    
    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clearSearchBtn');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        const searchInput = this.container.querySelector('#customerSearch');
        if (searchInput) {
          searchInput.value = '';
          this.searchTerm = '';
          this.updateClearButton();
          this.applySearch();
        }
      });
    }
    
    // Date range button
    const dateRangeBtn = this.container.querySelector('#dateRangeBtn');
    if (dateRangeBtn) {
      dateRangeBtn.addEventListener('click', () => this.showDateRangePicker());
    }
    
    // Refresh button
    const refreshBtn = this.container.querySelector('#refreshCustomersBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadCustomerData(true));
    }
    
    // Map view button
    const mapViewBtn = this.container.querySelector('#mapViewBtn');
    if (mapViewBtn) {
      mapViewBtn.addEventListener('click', () => this.toggleMapView());
    }
    
    // Back to table button
    const backToTableBtn = this.container.querySelector('#backToTableBtn');
    if (backToTableBtn) {
      backToTableBtn.addEventListener('click', () => this.toggleMapView('table'));
    }
    
    // Export button
    const exportBtn = this.container.querySelector('#exportCustomersBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportCustomerData());
    }
    
    // Settings button
    const settingsBtn = this.container.querySelector('#settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.showSettings());
    }
    
    // Load customers button (on empty state)
    const loadCustomersBtn = this.container.querySelector('#loadCustomersBtn');
    if (loadCustomersBtn) {
      loadCustomersBtn.addEventListener('click', () => this.loadCustomerData(true));
    }
    
    // Pagination buttons
    const paginationButtons = {
      first: this.container.querySelector('#pageFirst'),
      prev: this.container.querySelector('#pagePrev'),
      next: this.container.querySelector('#pageNext'),
      last: this.container.querySelector('#pageLast'),
      mobilePrev: this.container.querySelector('#mobilePagePrev'),
      mobileNext: this.container.querySelector('#mobilePageNext')
    };
    
    if (paginationButtons.first) {
      paginationButtons.first.addEventListener('click', () => this.goToPage(1));
    }
    
    if (paginationButtons.prev) {
      paginationButtons.prev.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    }
    
    if (paginationButtons.next) {
      paginationButtons.next.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    }
    
    if (paginationButtons.last) {
      paginationButtons.last.addEventListener('click', () => this.goToPage(this.getTotalPages()));
    }
    
    if (paginationButtons.mobilePrev) {
      paginationButtons.mobilePrev.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    }
    
    if (paginationButtons.mobileNext) {
      paginationButtons.mobileNext.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    }
  }

  async loadCustomerData(forceRefresh = false) {
    try {
      this.showLoading();
      
      // If not forcing refresh, try to load from IndexedDB first
      if (!forceRefresh) {
        try {
          const cachedCustomers = await this.getCustomersFromIndexedDB();
          if (cachedCustomers && cachedCustomers.length > 0) {
            this.customers = cachedCustomers;
            this.applySearch(); // This will set filteredCustomers and render the table
            return;
          }
        } catch (dbError) {
          console.warn('Error loading from IndexedDB, will try API or fallback to mock data:', dbError);
          // Continue with other methods if IndexedDB fails
        }
      }
      
      // Get connection status to check if connected to Acumatica
      if (!this.connectionManager) {
        // Try to import again if it failed the first time
        try {
          const module = await import('../../core/connection.js');
          this.connectionManager = module.connectionManager;
        } catch (error) {
          console.error('Error importing ConnectionManager on refresh:', error);
          this.showError('Failed to load connection manager. Using mock data instead.');
          // Generate mock data instead of returning
          const mockCustomers = this.generateMockCustomerData();
          this.customers = mockCustomers;
          this.applySearch();
          return;
        }
      }
      
      // Check if connected to Acumatica
      const connectionStatus = this.connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        this.showError('Not connected to Acumatica. Please connect first.');
        return;
      }
      
      // Get Acumatica instance from connection manager
      const instance = connectionStatus.acumatica.instance;
      if (!instance) {
        this.showError('Acumatica instance URL not found. Please reconnect.');
        return;
      }
      
      try {
        // Make the API call to get customers
        const customerData = await this.fetchAcumaticaCustomers(instance);
        
        if (!customerData || !customerData.length) {
          this.showError('No customer data received from Acumatica.');
          return;
        }
        
        // Parse and store customer data
        const parsedCustomers = this.parseAcumaticaCustomers(customerData);
        
        // Store in IndexedDB for future use
        await this.storeCustomersInIndexedDB(parsedCustomers);
        
        this.customers = parsedCustomers;
        this.applySearch(); // This will filter and render the table
      } catch (error) {
        console.error('Error fetching customers from Acumatica:', error);
        
        // If API call fails, try to load from IndexedDB as a fallback
        const cachedCustomers = await this.getCustomersFromIndexedDB();
        if (cachedCustomers && cachedCustomers.length > 0) {
          this.customers = cachedCustomers;
          this.applySearch();
          this.showError(`Using cached data. Failed to refresh: ${error.message}`);
        } else {
          // If no cached data, show error and generate mock data for demo
          console.warn('No cached data available. Generating mock data for demo purposes.');
          const mockCustomers = this.generateMockCustomerData();
          this.customers = mockCustomers;
          this.applySearch();
          this.showError(`Using generated data. API error: ${error.message}`);
        }
      }
    } catch (error) {
      console.error('Error loading customer data:', error);
      this.showError('Failed to load customer data. ' + error.message);
    }
  }

  // Fetch customers from Acumatica API
  async fetchAcumaticaCustomers(instance) {
    // Get cookies for authentication - Chrome extension might handle this through the connection manager
    // Construct the API URL
    const apiUrl = `${instance}/entity/default/22.200.001/Customer?$expand=MainContact,MainContact/Address`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
          // Cookies are sent automatically by the browser if the user is authenticated
        },
        credentials: 'include' // Include cookies in the request
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`API call failed with status ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Acumatica API request failed:', error);
      throw error;
    }
  }

  // Parse Acumatica customer data into a format that works with our UI
  parseAcumaticaCustomers(customerData) {
    return customerData.map(customer => {
      // Get the last order date or use last modified date as fallback
      const lastOrderDate = new Date(customer.LastModifiedDateTime?.value || new Date());
      
      // Extract the values we need
      return {
        id: customer.id || customer.NoteID?.value || `CU${Math.random().toString(36).substr(2, 9)}`,
        customerID: customer.CustomerID?.value || 'N/A',
        customerName: customer.CustomerName?.value || 'Unknown',
        status: customer.Status?.value || 'Unknown',
        totalSales: this.extractNumberValue(customer.CreditLimit) || 0,
        lastOrder: lastOrderDate,
        orderCount: Math.floor(Math.random() * 100), // This field isn't in the API response, so we'll generate it
        companyName: customer.MainContact?.CompanyName?.value || customer.CustomerName?.value || 'Unknown',
        city: customer.MainContact?.Address?.City?.value || 'N/A',
        country: customer.MainContact?.Address?.Country?.value || 'N/A',
        email: customer.MainContact?.Email?.value || 'N/A',
        phone: customer.MainContact?.Phone1?.value || 'N/A',
        currency: customer.CurrencyID?.value || 'N/A',
        terms: customer.Terms?.value || 'N/A',
        taxZone: customer.TaxZone?.value || 'N/A',
        customerClass: customer.CustomerClass?.value || 'N/A',
        // Store the full original data for detailed view
        rawData: customer
      };
    });
  }

  // Helper function to extract number values from Acumatica fields
  extractNumberValue(field) {
    if (!field || field.value === undefined) return 0;
    if (typeof field.value === 'number') return field.value;
    if (typeof field.value === 'string') {
      const parsed = parseFloat(field.value);
      return isNaN(parsed) ? 0 : parsed;
    }
    return 0;
  }

  async getCustomersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          
          // Check if the store exists before trying to access it
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.warn(`Store ${this.storeName} does not exist in database ${this.dbName}`);
            resolve([]);
            return;
          }
          
          try {
            const transaction = db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const getAllRequest = store.getAll();
            
            getAllRequest.onsuccess = () => {
              resolve(getAllRequest.result);
            };
            
            getAllRequest.onerror = (error) => {
              console.error('Error getting all customers:', error);
              resolve([]); // Resolve with empty array instead of rejecting
            };
          } catch (transactionError) {
            console.error('Transaction error:', transactionError);
            resolve([]); // Resolve with empty array instead of rejecting
          }
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB in getCustomersFromIndexedDB:', event.target.error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
      } catch (error) {
        console.error('Exception in getCustomersFromIndexedDB:', error);
        resolve([]); // Resolve with empty array instead of rejecting
      }
    });
  }

  async storeCustomersInIndexedDB(customers) {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          
          // Check if the store exists
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.warn(`Store ${this.storeName} does not exist, cannot store customers`);
            resolve();
            return;
          }
          
          try {
            const transaction = db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            
            // Clear existing data
            store.clear();
            
            // Add new data
            customers.forEach(customer => {
              store.add(customer);
            });
            
            transaction.oncomplete = () => {
              resolve();
            };
            
            transaction.onerror = (error) => {
              console.error('Error storing customers in IndexedDB:', error);
              resolve(); // Resolve anyway to avoid blocking the UI
            };
          } catch (transactionError) {
            console.error('Transaction error in storeCustomersInIndexedDB:', transactionError);
            resolve(); // Resolve anyway to avoid blocking the UI
          }
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB in storeCustomersInIndexedDB:', event.target.error);
          resolve(); // Resolve anyway to avoid blocking the UI
        };
      } catch (error) {
        console.error('Exception in storeCustomersInIndexedDB:', error);
        resolve(); // Resolve anyway to avoid blocking the UI
      }
    });
  }

  sortTable(field, toggleDirection = true) {
    // If the same field is clicked, toggle sort direction
    if (field === this.sortField && toggleDirection) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      // Default direction based on field type
      if (field === 'totalSales' || field === 'orderCount' || field === 'lastOrder') {
        this.sortDirection = 'desc'; // Default to descending for numeric and date fields
      } else {
        this.sortDirection = 'asc'; // Default to ascending for text fields
      }
    }
    
    // Update column headers to show sort direction
    const tableHeaders = this.container.querySelectorAll('th[data-sort]');
    tableHeaders.forEach(header => {
      const sortField = header.getAttribute('data-sort');
      const sortIcon = header.querySelector('.sort-icon i');
      
      if (sortIcon) {
        if (sortField === this.sortField) {
          sortIcon.className = this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        } else {
          sortIcon.className = 'fas fa-sort';
        }
      }
    });
    
    // Apply search/filter without calling applySearch (which would create a circular call)
    const searchTerm = this.searchTerm.toLowerCase();
    
    // Filter customers based on search term
    this.filteredCustomers = this.customers.filter(customer => {
      if (!searchTerm) return true;
      
      return (
        customer.customerID.toLowerCase().includes(searchTerm) ||
        customer.customerName.toLowerCase().includes(searchTerm) ||
        customer.status.toLowerCase().includes(searchTerm) ||
        (customer.city && customer.city.toLowerCase().includes(searchTerm)) ||
        (customer.country && customer.country.toLowerCase().includes(searchTerm)) ||
        (customer.companyName && customer.companyName.toLowerCase().includes(searchTerm))
      );
    });
    
    // Sort filtered customers directly
    this.filteredCustomers.sort((a, b) => {
      let valueA = a[this.sortField];
      let valueB = b[this.sortField];
      
      // Handle special cases for date sorting
      if (this.sortField === 'lastOrder') {
        valueA = new Date(valueA);
        valueB = new Date(valueB);
      }
      
      // Handle numeric sorting
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle date sorting
      if (valueA instanceof Date && valueB instanceof Date) {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle string sorting
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortDirection === 'asc' 
          ? valueA.localeCompare(valueB) 
          : valueB.localeCompare(valueA);
      }
      
      return 0;
    });
    
    // Go to first page and update the table
    this.currentPage = 1;
    this.updateTable();
    
    // Update map if it's initialized and in map view
    if (this.viewMode === 'map' && this.mapComponent) {
      this.mapComponent.updateCustomers(this.filteredCustomers);
    }
  }

  applySearch() {
    const searchTerm = this.searchTerm.toLowerCase();
    
    // Filter customers based on search term
    this.filteredCustomers = this.customers.filter(customer => {
      if (!searchTerm) return true;
      
      return (
        customer.customerID.toLowerCase().includes(searchTerm) ||
        customer.customerName.toLowerCase().includes(searchTerm) ||
        customer.status.toLowerCase().includes(searchTerm) ||
        (customer.city && customer.city.toLowerCase().includes(searchTerm)) ||
        (customer.country && customer.country.toLowerCase().includes(searchTerm)) ||
        (customer.companyName && customer.companyName.toLowerCase().includes(searchTerm))
      );
    });
    
    // Apply sorting directly without calling sortTable (which would create an infinite loop)
    this.filteredCustomers.sort((a, b) => {
      let valueA = a[this.sortField];
      let valueB = b[this.sortField];
      
      // Handle special cases for date sorting
      if (this.sortField === 'lastOrder') {
        valueA = new Date(valueA);
        valueB = new Date(valueB);
      }
      
      // Handle numeric sorting
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle date sorting
      if (valueA instanceof Date && valueB instanceof Date) {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle string sorting
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortDirection === 'asc' 
          ? valueA.localeCompare(valueB) 
          : valueB.localeCompare(valueA);
      }
      
      return 0;
    });
    
    // Reset to first page
    this.currentPage = 1;
    
    // Update the table if in table view
    if (this.viewMode === 'table') {
      this.updateTable();
    } 
    // Update the map if in map view and the map component is initialized
    else if (this.viewMode === 'map' && this.mapComponent) {
      this.mapComponent.updateCustomers(this.filteredCustomers);
      
      // Update the map customer count
      const mapCustomerCount = this.container.querySelector('#mapCustomerCount');
      if (mapCustomerCount) {
        mapCustomerCount.textContent = this.filteredCustomers.length;
      }
    }
  }

  updateClearButton() {
    const clearButton = this.container.querySelector('#clearSearchBtn');
    if (clearButton) {
      clearButton.classList.toggle('hidden', !this.searchTerm);
    }
  }

  updateTable() {
    // Avoid operating if the container is not available
    if (!this.container) {
      console.error('Container not available for updating table');
      return;
    }
    
    const tableBody = this.container.querySelector('#customersTableBody');
    if (!tableBody) {
      console.error('Table body element not found');
      return;
    }
    
    try {
      // Calculate pagination
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      const pagedCustomers = this.filteredCustomers.slice(start, end);
      
      // Clear loading and error states
      this.hideLoading();
      this.hideError();
      
      // Show empty state if no results
      if (this.filteredCustomers.length === 0) {
        this.showEmptyState();
        this.updatePagination();
        return;
      }
      
      // Hide empty state if we have results
      this.hideEmptyState();
      
      // Generate table rows - use a DocumentFragment for better performance
      const fragment = document.createDocumentFragment();
      
      // Clear previous content
      tableBody.innerHTML = '';
      
      // Add new rows
      pagedCustomers.forEach(customer => {
        const row = document.createElement('tr');
        
        const formattedDate = customer.lastOrder instanceof Date 
          ? customer.lastOrder.toLocaleDateString()
          : new Date(customer.lastOrder).toLocaleDateString();
        
        const formattedSales = new Intl.NumberFormat('en-US', { 
          style: 'currency', 
          currency: 'USD'
        }).format(customer.totalSales);
        
        const statusClass = this.getStatusClass(customer.status);
        
        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${customer.customerID}</td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="flex-shrink-0 h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium">${customer.customerName.substring(0, 2)}</span>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900 dark:text-white">${customer.customerName}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
              ${customer.status}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${formattedSales}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${formattedDate}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${customer.orderCount}</td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button data-customer-id="${customer.id}" class="view-customer text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
              <i class="fas fa-eye"></i>
            </button>
            <button data-customer-id="${customer.id}" class="edit-customer text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
              <i class="fas fa-pencil-alt"></i>
            </button>
          </td>
        `;
        
        fragment.appendChild(row);
      });
      
      // Append all rows at once
      tableBody.appendChild(fragment);
      
      // Add event listeners to action buttons
      const viewButtons = tableBody.querySelectorAll('.view-customer');
      viewButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const customerId = button.getAttribute('data-customer-id');
          this.viewCustomerDetails(customerId);
        });
      });
      
      const editButtons = tableBody.querySelectorAll('.edit-customer');
      editButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const customerId = button.getAttribute('data-customer-id');
          this.editCustomer(customerId);
        });
      });
      
      // Update pagination
      this.updatePagination();
    } catch (error) {
      console.error('Error updating table:', error);
      this.showError('An error occurred while updating the table: ' + error.message);
    }
  }

  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'on hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  updatePagination() {
    const totalItems = this.filteredCustomers.length;
    const totalPages = this.getTotalPages();
    
    // Update page numbers display
    const pageNumbers = this.container.querySelector('#pageNumbers');
    if (pageNumbers) {
      pageNumbers.textContent = `${this.currentPage} of ${totalPages}`;
    }
    
    // Update item count display
    const start = totalItems === 0 ? 0 : (this.currentPage - 1) * this.pageSize + 1;
    const end = Math.min(start + this.pageSize - 1, totalItems);
    
    const paginationStart = this.container.querySelector('#paginationStart');
    const paginationEnd = this.container.querySelector('#paginationEnd');
    const paginationTotal = this.container.querySelector('#paginationTotal');
    
    if (paginationStart) paginationStart.textContent = start;
    if (paginationEnd) paginationEnd.textContent = end;
    if (paginationTotal) paginationTotal.textContent = totalItems;
    
    // Disable/enable navigation buttons
    const isFirstPage = this.currentPage === 1;
    const isLastPage = this.currentPage === totalPages || totalPages === 0;
    
    const pageFirstBtn = this.container.querySelector('#pageFirst');
    const pagePrevBtn = this.container.querySelector('#pagePrev');
    const pageNextBtn = this.container.querySelector('#pageNext');
    const pageLastBtn = this.container.querySelector('#pageLast');
    const mobilePagePrevBtn = this.container.querySelector('#mobilePagePrev');
    const mobilePageNextBtn = this.container.querySelector('#mobilePageNext');
    
    if (pageFirstBtn) pageFirstBtn.disabled = isFirstPage;
    if (pagePrevBtn) pagePrevBtn.disabled = isFirstPage;
    if (pageNextBtn) pageNextBtn.disabled = isLastPage;
    if (pageLastBtn) pageLastBtn.disabled = isLastPage;
    if (mobilePagePrevBtn) mobilePagePrevBtn.disabled = isFirstPage;
    if (mobilePageNextBtn) mobilePageNextBtn.disabled = isLastPage;
    
    // Apply visual indication for disabled buttons
    [pageFirstBtn, pagePrevBtn, pageNextBtn, pageLastBtn, mobilePagePrevBtn, mobilePageNextBtn].forEach(btn => {
      if (btn) {
        btn.classList.toggle('opacity-50', btn.disabled);
        btn.classList.toggle('cursor-not-allowed', btn.disabled);
      }
    });
  }

  getTotalPages() {
    return Math.max(1, Math.ceil(this.filteredCustomers.length / this.pageSize));
  }

  goToPage(page) {
    const totalPages = this.getTotalPages();
    this.currentPage = Math.max(1, Math.min(page, totalPages));
    this.updateTable();
  }

  showLoading() {
    const loadingIndicator = this.container.querySelector('#customersLoadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.classList.remove('hidden');
    }
    
    this.hideEmptyState();
    this.hideError();
    this.isLoading = true;
  }

  hideLoading() {
    const loadingIndicator = this.container.querySelector('#customersLoadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.classList.add('hidden');
    }
    this.isLoading = false;
  }

  showEmptyState() {
    const emptyState = this.container.querySelector('#customersEmptyState');
    if (emptyState) {
      emptyState.classList.remove('hidden');
    }
  }

  hideEmptyState() {
    const emptyState = this.container.querySelector('#customersEmptyState');
    if (emptyState) {
      emptyState.classList.add('hidden');
    }
  }

  showError(message) {
    const errorState = this.container.querySelector('#customersErrorState');
    const errorMessage = this.container.querySelector('#errorMessage');
    
    if (errorState) {
      errorState.classList.remove('hidden');
    }
    
    if (errorMessage) {
      errorMessage.textContent = message;
    }
    
    this.hideLoading();
    this.hideEmptyState();
  }

  hideError() {
    const errorState = this.container.querySelector('#customersErrorState');
    if (errorState) {
      errorState.classList.add('hidden');
    }
  }

  viewCustomerDetails(customerId) {
    // Find the customer in our data
    const customer = this.customers.find(c => c.id === customerId);
    if (!customer) {
      alert('Customer details not found');
      return;
    }
    
    // Create a modal to display customer details
    const detailsModal = document.createElement('div');
    detailsModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    detailsModal.id = 'customerDetailsModal';
    
    // Format fields for display
    const formattedLastOrder = customer.lastOrder instanceof Date 
      ? customer.lastOrder.toLocaleDateString()
      : new Date(customer.lastOrder).toLocaleDateString();
    
    const formattedSales = new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: customer.currency || 'USD'
    }).format(customer.totalSales);
    
    // Extract additional fields from raw data if available
    const createdDate = customer.rawData?.CreatedDateTime?.value 
      ? new Date(customer.rawData.CreatedDateTime.value).toLocaleDateString()
      : 'N/A';
    
    const lastModifiedDate = customer.rawData?.LastModifiedDateTime?.value
      ? new Date(customer.rawData.LastModifiedDateTime.value).toLocaleDateString()
      : 'N/A';
    
    const fobPoint = customer.rawData?.FOBPoint?.value || 'N/A';
    const shippingRule = customer.rawData?.ShippingRule?.value || 'N/A';
    const statementCycle = customer.rawData?.StatementCycleID?.value || 'N/A';
    const shippingTerms = customer.rawData?.ShippingTerms?.value || 'N/A';
    const creditLimit = customer.rawData?.CreditLimit?.value 
      ? new Intl.NumberFormat('en-US', { style: 'currency', currency: customer.currency || 'USD' }).format(customer.rawData.CreditLimit.value)
      : 'N/A';
    
    // Create modal content with customer details
    detailsModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">${customer.customerName} (${customer.customerID})</h3>
          <button id="closeDetailsBtn" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Basic Info Section -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Basic Information</h4>
            <div class="space-y-2">
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Status</span>
                <p class="text-gray-900 dark:text-white">
                  <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${this.getStatusClass(customer.status)}">
                    ${customer.status}
                  </span>
                </p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Company</span>
                <p class="text-gray-900 dark:text-white">${customer.companyName}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Customer Class</span>
                <p class="text-gray-900 dark:text-white">${customer.customerClass}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Location</span>
                <p class="text-gray-900 dark:text-white">${customer.city}, ${customer.country}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Created Date</span>
                <p class="text-gray-900 dark:text-white">${createdDate}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Last Modified</span>
                <p class="text-gray-900 dark:text-white">${lastModifiedDate}</p>
              </div>
            </div>
          </div>
          
          <!-- Contact Info Section -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Contact Information</h4>
            <div class="space-y-2">
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Email</span>
                <p class="text-gray-900 dark:text-white">${customer.email || 'Not provided'}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Phone</span>
                <p class="text-gray-900 dark:text-white">${customer.phone || 'Not provided'}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Last Order</span>
                <p class="text-gray-900 dark:text-white">${formattedLastOrder}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Orders Count</span>
                <p class="text-gray-900 dark:text-white">${customer.orderCount}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Address</span>
                <p class="text-gray-900 dark:text-white">
                  ${customer.rawData?.MainContact?.Address?.AddressLine1?.value || ''}
                  ${customer.rawData?.MainContact?.Address?.AddressLine2?.value ? ', ' + customer.rawData.MainContact.Address.AddressLine2.value : ''}
                  ${customer.city ? ', ' + customer.city : ''}
                  ${customer.rawData?.MainContact?.Address?.State?.value ? ', ' + customer.rawData.MainContact.Address.State.value : ''}
                  ${customer.rawData?.MainContact?.Address?.PostalCode?.value ? ' ' + customer.rawData.MainContact.Address.PostalCode.value : ''}
                  ${customer.country ? ', ' + customer.country : ''}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Financial Info Section -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-3">Financial Information</h4>
            <div class="space-y-2">
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Total Sales</span>
                <p class="text-gray-900 dark:text-white">${formattedSales}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Credit Limit</span>
                <p class="text-gray-900 dark:text-white">${creditLimit}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Currency</span>
                <p class="text-gray-900 dark:text-white">${customer.currency}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Terms</span>
                <p class="text-gray-900 dark:text-white">${customer.terms}</p>
              </div>
              <div>
                <span class="text-sm text-gray-500 dark:text-gray-400">Tax Zone</span>
                <p class="text-gray-900 dark:text-white">${customer.taxZone}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Shipping & Handling Section -->
        <div class="mt-4">
          <h4 class="font-medium text-gray-900 dark:text-white mb-3">Shipping & Handling</h4>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div class="space-y-2">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">FOB Point</span>
                  <p class="text-gray-900 dark:text-white">${fobPoint}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Shipping Rule</span>
                  <p class="text-gray-900 dark:text-white">${shippingRule}</p>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div class="space-y-2">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Shipping Terms</span>
                  <p class="text-gray-900 dark:text-white">${shippingTerms}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Statement Cycle</span>
                  <p class="text-gray-900 dark:text-white">${statementCycle}</p>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div class="space-y-2">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Residential Delivery</span>
                  <p class="text-gray-900 dark:text-white">${customer.rawData?.ResidentialDelivery?.value === true ? 'Yes' : 'No'}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Saturday Delivery</span>
                  <p class="text-gray-900 dark:text-white">${customer.rawData?.SaturdayDelivery?.value === true ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
          <button id="closeCustomerBtn" class="py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            Close
          </button>
          <button id="editCustomerBtn" class="py-2 px-4 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
            Edit Customer
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(detailsModal);
    
    // Add event listeners
    const closeBtn = document.getElementById('closeDetailsBtn');
    const closeBtnBottom = document.getElementById('closeCustomerBtn');
    const editBtn = document.getElementById('editCustomerBtn');
    
    const closeModal = () => {
      document.body.removeChild(detailsModal);
    };
    
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (closeBtnBottom) closeBtnBottom.addEventListener('click', closeModal);
    
    if (editBtn) {
      editBtn.addEventListener('click', () => {
        closeModal();
        this.editCustomer(customerId);
      });
    }
  }

  editCustomer(customerId) {
    // Find the customer in our data
    const customer = this.customers.find(c => c.id === customerId);
    if (!customer) {
      alert('Customer details not found');
      return;
    }
    
    // Create a modal for editing customer information
    const editModal = document.createElement('div');
    editModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    editModal.id = 'customerEditModal';
    
    // Create modal content with editable fields
    editModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Edit Customer: ${customer.customerName}</h3>
          <button id="closeEditBtn" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <form id="editCustomerForm">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <!-- Basic Information -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Customer ID</label>
              <input type="text" id="edit-customerID" value="${customer.customerID}" readonly
                class="bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Customer Name</label>
              <input type="text" id="edit-customerName" value="${customer.customerName}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
              <select id="edit-status" class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white">
                <option value="Active" ${customer.status === 'Active' ? 'selected' : ''}>Active</option>
                <option value="Inactive" ${customer.status === 'Inactive' ? 'selected' : ''}>Inactive</option>
                <option value="On Hold" ${customer.status === 'On Hold' ? 'selected' : ''}>On Hold</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Company Name</label>
              <input type="text" id="edit-companyName" value="${customer.companyName}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <!-- Contact Information -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
              <input type="email" id="edit-email" value="${customer.email || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone</label>
              <input type="text" id="edit-phone" value="${customer.phone || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">City</label>
              <input type="text" id="edit-city" value="${customer.city || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Country</label>
              <input type="text" id="edit-country" value="${customer.country || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <!-- Financial Information -->
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Currency</label>
              <input type="text" id="edit-currency" value="${customer.currency || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Terms</label>
              <input type="text" id="edit-terms" value="${customer.terms || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Customer Class</label>
              <input type="text" id="edit-customerClass" value="${customer.customerClass || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tax Zone</label>
              <input type="text" id="edit-taxZone" value="${customer.taxZone || ''}"
                class="border border-gray-300 dark:border-gray-600 rounded-md w-full px-3 py-2 text-gray-900 dark:text-white"
              >
            </div>
          </div>
          
          <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4 flex justify-end space-x-3">
            <div id="editStatusMessage" class="text-sm text-gray-500 dark:text-gray-400 flex-grow"></div>
            <button type="button" id="cancelEditBtn" class="py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
              Cancel
            </button>
            <button type="submit" id="saveEditBtn" class="py-2 px-4 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
              Save Changes
            </button>
          </div>
        </form>
      </div>
    `;
    
    document.body.appendChild(editModal);
    
    // Add event listeners
    const closeBtn = document.getElementById('closeEditBtn');
    const cancelBtn = document.getElementById('cancelEditBtn');
    const form = document.getElementById('editCustomerForm');
    const statusMessage = document.getElementById('editStatusMessage');
    
    const closeModal = () => {
      document.body.removeChild(editModal);
    };
    
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);
    
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // In a real implementation, we would call the Acumatica API to update the customer
        // For now, we'll just show a message about API integration
        
        statusMessage.innerHTML = `
          <div class="bg-blue-50 dark:bg-blue-900 p-2 rounded">
            <p class="text-blue-800 dark:text-blue-200">
              <i class="fas fa-info-circle mr-1"></i>
              In a production environment, this would send an update to Acumatica API.
            </p>
          </div>
        `;
        
        // Simulate API delay
        setTimeout(() => {
          // Update the local customer object with form values
          customer.customerName = document.getElementById('edit-customerName').value;
          customer.status = document.getElementById('edit-status').value;
          customer.companyName = document.getElementById('edit-companyName').value;
          customer.email = document.getElementById('edit-email').value;
          customer.phone = document.getElementById('edit-phone').value;
          customer.city = document.getElementById('edit-city').value;
          customer.country = document.getElementById('edit-country').value;
          customer.currency = document.getElementById('edit-currency').value;
          customer.terms = document.getElementById('edit-terms').value;
          customer.customerClass = document.getElementById('edit-customerClass').value;
          customer.taxZone = document.getElementById('edit-taxZone').value;
          
          // Also update the raw data object for consistency
          if (customer.rawData) {
            customer.rawData.CustomerName = { value: customer.customerName };
            customer.rawData.Status = { value: customer.status };
            if (customer.rawData.MainContact) {
              customer.rawData.MainContact.CompanyName = { value: customer.companyName };
              customer.rawData.MainContact.Email = { value: customer.email };
              customer.rawData.MainContact.Phone1 = { value: customer.phone };
              if (customer.rawData.MainContact.Address) {
                customer.rawData.MainContact.Address.City = { value: customer.city };
                customer.rawData.MainContact.Address.Country = { value: customer.country };
              }
            }
            customer.rawData.CurrencyID = { value: customer.currency };
            customer.rawData.Terms = { value: customer.terms };
            customer.rawData.CustomerClass = { value: customer.customerClass };
            customer.rawData.TaxZone = { value: customer.taxZone };
          }
          
          // Update the UI
          this.applySearch();
          
          // Close the modal
          closeModal();
          
          // Show success message
          alert('Customer updated successfully in local data.');
        }, 1000);
      });
    }
  }

  exportCustomerData() {
    // Placeholder for export functionality
    try {
      const dataStr = JSON.stringify(this.filteredCustomers, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileName = `customer_data_${new Date().toISOString().slice(0, 10)}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileName);
      linkElement.click();
    } catch (error) {
      console.error('Error exporting customer data:', error);
      alert('Failed to export customer data. Please try again.');
    }
  }

  // Add these new methods for date range picker and settings functionality
  showDateRangePicker() {
    // This is a placeholder for future date range picker implementation
    console.log('Date range picker will be implemented here');
    
    // For now, just show a simple alert
    alert('Date range selection feature coming soon');
  }

  showSettings() {
    // This is a placeholder for future settings modal implementation
    console.log('Settings modal will be implemented here');
    
    // Create a settings modal dialog
    const settingsModal = document.createElement('div');
    settingsModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    settingsModal.id = 'settingsModal';
    
    settingsModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Customer Metrics Settings</h3>
          <button id="closeSettingsBtn" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Items per page</label>
            <select id="pageSizeSelect" class="block w-full rounded-md border border-gray-300 dark:border-gray-600 py-2 px-3 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="5">5</option>
              <option value="10" selected>10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default sort field</label>
            <select id="defaultSortSelect" class="block w-full rounded-md border border-gray-300 dark:border-gray-600 py-2 px-3 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="customerID">Customer ID</option>
              <option value="customerName" selected>Customer Name</option>
              <option value="totalSales">Total Sales</option>
              <option value="lastOrder">Last Order</option>
              <option value="orderCount">Order Count</option>
            </select>
          </div>
          
          <div class="flex items-center">
            <input type="checkbox" id="autoRefreshCheckbox" class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
            <label for="autoRefreshCheckbox" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Auto-refresh data (every 5 minutes)
            </label>
          </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
          <button id="cancelSettingsBtn" class="py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            Cancel
          </button>
          <button id="saveSettingsBtn" class="py-2 px-4 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(settingsModal);
    
    // Add event listeners for the modal buttons
    const closeBtn = document.getElementById('closeSettingsBtn');
    const cancelBtn = document.getElementById('cancelSettingsBtn');
    const saveBtn = document.getElementById('saveSettingsBtn');
    
    const closeModal = () => {
      document.body.removeChild(settingsModal);
    };
    
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);
    
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        // Get values from form controls
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        const defaultSortSelect = document.getElementById('defaultSortSelect');
        const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
        
        if (pageSizeSelect) {
          this.pageSize = parseInt(pageSizeSelect.value, 10);
        }
        
        if (defaultSortSelect) {
          this.sortField = defaultSortSelect.value;
          this.sortDirection = 'asc';
        }
        
        // Apply settings
        this.applySearch(); // This will update the table with new settings
        
        // Save settings to localStorage (could be implemented in the future)
        console.log('Settings saved:', {
          pageSize: this.pageSize,
          sortField: this.sortField,
          autoRefresh: autoRefreshCheckbox ? autoRefreshCheckbox.checked : false
        });
        
        closeModal();
      });
    }
  }

  // Generate mock customer data for demo purposes
  generateMockCustomerData() {
    const statusOptions = ['Active', 'Inactive', 'On Hold'];
    const customers = [];
    
    for (let i = 1; i <= 150; i++) {
      const id = 'CU' + i.toString().padStart(5, '0');
      const randomStatus = statusOptions[Math.floor(Math.random() * statusOptions.length)];
      const randomSales = Math.floor(Math.random() * 1000000) / 100;
      const randomDate = new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      const randomOrders = Math.floor(Math.random() * 100);
      
      customers.push({
        id: id, // Used as keyPath in IndexedDB
        customerID: id,
        customerName: `Customer ${i}`,
        status: randomStatus,
        totalSales: randomSales,
        lastOrder: randomDate,
        orderCount: randomOrders,
        companyName: `Company ${i}`,
        city: 'Calgary',
        country: 'CA',
        email: `customer${i}@example.com`,
        phone: `(555) 123-${i.toString().padStart(4, '0')}`,
        currency: 'CAD',
        terms: 'N30',
        taxZone: 'AB',
        customerClass: 'CUSTCAD',
        // Add a mock raw data object
        rawData: {
          id: id,
          CustomerID: { value: id },
          CustomerName: { value: `Customer ${i}` },
          Status: { value: randomStatus },
          CreditLimit: { value: randomSales },
          LastModifiedDateTime: { value: randomDate.toISOString() }
        }
      });
    }
    
    return customers;
  }

  toggleMapView(targetMode = null) {
    try {
      console.log('Toggling map view, current mode:', this.viewMode);
      
      // Set the view mode based on the provided target or toggle the current mode
      const previousMode = this.viewMode;
      this.viewMode = targetMode || (this.viewMode === 'table' ? 'map' : 'table');
      
      console.log('Switching to mode:', this.viewMode);
      
      // If nothing changed, exit early to prevent unnecessary updates
      if (previousMode === this.viewMode) {
        console.log('View mode unchanged, exiting early');
        return;
      }
      
      // Get view elements
      const tableView = this.container.querySelector('#tableView');
      const mapView = this.container.querySelector('#mapView');
      const mapContainer = this.container.querySelector('#customerMapContainer');
      const mapViewBtn = this.container.querySelector('#mapViewBtn');
      
      if (!tableView || !mapView) {
        console.error('Table or map view elements not found');
        return;
      }
      
      if (this.viewMode === 'map') {
        console.log('Showing map view');
        
        // Make sure map container is visible
        if (mapContainer) {
          mapContainer.style.display = 'block';
          mapContainer.style.height = '550px';
          mapContainer.style.width = '100%';
          
          console.log('Map container dimensions set:', 
            mapContainer.offsetWidth, 
            mapContainer.offsetHeight,
            'Style:',
            window.getComputedStyle(mapContainer).width,
            window.getComputedStyle(mapContainer).height
          );
        } else {
          console.error('Map container not found');
        }
        
        // Hide table view, show map view
        tableView.classList.add('hidden');
        mapView.classList.remove('hidden');
        
        // Update map view button appearance to indicate it's active
        if (mapViewBtn) {
          mapViewBtn.classList.remove('text-gray-400', 'dark:text-gray-400');
          mapViewBtn.classList.add('text-blue-500', 'dark:text-blue-400');
        }
        
        // Initialize map if it hasn't been initialized yet
        if (!this.mapComponent) {
          console.log('Map component not initialized, initializing now...');
          // Don't call updateTable here - it will be called after map initialization
          this.initMapComponent();
        } else {
          console.log('Map component already initialized, updating customers');
          // Just update existing map component with current data
          this.mapComponent.updateCustomers(this.filteredCustomers);
          
          // Update the map customer count directly without calling updateTable
          const mapCustomerCount = this.container.querySelector('#mapCustomerCount');
          if (mapCustomerCount) {
            mapCustomerCount.textContent = this.filteredCustomers.length;
          }
        }
      } else {
        console.log('Showing table view');
        // Hide map view, show table view
        tableView.classList.remove('hidden');
        mapView.classList.add('hidden');
        
        // Reset map view button appearance
        if (mapViewBtn) {
          mapViewBtn.classList.add('text-gray-400', 'dark:text-gray-400');
          mapViewBtn.classList.remove('text-blue-500', 'dark:text-blue-400');
        }
        
        // Just update the table now that we're in table view
        this.updateTable();
      }
    } catch (error) {
      console.error('Error toggling map view:', error);
      // Fallback to table view on error
      this.viewMode = 'table';
      
      const tableView = this.container.querySelector('#tableView');
      const mapView = this.container.querySelector('#mapView');
      
      if (tableView) tableView.classList.remove('hidden');
      if (mapView) mapView.classList.add('hidden');
      
      // Update the table in error state
      this.updateTable();
    }
  }

  async initMapComponent() {
    try {
      console.log('Initializing map component...');
      
      // Show the loading indicator
      const mapLoadingIndicator = this.container.querySelector('#mapLoadingIndicator');
      const mapContainer = this.container.querySelector('#customerMapContainer');
      
      if (mapLoadingIndicator) {
        mapLoadingIndicator.classList.remove('hidden');
        console.log('Map loading indicator shown');
      }
      
      if (mapContainer) {
        // Ensure the container is visible before initializing the map
        mapContainer.style.display = 'block';
        mapContainer.style.height = '550px';
        mapContainer.style.width = '100%';
        
        console.log('Map container dimensions before libraries load:', 
          mapContainer.offsetWidth, 
          mapContainer.offsetHeight
        );
      } else {
        console.error('Map container not found');
        throw new Error('Map container element not found');
      }
      
      // Load Mapbox libraries
      console.log('Loading Mapbox libraries...');
      await this.loadMapboxLibraries();
      console.log('Mapbox libraries loaded successfully');
      
      // Short delay to ensure the container is fully visible
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (mapContainer) {
        console.log('Map container dimensions after libraries load:', 
          mapContainer.offsetWidth, 
          mapContainer.offsetHeight
        );
      }
      
      // Import the CustomerMapComponent class for map functionality
      console.log('Importing CustomerMapComponent...');
      let importTimeout = null;
      
      const CustomerMapComponentModule = await Promise.race([
        import('./customers_map.js'),
        new Promise((_, reject) => {
          importTimeout = setTimeout(() => {
            reject(new Error('Timeout importing CustomerMapComponent'));
          }, 5000); // 5 second timeout
        })
      ]);
      
      // Clear timeout if import succeeded
      if (importTimeout) clearTimeout(importTimeout);
      console.log('CustomerMapComponent imported successfully');
      
      // Get the CustomerMapComponent class
      const { CustomerMapComponent } = CustomerMapComponentModule;
      
      // Initialize the map component with the map container
      console.log('Creating map component instance...');
      this.mapComponent = new CustomerMapComponent(mapContainer);
      
      // Set up event listeners for the map component before initialization
      // to ensure they're available even if initialization is partial
      this.mapComponent.onMarkerClick = (customer) => {
        this.viewCustomerDetails(customer.id);
      };
      
      this.mapComponent.onBackToTable = () => {
        // Use the explicit mode to avoid toggling
        this.toggleMapView('table');
      };
      
      // Initialize the map component
      await this.mapComponent.init();
      
      // Add customers to the map only if initialization completed successfully
      if (this.mapComponent && this.mapComponent.map) {
        this.mapComponent.updateCustomers(this.filteredCustomers);
        
        // Update the map customer count
        const mapCustomerCount = this.container.querySelector('#mapCustomerCount');
        if (mapCustomerCount) {
          mapCustomerCount.textContent = this.filteredCustomers.length;
        }
      } else {
        throw new Error('Map component initialization did not complete properly');
      }
      
      // Hide loading indicator, show map
      if (mapLoadingIndicator) mapLoadingIndicator.classList.add('hidden');
      if (mapContainer) mapContainer.classList.remove('hidden');
    } catch (error) {
      console.error('Error initializing map component:', error);
      // Show error message
      this.showError('Failed to initialize map. ' + error.message);
      
      // Reset the map component to null to prevent issues
      this.mapComponent = null;
      
      // Switch back to table view (using direct mode to avoid toggling)
      this.viewMode = 'table';  // Set directly instead of using toggleMapView to avoid recursion
      
      const tableView = this.container.querySelector('#tableView');
      const mapView = this.container.querySelector('#mapView');
      const mapViewBtn = this.container.querySelector('#mapViewBtn'); 
      
      // Switch back to table view
      this.toggleMapView('table');
    }
  }

  async loadMapboxLibraries() {
    return new Promise((resolve, reject) => {
      try {
        // Check if mapboxgl is already loaded
        if (window.mapboxgl) {
          console.log('Mapbox GL JS already loaded');
          resolve();
          return;
        }
        
        console.log('Loading Mapbox libraries from correct path...');
        
        // Set a timeout for loading the libraries to prevent hanging
        const loadTimeout = setTimeout(() => {
          console.error('Timeout loading Mapbox libraries after 10 seconds');
          reject(new Error('Timeout loading Mapbox libraries'));
        }, 10000); // 10 second timeout
        
        // Try the local path first (now that we've copied files to sales directory)
        const cssPath = './map/mapbox-gl.css';
        const jsPath = './map/mapbox-gl.js';
        
        console.log('Using updated paths:', cssPath, jsPath);
        
        // Load CSS file
        const existingCss = document.getElementById('mapbox-gl-css');
        if (!existingCss) {
          const mapboxCss = document.createElement('link');
          mapboxCss.id = 'mapbox-gl-css';
          mapboxCss.rel = 'stylesheet';
          mapboxCss.href = cssPath;
          
          console.log('Loading Mapbox CSS from:', cssPath);
          
          // Listen for CSS load errors
          mapboxCss.onerror = (error) => {
            console.error('Error loading Mapbox CSS:', error);
            // Try direct loading from CDN as fallback
            mapboxCss.href = 'https://api.mapbox.com/mapbox-gl-js/v2.13.0/mapbox-gl.css';
            console.log('Falling back to CDN for CSS:', mapboxCss.href);
          };
          
          document.head.appendChild(mapboxCss);
        }
        
        // Load the JS file
        const mapboxScript = document.createElement('script');
        mapboxScript.src = jsPath;
        console.log('Loading Mapbox JS from:', jsPath);
        
        mapboxScript.onload = () => {
          clearTimeout(loadTimeout);
          console.log('Mapbox GL JS loaded successfully');
          
          // Check if window.mapboxgl is actually defined
          if (!window.mapboxgl) {
            console.error('Mapbox GL JS loaded but global object not defined, trying CDN...');
            
            // Try loading from CDN as fallback
            const cdnScript = document.createElement('script');
            cdnScript.src = 'https://api.mapbox.com/mapbox-gl-js/v2.13.0/mapbox-gl.js';
            
            cdnScript.onload = () => {
              if (window.mapboxgl) {
                console.log('Mapbox GL JS loaded from CDN successfully');
                
                // Create mock MapboxGeocoder if needed
                if (!window.MapboxGeocoder) {
                  console.log('Creating mock MapboxGeocoder');
                  window.MapboxGeocoder = function(options) {
                    this.accessToken = options.accessToken;
                    this.mapboxgl = options.mapboxgl;
                    this.marker = options.marker || false;
                    this.placeholder = options.placeholder || 'Search';
                    this.map = null;
                    
                    this.onAdd = function(map) {
                      this.map = map;
                      const container = document.createElement('div');
                      container.className = 'mapboxgl-ctrl mapboxgl-ctrl-geocoder';
                      
                      const input = document.createElement('input');
                      input.type = 'text';
                      input.placeholder = this.placeholder;
                      input.className = 'mapboxgl-ctrl-geocoder--input';
                      container.appendChild(input);
                      
                      return container;
                    };
                    
                    this.onRemove = function() {
                      // Stub
                    };
                  };
                }
                
                resolve();
              } else {
                reject(new Error('Failed to load Mapbox GL JS from CDN'));
              }
            };
            
            cdnScript.onerror = (error) => {
              console.error('Error loading Mapbox GL JS from CDN:', error);
              reject(new Error('Failed to load Mapbox GL JS from any source'));
            };
            
            document.head.appendChild(cdnScript);
            return;
          }
          
          // Create mock MapboxGeocoder if needed
          if (!window.MapboxGeocoder) {
            console.log('Creating mock MapboxGeocoder');
            window.MapboxGeocoder = function(options) {
              this.accessToken = options.accessToken;
              this.mapboxgl = options.mapboxgl;
              this.marker = options.marker || false;
              this.placeholder = options.placeholder || 'Search';
              this.map = null;
              
              this.onAdd = function(map) {
                this.map = map;
                const container = document.createElement('div');
                container.className = 'mapboxgl-ctrl mapboxgl-ctrl-geocoder';
                
                const input = document.createElement('input');
                input.type = 'text';
                input.placeholder = this.placeholder;
                input.className = 'mapboxgl-ctrl-geocoder--input';
                container.appendChild(input);
                
                return container;
              };
              
              this.onRemove = function() {
                // Stub
              };
            };
          }
          
          resolve();
        };
        
        mapboxScript.onerror = (error) => {
          console.error('Error loading Mapbox GL JS from local path:', error);
          clearTimeout(loadTimeout);
          
          // Try loading from CDN as fallback
          const cdnScript = document.createElement('script');
          cdnScript.src = 'https://api.mapbox.com/mapbox-gl-js/v2.13.0/mapbox-gl.js';
          console.log('Falling back to CDN for JS:', cdnScript.src);
          
          cdnScript.onload = () => {
            if (window.mapboxgl) {
              console.log('Mapbox GL JS loaded from CDN successfully');
              
              // Create mock MapboxGeocoder if needed
              if (!window.MapboxGeocoder) {
                console.log('Creating mock MapboxGeocoder');
                window.MapboxGeocoder = function(options) {
                  this.accessToken = options.accessToken;
                  this.mapboxgl = options.mapboxgl;
                  this.marker = options.marker || false;
                  this.placeholder = options.placeholder || 'Search';
                  this.map = null;
                  
                  this.onAdd = function(map) {
                    this.map = map;
                    const container = document.createElement('div');
                    container.className = 'mapboxgl-ctrl mapboxgl-ctrl-geocoder';
                    
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.placeholder = this.placeholder;
                    input.className = 'mapboxgl-ctrl-geocoder--input';
                    container.appendChild(input);
                    
                    return container;
                  };
                  
                  this.onRemove = function() {
                    // Stub
                  };
                };
              }
              
              resolve();
            } else {
              reject(new Error('Failed to load Mapbox GL JS from CDN'));
            }
          };
          
          cdnScript.onerror = (cdnError) => {
            console.error('Error loading Mapbox GL JS from CDN:', cdnError);
            reject(new Error('Failed to load Mapbox GL JS from any source'));
          };
          
          document.head.appendChild(cdnScript);
        };
        
        document.head.appendChild(mapboxScript);
      } catch (error) {
        console.error('Error setting up Mapbox libraries:', error);
        reject(error);
      }
    });
  }

  async openDatabase() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        request.onsuccess = event => resolve(event.target.result);
        request.onerror = event => {
          console.error('Error opening database in openDatabase:', event.target.error);
          // Return null instead of rejecting to allow fallback
          resolve(null);
        };
      } catch (error) {
        console.error('Exception in openDatabase:', error);
        resolve(null);
      }
    });
  }

  async lookupCustomerNames() {
    // Get customer ID to name mapping from IndexedDB
    try {
      const db = await this.openDatabase();
      
      // If db is null, skip the lookup and use basic customer info
      if (!db) {
        console.warn('Database not available for customer name lookup');
        this.salesOrders = this.salesOrders.map(order => {
          return {
            ...order,
            customerName: `Customer ${order.customerID}`
          };
        });
        return;
      }
      
      const customers = await this.getCustomersFromDB(db);
      
      // Create a lookup map
      const customerMap = {};
      customers.forEach(customer => {
        customerMap[customer.customerID] = customer.customerName;
      });
      
      // Update salesOrders with customer names
      this.salesOrders = this.salesOrders.map(order => {
        return {
          ...order,
          customerName: customerMap[order.customerID] || `Customer ${order.customerID}`
        };
      });
    } catch (error) {
      console.error('Error looking up customer names:', error);
      // If lookup fails, just use the customer ID
      this.salesOrders = this.salesOrders.map(order => {
        return {
          ...order,
          customerName: `Customer ${order.customerID}`
        };
      });
    }
  }

  async getCustomersFromDB(db) {
    return new Promise((resolve, reject) => {
      try {
        if (!db) {
          console.warn('Database is null in getCustomersFromDB');
          resolve([]);
          return;
        }
        
        if (!db.objectStoreNames.contains('customers')) {
          console.warn('Customers store not found in DB');
          resolve([]);
          return;
        }
        
        const transaction = db.transaction(['customers'], 'readonly');
        const store = transaction.objectStore('customers');
        const request = store.getAll();
        
        request.onsuccess = () => {
          resolve(request.result);
        };
        
        request.onerror = (error) => {
          console.error('Error in getCustomersFromDB request:', error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
      } catch (error) {
        console.error('Error in getCustomersFromDB:', error);
        resolve([]); // Resolve with empty array instead of rejecting
      }
    });
  }
} 