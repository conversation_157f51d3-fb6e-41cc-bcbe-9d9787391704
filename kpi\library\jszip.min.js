/**
 * Minified by jsDelivr using Terser v5.15.1.
 * Original file: /npm/jszip-sync@3.2.1-sync/lib/index.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
"use strict";function JSZip(){if(!(this instanceof JSZip))return new JSZip;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files={},this.comment=null,this.root="",this.clone=function(){var e=new JSZip;for(var i in this)"function"!=typeof this[i]&&(e[i]=this[i]);return e}}JSZip.prototype=require("./object"),JSZip.prototype.loadAsync=require("./load"),JSZip.support=require("./support"),JSZip.defaults=require("./defaults"),JSZip.version="3.2.1",JSZip.loadAsync=function(e,i){return(new JSZip).loadAsync(e,i)},JSZip.external=require("./external"),module.exports=JSZip;
//# sourceMappingURL=/sm/57fda8346a9298d9efa135a9801f00767291d592a780feebe46c8f57c8990d51.map