// Inventory Impact component for KPI Dashboard
export class InventoryImpactComponent {
  constructor(container) {
    this.container = container;
    this.timeRange = '30d';
    this.purchaseData = [];
    this.inventoryData = [];
    this.productCategories = [];
    this.isLoading = true;
    this.charts = {};
  }

  async init() {
    try {
      console.log("Initializing Inventory Impact component");
      
      // Render loading state
      this.renderLoading();
      
      // Generate sample data
      await this.generateSampleData();
      
      // Process data for visualization
      this.processData();
      
      // Render UI
      this.isLoading = false;
      this.render();
      
      // Render charts
      this.renderCharts();
      
    } catch (error) {
      console.error("Error initializing Inventory Impact component:", error);
      this.showError("Error initializing component: " + error.message);
    }
  }
  
  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading inventory impact data...</p>
      </div>
    `;
  }
  
  showError(message) {
    this.container.innerHTML = `
      <div class="bg-red-50 dark:bg-red-900 border-l-4 border-red-500 p-4 mb-4">
        <div class="flex items-center">
          <div class="flex-shrink-0 text-red-500 dark:text-red-400">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700 dark:text-red-300">${message}</p>
          </div>
        </div>
      </div>
    `;
  }
  
  async generateSampleData() {
    // Generate product categories
    this.productCategories = [
      { id: 'CAT001', name: 'Raw Materials' },
      { id: 'CAT002', name: 'Components' },
      { id: 'CAT003', name: 'Packaging' },
      { id: 'CAT004', name: 'Office Supplies' },
      { id: 'CAT005', name: 'Maintenance Items' }
    ];
    
    // Generate inventory data
    this.inventoryData = [];
    const inventoryItems = [];
    
    // Create 20 inventory items across categories
    for (let i = 0; i < 20; i++) {
      const category = this.productCategories[Math.floor(Math.random() * this.productCategories.length)];
      const itemNumber = i + 1;
      const item = {
        id: `ITEM${itemNumber.toString().padStart(4, '0')}`,
        name: `Item ${itemNumber}`,
        category: category.id,
        categoryName: category.name,
        unitCost: Math.round((Math.random() * 490 + 10) * 100) / 100, // $10-$500
        reorderPoint: Math.floor(Math.random() * 50) + 10, // 10-60
        targetStock: Math.floor(Math.random() * 100) + 50, // 50-150
        leadTime: Math.floor(Math.random() * 14) + 3, // 3-17 days
        currentStock: Math.floor(Math.random() * 150)
      };
      
      inventoryItems.push(item);
    }
    
    // Generate inventory snapshots for the last 90 days
    const today = new Date();
    for (let d = 90; d >= 0; d--) {
      const date = new Date(today);
      date.setDate(today.getDate() - d);
      
      // For each item, create a snapshot with some random fluctuation
      inventoryItems.forEach(item => {
        let stockLevel = item.currentStock;
        
        // For past dates, add some randomness to stock levels
        if (d > 0) {
          const variance = Math.floor(Math.random() * 20) - 10; // -10 to +10
          stockLevel = Math.max(0, stockLevel + variance);
        }
        
        this.inventoryData.push({
          date: date,
          itemId: item.id,
          itemName: item.name,
          categoryId: item.category,
          categoryName: item.categoryName,
          stockLevel: stockLevel,
          reorderPoint: item.reorderPoint,
          targetStock: item.targetStock,
          status: this.getStockStatus(stockLevel, item.reorderPoint, item.targetStock)
        });
      });
    }
    
    // Generate purchase order data
    this.purchaseData = [];
    
    // Create 50 purchase orders spread over the last 90 days
    for (let i = 0; i < 50; i++) {
      const orderDate = new Date(today);
      orderDate.setDate(today.getDate() - Math.floor(Math.random() * 90));
      
      // Randomly select 1-3 items for this purchase order
      const numItems = Math.floor(Math.random() * 3) + 1;
      const poItems = [];
      const usedItemIndexes = new Set();
      
      for (let j = 0; j < numItems; j++) {
        // Select a random item that hasn't been used in this PO
        let itemIndex;
        do {
          itemIndex = Math.floor(Math.random() * inventoryItems.length);
        } while (usedItemIndexes.has(itemIndex));
        
        usedItemIndexes.add(itemIndex);
        const item = inventoryItems[itemIndex];
        
        // Quantity ordered (trying to get closer to target stock)
        const latestSnapshot = this.inventoryData.find(snap => 
          snap.itemId === item.id && snap.date.getTime() === today.getTime()
        );
        
        const currentStock = latestSnapshot ? latestSnapshot.stockLevel : item.currentStock;
        const deficit = item.targetStock - currentStock;
        let quantity = Math.max(5, Math.floor(deficit * (Math.random() * 0.5 + 0.5)));
        
        if (currentStock > item.targetStock) {
          quantity = Math.floor(Math.random() * 10) + 5; // Small order if stock is good
        }
        
        // Expected delivery date
        const deliveryDate = new Date(orderDate);
        deliveryDate.setDate(orderDate.getDate() + item.leadTime);
        
        poItems.push({
          itemId: item.id,
          itemName: item.name,
          category: item.category,
          categoryName: item.categoryName,
          quantity: quantity,
          unitCost: item.unitCost,
          totalCost: Math.round(quantity * item.unitCost * 100) / 100,
          startingStock: currentStock,
          expectedEndingStock: currentStock + quantity,
          expectedImpact: this.calculateStockImpact(currentStock, quantity, item.reorderPoint, item.targetStock)
        });
      }
      
      // Calculate total PO cost
      const total = poItems.reduce((sum, item) => sum + item.totalCost, 0);
      
      // Determine status based on order date
      let status;
      if (orderDate > today) {
        status = 'Planned';
      } else if (orderDate.getTime() === today.getTime()) {
        status = 'Submitted';
      } else {
        // For past orders, mostly received with some still open
        status = Math.random() > 0.2 ? 'Received' : 'Open';
      }
      
      this.purchaseData.push({
        id: `PO-${10000 + i}`,
        date: orderDate,
        status: status,
        total: total,
        items: poItems
      });
    }
  }
  
  getStockStatus(stockLevel, reorderPoint, targetStock) {
    if (stockLevel <= 0) {
      return 'Out of Stock';
    } else if (stockLevel <= reorderPoint * 0.5) {
      return 'Critical';
    } else if (stockLevel <= reorderPoint) {
      return 'Low';
    } else if (stockLevel >= targetStock * 1.5) {
      return 'Excess';
    } else if (stockLevel >= targetStock) {
      return 'Optimal';
    } else {
      return 'Adequate';
    }
  }
  
  calculateStockImpact(currentStock, quantity, reorderPoint, targetStock) {
    const newStock = currentStock + quantity;
    
    if (currentStock <= reorderPoint && newStock >= reorderPoint) {
      if (newStock >= targetStock) {
        return 'Optimal';
      } else {
        return 'Adequate';
      }
    } else if (currentStock < targetStock && newStock >= targetStock) {
      return 'Optimal';
    } else if (currentStock <= 0 && newStock > 0) {
      return 'Resolved Stockout';
    } else if (newStock > targetStock * 1.5) {
      return 'Potential Overstock';
    } else {
      return 'Minimal Impact';
    }
  }
  
  processData() {
    // Process data for charts and KPIs
    this.calculateCategoryDistribution();
    this.calculateLowStockItems();
    this.calculateStockoutPrevention();
    this.calculateInventoryTurnover();
  }
  
  calculateCategoryDistribution() {
    // Calculate the distribution of purchases by category
    this.categoryDistribution = {};
    
    this.purchaseData.forEach(po => {
      po.items.forEach(item => {
        if (!this.categoryDistribution[item.categoryName]) {
          this.categoryDistribution[item.categoryName] = {
            count: 0,
            value: 0
          };
        }
        
        this.categoryDistribution[item.categoryName].count++;
        this.categoryDistribution[item.categoryName].value += item.totalCost;
      });
    });
  }
  
  calculateLowStockItems() {
    // Get the latest inventory data
    const latestDate = new Date(Math.max(...this.inventoryData.map(item => item.date.getTime())));
    
    this.lowStockItems = this.inventoryData
      .filter(item => 
        item.date.getTime() === latestDate.getTime() && 
        item.stockLevel <= item.reorderPoint
      )
      .sort((a, b) => (a.stockLevel / a.reorderPoint) - (b.stockLevel / b.reorderPoint));
  }
  
  calculateStockoutPrevention() {
    // Calculate how many stockouts were prevented by purchase orders
    this.stockoutPrevention = {
      prevented: 0,
      total: 0
    };
    
    // Group inventory data by item and date
    const itemDateMap = {};
    
    this.inventoryData.forEach(snapshot => {
      const key = `${snapshot.itemId}-${snapshot.date.toISOString().split('T')[0]}`;
      itemDateMap[key] = snapshot;
    });
    
    // Check each purchase order's impact
    this.purchaseData.forEach(po => {
      if (po.status === 'Received') {
        po.items.forEach(item => {
          // Get the inventory snapshot right before the order date
          const orderDate = new Date(po.date);
          
          // Go back a few days to find a snapshot
          for (let d = 0; d < 7; d++) {
            const checkDate = new Date(orderDate);
            checkDate.setDate(orderDate.getDate() - d);
            
            const key = `${item.itemId}-${checkDate.toISOString().split('T')[0]}`;
            const snapshot = itemDateMap[key];
            
            if (snapshot) {
              // If the stock was low and this order came in time
              if (snapshot.stockLevel <= snapshot.reorderPoint) {
                this.stockoutPrevention.total++;
                
                // If without this order, it would have gone to stockout
                if (snapshot.stockLevel - item.quantity <= 0) {
                  this.stockoutPrevention.prevented++;
                }
              }
              break;
            }
          }
        });
      }
    });
  }
  
  calculateInventoryTurnover() {
    // Calculate the inventory turnover rate based on purchases
    this.inventoryTurnover = {
      overall: 0,
      byCategory: {}
    };
    
    // This is a simplistic implementation
    // In a real system, this would account for actual usage/sales data
    
    // Group by category
    const categoryCosts = {};
    const categoryInventory = {};
    
    // Calculate average inventory level by category
    this.inventoryData.forEach(snapshot => {
      if (!categoryInventory[snapshot.categoryName]) {
        categoryInventory[snapshot.categoryName] = {
          levels: [],
          average: 0
        };
      }
      
      categoryInventory[snapshot.categoryName].levels.push(snapshot.stockLevel);
    });
    
    // Calculate averages
    Object.keys(categoryInventory).forEach(category => {
      const levels = categoryInventory[category].levels;
      categoryInventory[category].average = levels.reduce((sum, level) => sum + level, 0) / levels.length;
    });
    
    // Calculate purchases by category
    this.purchaseData.forEach(po => {
      if (po.status === 'Received') {
        po.items.forEach(item => {
          if (!categoryCosts[item.categoryName]) {
            categoryCosts[item.categoryName] = 0;
          }
          
          categoryCosts[item.categoryName] += item.totalCost;
        });
      }
    });
    
    // Calculate turnover by category
    let totalCost = 0;
    let totalAvgInventory = 0;
    
    Object.keys(categoryCosts).forEach(category => {
      const cost = categoryCosts[category];
      const avgInventory = categoryInventory[category]?.average || 1;
      
      totalCost += cost;
      totalAvgInventory += avgInventory;
      
      this.inventoryTurnover.byCategory[category] = {
        cost: cost,
        averageInventory: avgInventory,
        turnover: cost / avgInventory
      };
    });
    
    // Calculate overall turnover
    this.inventoryTurnover.overall = totalCost / totalAvgInventory;
  }
  
  render() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Inventory Impact Analysis</h2>
          
          <div class="flex gap-2">
            <select id="time-range-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="7d" ${this.timeRange === '7d' ? 'selected' : ''}>Last 7 Days</option>
              <option value="30d" ${this.timeRange === '30d' ? 'selected' : ''}>Last 30 Days</option>
              <option value="90d" ${this.timeRange === '90d' ? 'selected' : ''}>Last 90 Days</option>
            </select>
          </div>
        </div>
        
        <!-- KPI Cards Row -->
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <!-- Orders Affecting Inventory Card -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-blue-50 dark:bg-blue-900 rounded-md">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">Pending Orders</p>
                <p class="text-xl font-bold text-gray-800 dark:text-white">${this.countPendingOrders()}</p>
              </div>
            </div>
          </div>
          
          <!-- Low Stock Items Card -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-yellow-50 dark:bg-yellow-900 rounded-md">
                <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">Low Stock Items</p>
                <p class="text-xl font-bold text-gray-800 dark:text-white">${this.lowStockItems.length}</p>
              </div>
            </div>
          </div>
          
          <!-- Stockout Prevention Card -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-green-50 dark:bg-green-900 rounded-md">
                <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">Stockouts Prevented</p>
                <p class="text-xl font-bold text-gray-800 dark:text-white">${this.stockoutPrevention.prevented}/${this.stockoutPrevention.total}</p>
              </div>
            </div>
          </div>
          
          <!-- Inventory Turnover Card -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <div class="flex items-center">
              <div class="p-2 bg-purple-50 dark:bg-purple-900 rounded-md">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </div>
              <div class="ml-4">
                <p class="text-xs text-gray-500 dark:text-gray-400">Inventory Turnover</p>
                <p class="text-xl font-bold text-gray-800 dark:text-white">${this.inventoryTurnover.overall.toFixed(2)}x</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Purchase Impact Chart -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">Purchase Impact on Inventory</h3>
            <div id="purchase-impact-chart" class="h-64 w-full"></div>
          </div>
          
          <!-- Inventory Status Chart -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">Inventory Status Distribution</h3>
            <div id="inventory-status-chart" class="h-64 w-full"></div>
          </div>
          
          <!-- Category Purchase Value Chart -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">Purchase Value by Category</h3>
            <div id="category-value-chart" class="h-64 w-full"></div>
          </div>
          
          <!-- Low Stock Items Table -->
          <div class="bg-white dark:bg-gray-800 shadow-sm border border-gray-100 dark:border-gray-700 rounded-lg p-4">
            <h3 class="text-md font-medium text-gray-700 dark:text-gray-300 mb-4">Critical Inventory Items</h3>
            
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item</th>
                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Stock</th>
                    <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                    <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                  ${this.renderLowStockItems()}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Set up event listeners
    this.setupEventListeners();
  }
  
  renderLowStockItems() {
    if (this.lowStockItems.length === 0) {
      return `
        <tr>
          <td colspan="5" class="px-3 py-8 text-center text-gray-500 dark:text-gray-400">
            No items below reorder point
          </td>
        </tr>
      `;
    }
    
    // Show up to 5 items
    const itemsToShow = this.lowStockItems.slice(0, 5);
    
    return itemsToShow.map(item => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(item.itemName)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-center text-gray-500 dark:text-gray-400">
          ${this.escapeHtml(item.categoryName)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
          <div class="flex items-center justify-center">
            <span class="font-medium ${this.getStockLevelClass(item.status)}">${item.stockLevel}</span>
            <span class="text-gray-500 dark:text-gray-400 ml-1">/ ${item.reorderPoint}</span>
          </div>
          <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
            <div class="h-1.5 rounded-full ${this.getStockLevelBarClass(item.status)}" 
              style="width: ${Math.min(100, Math.round((item.stockLevel / item.reorderPoint) * 100))}%"></div>
          </div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-center">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusBadgeClass(item.status)}">
            ${item.status}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm">
          <button class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 create-po-btn" data-item-id="${item.itemId}">
            Create PO
          </button>
        </td>
      </tr>
    `).join('');
  }
  
  getStockLevelClass(status) {
    switch (status) {
      case 'Out of Stock':
        return 'text-red-600 dark:text-red-400';
      case 'Critical':
        return 'text-red-600 dark:text-red-400';
      case 'Low':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'Adequate':
        return 'text-blue-600 dark:text-blue-400';
      case 'Optimal':
        return 'text-green-600 dark:text-green-400';
      case 'Excess':
        return 'text-purple-600 dark:text-purple-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  }
  
  getStockLevelBarClass(status) {
    switch (status) {
      case 'Out of Stock':
        return 'bg-red-600';
      case 'Critical':
        return 'bg-red-500';
      case 'Low':
        return 'bg-yellow-500';
      case 'Adequate':
        return 'bg-blue-500';
      case 'Optimal':
        return 'bg-green-500';
      case 'Excess':
        return 'bg-purple-500';
      default:
        return 'bg-gray-500';
    }
  }
  
  getStatusBadgeClass(status) {
    switch (status) {
      case 'Out of Stock':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'Critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'Low':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'Adequate':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'Optimal':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'Excess':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }
  
  setupEventListeners() {
    // Time range filter change handler
    const timeRangeFilter = document.getElementById('time-range-filter');
    if (timeRangeFilter) {
      timeRangeFilter.addEventListener('change', () => {
        this.timeRange = timeRangeFilter.value;
        // In a real implementation, this would reload data with the new time range
        this.renderCharts();
      });
    }
    
    // Create Purchase Order buttons
    const createPoButtons = document.querySelectorAll('.create-po-btn');
    createPoButtons.forEach(button => {
      button.addEventListener('click', () => {
        const itemId = button.getAttribute('data-item-id');
        this.createPurchaseOrder(itemId);
      });
    });
  }
  
  createPurchaseOrder(itemId) {
    // In a real implementation, this would open a form to create a new purchase order
    // For now, just show an alert
    const item = this.lowStockItems.find(item => item.itemId === itemId);
    if (item) {
      alert(`Creating purchase order for ${item.itemName}`);
    }
  }
  
  countPendingOrders() {
    return this.purchaseData.filter(po => 
      po.status === 'Open' || po.status === 'Pending' || po.status === 'Submitted'
    ).length;
  }
  
  renderCharts() {
    // In a real implementation, this would render charts using a library like ApexCharts
    // For now, just show placeholder text
    
    this.renderPurchaseImpactChart();
    this.renderInventoryStatusChart();
    this.renderCategoryValueChart();
  }
  
  renderPurchaseImpactChart() {
    const chartElement = document.getElementById('purchase-impact-chart');
    if (!chartElement) return;
    
    // Count purchases by their expected impact
    const impactCounts = {};
    
    this.purchaseData.forEach(po => {
      po.items.forEach(item => {
        const impact = item.expectedImpact;
        impactCounts[impact] = (impactCounts[impact] || 0) + 1;
      });
    });
    
    // Placeholder chart
    chartElement.innerHTML = `
      <div class="flex items-center justify-center h-full">
        <p class="text-gray-500 dark:text-gray-400">
          Purchase impact chart would show the expected impact of purchases:
          ${Object.entries(impactCounts)
            .map(([impact, count]) => `${impact}: ${count} items`)
            .join(', ')}
        </p>
      </div>
    `;
  }
  
  renderInventoryStatusChart() {
    const chartElement = document.getElementById('inventory-status-chart');
    if (!chartElement) return;
    
    // Get the latest inventory data
    const latestDate = new Date(Math.max(...this.inventoryData.map(item => item.date.getTime())));
    const latestData = this.inventoryData.filter(item => item.date.getTime() === latestDate.getTime());
    
    // Count items by status
    const statusCounts = {};
    
    latestData.forEach(item => {
      statusCounts[item.status] = (statusCounts[item.status] || 0) + 1;
    });
    
    // Placeholder chart
    chartElement.innerHTML = `
      <div class="flex items-center justify-center h-full">
        <p class="text-gray-500 dark:text-gray-400">
          Inventory status chart would show the distribution of inventory status:
          ${Object.entries(statusCounts)
            .map(([status, count]) => `${status}: ${count} items`)
            .join(', ')}
        </p>
      </div>
    `;
  }
  
  renderCategoryValueChart() {
    const chartElement = document.getElementById('category-value-chart');
    if (!chartElement) return;
    
    // Placeholder chart
    chartElement.innerHTML = `
      <div class="flex items-center justify-center h-full">
        <p class="text-gray-500 dark:text-gray-400">
          Category value chart would show the distribution of purchase value across categories:
          ${Object.entries(this.categoryDistribution)
            .map(([category, data]) => `${category}: $${data.value.toFixed(2)}`)
            .join(', ')}
        </p>
      </div>
    `;
  }
  
  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
} 