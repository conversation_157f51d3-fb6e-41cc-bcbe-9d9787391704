// Enhanced inventory-sync.js with improved error handling and user experience
import { NotificationSystem } from "../core/notifications.js";
import { connectionManager } from "../core/connection.js";

export class InventorySyncComponent {
  constructor(container) {
    this.container = container;
    this.notificationSystem = new NotificationSystem();
    this.stockItems = [];
    this.masterParts = [];
    this.isLoading = false;
    this.selectedParts = new Set();
    this.syncProgress = 0;
    this.isSyncing = false;
    
    // Search parameters
    this.searchTerm = '';
    this.filteredItems = [];
    
    // Pagination settings
    this.currentPage = 0;
    this.pageSize = 9; // Show 9 items per page to avoid scrollbars
    this.totalItems = 0;
    this.hasMorePages = true;
    
    // Request timeout (increased to 45 seconds for larger datasets)
    this.requestTimeout = 45000;
    
    // Added: tracking for retry attempts
    this.retryAttempts = 0;
    this.maxRetryAttempts = 3;
    
    // Added: retry delay with exponential backoff (starting at 2 seconds)
    this.retryDelay = 2000;
    
    // Added: progress tracking
    this.loadingProgress = 0;
    this.processingProgress = 0;
    
    // Added: Dymo labeler state
    this.dymoAvailable = false;
  }

  async init() {
    try {
      console.log("Initializing inventory sync component");
      this.isLoading = true;
      await this.render();
      
      // Check if already connected to Acumatica
      const connectionStatus = connectionManager.getConnectionStatus();
      if (connectionStatus.acumatica.isConnected) {
        console.log("Already connected to Acumatica");
        this.addNotification("Loading stock items from Acumatica...", "info");
        await this.loadStockItemsBatch();
      } else {
        this.addNotification("Connect to Acumatica to load stock items", "info");
      }
      
      // Check if Dymo labeler is available
      this.dymoAvailable = connectionStatus.dymo.isConnected;
      if (this.dymoAvailable) {
        console.log("Dymo labeler is available");
      }
      
      this.isLoading = false;
      await this.render();
      
    } catch (error) {
      console.error("Error initializing inventory sync component:", error);
      this.addNotification("Error initializing component: " + error.message, "error");
      this.isLoading = false;
      await this.render();
    }
    
    // Listen for connection change events
    document.addEventListener('acumaticaConnectionChanged', this.handleConnectionChange.bind(this));
  }
  
  // Handle connection change events
  handleConnectionChange(event) {
    const connectionStatus = connectionManager.getConnectionStatus();
    
    // Update Dymo availability
    this.dymoAvailable = connectionStatus.dymo.isConnected;
    
    // Refresh the UI to reflect connection changes
    this.render();
  }

  async loadStockItemsBatch() {
    try {
      const instance = connectionManager.connections.acumatica.instance;
      if (!instance) {
        return { success: false, error: 'No Acumatica instance URL found. Please reconnect.' };
      }

      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          if (!cookies || cookies.length === 0) {
            return { success: false, error: 'No authentication cookies found. Please log in to Acumatica first.' };
          }
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        } catch (cookieError) {
          console.warn('Error getting cookies:', cookieError);
          return { success: false, error: 'Failed to retrieve authentication cookies.' };
        }
      }

      // Show loading state
      this.isLoading = true;
      this.loadingProgress = 10;
      await this.render();

      console.log("Fetching stock items from Acumatica...");
      
      // Update progress to show we're starting the API call
      this.loadingProgress = 30;
      await this.render();
      
      // Simple fetch request
      const response = await fetch(`${instance}/entity/Default/20.200.001/StockItem/?$expand=Attributes`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Update progress to show we're parsing the data
      this.loadingProgress = 60;
      await this.render();
      
      // Store the raw data directly
      const data = await response.json();
      console.log("Received data from Acumatica:", data);
      
      // Start processing data
      this.processingProgress = 30;
      await this.render();
      
      // Store the raw data - don't process it
      this.stockItems = data;
      this.hasMorePages = false;
      
      // Finish processing
      this.processingProgress = 100;
      this.loadingProgress = 100;
      await this.render();
      
      // Small delay to show completed progress bars
      setTimeout(() => {
        this.isLoading = false;
        this.render();
      }, 500);
      
      this.addNotification(`Successfully loaded ${data.length} items`, 'success');
      return { success: true, items: data };
      
    } catch (error) {
      console.error('Error loading stock items:', error);
      this.addNotification(`Error loading stock items: ${error.message}`, 'error');
      this.isLoading = false;
      this.loadingProgress = 0;
      this.processingProgress = 0;
      await this.render();
      return { success: false, error: error.message };
    }
  }

  async render() {
    console.log("Rendering inventory sync component");
    
    // Find the content area to update - directly render content instead of nesting backgrounds
    const contentArea = this.container.querySelector('#tabContentContainer');
    if (contentArea) {
      contentArea.innerHTML = this.isLoading ? this.renderLoading() : this.renderContent();
    } else {
      console.warn('No #tabContentContainer found, unable to render inventory sync content');
    }
    
    this.handleDarkMode();
    this.setupComponentEventHandlers();
  }

  renderLoading() {
    return `
      <div class="text-center py-8 text-gray-500 dark:text-gray-400">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p class="text-lg font-medium">Loading Stock Items...</p>
        <p class="text-sm mt-2">Please wait while we retrieve data from Acumatica.</p>
        <p class="text-xs mt-1 text-gray-400">This may take several minutes for large inventories</p>
        
        ${this.loadingProgress > 0 ? `
          <div class="relative pt-1 mt-4 max-w-lg mx-auto">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-blue-600 bg-blue-200 dark:bg-blue-900 dark:text-blue-200">
                  Loading
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-blue-600 dark:text-blue-300">
                  ${this.loadingProgress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-200 dark:bg-blue-900">
              <div style="width:${this.loadingProgress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-500 dark:bg-blue-400 transition-all duration-500"></div>
            </div>
          </div>
        ` : ''}
        
        ${this.processingProgress > 0 ? `
          <div class="relative pt-1 mt-4 max-w-lg mx-auto">
            <div class="flex mb-2 items-center justify-between">
              <div>
                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200 dark:bg-green-900 dark:text-green-200">
                  Processing
                </span>
              </div>
              <div class="text-right">
                <span class="text-xs font-semibold inline-block text-green-600 dark:text-green-300">
                  ${this.processingProgress}%
                </span>
              </div>
            </div>
            <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-green-200 dark:bg-green-900">
              <div style="width:${this.processingProgress}%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500 dark:bg-green-400 transition-all duration-500"></div>
            </div>
          </div>
        ` : ''}
      </div>
    `;
  }

  renderContent() {
    if (!this.stockItems || this.stockItems.length === 0) {
      return `
        <div class="text-center py-12 text-gray-500 dark:text-gray-400">
          <p class="text-lg">No stock items found</p>
          <button id="loadStockItemsBtn" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
            Load Stock Items
          </button>
        </div>
      `;
    }

    console.log("Rendering table with", this.stockItems.length, "items");
    
    // Apply search filter if needed
    const itemsToDisplay = this.searchTerm ? this.filteredItems : this.stockItems;
    
    // Calculate pagination values
    const totalPages = Math.ceil(itemsToDisplay.length / this.pageSize);
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, itemsToDisplay.length);
    const displayedItems = itemsToDisplay.slice(startIndex, endIndex);

    return `
      <div class="w-full" style="max-width: 780px;">
        <!-- Search and Action Buttons -->
        <div class="mb-2 flex flex-wrap items-center">
          <div class="relative flex-grow mr-2">
            <input type="text" id="searchInput" class="w-full pl-9 pr-4 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white" placeholder="Search inventory..." value="${this.searchTerm}">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
            <button id="clearSearchBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${this.searchTerm ? '' : 'hidden'}">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Sync Button -->
          <button id="syncBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 relative text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Sync to Acumatica">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
            </svg>
          </button>
          
          <!-- Update Button -->
          <button id="updateBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Update from Master Parts">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
          </button>
          
          <!-- Print Labels Button - New -->
          <button id="printLabelsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 text-gray-400 dark:text-gray-400 flex items-center justify-center ${this.dymoAvailable ? '' : 'opacity-50 cursor-not-allowed'}" style="height: 30px; width: 28px;" title="${this.dymoAvailable ? 'Print Labels' : 'Connect Dymo Labeler to Print'}">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
          </button>
          
          <!-- Export Button -->
          <button id="exportBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Export Inventory">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
          </button>
          
          <!-- Refresh Button -->
          <button id="refreshBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Refresh Data">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </button>
          
          <!-- Settings Button -->
          <button id="settingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;" title="Settings">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </button>
        </div>
        
        <div>
          <table class="w-full table-fixed divide-y divide-gray-200 dark:divide-gray-700 border border-gray-200 dark:border-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
              <tr>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[110px]">Inventory ID</th>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[180px]">Description</th>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[160px]">Address of Manuf.</th>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[110px]">Country of Origin</th>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[100px]">ECCN</th>
                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase w-[120px]">HS Code</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
              ${displayedItems.map(item => {
                // Extract attributes for easier access
                const attrs = {};
                if (item.Attributes && Array.isArray(item.Attributes)) {
                  item.Attributes.forEach(attr => {
                    if (attr && attr.AttributeID && attr.AttributeID.value && attr.Value && attr.Value.value) {
                      attrs[attr.AttributeID.value] = attr.Value.value;
                    }
                  });
                }
                
                // Get values with fallbacks
                const inventoryId = item.InventoryID?.value || '-';
                const description = item.Description?.value || '-';
                const aom = attrs['AOM'] || '-';
                const coo = attrs['COO'] || '-';
                const eccn = attrs['ECCN'] || '-';
                const hsCode = attrs['HSCODE'] || '-';
                
                // Truncate long text
                const truncateText = (text, maxLength = 20) => {
                  if (text && text.length > maxLength) {
                    return text.substring(0, maxLength) + '...';
                  }
                  return text;
                };
                
                return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${inventoryId}">${inventoryId}</td>
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${description}">${truncateText(description, 25)}</td>
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${aom}">${truncateText(aom, 20)}</td>
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${coo}">${coo}</td>
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${eccn}">${eccn}</td>
                    <td class="px-2 py-2 text-xs text-gray-900 dark:text-gray-300 truncate" title="${hsCode}">${hsCode}</td>
                  </tr>
                `;
              }).join('')}
            </tbody>
          </table>
        </div>
        
        ${this.renderPagination(totalPages)}
        
        <!-- Sync Options Modal -->
        <div id="syncModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-3 w-80 max-w-full max-h-[450px] overflow-y-auto">
            <div class="flex justify-between items-center mb-2 pb-1 border-b border-gray-200 dark:border-gray-700 sticky top-0 bg-white dark:bg-gray-800 z-10">
              <h3 class="text-md font-medium text-gray-800 dark:text-white">Sync Options</h3>
              <button id="closeSyncModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <div class="space-y-2">
              <!-- Sync All Option (with password) -->
              <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync All Items</h4>
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Update all inventory items to Acumatica.</p>
                <div class="mt-1">
                  <label class="block text-xs text-gray-600 dark:text-gray-400 mb-1">Enter admin password:</label>
                  <input type="password" id="syncAllPassword" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white">
                </div>
                <button id="syncAllBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                  Sync All
                </button>
              </div>
              
              <!-- Sync by Confirmation -->
              <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync with Confirmation</h4>
                <p class="text-xs text-gray-500 dark:text-gray-400">Sync items in batches of 20 with confirmation.</p>
                <button id="syncBatchBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                  Sync by Batch
                </button>
              </div>
              
              <!-- Sync Specific Items -->
              <div class="p-2 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Sync Specific Items</h4>
                <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">Enter inventory IDs separated by commas.</p>
                <div class="mt-1">
                  <textarea id="syncSpecificItems" class="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-xs dark:bg-gray-700 dark:text-white" rows="2" placeholder="e.g. ITEM001, ITEM002, ITEM003"></textarea>
                </div>
                <button id="syncSpecificBtn" class="mt-1 px-2 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-xs w-full">
                  Sync Specific Items
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Update Confirmation Modal -->
        <div id="updateModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96 max-w-full">
            <div class="flex justify-between items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Update from Master Parts</h3>
              <button id="closeUpdateModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                This will update the following fields from the Master Parts data:
              </p>
              
              <ul class="list-disc pl-5 mb-4 text-sm text-gray-600 dark:text-gray-400">
                <li>Address of Manufacturing (AOM)</li>
                <li>Country of Origin (COO)</li>
                <li>ECCN</li>
                <li>HS Code</li>
              </ul>
              
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Only matches based on Inventory ID = Part ID will be updated. Any existing data will be overwritten.
              </p>
              
              <div class="flex justify-end space-x-2 mt-4 pt-2 border-t border-gray-200 dark:border-gray-700">
                <button id="cancelUpdateBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
                  Cancel
                </button>
                <button id="confirmUpdateBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                  Update
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Print Labels Modal -->
        <div id="printLabelsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96 max-w-full">
            <div class="flex justify-between items-center mb-4 pb-2 border-b border-gray-200 dark:border-gray-700">
              <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Print Inventory Labels</h3>
              <button id="closePrintLabelsModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <div>
              ${this.dymoAvailable ? `
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Select Label Type</label>
                  <select id="labelTypeSelect" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white">
                    <option value="standard">Standard (1.125" x 3.5")</option>
                    <option value="small">Small (1" x 2.125")</option>
                    <option value="large">Large (2.125" x 4")</option>
                  </select>
                </div>
                
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Print Selection</label>
                  <div class="space-y-2">
                    <div>
                      <input type="radio" id="printCurrentPage" name="printSelection" value="current" checked>
                      <label for="printCurrentPage" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Current Page Items</label>
                    </div>
                    <div>
                      <input type="radio" id="printAllItems" name="printSelection" value="all">
                      <label for="printAllItems" class="ml-2 text-sm text-gray-700 dark:text-gray-300">All Items</label>
                    </div>
                    <div>
                      <input type="radio" id="printSpecific" name="printSelection" value="specific">
                      <label for="printSpecific" class="ml-2 text-sm text-gray-700 dark:text-gray-300">Specific Items</label>
                    </div>
                  </div>
                </div>
                
                <div id="specificItemsContainer" class="mb-4 hidden">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Enter Inventory IDs (comma separated)</label>
                  <textarea id="specificItemsInput" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white" rows="3" placeholder="e.g. ITEM001, ITEM002, ITEM003"></textarea>
                </div>
                
                <div class="flex justify-end space-x-2 mt-4 pt-2 border-t border-gray-200 dark:border-gray-700">
                  <button id="cancelPrintBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
                    Cancel
                  </button>
                  <button id="printBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    Print Labels
                  </button>
                </div>
              ` : `
                <div class="text-center py-4">
                  <svg class="h-12 w-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                  </svg>
                  <p class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">Dymo Labeler Not Connected</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Connect to your Dymo Labeler to print inventory labels.</p>
                  <button id="connectDymoFromModalBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    Connect to Dymo
                  </button>
                </div>
              `}
            </div>
          </div>
        </div>
      </div>
    `;
  }

  renderPagination(totalPages) {
    if (totalPages <= 1) return '';
    
    // Generate page buttons similar to master-parts.js
    let paginationHTML = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(0, Math.min(this.currentPage - Math.floor(maxVisiblePages / 2), totalPages - maxVisiblePages));
    if (startPage < 0) startPage = 0;
    const endPage = Math.min(startPage + maxVisiblePages, totalPages);
    
    for (let i = startPage; i < endPage; i++) {
      const isActive = i === this.currentPage;
      paginationHTML += `
        <button data-page="${i}" class="px-2 py-1 border ${isActive 
          ? 'border-blue-500 bg-blue-500 text-white' 
          : 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'} rounded-md text-xs">
          ${i + 1}
        </button>
      `;
    }
    
    return `
      <div class="flex items-center justify-between mt-2 text-xs">
        <div class="text-gray-500 dark:text-gray-400">
          Showing <span id="showingStart">${this.currentPage * this.pageSize + 1}</span> to <span id="showingEnd">${Math.min((this.currentPage + 1) * this.pageSize, this.stockItems.length)}</span> of <span id="totalItems">${this.stockItems.length}</span> items
        </div>
        <div class="flex space-x-1">
          <button id="prevPageBtn" data-page="prev" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage <= 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div id="pagination" class="flex space-x-1">
            ${paginationHTML}
          </div>
          <button id="nextPageBtn" data-page="next" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage >= totalPages - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
  }

  handleDarkMode() {
    const isDarkMode = document.body.classList.contains("dark-mode");
    const title = this.container.querySelector("h2");
    if (title) {
      title.style.color = isDarkMode ? "white" : "";
    }
  }
  
  addNotification(message, type = 'info', duration = 3000) {
    this.notificationSystem.addNotification(message, type, duration);
  }

  setupComponentEventHandlers() {
    // Load stock items button
    const loadBtn = this.container.querySelector('#loadStockItemsBtn');
    if (loadBtn) {
      const newLoadBtn = loadBtn.cloneNode(true);
      loadBtn.parentNode.replaceChild(newLoadBtn, loadBtn);
      newLoadBtn.addEventListener('click', async () => {
        try {
          await this.loadStockItemsBatch();
        } catch (error) {
          console.error('Error loading stock items:', error);
          this.addNotification(error.message, 'error');
        }
      });
    }
    
    // Refresh button
    const refreshBtn = this.container.querySelector('#refreshBtn');
    if (refreshBtn) {
      const newRefreshBtn = refreshBtn.cloneNode(true);
      refreshBtn.parentNode.replaceChild(newRefreshBtn, refreshBtn);
      newRefreshBtn.addEventListener('click', async () => {
        try {
          await this.loadStockItemsBatch();
        } catch (error) {
          console.error('Error refreshing stock items:', error);
          this.addNotification(error.message, 'error');
        }
      });
    }
    
    // Previous page button
    const prevPageBtn = this.container.querySelector('#prevPageBtn');
    if (prevPageBtn) {
      const newPrevBtn = prevPageBtn.cloneNode(true);
      prevPageBtn.parentNode.replaceChild(newPrevBtn, prevPageBtn);
      newPrevBtn.addEventListener('click', () => {
        if (this.currentPage > 0) {
          this.currentPage--;
          this.render();
          this.updatePaginationInfo();
        }
      });
    }
    
    // Next page button
    const nextPageBtn = this.container.querySelector('#nextPageBtn');
    if (nextPageBtn) {
      const newNextBtn = nextPageBtn.cloneNode(true);
      nextPageBtn.parentNode.replaceChild(newNextBtn, nextPageBtn);
      newNextBtn.addEventListener('click', () => {
        const totalPages = Math.ceil(this.stockItems.length / this.pageSize);
        if (this.currentPage < totalPages - 1) {
          this.currentPage++;
          this.render();
          this.updatePaginationInfo();
        }
      });
    }
    
    // Individual page buttons
    const pageButtons = this.container.querySelectorAll('#pagination button');
    pageButtons.forEach(button => {
      const newPageBtn = button.cloneNode(true);
      button.parentNode.replaceChild(newPageBtn, button);
      newPageBtn.addEventListener('click', () => {
        const page = parseInt(newPageBtn.getAttribute('data-page'));
        if (page !== this.currentPage) {
          this.currentPage = page;
          this.render();
          this.updatePaginationInfo();
        }
      });
    });
    
    // Search input
    const searchInput = this.container.querySelector('#searchInput');
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        this.searchTerm = e.target.value.trim();
        this.applySearch();
        this.currentPage = 0; // Reset to first page when searching
        this.render();
      });
    }
    
    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clearSearchBtn');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        const searchInput = this.container.querySelector('#searchInput');
        if (searchInput) searchInput.value = '';
        this.applySearch();
        this.render();
      });
    }
    
    // Sync button - opens the sync modal
    const syncBtn = this.container.querySelector('#syncBtn');
    if (syncBtn) {
      syncBtn.addEventListener('click', () => {
        this.showSyncModal();
      });
    }
    
    // Close sync modal button
    const closeSyncModalBtn = this.container.querySelector('#closeSyncModalBtn');
    if (closeSyncModalBtn) {
      closeSyncModalBtn.addEventListener('click', () => {
        this.hideSyncModal();
      });
    }
    
    // Sync All button (with password check)
    const syncAllBtn = this.container.querySelector('#syncAllBtn');
    if (syncAllBtn) {
      syncAllBtn.addEventListener('click', () => {
        const password = this.container.querySelector('#syncAllPassword').value;
        if (password === '@Enveneengineering2002') {
          this.addNotification('Sync all initiated. Syncing all items to Acumatica...', 'info');
          this.hideSyncModal();
          // Implement the actual sync functionality here
        } else {
          this.addNotification('Incorrect password. Sync all requires administrator password.', 'error');
        }
      });
    }
    
    // Sync by Batch button
    const syncBatchBtn = this.container.querySelector('#syncBatchBtn');
    if (syncBatchBtn) {
      syncBatchBtn.addEventListener('click', () => {
        this.addNotification('Sync by batch initiated. First 20 items will be synced.', 'info');
        this.hideSyncModal();
        // Implement the batch sync functionality here
      });
    }
    
    // Sync Specific Items button
    const syncSpecificBtn = this.container.querySelector('#syncSpecificBtn');
    if (syncSpecificBtn) {
      syncSpecificBtn.addEventListener('click', () => {
        const itemsText = this.container.querySelector('#syncSpecificItems').value;
        if (!itemsText.trim()) {
          this.addNotification('Please enter inventory IDs to sync.', 'warning');
          return;
        }
        
        const itemIds = itemsText.split(',').map(id => id.trim()).filter(id => id);
        if (itemIds.length === 0) {
          this.addNotification('Please enter valid inventory IDs to sync.', 'warning');
          return;
        }
        
        this.addNotification(`Syncing ${itemIds.length} specific items to Acumatica...`, 'info');
        this.hideSyncModal();
        // Implement the specific sync functionality here
      });
    }
    
    // Update button - shows update confirmation modal
    const updateBtn = this.container.querySelector('#updateBtn');
    if (updateBtn) {
      updateBtn.addEventListener('click', () => {
        this.showUpdateModal();
      });
    }
    
    // Close update modal button
    const closeUpdateModalBtn = this.container.querySelector('#closeUpdateModalBtn');
    if (closeUpdateModalBtn) {
      closeUpdateModalBtn.addEventListener('click', () => {
        this.hideUpdateModal();
      });
    }
    
    // Cancel update button
    const cancelUpdateBtn = this.container.querySelector('#cancelUpdateBtn');
    if (cancelUpdateBtn) {
      cancelUpdateBtn.addEventListener('click', () => {
        this.hideUpdateModal();
      });
    }
    
    // Confirm update button
    const confirmUpdateBtn = this.container.querySelector('#confirmUpdateBtn');
    if (confirmUpdateBtn) {
      confirmUpdateBtn.addEventListener('click', () => {
        this.updateFromMasterParts();
        this.hideUpdateModal();
      });
    }
    
    // Export button
    const exportBtn = this.container.querySelector('#exportBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        this.exportInventoryData();
      });
    }
    
    // Settings button
    const settingsBtn = this.container.querySelector('#settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => {
        this.showSettings();
      });
    }
    
    // Print Labels button
    const printLabelsBtn = this.container.querySelector('#printLabelsBtn');
    if (printLabelsBtn) {
      printLabelsBtn.addEventListener('click', () => {
        if (this.dymoAvailable) {
          this.showPrintLabelsModal();
        } else {
          this.addNotification('Dymo Labeler not connected. Connect Dymo to print labels.', 'warning');
        }
      });
    }
    
    // Close print labels modal button
    const closePrintLabelsModalBtn = this.container.querySelector('#closePrintLabelsModalBtn');
    if (closePrintLabelsModalBtn) {
      closePrintLabelsModalBtn.addEventListener('click', () => {
        this.hidePrintLabelsModal();
      });
    }
    
    // Connect Dymo from modal button
    const connectDymoFromModalBtn = this.container.querySelector('#connectDymoFromModalBtn');
    if (connectDymoFromModalBtn) {
      connectDymoFromModalBtn.addEventListener('click', async () => {
        // Close the modal
        this.hidePrintLabelsModal();
        
        // Open the connections panel
        document.getElementById('connectionButton')?.click();
      });
    }
    
    // Print button handlers
    if (this.dymoAvailable) {
      // Toggle specific items container visibility
      const printSelectionRadios = this.container.querySelectorAll('input[name="printSelection"]');
      printSelectionRadios.forEach(radio => {
        radio.addEventListener('change', () => {
          const specificItemsContainer = this.container.querySelector('#specificItemsContainer');
          if (specificItemsContainer) {
            specificItemsContainer.classList.toggle('hidden', radio.value !== 'specific');
          }
        });
      });
      
      // Print button
      const printBtn = this.container.querySelector('#printBtn');
      if (printBtn) {
        printBtn.addEventListener('click', () => {
          this.printLabels();
        });
      }
      
      // Cancel print button
      const cancelPrintBtn = this.container.querySelector('#cancelPrintBtn');
      if (cancelPrintBtn) {
        cancelPrintBtn.addEventListener('click', () => {
          this.hidePrintLabelsModal();
        });
      }
    }
  }
  
  // Update pagination info after changing pages
  updatePaginationInfo() {
    const startElement = this.container.querySelector('#showingStart');
    const endElement = this.container.querySelector('#showingEnd');
    const totalElement = this.container.querySelector('#totalItems');
    
    if (startElement && endElement && totalElement) {
      const startIndex = this.currentPage * this.pageSize + 1;
      const endIndex = Math.min((this.currentPage + 1) * this.pageSize, this.stockItems.length);
      
      startElement.textContent = startIndex;
      endElement.textContent = endIndex;
      totalElement.textContent = this.stockItems.length;
    }
  }
  
  // Apply search filter
  applySearch() {
    if (!this.searchTerm) {
      this.filteredItems = this.stockItems;
      return;
    }
    
    const searchLower = this.searchTerm.toLowerCase();
    this.filteredItems = this.stockItems.filter(item => {
      // Get values with fallbacks
      const inventoryId = item.InventoryID?.value || '';
      const description = item.Description?.value || '';
      
      // Extract attributes for easier access
      const attrs = {};
      if (item.Attributes && Array.isArray(item.Attributes)) {
        item.Attributes.forEach(attr => {
          if (attr && attr.AttributeID && attr.AttributeID.value && attr.Value && attr.Value.value) {
            attrs[attr.AttributeID.value] = attr.Value.value;
          }
        });
      }
      
      const aom = attrs['AOM'] || '';
      const coo = attrs['COO'] || '';
      const eccn = attrs['ECCN'] || '';
      const hsCode = attrs['HSCODE'] || '';
      
      // Search in all fields
      return inventoryId.toLowerCase().includes(searchLower) ||
             description.toLowerCase().includes(searchLower) ||
             aom.toLowerCase().includes(searchLower) ||
             coo.toLowerCase().includes(searchLower) ||
             eccn.toLowerCase().includes(searchLower) ||
             hsCode.toLowerCase().includes(searchLower);
    });
  }
  
  // Export inventory data to CSV
  exportInventoryData() {
    try {
      // Since we removed checkboxes, we'll export all items shown in the current filter
      const items = this.searchTerm ? this.filteredItems : this.stockItems;
      
      if (items.length === 0) {
        this.addNotification("No items to export", "warning");
        return;
      }
      
      // Create CSV content
      let csvContent = "Inventory ID,Description,Address of Manufacturing,Country of Origin,ECCN,HS Code\n";
      
      items.forEach(item => {
        // Extract attributes for easier access
        const attrs = {};
        if (item.Attributes && Array.isArray(item.Attributes)) {
          item.Attributes.forEach(attr => {
            if (attr && attr.AttributeID && attr.AttributeID.value && attr.Value && attr.Value.value) {
              attrs[attr.AttributeID.value] = attr.Value.value;
            }
          });
        }
        
        // Get values with fallbacks
        const inventoryId = item.InventoryID?.value || '';
        const description = item.Description?.value || '';
        const aom = attrs['AOM'] || '';
        const coo = attrs['COO'] || '';
        const eccn = attrs['ECCN'] || '';
        const hsCode = attrs['HSCODE'] || '';
        
        // Escape values for CSV
        const escapeForCSV = (value) => {
          if (!value) return '';
          let escaped = value.replace(/"/g, '""');
          return `"${escaped}"`;
        };
        
        csvContent += `${escapeForCSV(inventoryId)},${escapeForCSV(description)},${escapeForCSV(aom)},${escapeForCSV(coo)},${escapeForCSV(eccn)},${escapeForCSV(hsCode)}\n`;
      });
      
      // Create and trigger download
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'inventory_export.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.addNotification(`Exported ${items.length} items successfully`, "success");
    } catch (error) {
      console.error('Error exporting inventory data:', error);
      this.addNotification(`Error exporting: ${error.message}`, "error");
    }
  }
  
  // Show/hide modal methods
  showSyncModal() {
    const modal = this.container.querySelector('#syncModal');
    if (modal) {
      modal.classList.remove('hidden');
    }
  }
  
  hideSyncModal() {
    const modal = this.container.querySelector('#syncModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }
  
  showUpdateModal() {
    const modal = this.container.querySelector('#updateModal');
    if (modal) {
      modal.classList.remove('hidden');
    }
  }
  
  hideUpdateModal() {
    const modal = this.container.querySelector('#updateModal');
    if (modal) {
      modal.classList.add('hidden');
    }
  }
  
  // Update from master parts - implement the actual functionality
  updateFromMasterParts() {
    this.addNotification('Updating inventory data from Master Parts...', 'info');
    
    try {
      // Show loading state
      this.isLoading = true;
      this.render();
      
      // Try multiple possible locations for the masterparts.json file
      const jsonUrl = chrome.runtime.getURL('json/masterparts.json');
      console.log("Attempting to fetch Master Parts data from:", jsonUrl);
      
      fetch(jsonUrl)
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to load Master Parts data: ${response.status} ${response.statusText}`);
          }
          return response.json();
        })
        .then(masterPartsData => {
          console.log("Successfully loaded Master Parts data:", masterPartsData.length + " items");
          
          // Create a map for faster lookup
          const masterPartsMap = new Map();
          
          // Assuming masterPartsData is an array of parts
          masterPartsData.forEach(part => {
            // Use part number as the key for matching with inventory ID
            if (part['Part Number']) {
              masterPartsMap.set(part['Part Number'], {
                aom: part['Manufacturer & Address'] || '',
                coo: part['Country of Origin'] || '',
                eccn: part['ECCN'] || '',
                hsCode: part['HS Code'] || ''
              });
            }
          });
          
          // Counter for updated items
          let updatedCount = 0;
          
          // Update the stockItems with data from masterPartsMap
          this.stockItems.forEach(item => {
            const inventoryId = item.InventoryID?.value;
            if (!inventoryId) return;
            
            // Check if we have matching master part data
            const masterPartData = masterPartsMap.get(inventoryId);
            if (masterPartData) {
              // Update attributes in the item
              if (!item.Attributes) {
                item.Attributes = [];
              }
              
              // Helper function to update or add an attribute
              const updateAttribute = (attrId, value) => {
                if (!value) return;
                
                // Find existing attribute
                const existingAttr = item.Attributes.find(attr => 
                  attr.AttributeID && attr.AttributeID.value === attrId
                );
                
                if (existingAttr) {
                  // Update existing attribute
                  existingAttr.Value = { value: value };
                } else {
                  // Add new attribute
                  item.Attributes.push({
                    AttributeID: { value: attrId },
                    Value: { value: value }
                  });
                }
              };
              
              // Update the four specific attributes
              updateAttribute('AOM', masterPartData.aom);
              updateAttribute('COO', masterPartData.coo);
              updateAttribute('ECCN', masterPartData.eccn);
              updateAttribute('HSCODE', masterPartData.hsCode);
              
              updatedCount++;
            }
          });
          
          // Finish loading and render updated data
          this.isLoading = false;
          this.render();
          
          if (updatedCount > 0) {
            this.addNotification(`Successfully updated ${updatedCount} items from Master Parts!`, 'success');
          } else {
            this.addNotification('No matching items found to update.', 'warning');
          }
        })
        .catch(error => {
          console.error('Error fetching master parts data:', error);
          
          // Try alternative method - check if in chrome.storage
          if (chrome?.storage?.local) {
            chrome.storage.local.get(['masterparts'], (result) => {
              if (result.masterparts && Array.isArray(result.masterparts) && result.masterparts.length > 0) {
                console.log(`Found ${result.masterparts.length} parts in chrome.storage`);
                this.processMasterPartsData(result.masterparts);
              } else {
                this.isLoading = false;
                this.render();
                this.addNotification(`Error updating: ${error.message}`, 'error');
              }
            });
          } else {
            this.isLoading = false;
            this.render();
            this.addNotification(`Error updating: ${error.message}`, 'error');
          }
        });
        
    } catch (error) {
      console.error('Error updating from master parts:', error);
      this.isLoading = false;
      this.render();
      this.addNotification(`Error updating: ${error.message}`, 'error');
    }
  }
  
  // Helper method to process master parts data
  processMasterPartsData(masterPartsData) {
    try {
      console.log("Processing Master Parts data:", masterPartsData.length + " items");
      
      // Create a map for faster lookup
      const masterPartsMap = new Map();
      
      // Assuming masterPartsData is an array of parts
      masterPartsData.forEach(part => {
        // Use part number as the key for matching with inventory ID
        if (part['Part Number']) {
          masterPartsMap.set(part['Part Number'], {
            aom: part['Manufacturer & Address'] || '',
            coo: part['Country of Origin'] || '',
            eccn: part['ECCN'] || '',
            hsCode: part['HS Code'] || ''
          });
        }
      });
      
      // Counter for updated items
      let updatedCount = 0;
      
      // Update the stockItems with data from masterPartsMap
      this.stockItems.forEach(item => {
        const inventoryId = item.InventoryID?.value;
        if (!inventoryId) return;
        
        // Check if we have matching master part data
        const masterPartData = masterPartsMap.get(inventoryId);
        if (masterPartData) {
          // Update attributes in the item
          if (!item.Attributes) {
            item.Attributes = [];
          }
          
          // Helper function to update or add an attribute
          const updateAttribute = (attrId, value) => {
            if (!value) return;
            
            // Find existing attribute
            const existingAttr = item.Attributes.find(attr => 
              attr.AttributeID && attr.AttributeID.value === attrId
            );
            
            if (existingAttr) {
              // Update existing attribute
              existingAttr.Value = { value: value };
            } else {
              // Add new attribute
              item.Attributes.push({
                AttributeID: { value: attrId },
                Value: { value: value }
              });
            }
          };
          
          // Update the four specific attributes
          updateAttribute('AOM', masterPartData.aom);
          updateAttribute('COO', masterPartData.coo);
          updateAttribute('ECCN', masterPartData.eccn);
          updateAttribute('HSCODE', masterPartData.hsCode);
          
          updatedCount++;
        }
      });
      
      // Finish loading and render updated data
      this.isLoading = false;
      this.render();
      
      if (updatedCount > 0) {
        this.addNotification(`Successfully updated ${updatedCount} items from Master Parts!`, 'success');
      } else {
        this.addNotification('No matching items found to update.', 'warning');
      }
    } catch (error) {
      console.error('Error processing master parts data:', error);
      this.isLoading = false;
      this.render();
      this.addNotification(`Error processing data: ${error.message}`, 'error');
    }
  }
  
  // Show settings dialog
  showSettings() {
    this.addNotification("Settings functionality will be implemented later", "info");
  }
  
  // Print Labels Modal Functions
  showPrintLabelsModal() {
    const printLabelsModal = this.container.querySelector('#printLabelsModal');
    if (printLabelsModal) {
      printLabelsModal.classList.remove('hidden');
    }
  }
  
  hidePrintLabelsModal() {
    const printLabelsModal = this.container.querySelector('#printLabelsModal');
    if (printLabelsModal) {
      printLabelsModal.classList.add('hidden');
    }
  }
  
  // Print Labels Function
  printLabels() {
    try {
      // Get selected label type
      const labelTypeSelect = this.container.querySelector('#labelTypeSelect');
      const labelType = labelTypeSelect ? labelTypeSelect.value : 'standard';
      
      // Get print selection
      const printSelection = this.container.querySelector('input[name="printSelection"]:checked');
      const selectionType = printSelection ? printSelection.value : 'current';
      
      // Get items to print
      let itemsToPrint = [];
      
      if (selectionType === 'current') {
        // Current page items
        const startIndex = this.currentPage * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, this.stockItems.length);
        itemsToPrint = this.stockItems.slice(startIndex, endIndex);
      } else if (selectionType === 'all') {
        // All items
        itemsToPrint = this.stockItems;
      } else if (selectionType === 'specific') {
        // Specific items by inventory ID
        const specificItemsInput = this.container.querySelector('#specificItemsInput');
        if (specificItemsInput && specificItemsInput.value.trim()) {
          const inventoryIds = specificItemsInput.value.split(',').map(id => id.trim()).filter(id => id);
          
          if (inventoryIds.length > 0) {
            itemsToPrint = this.stockItems.filter(item => 
              inventoryIds.includes(item.InventoryID?.value || '')
            );
          }
        }
      }
      
      if (itemsToPrint.length === 0) {
        this.addNotification('No items selected for printing.', 'warning');
        return;
      }
      
      // Placeholder for actual printing - would integrate with Dymo SDK here
      console.log(`Printing ${itemsToPrint.length} labels with type: ${labelType}`);
      this.addNotification(`Printing ${itemsToPrint.length} labels with Dymo. Please wait...`, 'info');
      
      // Simulate print process
      setTimeout(() => {
        this.addNotification('Labels printed successfully!', 'success');
        this.hidePrintLabelsModal();
      }, 1500);
      
    } catch (error) {
      console.error('Error printing labels:', error);
      this.addNotification(`Error printing labels: ${error.message}`, 'error');
    }
  }
}

export default InventorySyncComponent;