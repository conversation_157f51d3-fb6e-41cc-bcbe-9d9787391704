// Purchase Analytics component for KPI Dashboard
// ApexCharts is loaded via script tag and available globally
import { 
  adaptDataForPurchaseByVendors, 
  resolveVendorNames, 
  getPurchaseByVendorsTooltipFormatter,
  adaptDataForVendorWeekdayBreakdown, 
  createVendorWeekdayChartOptions, 
  getVendorWeekdayTooltipFormatter,
  adaptDataForTop10Vendors,
  createTop10VendorsChartOptions,
  getTop10VendorsTooltipFormatter
} from './purchase_analytics_helper.js';

export class PurchaseAnalyticsComponent {
  constructor(container) {
    this.container = container;
    
    // Define chart instances
    this.charts = {
      purchaseTrend: null,
      purchaseByStatus: null,
      leadTimeAverage: null,
      mainKpi: null,
      purchaseByDepartment: null,
      weeklyPurchases: null,
      purchaseByVendors: null, // Added new chart
      top5VendorsByDay: null // Added new chart
    };
    
    // Default time range - standardized
    this.timeRange = '30d'; // Default to Monthly
    
    // Currency formatter
    this.currencyFormatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2, // Changed from 0 to 2 to show exact currency values
      maximumFractionDigits: 2  // Changed from 0 to 2 to show exact currency values
    });
    
    // Data properties
    this.purchaseOrders = [];
    this.filteredPurchaseOrders = []; // Data filtered by the *global* time range
    this.vendors = [];
    this.vendorLookup = {};
    
    // Time range filters for individual charts - standardized
    this.chartTimeRanges = {
      purchaseTrend: '30d',
      purchaseByStatus: '30d',
      leadTimeAverage: '30d',
      mainKpi: '30d', // Default main chart to Monthly
      purchaseByDepartment: '30d',
      weeklyPurchases: '1y',  // Default to current year view
      purchaseByVendors: '7d', // Default new chart to weekly view
      top5VendorsByDay: '7d'  // Default to weekly view for new chart
    };
    
    // Custom date ranges for individual charts
    this.customDateRanges = {}; // Initialize custom date ranges
    
    // Color schemes for charts
    this.chartColors = {
      primary: ['#3b82f6', '#60a5fa', '#93c5fd', '#bfdbfe', '#dbeafe'],
      secondary: ['#8b5cf6', '#a78bfa', '#c4b5fd', '#ddd6fe', '#ede9fe'],
      success: ['#10b981', '#34d399', '#6ee7b7', '#a7f3d0', '#d1fae5'],
      danger: ['#ef4444', '#f87171', '#fca5a5', '#fecaca', '#fee2e2'],
      warning: ['#f59e0b', '#fbbf24', '#fcd34d', '#fde68a', '#fef3c7'],
      gray: ['#1f2937', '#4b5563', '#9ca3af', '#d1d5db', '#f3f4f6'],
      status: ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6', '#6b7280'] // green, blue, yellow, red, purple, gray
    };
    
    // Mini chart instances for KPI cards
    this.miniCharts = {
      orders: null,
      spending: null,
      leadTime: null,
      pending: null, // Added pending mini chart
      ontime: null // Added on-time mini chart
    };

    // Database properties - Updated to match other modules
    this.poDbName = 'purchaseOrdersDb';  // Matches purchase_order.js
    this.poStoreName = 'purchaseOrders'; // Matches purchase_order.js
    this.vendorDbName = 'vendorMetricsDB'; // Matches vendor-metrics.js
    this.vendorStoreName = 'vendors'; // Matches vendor-metrics.js
    this.db = null;
    this.vendorDb = null;
    this.dbReady = false;
    this.vendorDbReady = false;
    
    // Loading state flag
    this.isLoading = true;
    
    // Flag to control mock data fallback behavior - REMOVED
    // this.useMockData = true;
  }

  async init() {
    console.log("Initializing purchase analytics component");
    
    // Initialize chart time ranges (redundant, done in constructor, but safe)
    this.chartTimeRanges = {
      purchaseTrend: '30d',
      purchaseByStatus: '30d',
      leadTimeAverage: '30d',
      mainKpi: '30d',
      purchaseByDepartment: '30d',
      weeklyPurchases: '1y',
      purchaseByVendors: '7d',
      top5VendorsByDay: '7d'
    };
    
    // Load manual categorizations from localStorage
    this.loadManualCategorizations();
    
    // Show loading indicator
    this.showLoading();
    
    // Render initial loading UI structure
    this.renderLoading(); // Render loading state first

    try {
      // Load ApexCharts library first if not already loaded
      if (typeof ApexCharts === 'undefined') {
        console.log("Loading ApexCharts library...");
        try {
          await this.loadApexChartsLibrary();
          console.log("ApexCharts library loaded successfully");
        } catch (chartLibError) {
          console.error("Failed to load ApexCharts library:", chartLibError);
          this.showError("Chart library could not be loaded: " + chartLibError.message);
          this.hideLoading();
          this.render(); // Render content structure even without charts
          return; // Stop initialization if charts fail
        }
      }
      
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Load data (prioritizes DB, falls back to mock)
      await this.loadData();

      // Hide loading indicator
      this.hideLoading();
      
      // Update UI with loaded data (render the content structure)
      this.render();
      
      // Allow DOM time to update after rendering content
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Update all charts now that DOM elements should exist
      // This uses the initial chartTimeRanges settings
      this.updateAllCharts();
      
      // Re-attach event listeners to the newly rendered content
      this.setupEventListeners();

    } catch (error) {
      console.error("Error initializing purchase analytics:", error);
      this.hideLoading();
      
      // Render the content structure but show an error message
      this.render(); // Render the layout
      this.showError("Failed to initialize analytics: " + error.message);
      
      // Optionally, try rendering with mock data as a last resort
      try {
        console.log("Attempting to render with mock data after error...");
      const mockData = await this.generateMockData();
      this.purchaseOrders = mockData.purchaseOrders;
      this.vendors = mockData.vendors;
        this.createVendorLookup(); // Create lookup from mock data
        this.applyGlobalFilter(); // Apply filter to mock data
        this.render(); // Re-render with mock data structure
        await new Promise(resolve => setTimeout(resolve, 50)); // Allow DOM time
        this.updateAllCharts(); // Try updating charts with mock data
        this.setupEventListeners(); // Attach listeners
      } catch (mockError) {
        console.error("Failed to render even with mock data:", mockError);
        // Show a more permanent error if mock data also fails
        this.container.innerHTML = `<div class="p-4 text-red-600">Critical Error: Could not load analytics data or mock data. ${error.message}</div>`;
      }
    }
  }
  
  // Load ApexCharts library if needed
  async loadApexChartsLibrary() {
    return new Promise((resolve, reject) => {
      if (typeof ApexCharts !== 'undefined') {
        console.log("ApexCharts already loaded");
        return resolve();
      }
      
      const script = document.createElement('script');
      script.src = '../library/apexcharts.min.js';
      script.async = true;
      
      script.onload = () => {
        if (typeof ApexCharts === 'undefined') {
          reject(new Error("ApexCharts failed to initialize after loading"));
        } else {
          resolve();
        }
      };
      
      script.onerror = () => {
        reject(new Error("Failed to load ApexCharts script"));
      };
      
      document.head.appendChild(script);
    });
  }

  async initDatabase() {
    try {
      // Initialize purchase orders database
      await this.initPurchaseOrdersDB();
      
      // Initialize vendors database
      await this.initVendorsDB();
      
      console.log("Both databases initialized successfully");
    } catch (error) {
      console.error("Error initializing databases:", error);
      throw error; // Propagate the error
    }
  }
  
  async initPurchaseOrdersDB() {
    return new Promise((resolve, reject) => {
      if (this.dbReady && this.db) {
          console.log("PO DB already initialized.");
          return resolve(this.db);
      }
      console.log("Initializing PO DB...");
      // Check for mock first
      const dbOpen = window.indexedDB || indexedDB;
      const request = dbOpen.open(this.poDbName); // Use existing version
      
      request.onerror = (event) => {
        console.error("IndexedDB error (purchase orders):", event.target.error);
        reject("Could not open purchase orders database. Please ensure your browser supports IndexedDB.");
      };
      
      request.onsuccess = (event) => {
        this.db = event.target.result;
        this.dbReady = true;
        console.log("Purchase orders database initialized successfully with version:", this.db.version);
        resolve(this.db); // Resolve with the db instance
      };
      
      // No onupgradeneeded handled here - assumed created elsewhere
    });
  }
  
  async initVendorsDB() {
    return new Promise((resolve, reject) => {
       if (this.vendorDbReady && this.vendorDb) {
           console.log("Vendor DB already initialized.");
           return resolve(this.vendorDb);
       }
       console.log("Initializing Vendor DB...");
       // Check for mock first
       const dbOpen = window.indexedDB || indexedDB;
       const request = dbOpen.open(this.vendorDbName); // Use existing version
      
      request.onerror = (event) => {
        console.error("IndexedDB error (vendors):", event.target.error);
        reject("Could not open vendors database. Please ensure your browser supports IndexedDB.");
      };
      
      request.onsuccess = (event) => {
        this.vendorDb = event.target.result;
        this.vendorDbReady = true;
        console.log("Vendors database initialized successfully with version:", this.vendorDb.version);
         resolve(this.vendorDb); // Resolve with the db instance
      };
      
       // No onupgradeneeded handled here - assumed created elsewhere
    });
  }

  async loadData(forceRefresh = false) {
      this.showLoading();
      this.isLoading = true;
      
    try {
      // Make sure databases are initialized
      if (!this.dbReady || !this.vendorDbReady) {
        console.log("Databases not ready, initializing...");
           await this.initDatabase();
        
           if (!this.dbReady || !this.vendorDbReady) {
          throw new Error("Failed to initialize databases");
        }
      }

      // Try loading purchase orders from IndexedDB
      try {
        console.log("Loading purchase orders from IndexedDB...");
        const purchaseOrders = await this.getPurchaseOrdersFromIndexedDB();
        console.log(`Loaded ${purchaseOrders.length} purchase orders from database`);
        
        if (purchaseOrders.length > 0) {
          this.purchaseOrders = purchaseOrders;
          // Log some POs to verify the data includes convertedTotal
          console.log("Sample PO data:", this.purchaseOrders.slice(0, 2));
        } else {
          console.warn("No purchase orders found in database, will use mock data");
          const mockData = await this.generateMockData();
          this.purchaseOrders = mockData.purchaseOrders;
        }
      } catch (error) {
        console.error("Error loading purchase orders:", error);
        const mockData = await this.generateMockData();
        this.purchaseOrders = mockData.purchaseOrders;
      }

      // Try loading vendors from IndexedDB
      try {
        console.log("Loading vendors from IndexedDB...");
        const vendors = await this.getVendorsFromIndexedDB();
        console.log(`Loaded ${vendors.length} vendors from database`);
        
        if (vendors.length > 0) {
          this.vendors = vendors;
        } else {
          console.warn("No vendors found in database, will use mock data");
          const mockData = await this.generateMockData();
          this.vendors = mockData.vendors;
        }
      } catch (error) {
        console.error("Error loading vendors:", error);
        const mockData = await this.generateMockData();
          this.vendors = mockData.vendors;
      }
      
      // Create vendor lookup map
      this.createVendorLookup();

      // Apply the global filter based on the current setting
      this.applyGlobalFilter();
      
      // Data is ready
      this.isLoading = false;
      this.hideLoading();
      console.log("Data loading complete with", this.purchaseOrders.length, "purchase orders");
      
    } catch (error) {
      console.error('Error loading data:', error);
      this.isLoading = false;
      this.hideLoading();
      this.showError('Failed to load purchase data: ' + error.message);
      
      // Keep existing data (if any) or use mock data as last resort
      if (this.purchaseOrders.length === 0) {
        console.warn("No existing data, generating mock data as fallback");
        const mockData = await this.generateMockData();
        this.purchaseOrders = mockData.purchaseOrders;
        this.vendors = mockData.vendors;
        this.applyGlobalFilter();
      }
    }
  }

  // Helper to create vendor lookup map
  createVendorLookup() {
     this.vendorLookup = this.vendors.reduce((lookup, vendor) => {
        // Prioritize vendorId if available and valid
        if (vendor.vendorId && typeof vendor.vendorId === 'string' && vendor.vendorId.trim() !== '') {
           lookup[vendor.vendorId] = vendor;
        } else if (vendor.id) { // Fallback to id if vendorId is missing
           lookup[vendor.id] = vendor;
        }
        return lookup;
     }, {});
     console.log(`Created vendor lookup with ${Object.keys(this.vendorLookup).length} entries.`);
     // Log a sample lookup entry if available
     const firstKey = Object.keys(this.vendorLookup)[0];
     if (firstKey) {
        console.log("Sample vendor lookup entry:", this.vendorLookup[firstKey]);
     }
  }

  showLoading() {
    // Avoid adding multiple loaders
    if (document.getElementById('purchase-analytics-loader')) {
        return;
      }
      
      const loader = document.createElement('div');
      loader.id = 'purchase-analytics-loader';
      loader.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50';
      loader.innerHTML = `
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
          <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p class="mt-4 text-gray-600 dark:text-gray-400">Loading purchase data...</p>
        </div>
      `;
      document.body.appendChild(loader);
    this.isLoading = true; // Ensure loading state is true
  }

  hideLoading() {
    const loader = document.getElementById('purchase-analytics-loader');
    if (loader) {
      loader.remove();
    }
    this.isLoading = false; // Ensure loading state is false
  }

  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.id = 'purchase-analytics-error';
    errorDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4';
    errorDiv.innerHTML = `
      <p>${message}</p>
      <button class="mt-2 px-3 py-1 bg-red-200 hover:bg-red-300 rounded text-red-800">Dismiss</button>
    `;
    
    // Add to container
    this.container.prepend(errorDiv);
    
    // Add event listener to dismiss button
    errorDiv.querySelector('button').addEventListener('click', () => {
      this.hideError();
    });
  }
  
  hideError() {
    const errorDiv = document.getElementById('purchase-analytics-error');
    if (errorDiv) {
      errorDiv.remove();
    }
  }

  async generateMockData() {
    console.log("Generating mock purchase orders and vendor data...");
    // Generate mock purchase orders and vendor data
    const purchaseOrders = [];
    const vendors = [];
    const today = new Date();
    
    // Vendor data
    const vendorNames = [
      'Acme Supply Co', 
      'Global Materials Ltd', 
      'Tech Components Inc', 
      'Quality Hardware LLC', 
      'Precision Parts Corp',
      'Atlas Manufacturing',
      'Zenith Industrial Supply',
      'Metro Equipment Co',
      'Superior Materials Inc',
      'Royal Metal Works'
    ];
    
    // Create vendors
    vendorNames.forEach((name, index) => {
      vendors.push({
        id: `v${index + 1}`, // Keep internal ID
        vendorId: `V${10000 + index}`, // Acumatica-like ID
        name: name,
        status: 'Active',
        leadTime: Math.floor(Math.random() * 20) + 5, // Mock lead time (numeric)
        onTimeDelivery: Math.floor(Math.random() * 30) + 70, // Mock OTD% (numeric)
        lastModified: new Date(today.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000) // Date object
      });
    });
    
    // Statuses for purchase orders
    const statuses = ['Open', 'Closed', 'Canceled', 'Hold', 'Pending Approval', 'Awaiting Receipt'];
    
    // Generate 100 purchase orders
    for (let i = 0; i < 100; i++) {
      const randomVendorIndex = Math.floor(Math.random() * vendors.length);
      const vendor = vendors[randomVendorIndex];
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365 * 1.5)); // Random date within last ~1.5 years (Date object)
      date.setHours(12, 0, 0, 0); // Set to midday to avoid timezone issues at boundaries
      
      const amount = Math.floor(Math.random() * 10000) + 500; // Numeric amount
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      
      // Calculate expected and actual delivery dates (Date objects)
      const expectedDelivery = new Date(date);
      expectedDelivery.setDate(expectedDelivery.getDate() + vendor.leadTime); // Use numeric leadTime
      
      let actualDelivery = null;
      let isOnTime = false;
      if (status === 'Closed' || status === 'Received') {
          actualDelivery = new Date(expectedDelivery);
      const deliveryVariance = Math.floor(Math.random() * 15) - 7; // -7 to +7 days variance
      actualDelivery.setDate(actualDelivery.getDate() + deliveryVariance);
          isOnTime = actualDelivery <= expectedDelivery;
      }
      
      purchaseOrders.push({
        id: `po${i + 1}`, // Internal ID
        orderNbr: `PO-${20230 + i}`, // Acumatica-like PO number
        vendorId: vendor.vendorId, // Use Acumatica-like vendor ID
        // vendorName: vendor.name, // Don't store vendorName, use lookup
        date: date, // Store as Date object
        total: amount, // Ensure 'total' is numeric
        status: status,
        promisedDate: expectedDelivery, // Store as Date object
        actualDelivery: actualDelivery, // Store as Date object or null
        isOnTime: isOnTime, // Boolean flag for OTD calculation
        lastModified: new Date(date.getTime() + Math.random() * 10 * 24 * 60 * 60 * 1000), // Date object
        lineItems: [] // Match PO schema
      });
    }
    
    console.log(`Generated ${purchaseOrders.length} mock POs and ${vendors.length} mock vendors.`);
    console.log("First 5 mock POs:", purchaseOrders.slice(0, 5));
    console.log("First 5 mock Vendors:", vendors.slice(0, 5));
    return { purchaseOrders, vendors };
  }

  // Update the filterDataByTimeRange method to handle custom date ranges
  filterDataByTimeRange(data, timeRange, chartId) {
    console.log(`Filtering data for ${chartId} with time range: ${timeRange}`);
    
    if (!data || !data.length) {
      console.warn("No data to filter");
        return [];
    }
    
    // Standardize current date to UTC midnight for consistent comparison
    const now = new Date();
    now.setUTCHours(0, 0, 0, 0);
    
    // Check if a custom date range exists for this chart
    if (this.customDateRanges && this.customDateRanges[chartId]) {
      // Use the exact custom date range - assume they are already UTC midnight Date objects
      const customRange = this.customDateRanges[chartId];
      let startDate = customRange.start instanceof Date ? customRange.start : new Date(customRange.start);
      let endDate = customRange.end instanceof Date ? customRange.end : new Date(customRange.end);
      
      // Ensure they represent UTC midnight
      startDate.setUTCHours(0, 0, 0, 0);
      endDate.setUTCHours(0, 0, 0, 0);
      
      console.log(`Using exact custom UTC date range for ${chartId}: ${startDate.toISOString()} to ${endDate.toISOString()}`);
      
      // Filter data using the exact UTC midnight custom date range
      return data.filter(item => {
        // Assume item.date is already a UTC midnight Date object or null
        const itemDate = item.date; 
        if (!(itemDate instanceof Date)) {
          return false;
        }
        
        // Comparison uses UTC midnight dates
        return itemDate >= startDate && itemDate <= endDate;
      });
    }
    
    // Standard time ranges - determine start date based on timeRange, relative to UTC midnight 'now'
    let startDate;
    
    switch(timeRange) {
      case '7d':
        startDate = new Date(now); // Already UTC midnight
        startDate.setUTCDate(now.getUTCDate() - 7);
        break;
      case '30d':
        startDate = new Date(now); // Already UTC midnight
        startDate.setUTCDate(now.getUTCDate() - 30);
        break;
      case '1y':
        startDate = new Date(now); // Already UTC midnight
        startDate.setUTCFullYear(now.getUTCFullYear() - 1);
        break;
      default:
        console.warn(`Unknown time range: ${timeRange}, defaulting to full range.`);
        // Find earliest valid UTC date
        const validDates = data.map(po => po.date).filter(d => d instanceof Date);
        startDate = validDates.length ? new Date(Math.min(...validDates.map(d => d.getTime()))) : new Date(0); // Use earliest date or epoch
        startDate.setUTCHours(0, 0, 0, 0); // Ensure it's UTC midnight
    }
    
    // Filter by date - comparing against UTC midnight 'startDate' and 'now'
    return data.filter(item => {
      // Assume item.date is already a UTC midnight Date object or null
      const itemDate = item.date;
      if (!(itemDate instanceof Date)) {
        return false;
      }
    
      // Comparison uses UTC midnight dates
      return itemDate >= startDate && itemDate <= now;
    });
  }

  // Update applyFilterToChartData to pass the chart ID
  applyFilterToChartData(chartId) {
    console.log(`Applying filter to chart ${chartId} with timeRange: ${this.chartTimeRanges[chartId]}`);
    
    if (this.purchaseOrders.length === 0) {
      console.warn("No purchase orders available for filtering");
      // Return empty data structure expected by the chart
      return []; // Or a more specific empty structure if needed
    }
    
    const chartTimeRange = this.chartTimeRanges[chartId];
    const filteredData = this.filterDataByTimeRange(this.purchaseOrders, chartTimeRange, chartId);
    
    console.log(`Filtered data for ${chartId}: ${filteredData.length} records`);
    
    // Update the specific chart with filtered data
    this.updateChart(chartId, filteredData, chartTimeRange);
  }
  
  updateAllCharts() {
    console.log("Updating all charts...");
    // Ensure charts are updated only if ApexCharts is available
     if (typeof ApexCharts === 'undefined') {
       console.warn("ApexCharts not available, skipping chart updates.");
       return;
     }
     
    // Call applyFilterToChartData for each chart type using its specific time range
    Object.keys(this.charts).forEach(chartId => {
      // Skip if chart instance is null (e.g., if removed)
      if (this.charts.hasOwnProperty(chartId)) {
         // Special handling for weeklyPurchases to ensure it loads with all data
         if (chartId === 'weeklyPurchases') {
           console.log(`Updating ${chartId} chart with all historical data`);
           // Use the unfiltered purchaseOrders data for weekly purchases to ensure we get all historical data
           const timeRange = this.chartTimeRanges[chartId];
           this.updateChart(chartId, this.purchaseOrders, timeRange);
         } else {
         this.applyFilterToChartData(chartId);
         }
      }
    });
    
    // Update KPI cards using the globally filtered data
    this.updateKPICards();
  }
  
  updateKPICards() {
    console.log("Updating KPI cards...");
    // Update KPI card values and mini charts using this.filteredPurchaseOrders
    
    // Total POs
    const totalPOs = this.filteredPurchaseOrders.length;
    const totalPOsElement = document.getElementById('total-pos');
    if (totalPOsElement) totalPOsElement.textContent = totalPOs;
    
    // Calculate % change vs previous period
    const previousPeriod = this.getPreviousPeriodData();
    const previousPOCount = previousPeriod ? previousPeriod.length : 0;
    const poPercentChange = previousPOCount > 0 ? ((totalPOs - previousPOCount) / previousPOCount * 100).toFixed(1) : 0;
    const poTrendElement = document.getElementById('pos-trend');
    if (poTrendElement) {
      poTrendElement.textContent = `${poPercentChange}% from last period`;
    }
    
    // Total Spending - Use convertedTotal instead of total
    const totalSpending = this.filteredPurchaseOrders.reduce((sum, po) => {
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      return sum + amount;
    }, 0);
    const totalSpendingElement = document.getElementById('total-spending');
    if (totalSpendingElement) totalSpendingElement.textContent = this.currencyFormatter.format(totalSpending);
    
    // Calculate spending % change
    const previousSpending = previousPeriod ? previousPeriod.reduce((sum, po) => {
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      return sum + amount;
    }, 0) : 0;
    const spendingPercentChange = previousSpending > 0 ? ((totalSpending - previousSpending) / previousSpending * 100).toFixed(1) : 0;
    const spendingTrendElement = document.getElementById('spending-trend');
    if (spendingTrendElement) {
      spendingTrendElement.textContent = `${spendingPercentChange}% from last period`;
    }
    
    // Calculate Average PO Value
    let avgPOValue = 0;
    if (totalPOs > 0) {
      avgPOValue = totalSpending / totalPOs;
    }
    const avgPOValueElement = document.getElementById('avg-po-value');
    if (avgPOValueElement) avgPOValueElement.textContent = this.currencyFormatter.format(avgPOValue);
    
    // Calculate avg PO value % change
    const previousAvgValue = previousPOCount > 0 && previousSpending > 0 ? 
      previousSpending / previousPOCount : 0;
    const avgValuePercentChange = previousAvgValue > 0 ? 
      ((avgPOValue - previousAvgValue) / previousAvgValue * 100).toFixed(1) : 0;
    const avgValueTrendElement = document.getElementById('avg-value-trend');
    if (avgValueTrendElement) {
      avgValueTrendElement.textContent = `${avgValuePercentChange}% from last period`;
    }
    
    // Get Top Vendor (replacing Lead Time)
    const vendorCounts = {};
    this.filteredPurchaseOrders.forEach(po => {
      const vendorId = po.vendorId || 'unknown';
      if (!vendorCounts[vendorId]) {
        vendorCounts[vendorId] = {
          count: 0,
          total: 0,
          name: this.getVendorName(vendorId)
        };
      }
      vendorCounts[vendorId].count += 1;
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      vendorCounts[vendorId].total += amount;
    });
    
    // Find the top vendor by order count
    let topVendor = { name: 'None', count: 0, percentage: 0 };
    if (Object.keys(vendorCounts).length > 0) {
      const sortedVendors = Object.entries(vendorCounts)
        .sort((a, b) => b[1].count - a[1].count);
      
      if (sortedVendors.length > 0) {
        const [vendorId, data] = sortedVendors[0];
        const percentage = totalPOs > 0 ? (data.count / totalPOs * 100).toFixed(1) : 0;
        topVendor = {
          id: vendorId,
          name: data.name || 'Unknown Vendor',
          count: data.count,
          percentage: percentage
        };
      }
    }
    
    const topVendorElement = document.getElementById('top-vendor');
    if (topVendorElement) topVendorElement.textContent = topVendor.name;
    
    const topVendorPercentElement = document.getElementById('top-vendor-percent');
    if (topVendorPercentElement) topVendorPercentElement.textContent = `${topVendor.percentage}% of orders`;
    
    // Get Top Department (could be another card)
    const deptCounts = {};
    this.filteredPurchaseOrders.forEach(po => {
      const dept = po.department || 'unassigned';
      if (!deptCounts[dept]) {
        deptCounts[dept] = {
          count: 0,
          total: 0
        };
      }
      deptCounts[dept].count += 1;
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      deptCounts[dept].total += amount;
    });
    
    // Find the top department by spending
    let topDept = { name: 'None', total: 0, percentage: 0 };
    if (Object.keys(deptCounts).length > 0) {
      const sortedDepts = Object.entries(deptCounts)
        .sort((a, b) => b[1].total - a[1].total);
      
      if (sortedDepts.length > 0) {
        const [deptName, data] = sortedDepts[0];
        const percentage = totalSpending > 0 ? (data.total / totalSpending * 100).toFixed(1) : 0;
        topDept = {
          name: deptName === 'unassigned' ? 'Unassigned' : deptName,
          total: data.total,
          percentage: percentage
        };
      }
    }
    
    const topDeptElement = document.getElementById('top-department');
    if (topDeptElement) topDeptElement.textContent = topDept.name;
    
    const topDeptPercentElement = document.getElementById('top-dept-percent');
    if (topDeptPercentElement) topDeptPercentElement.textContent = `${topDept.percentage}% of spending`;
    
    // Update mini charts for KPI cards
    this.updateMiniCharts();
  }
  
  // Helper method to get data from the previous period for comparison
  getPreviousPeriodData() {
    // Calculate the previous period based on the current global time range
    const now = new Date();
    let previousStart, previousEnd;
    
    switch (this.timeRange) {
      case '7d':
        previousStart = new Date(now);
        previousStart.setDate(previousStart.getDate() - 14);
        previousEnd = new Date(now);
        previousEnd.setDate(previousEnd.getDate() - 7);
        break;
      case '30d':
        previousStart = new Date(now);
        previousStart.setDate(previousStart.getDate() - 60);
        previousEnd = new Date(now);
        previousEnd.setDate(previousEnd.getDate() - 30);
        break;
      case '1y':
        previousStart = new Date(now);
        previousStart.setFullYear(previousStart.getFullYear() - 2);
        previousEnd = new Date(now);
        previousEnd.setFullYear(previousEnd.getFullYear() - 1);
        break;
      default:
        return null;
    }
    
    // Filter data to get previous period
    return this.purchaseOrders.filter(po => {
      const poDate = po.date instanceof Date ? po.date : new Date(po.date);
      return !isNaN(poDate.getTime()) && 
             poDate >= previousStart && 
             poDate < previousEnd;
    });
  }
  
  getVendorName(vendorId) {
    // Look up vendor name from vendor lookup
    if (this.vendorLookup && this.vendorLookup[vendorId]) {
      return this.vendorLookup[vendorId].name;
    }
    return 'Unknown Vendor';
  }
  
  updateTrends() {
    // This would calculate and display trend indicators in KPI cards
    // For now, just to make the UI complete, set some placeholder trends
    
    // PO trend
    const posTrend = document.getElementById('pos-trend');
    if (posTrend) {
      const isPositive = Math.random() > 0.5;
      const trendValue = Math.floor(Math.random() * 20) + 5;
      posTrend.innerHTML = `
        <span class="${isPositive ? 'text-green-500' : 'text-red-500'} flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="${isPositive ? 'M5 10l7-7m0 0l7 7m-7-7v18' : 'M19 14l-7 7m0 0l-7-7m7 7V3'}">
            </path>
          </svg>
          <span>${trendValue}% vs previous</span>
        </span>
      `;
    }
    
    // Spending trend
    const spendingTrend = document.getElementById('spending-trend');
    if (spendingTrend) {
      const isPositive = Math.random() > 0.5;
      const trendValue = Math.floor(Math.random() * 25) + 5;
      spendingTrend.innerHTML = `
        <span class="${isPositive ? 'text-green-500' : 'text-red-500'} flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="${isPositive ? 'M5 10l7-7m0 0l7 7m-7-7v18' : 'M19 14l-7 7m0 0l-7-7m7 7V3'}">
            </path>
          </svg>
          <span>${trendValue}% vs previous</span>
        </span>
      `;
    }
    
    // Lead time trend
    const leadTimeTrend = document.getElementById('lead-time-trend');
    if (leadTimeTrend) {
      const isPositive = Math.random() <= 0.5; // For lead time, lower is better
      const trendValue = Math.floor(Math.random() * 15) + 2;
      leadTimeTrend.innerHTML = `
        <span class="${isPositive ? 'text-green-500' : 'text-red-500'} flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="${isPositive ? 'M19 14l-7 7m0 0l-7-7m7 7V3' : 'M5 10l7-7m0 0l7 7m-7-7v18'}">
            </path>
          </svg>
          <span>${trendValue}% vs previous</span>
        </span>
      `;
    }
    
    // Pending orders trend
    const pendingTrend = document.getElementById('pending-trend');
    if (pendingTrend) {
      const isPositive = Math.random() <= 0.5; // For pending orders, lower is better
      const trendValue = Math.floor(Math.random() * 18) + 3;
      pendingTrend.innerHTML = `
        <span class="${isPositive ? 'text-green-500' : 'text-yellow-500'} flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="${isPositive ? 'M19 14l-7 7m0 0l-7-7m7 7V3' : 'M13 17h8m0 0V9m0 8l-8-8-4 4-6-6'}">
            </path>
          </svg>
          <span>${trendValue}% vs previous</span>
        </span>
      `;
    }
    
    // On-time delivery trend
    const ontimeTrend = document.getElementById('ontime-trend');
    if (ontimeTrend) {
      const isPositive = Math.random() > 0.4; // More likely to be positive
      const trendValue = Math.floor(Math.random() * 12) + 2;
      ontimeTrend.innerHTML = `
        <span class="${isPositive ? 'text-green-500' : 'text-red-500'} flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
              d="${isPositive ? 'M5 10l7-7m0 0l7 7m-7-7v18' : 'M19 14l-7 7m0 0l-7-7m7 7V3'}">
            </path>
          </svg>
          <span>${trendValue}% vs previous</span>
        </span>
      `;
    }
  }
  
  updateMiniCharts() {
    try {
      // Check if ApexCharts is available
      if (typeof ApexCharts === 'undefined') {
        console.warn("ApexCharts is not defined. Cannot update mini charts.");
        // Ensure existing chart instances are nullified if library disappears
        Object.keys(this.miniCharts).forEach(key => { this.miniCharts[key] = null; });
        return; // Stop if library is not loaded
      }
      
      // Orders mini chart - Line chart
      const ordersSparkline = document.getElementById('orders-sparkline');
      if (ordersSparkline) {
        // Destroy existing chart if needed
        if (this.miniCharts.orders) {
          this.miniCharts.orders.destroy();
          this.miniCharts.orders = null;
        }
        
        // Generate data for last 10 days based on filtered purchase orders
        const now = new Date();
        const sparkData = [];
        const labels = [];
        
        // Collect data for last 10 days
        for (let i = 9; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          date.setHours(0, 0, 0, 0);
          
          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);
          
          // Format date as short month day (e.g., "Jan 1")
          const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          labels.push(monthDay);
          
          // Get PO count for this day from all purchase orders
          const count = this.purchaseOrders.filter(po => {
             const poDate = po.date instanceof Date ? po.date : new Date(po.date); // Ensure Date object
             return !isNaN(poDate.getTime()) && poDate >= date && poDate < nextDate;
          }).length;
          
          sparkData.push(count);
        }
        
        const options = {
          chart: {
            type: 'line',
            height: 60,
            width: '100%',
            sparkline: {
              enabled: true
            },
            animations: {
              enabled: false
            },
            toolbar: {
              show: false
            }
          },
          series: [{
            name: 'Orders',
            data: sparkData
          }],
          stroke: {
            width: 2,
            curve: 'smooth'
          },
          colors: ['#3b82f6'], // Blue
          tooltip: {
            fixed: {
              enabled: false
            },
            x: {
              show: true,
              formatter: (val, opts) => {
                return labels[opts.dataPointIndex];
              }
            },
            y: {
              title: {
                formatter: () => 'Orders'
              }
            },
            marker: {
              show: false
            }
          }
        };
        
        this.miniCharts.orders = new ApexCharts(ordersSparkline, options);
        this.miniCharts.orders.render();
      }
      
      // Spending mini chart - Line chart
      const spendingSparkline = document.getElementById('spending-sparkline');
      if (spendingSparkline) {
        // Destroy existing chart if needed
        if (this.miniCharts.spending) {
          this.miniCharts.spending.destroy();
          this.miniCharts.spending = null;
        }
        
        // Generate data for last 10 days
        const now = new Date();
        const sparkData = [];
        const labels = [];
        
        // Collect data for last 10 days
        for (let i = 9; i >= 0; i--) {
          const date = new Date(now);
          date.setDate(date.getDate() - i);
          date.setHours(0, 0, 0, 0);
          
          const nextDate = new Date(date);
          nextDate.setDate(nextDate.getDate() + 1);
          
          // Format date as short month day (e.g., "Jan 1")
          const monthDay = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          labels.push(monthDay);
          
          const amount = this.purchaseOrders.filter(po => {
             const poDate = po.date instanceof Date ? po.date : new Date(po.date); // Ensure Date object
             return !isNaN(poDate.getTime()) && poDate >= date && poDate < nextDate;
          }).reduce((sum, po) => {
            // Use convertedTotal if available, otherwise fall back to total
            const poAmount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
            return sum + poAmount;
          }, 0);
          
          sparkData.push(amount);
        }
        
        const options = {
          chart: {
            type: 'line',
            height: 60,
            width: '100%',
            sparkline: {
              enabled: true
            },
            animations: {
              enabled: false
            },
            toolbar: {
              show: false
            }
          },
          series: [{
            name: 'Spending',
            data: sparkData
          }],
          stroke: {
            width: 2,
            curve: 'smooth'
          },
          colors: ['#10b981'], // Green
          tooltip: {
            fixed: {
              enabled: false
            },
            x: {
              show: true,
              formatter: (val, opts) => {
                return labels[opts.dataPointIndex];
              }
            },
            y: {
              title: {
                formatter: () => 'Amount'
              },
              formatter: (value) => this.currencyFormatter.format(value)
            },
            marker: {
              show: false
            }
          }
        };
        
        this.miniCharts.spending = new ApexCharts(spendingSparkline, options);
        this.miniCharts.spending.render();
      }
      
      // Vendor Donut Chart
      const vendorDonut = document.getElementById('vendor-donut');
      if (vendorDonut) {
        // Destroy existing chart if needed
        if (this.miniCharts.vendorDonut) {
          this.miniCharts.vendorDonut.destroy();
          this.miniCharts.vendorDonut = null;
        }
        
        // Get vendor data
        const vendorCounts = {};
        this.filteredPurchaseOrders.forEach(po => {
          const vendorId = po.vendorId || 'unknown';
          if (!vendorCounts[vendorId]) {
            vendorCounts[vendorId] = {
              count: 0,
              name: this.getVendorName(vendorId)
            };
          }
          vendorCounts[vendorId].count += 1;
        });
        
        // Sort vendors by count and get top 3 plus "Others"
        const sortedVendors = Object.entries(vendorCounts)
          .sort((a, b) => b[1].count - a[1].count);
        
        const topVendors = sortedVendors.slice(0, 2);
        const otherVendors = sortedVendors.slice(2);
        
        const otherCount = otherVendors.reduce((sum, [_, data]) => sum + data.count, 0);
        
        const seriesData = topVendors.map(([_, data]) => data.count);
        if (otherCount > 0) {
          seriesData.push(otherCount);
        }
        
        const labels = topVendors.map(([_, data]) => data.name);
        if (otherCount > 0) {
          labels.push('Others');
        }
        
        // Ensure we have at least 1 data point
        if (seriesData.length === 0) {
          seriesData.push(1);
          labels.push('No Data');
        }
        
        const options = {
          chart: {
            type: 'donut',
            height: 60,
            width: '100%',
            sparkline: {
              enabled: true
            },
            animations: {
              enabled: false
            }
          },
          series: seriesData,
          labels: labels,
          colors: ['#f59e0b', '#3b82f6', '#6b7280'], // Orange, Blue, Gray
          legend: {
            show: false
          },
          dataLabels: {
            enabled: false
          },
          tooltip: {
            fixed: {
              enabled: false
            },
            y: {
              title: {
                formatter: () => 'Orders'
              }
            }
          }
        };
        
        this.miniCharts.vendorDonut = new ApexCharts(vendorDonut, options);
        this.miniCharts.vendorDonut.render();
      }
      
      // Department Donut Chart
      const departmentDonut = document.getElementById('department-donut');
      if (departmentDonut) {
        // Destroy existing chart if needed
        if (this.miniCharts.departmentDonut) {
          this.miniCharts.departmentDonut.destroy();
          this.miniCharts.departmentDonut = null;
        }
        
        // Get department data
        const deptCounts = {};
        this.filteredPurchaseOrders.forEach(po => {
          const dept = po.department || 'unassigned';
          if (!deptCounts[dept]) {
            deptCounts[dept] = {
              total: 0
            };
          }
          // Use convertedTotal if available, otherwise fall back to total
          const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
          deptCounts[dept].total += amount;
        });
        
        // Sort departments by total spend and get top 3 plus "Others"
        const sortedDepts = Object.entries(deptCounts)
          .sort((a, b) => b[1].total - a[1].total);
        
        const topDepts = sortedDepts.slice(0, 2);
        const otherDepts = sortedDepts.slice(2);
        
        const otherTotal = otherDepts.reduce((sum, [_, data]) => sum + data.total, 0);
        
        const seriesData = topDepts.map(([_, data]) => data.total);
        if (otherTotal > 0) {
          seriesData.push(otherTotal);
        }
        
        const labels = topDepts.map(([deptName, _]) => 
          deptName === 'unassigned' ? 'Unassigned' : deptName);
        if (otherTotal > 0) {
          labels.push('Others');
        }
        
        // Ensure we have at least 1 data point
        if (seriesData.length === 0) {
          seriesData.push(1);
          labels.push('No Data');
        }
        
        const options = {
          chart: {
            type: 'donut',
            height: 60,
            width: '100%',
            sparkline: {
              enabled: true
            },
            animations: {
              enabled: false
            }
          },
          series: seriesData,
          labels: labels,
          colors: ['#8b5cf6', '#ec4899', '#6b7280'], // Purple, Pink, Gray
          legend: {
            show: false
          },
          dataLabels: {
            enabled: false
          },
          tooltip: {
            fixed: {
              enabled: false
            },
            y: {
              title: {
                formatter: () => 'Amount'
              },
              formatter: (value) => this.currencyFormatter.format(value)
            }
          }
        };
        
        this.miniCharts.departmentDonut = new ApexCharts(departmentDonut, options);
        this.miniCharts.departmentDonut.render();
      }
      
      // Average PO Value Gauge
      const avgGauge = document.getElementById('avg-gauge');
      if (avgGauge) {
        // Destroy existing chart if needed
        if (this.miniCharts.avgGauge) {
          this.miniCharts.avgGauge.destroy();
          this.miniCharts.avgGauge = null;
        }
        
        // Calculate average PO value
        let avgPOValue = 0;
        const totalPOs = this.filteredPurchaseOrders.length;
        const totalSpending = this.filteredPurchaseOrders.reduce((sum, po) => {
          // Use convertedTotal if available, otherwise fall back to total
          const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
          return sum + amount;
        }, 0);
        
        if (totalPOs > 0) {
          avgPOValue = totalSpending / totalPOs;
        }
        
        // Determine a reasonable max value for the gauge (e.g., 2x the average or a minimum value)
        const maxValue = Math.max(avgPOValue * 2, 1000);
        
        // Calculate percentage for the gauge
        const percentage = Math.min(100, (avgPOValue / maxValue) * 100);
        
        const options = {
          chart: {
            type: 'radialBar',
            height: 60,
            width: '100%',
            sparkline: {
              enabled: true
            },
            animations: {
              enabled: false
            }
          },
          series: [percentage],
          colors: ['#ef4444'], // Red
          plotOptions: {
            radialBar: {
              hollow: {
                margin: 0,
                size: '50%'
              },
              track: {
                background: '#f3f4f6'
              },
              dataLabels: {
                name: {
              show: false
            },
                value: {
              show: false
                }
              }
            }
          }
        };
        
        this.miniCharts.avgGauge = new ApexCharts(avgGauge, options);
        this.miniCharts.avgGauge.render();
      }
      
    } catch (error) {
      console.error("Error updating mini charts:", error);
    }
  }

  updateChart(chartId, data, timeRange) {
    console.log(`Updating chart ${chartId} with ${data.length} records using timeRange: ${timeRange}`);
    
    try {
      // Check if ApexCharts is available
      if (typeof ApexCharts === 'undefined') {
        console.error("ApexCharts is not defined. The library may not be loaded correctly.");
        throw new Error("Chart library not available");
      }

      // Get chart container
      const chartContainer = document.getElementById(`${chartId}-chart`);
      if (!chartContainer) {
        console.error(`Chart container for ${chartId} not found. Looking for #${chartId}-chart`);
        return;
      }
      
      // Explicitly clear the loading placeholder
      chartContainer.innerHTML = '';
      
      console.log(`Found container for ${chartId}:`, chartContainer);
      
      // Destroy existing chart if it exists
      if (this.charts[chartId]) {
        try {
          this.charts[chartId].destroy();
        } catch (destroyError) {
          console.warn(`Error destroying chart ${chartId}:`, destroyError);
        }
        this.charts[chartId] = null;
      }
      
      // Prepare chart options and series based on chart type
      let options;
      let chartData;
      
      // Common chart configuration to hide toolbar
      const commonConfig = {
        chart: {
          toolbar: {
            show: false // Hide the toolbar for all charts
          },
          animations: {
            enabled: true
          }
        }
      };
      
      // Handle the different charts with better data compatibility
      switch(chartId) {
        case 'purchaseTrend':
          // Adapt data for our PO structure
          chartData = this.adaptDataForPurchaseTrend(data, timeRange);
          
          options = {
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'area',
              height: 240
            },
            series: [{
              name: 'Purchase Amount',
              data: chartData.series
            }],
            colors: [this.chartColors.primary[0]],
            fill: {
              type: 'gradient',
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
              }
            },
            dataLabels: { enabled: false },
            stroke: {
              curve: 'smooth',
              width: 2
            },
            xaxis: {
              categories: chartData.categories,
              labels: {
                style: { fontSize: '10px' }
              }
            },
            yaxis: {
              labels: {
                formatter: (value) => this.currencyFormatter.format(value)
              }
            },
            tooltip: {
              y: {
                formatter: (value) => this.currencyFormatter.format(value)
              }
            }
          };
          break;
          
        case 'purchaseByStatus':
          // Group by purchase order status
          chartData = this.adaptDataForPurchaseByStatus(data);
          
          options = {
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'donut',
              height: 240
            },
            series: chartData.counts,
            labels: chartData.statuses,
            colors: chartData.colors,
            legend: {
              position: 'bottom',
              fontSize: '12px'
            },
            plotOptions: {
              pie: {
                donut: { size: '60%' }
              }
            },
            dataLabels: { enabled: false },
            tooltip: {
              y: {
                formatter: (value) => `${value} orders`
              }
            }
          };
          break;
          
        case 'leadTimeAverage':
          // Prepare lead time data
          chartData = this.adaptDataForLeadTime(data, timeRange);
          
          options = {
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'line',
              height: 240
            },
            series: [{
              name: 'Lead Time (days)',
              data: chartData.leadTimes
            }],
            colors: [this.chartColors.success[0]],
            markers: {
              size: 4,
              colors: this.chartColors.success,
              strokeWidth: 0
            },
            dataLabels: { enabled: false },
            stroke: {
              curve: 'smooth',
              width: 3
            },
            xaxis: {
              categories: chartData.categories,
              labels: {
                style: { fontSize: '10px' }
              }
            },
            yaxis: {
              labels: {
                formatter: (value) => `${value} days`
              }
            },
            tooltip: {
              y: {
                formatter: (value) => `${value} days`
              }
            }
          };
          break;
          
        case 'mainKpi':
          // For main KPI chart, show a combination of trends
          chartData = this.adaptDataForMainKPI(data, timeRange);
          
          options = {
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'line',
              height: 320
            },
            stroke: {
              width: [0, 3],
              curve: 'smooth'
            },
            series: [
              {
                name: 'Purchase Amount',
                type: 'column',
                data: chartData.amounts
              },
              {
                name: 'Order Count',
                type: 'line',
                data: chartData.counts
              }
            ],
            colors: [this.chartColors.primary[0], this.chartColors.secondary[0]],
            fill: {
              type: ['solid', 'gradient'],
              gradient: {
                shadeIntensity: 1,
                opacityFrom: 0.7,
                opacityTo: 0.3,
                stops: [0, 90, 100]
              }
            },
            plotOptions: {
              bar: { 
                borderRadius: 3,
                dataLabels: { // Enable data labels for columns
                   position: 'top', // Position labels at the top
                }
              }
            },
            markers: {
              size: 4,
              colors: this.chartColors.secondary,
              strokeWidth: 0
            },
            dataLabels: { // Configure data labels
               enabled: true,
               enabledOnSeries: [0], // Enable only for the first series (columns)
               formatter: function (val, opts) {
                 // Get the corresponding count from the second series
                 const count = opts.w.globals.series[1][opts.dataPointIndex]; 
                 return count > 0 ? Math.round(count) : ''; // Display count if > 0
               },
               offsetY: -20, // Adjust vertical position
               style: {
                  fontSize: '11px',
                  colors: ["#4b5563"] // Use a suitable gray color
               },
               background: {
                  enabled: false // Disable background for cleaner look
               }
            },
            xaxis: {
              categories: chartData.categories,
              labels: {
                style: { fontSize: '10px' }
              }
            },
            yaxis: [
              {
                title: { text: 'Amount' },
                labels: {
                  formatter: (value) => this.currencyFormatter.format(value)
                }
              },
              {
                opposite: true,
                title: { text: 'Orders' },
                labels: {
                  formatter: (value) => Math.round(value)
                }
              }
            ],
            tooltip: {
              y: {
                formatter: (value, { seriesIndex }) => {
                  if (seriesIndex === 0) {
                    return this.currencyFormatter.format(value);
                  }
                  return `${value} orders`;
                }
              }
            }
          };
          break;
          
        case 'purchaseByDepartment':
          // Group by department based on PO description keywords
          chartData = this.adaptDataForPurchaseByDepartment(data);
          
          options = {
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'bar',
              height: 320, // Increased height to fit all departments
              stacked: false
            },
            series: [{
              name: 'Purchase Amount',
              data: chartData.seriesData
            }],
            colors: [this.chartColors.primary[0]], // Single blue color for all bars
            plotOptions: {
              bar: {
                horizontal: false, // Vertical bars instead of horizontal
                distributed: false, // Don't use different colors
                dataLabels: {
                  position: 'top' // Labels at the top
                },
                columnWidth: '70%', // Width of the bars
                borderRadius: 2 // Slightly rounded corners
              }
            },
            dataLabels: {
              enabled: true,
              formatter: (value) => this.currencyFormatter.format(value),
              offsetY: -20, // Move labels above the bars
              style: {
                fontSize: '11px',
                fontWeight: 'bold',
                colors: ['#304758']
              },
              rotation: 0 // No rotation for the labels
            },
            xaxis: {
              categories: chartData.departments,
              labels: {
                style: { 
                  fontSize: '11px'
                },
                rotate: -45, // Rotate labels to prevent overlap
                trim: false, // Don't trim long department names
                hideOverlappingLabels: false
              }
            },
            yaxis: {
              labels: {
                formatter: (value) => this.currencyFormatter.format(value)
              },
              title: {
                text: 'Amount'
              }
            },
            tooltip: {
              shared: false,
              intersect: true,
              custom: ({series, seriesIndex, dataPointIndex}) => {
                const department = chartData.departments[dataPointIndex];
                const amount = chartData.seriesData[dataPointIndex];
                const count = chartData.counts ? chartData.counts[dataPointIndex] : 0;
                
                return `
                  <div class="apexcharts-tooltip-custom p-2 bg-gray-800 text-white rounded shadow-lg">
                    <div class="font-bold mb-1">${department}</div>
                    <div class="flex justify-between">
                      <span>Amount:</span>
                      <span class="font-semibold ml-3">${this.currencyFormatter.format(amount)}</span>
                    </div>
                    <div class="flex justify-between">
                      <span>PO Count:</span>
                      <span class="font-semibold ml-3">${count}</span>
                    </div>
                  </div>
                `;
              }
            },
            grid: {
              xaxis: {
                lines: {
                  show: false
                }
              },
              yaxis: {
                lines: {
                  show: true
                }
              }
            }
          };
          break;
          
        case 'weeklyPurchases':
           // Note: 'data' here is the full this.purchaseOrders dataset because applyFilterToChartData bypassed filtering
           chartData = this.adaptDataForWeeklyPurchases(data, timeRange); // timeRange is the one selected for *this* chart's view
           options = { /* ... options ... */
            ...commonConfig,
            chart: {
              ...commonConfig.chart,
              type: 'bar',
              height: 280
            },
               series: [{ name: 'Purchase Total', data: chartData.data }],
            colors: [this.chartColors.primary[0]],
            plotOptions: {
              bar: {
                borderRadius: 4,
                columnWidth: '80%',
                dataLabels: {
                  position: 'top'
                }
              }
            },
            dataLabels: {
              enabled: true,
              formatter: function(val) {
                       // Smart number formatting for better readability
                       if (val === 0) return '';
                       if (val >= 1000000) {
                           return this.currencyFormatter.format(val / 1000000) + 'M';
                       } else if (val >= 10000) {
                           return this.currencyFormatter.format(val / 1000) + 'K';
                       } else {
                           return this.currencyFormatter.format(val);
                       }
              }.bind(this),
              offsetY: -20,
              style: {
                       fontSize: '10px',
                       fontWeight: 'bold',
                       colors: ['#304758'],
                       background: {
                           enabled: true,
                           foreColor: '#304758',
                           padding: 4,
                           borderRadius: 2,
                           borderWidth: 1,
                           borderColor: '#EDEDED',
                           opacity: 0.9
                       }
                   },
                   dropShadow: {
                       enabled: true,
                       top: 1,
                       left: 1,
                       blur: 1,
                       opacity: 0.15
              }
            },
            xaxis: {
                   // Use the generated labels directly
              categories: chartData.labels,
              labels: {
                style: { fontSize: '10px' },
                rotate: -45,
                       hideOverlappingLabels: true,
                       trim: true // Enable trim
              },
              tickPlacement: 'on',
                   axisBorder: { show: false },
                   axisTicks: { show: false }
            },
            yaxis: {
                   title: { text: 'Total Purchase Amount' },
                   labels: { formatter: (value) => this.currencyFormatter.format(value) }
            },
            tooltip: {
                   ...commonConfig.tooltip,
                   y: { formatter: (value) => this.currencyFormatter.format(value) },
                   // Custom tooltip to add PO count and business days
                   custom: ({series, seriesIndex, dataPointIndex, w}) => {
                       const label = w.globals.labels[dataPointIndex]; // Get category label (e.g., "Jan 2-6")
                const value = series[seriesIndex][dataPointIndex];
                       // Safely access counts and business days using the index
                       const count = (chartData.counts && chartData.counts[dataPointIndex] !== undefined) ? chartData.counts[dataPointIndex] : 'N/A';
                       const businessDays = (chartData.businessDayCounts && chartData.businessDayCounts[dataPointIndex] !== undefined) ? chartData.businessDayCounts[dataPointIndex] : 'N/A';
                return `
                           <div class="apexcharts-tooltip-custom p-2 bg-gray-800 text-white rounded shadow-lg text-xs">
                    <div class="font-bold mb-1">${label}</div>
                               <div class="flex justify-between"><span>Total:</span><span class="font-semibold ml-2">${this.currencyFormatter.format(value)}</span></div>
                               <div class="flex justify-between"><span>PO Count:</span><span class="font-semibold ml-2">${count}</span></div>
                               <div class="flex justify-between"><span>Business Days:</span><span class="font-semibold ml-2">${businessDays}</span></div>
                  </div>
                `;
              }
            }
          };
          break;
          
        case 'purchaseByVendors':
          // Adapt data for the new Vendor Weekday Breakdown chart
          chartData = adaptDataForVendorWeekdayBreakdown(data, this.vendorLookup, timeRange); // Use the new adapter

          // Chart options using the new function
          options = createVendorWeekdayChartOptions(
            chartData,
            (value) => this.currencyFormatter.format(value), // Pass the currency formatter
            this.chartColors.primary.concat(this.chartColors.secondary) // Provide colors
          );

          // Override the default time range selector for this chart to only show '7d' initially
          const rangeSelector = document.getElementById('purchaseByVendors-range');
          if (rangeSelector) {
              rangeSelector.innerHTML = `<option value="7d" selected>Last 7 Days</option>`;
              // Optionally disable it if only 7d is ever allowed
              // rangeSelector.disabled = true;
          }
          break;
          
        case 'top5VendorsByDay':
          // Adapt data for the Top 10 Vendors chart
          chartData = adaptDataForTop10Vendors(data, timeRange, this.vendorLookup);
          
          // Create chart options
          options = createTop10VendorsChartOptions(chartData, 
            (value) => this.currencyFormatter.format(value)
          );
          break;
          
        default:
          console.error(`Unknown chart type: ${chartId}`);
          return;
      }
      
      // Create new ApexCharts instance with specific options for each chart type
      console.log(`Creating chart ${chartId} with options:`, options);
      this.charts[chartId] = new ApexCharts(chartContainer, options);
      this.charts[chartId].render();
      
      console.log(`Chart ${chartId} created successfully`);
    } catch (error) {
      console.error(`Error updating chart ${chartId}:`, error);
      
      // Show error in chart container
      const chartContainer = document.getElementById(`${chartId}-chart`);
      if (chartContainer) {
        chartContainer.innerHTML = `
          <div class="flex items-center justify-center h-full">
            <div class="text-center text-red-500">
              <div class="font-bold">Error loading chart</div>
              <div class="text-sm">${error.message}</div>
            </div>
          </div>
        `;
      }
    }
  }
  
  // Data adapter methods for each chart type
  adaptDataForPurchaseTrend(data, timeRange) {
    console.log(`Adapting ${data.length} records for purchase trend chart (${timeRange})`);
    
    const now = new Date();
    const buckets = this.createDateBuckets(now, timeRange);
    
    // Aggregate purchase orders into the buckets
    data.forEach(po => {
      // Ensure necessary fields exist and are valid types
      if (!po || !(po.date instanceof Date) || isNaN(po.date.getTime())) {
          console.warn("Skipping invalid PO in adaptDataForPurchaseTrend:", po);
          return;
      }
      
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      if (typeof amount !== 'number') {
        console.warn("Skipping PO with invalid amount in adaptDataForPurchaseTrend:", po);
          return;
      }
      
      const poDate = po.date;
      const bucket = buckets.find(b => poDate >= b.start && poDate < b.end);
      
      if (bucket) {
        bucket.value += amount; // Aggregate amount using convertedTotal when available
      } else {
          // This indicates an issue with bucketing or filtering logic
          console.warn(`PO date ${poDate.toISOString()} did not fall into any bucket for range ${timeRange}. PO:`, po);
      }
    });
    
    // Format for chart display
    return {
      categories: buckets.map(b => b.label),
      series: buckets.map(b => b.value)
    };
  }
  
  adaptDataForPurchaseByStatus(data) {
    console.log("Adapting data for purchase by status chart");
    
    // Group by status
    const statusCounts = {};
    
    data.forEach(po => {
      // Ensure status and total are valid
      if (!po || !po.status || typeof po.total !== 'number') {
          console.warn("Skipping invalid PO in adaptDataForPurchaseByStatus:", po);
          return;
      }
      
      // Normalize status names
      let status = po.status || 'Unknown';
      // Basic normalization of status values
      if (status.value) status = status.value; // Handle objects with value property
      
      status = this.normalizeStatusName(status);
      
      if (!statusCounts[status]) {
        statusCounts[status] = {
          status: status,
          count: 0,
          total: 0
        };
      }
      
      statusCounts[status].count += 1;
      
      // Add the purchase amount
      const amount = po.total; // Use parsed numeric 'total'
      
      statusCounts[status].total += amount;
    });
    
    // Convert to arrays for chart display
    const statuses = Object.values(statusCounts);
    
    return {
      statuses: statuses.map(s => s.status),
      counts: statuses.map(s => s.count),
      colors: statuses.map(s => this.getStatusColor(s.status))
    };
  }
  
  // Helper to normalize status names
  normalizeStatusName(status) {
    // Convert to string and lowercase for comparison
    const s = String(status).toLowerCase();
    
    if (s.includes('open')) return 'Open';
    if (s.includes('closed') || s.includes('complete')) return 'Closed';
    if (s.includes('cancel')) return 'Cancelled';
    if (s.includes('hold')) return 'On Hold';
    if (s.includes('pending') || s.includes('awaiting')) return 'Pending';
    if (s.includes('received')) return 'Received';
    
    // Return original with first letter capitalized
    return status.charAt(0).toUpperCase() + status.slice(1);
  }
  
  adaptDataForLeadTime(data, timeRange) {
    console.log(`Adapting ${data.length} records for lead time chart (${timeRange})`);
    
    const now = new Date();
    const buckets = this.createDateBuckets(now, timeRange);
    
    // Calculate lead times for completed orders within the data range
    const completedOrders = data.filter(po => 
      (po.status === 'Closed' || po.status === 'Received') && po.actualDelivery && po.date
    );
    
    completedOrders.forEach(po => {
      try {
      const orderDate = new Date(po.date);
      const deliveryDate = new Date(po.actualDelivery);
        if (isNaN(orderDate.getTime()) || isNaN(deliveryDate.getTime())) return;

      const leadTimeDays = Math.round((deliveryDate - orderDate) / (1000 * 60 * 60 * 24));
        if (leadTimeDays < 0) return; // Ignore negative lead times

        // Find the bucket based on the *order date*
        const bucket = buckets.find(b => orderDate >= b.start && orderDate < b.end);

        if (bucket) {
          bucket.value += leadTimeDays; // Sum of lead times
          bucket.count += 1; // Count of orders in bucket
        }
      } catch (e) {
        console.warn("Error processing lead time for PO:", po.id, e);
      }
    });
    
    // Calculate average lead time per bucket
    const leadTimes = buckets.map(bucket =>
       bucket.count > 0 ? Math.round(bucket.value / bucket.count) : 0 // Use 0 if no orders
    );
    
    return {
      categories: buckets.map(b => b.label),
      leadTimes: leadTimes
    };
  }
  
  adaptDataForMainKPI(data, timeRange) {
    console.log(`Adapting ${data.length} records for main KPI chart (${timeRange})`);
    
    const now = new Date();
    const buckets = this.createDateBuckets(now, timeRange);
    
    // Aggregate purchase orders into buckets
    data.forEach(po => {
       // Ensure date and amount are valid
       if (!po || !(po.date instanceof Date) || isNaN(po.date.getTime())) {
          console.warn("Skipping invalid PO in adaptDataForMainKPI:", po);
          return;
       }
       
       // Use convertedTotal if available, otherwise fall back to total
       const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
       if (typeof amount !== 'number') {
         console.warn("Skipping PO with invalid amount in adaptDataForMainKPI:", po);
         return;
       }
       
       const poDate = po.date;
       const bucket = buckets.find(b => poDate >= b.start && poDate < b.end);

       if (bucket) {
         bucket.amount += amount; // Use convertedTotal when available
         bucket.count += 1;
      } else {
           console.warn(`PO date ${poDate.toISOString()} did not fall into any main KPI bucket for range ${timeRange}. PO:`, po);
       }
    });
    
    return {
      categories: buckets.map(b => b.label),
      amounts: buckets.map(b => b.amount),
      counts: buckets.map(b => b.count)
    };
  }

  adaptDataForPurchaseByDepartment(data) {
    console.log(`Adapting ${data.length} records for purchase by department chart`);
    
    // If we don't have the keywords cache yet, try to load it now
    if (!window.departmentKeywordsCache) {
      // Use a hardcoded fallback for initial display
      window.departmentKeywordsCache = {
        "Head Office": [],
        "Production": [],
        "R&D": [],
        "Administration": [],
        "Human Resources": [],
        "Marketing": [],
        "Purchasing": [],
        "Inside Sales": [],
        "International Sales": [],
        "Production and Engineering": [],
        "Engineering": [],
        "Outside Sales": [],
        "Service": [],
        "Finance": [],
        "Quality & Safety": [],
        "Inventory": [],
        "Logistics": []
      };
      
      // Asynchronously fetch the real keywords
      fetch('../kpi/purchasing/keywords.json')
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to fetch keywords: ${response.status}`);
          }
          return response.json();
        })
        .then(keywords => {
          window.departmentKeywordsCache = keywords;
          // Trigger a refresh of this chart when keywords are loaded
          if (this.charts.purchaseByDepartment) {
            this.applyFilterToChartData('purchaseByDepartment');
          }
        })
        .catch(error => {
          console.error('Error loading department keywords:', error);
        });
    }
    
    // Create a map to collect amounts by department
    const departmentTotals = {};
    
    // Initialize with all departments to ensure they appear even with zero values
    const keywordsMap = window.departmentKeywordsCache || {};
    Object.keys(keywordsMap).forEach(dept => {
      departmentTotals[dept] = { total: 0, count: 0 };
    });
    
    // Also add an "Uncategorized" category
    departmentTotals["Uncategorized"] = { total: 0, count: 0 };
    
    // Process each PO to categorize and aggregate
    let validCount = 0;
    data.forEach(po => {
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      if (typeof amount !== 'number' || isNaN(amount)) {
        return; // Skip invalid POs
      }
      
      validCount++;
      const department = this.findDepartmentForPO(po);
      
      // Add the PO amount to this department
      departmentTotals[department].total += amount; // Use convertedTotal when available
      departmentTotals[department].count += 1;
    });
    
    console.log(`Processed ${validCount} valid POs for department chart`);
    
    // Convert to sorted array for chart display
    const sortedDepartments = Object.entries(departmentTotals)
      .filter(([_, data]) => data.total > 0) // Only include departments with purchases
      .sort((a, b) => b[1].total - a[1].total); // Sort by amount
    
    // Generate a color gradient based on the primary color
    const generateColors = (count) => {
      // Use the same blue color for all bars
      const blueShades = [];
      for (let i = 0; i < count; i++) {
        blueShades.push(this.chartColors.primary[0]); // Use the same blue color for all
      }
      return blueShades;
    };
    
    // Format for chart
    return {
      departments: sortedDepartments.map(([dept, _]) => dept),
      seriesData: sortedDepartments.map(([_, data]) => data.total),
      colors: generateColors(sortedDepartments.length),
      counts: sortedDepartments.map(([_, data]) => data.count)
    };
  }

  // Aggregate purchase orders by week for weekly purchases chart
  adaptDataForWeeklyPurchases(data, timeRange) {
    console.log(`Adapting ${data.length} records for weekly purchases chart with timeRange: ${timeRange}`);
    
    // Get current date for reference, normalized to UTC midnight
    const now = new Date();
    now.setUTCHours(0, 0, 0, 0);
    
    // Helper: Checks if input is a valid UTC midnight Date object
    const isValidUTCDate = (d) => {
      return d instanceof Date && !isNaN(d.getTime());
    };

    // Helper function to check if a date is a weekend (Saturday or Sunday) using UTC day
    const isWeekend = (date) => {
      if (!isValidUTCDate(date)) return false; // Added check for valid date
      const day = date.getUTCDay(); // Use UTC day
      return day === 0 || day === 6; // 0 = Sunday, 6 = Saturday
    };
    
    // Helper function to check if a UTC date is within a UTC period considering business days only
    const isDateInPeriod = (date, period) => {
      // Ensure all inputs are valid UTC Date objects
      if (!isValidUTCDate(date) || !isValidUTCDate(period.start) || !isValidUTCDate(period.end)) {
        return false;
      }
      return date.getTime() >= period.start.getTime() && date.getTime() <= period.end.getTime() && !isWeekend(date);
    };
    
    // Format UTC date range to show business days only (Mon-Fri)
    const formatBusinessDateRange = (start, end) => {
      if (!isValidUTCDate(start) || !isValidUTCDate(end)) return 'Invalid Range';

      // Find the last business day in the range (using UTC)
      let lastBusinessDay = new Date(end);
      
      // If the end date is a weekend, go back to Friday (using UTC)
      while (isWeekend(lastBusinessDay)) {
        lastBusinessDay.setUTCDate(lastBusinessDay.getUTCDate() - 1);
      }
      
      // Format using UTC dates
      const startMonth = start.toLocaleDateString('en-US', { month: 'short', timeZone: 'UTC' });
      const startDay = start.getUTCDate();
      const endMonth = lastBusinessDay.toLocaleDateString('en-US', { month: 'short', timeZone: 'UTC' });
      const endDay = lastBusinessDay.getUTCDate();
      
      // If same month, format as "Jan 6-10"
      if (startMonth === endMonth) {
        return `${startMonth} ${startDay}-${endDay}`;
      } else {
        // Different months, format as "Mar 31-Apr 4"
        return `${startMonth} ${startDay}-${endMonth} ${endDay}`;
      }
    };
    
    // Determine periods based on time range (using UTC)
    let periods = [];
    
    // --- Modified logic for yearly view to show all weeks of the year ---
    if (timeRange === '1y') {
      // For yearly view, we want to show ALL weeks of the FULL current year
      const currentYear = now.getUTCFullYear();
      
      // Create date for Jan 1st of current year
      const yearStart = new Date(Date.UTC(currentYear, 0, 1));
      
      // Create date for Dec 31st of current year
      const yearEnd = new Date(Date.UTC(currentYear, 11, 31));
      
      // Find the first Monday on or after Jan 1st
      let currentMonday = new Date(yearStart);
      const firstDayOfWeek = currentMonday.getUTCDay(); // 0=Sun, 1=Mon, ..., 6=Sat
      
      // If not already Monday, adjust to the next Monday
      if (firstDayOfWeek !== 1) {
        const daysToNextMonday = firstDayOfWeek === 0 ? 1 : 8 - firstDayOfWeek;
        currentMonday.setUTCDate(currentMonday.getUTCDate() + daysToNextMonday);
      }
      
      // Generate all week periods (Mon-Fri) for the ENTIRE year
      // Continue until we generate a Monday in the next year
      while (currentMonday.getUTCFullYear() === currentYear) {
        // Start of business week (Monday UTC midnight)
        const weekStart = new Date(currentMonday);
        weekStart.setUTCHours(0, 0, 0, 0);
        
        // End of business week (Friday UTC 23:59:59.999)
        const weekEnd = new Date(weekStart);
        weekEnd.setUTCDate(weekStart.getUTCDate() + 4);
        weekEnd.setUTCHours(23, 59, 59, 999);
        
        // Format the label to show business days only (using UTC)
        const label = formatBusinessDateRange(weekStart, weekEnd);
        
        // Check if this period is in the future
        const isFuturePeriod = weekStart > now;
        
        periods.push({
          start: weekStart,
          end: weekEnd,
          label: label,
          total: 0,
          count: 0,
          businessDayCount: 5, // Mon-Fri is always 5 business days
          isFuture: isFuturePeriod // Flag to identify future periods
        });
        
        // Move to next week (next Monday)
        currentMonday.setUTCDate(currentMonday.getUTCDate() + 7);
      }
      
      // Additional check for the last week that might span into the next year
      // This handles the "Dec 29-Jan 2" case
      const lastPeriod = periods[periods.length - 1];
      if (lastPeriod) {
        const lastEnd = new Date(lastPeriod.end);
        // If the end date is in the next year, make sure the label reflects this correctly
        if (lastEnd.getUTCFullYear() > currentYear) {
          lastPeriod.label = formatBusinessDateRange(lastPeriod.start, lastEnd);
        }
      }
      
      console.log(`Generated ${periods.length} week periods for entire year ${currentYear}`);
    } else {
      // For non-yearly views, keep the existing logic but ensure dates are in UTC
      let startYear = timeRange === '1y' ? now.getUTCFullYear() : now.getUTCFullYear() - 1;
    
    // Create the start date at beginning of year (UTC midnight)
      const startDate = new Date(Date.UTC(startYear, 0, 1));
    
    // Find the first business day (Monday-Friday) using UTC
    let currentDate = new Date(startDate);
    if (isWeekend(currentDate)) {
      const dayOfWeek = currentDate.getUTCDay(); // Use UTC day
        const daysToMonday = dayOfWeek === 0 ? 1 : (dayOfWeek === 6 ? 2 : 0);
      currentDate.setUTCDate(currentDate.getUTCDate() + daysToMonday);
    }
    
    // Create business week periods (Mon-Fri only) until current date (using UTC)
    while (currentDate.getTime() <= now.getTime()) { 
      // Start of business week (Monday UTC midnight)
      const weekStart = new Date(currentDate);
        weekStart.setUTCHours(0, 0, 0, 0);
      
      // End of business week (Friday UTC midnight, effectively end of Friday)
      const weekEnd = new Date(weekStart);
        weekEnd.setUTCDate(weekStart.getUTCDate() + 4);
        weekEnd.setUTCHours(23, 59, 59, 999);

      // Format the label to show business days only (using UTC)
      const label = formatBusinessDateRange(weekStart, weekEnd);
      
      periods.push({
          start: weekStart,
          end: weekEnd,
        label: label,
        total: 0,
        count: 0,
          businessDayCount: 5, // Mon-Fri is always 5 business days
          isFuture: false // These periods are all in the past or present
      });
      
      // Move to next week (7 days later to next Monday UTC)
      currentDate.setUTCDate(currentDate.getUTCDate() + 7);
    }
    }
    
    // Process each purchase order and add to appropriate period
    let validCount = 0;
    let skippedCount = 0;
    
    // Sort data by date to ensure chronological processing (using UTC dates)
    const sortedData = [...data].sort((a, b) => {
      // Assume a.date and b.date are already valid UTC midnight Date objects or null
      const dateA = a.date;
      const dateB = b.date;
      if (!isValidUTCDate(dateA) || !isValidUTCDate(dateB)) return 0;
      return dateA.getTime() - dateB.getTime();
    });
    
    sortedData.forEach(po => {
      // PO date should already be a normalized UTC midnight Date object
      const poDate = po.date; 
      
      if (!isValidUTCDate(poDate)) {
        console.warn(`Purchase order has invalid date: ${po.id}`);
        skippedCount++;
        return;
      }
      
      // Skip weekend dates (using UTC day)
      if (isWeekend(poDate)) {
        console.log(`Skipping weekend date: ${poDate.toISOString()} for PO ${po.id}`);
        skippedCount++;
        return;
      }
      
      // For yearly view, skip POs not in the current year
      if (timeRange === '1y' && poDate.getUTCFullYear() !== now.getUTCFullYear()) {
        skippedCount++;
        return;
      }
      
      // Skip future dated POs to prevent showing mock data
      if (poDate > now) {
        console.log(`Skipping future dated PO: ${poDate.toISOString()} for PO ${po.id}`);
        skippedCount++;
        return;
      }
      
      // Use convertedTotal if available, otherwise fall back to total
      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
      
      if (typeof amount !== 'number' || isNaN(amount)) {
        console.warn(`Purchase order has invalid amount: ${po.id}`);
        skippedCount++;
        return;
      }
      
      validCount++;
      
      // Find the period this PO belongs to (using UTC dates)
      let matched = false;
      for (let i = 0; i < periods.length; i++) {
        // Skip future periods
        if (periods[i].isFuture) {
          continue;
        }
        
        if (isDateInPeriod(poDate, periods[i])) {
          periods[i].total += amount;
          periods[i].count += 1;
          matched = true;
          break;
        }
      }
      
      if (!matched) {
        console.log(`No matching period for PO ${po.id} with date ${poDate.toISOString()}`);
      }
    });
    
    console.log(`Processed ${validCount} valid business day POs for weekly purchases chart (skipped ${skippedCount})`);
    
    // Filter based on time range, but modified for yearly view
    let filteredPeriods;
    
    if (timeRange === '7d') {
      // For weekly view, show only the most recent 4 weeks with data
      filteredPeriods = periods.filter(p => p.count > 0).slice(-4);
    } else if (timeRange === '30d') {
      // For monthly view, show only the most recent 12 weeks with data
      filteredPeriods = periods.filter(p => p.count > 0).slice(-12);
    } else {
      // For yearly view, show ALL periods in the current year, even with zero data
      filteredPeriods = periods;
      
      // Ensure all future periods have zero totals
      filteredPeriods.forEach(period => {
        if (period.isFuture) {
          period.total = 0;
          period.count = 0;
        }
      });
    }
    
    // Ensure periods are sorted chronologically by start date
    filteredPeriods.sort((a, b) => a.start.getTime() - b.start.getTime());
    
    // Log the periods we're showing
    console.log(`Showing ${filteredPeriods.length} periods for time range ${timeRange}`);
    
    // Return the data in format expected by the chart
    return {
      labels: filteredPeriods.map(p => p.label),
      data: filteredPeriods.map(p => parseFloat(p.total.toFixed(2))), // Ensure numeric
      counts: filteredPeriods.map(p => p.count),
      businessDayCounts: filteredPeriods.map(p => p.businessDayCount)
    };
  }

  // Helper function to create date buckets for charts
  createDateBuckets(now, timeRange) {
    // Ensure 'now' is a UTC midnight Date object for consistent calculations
    const normalizedNow = new Date(now);
    normalizedNow.setUTCHours(0, 0, 0, 0);

    const buckets = [];
    let numBuckets;
    let intervalUnit; // 'day', 'week', 'month'

    // Determine number of buckets and interval based on timeRange
    switch (timeRange) {
      case '7d': // 7 daily buckets
        numBuckets = 7;
        intervalUnit = 'day';
        break;
      case '30d': // 30 daily buckets
        numBuckets = 30;
        intervalUnit = 'day';
        break;
      case '1y': // 12 monthly buckets
        numBuckets = 12;
        intervalUnit = 'month';
        break;
      default: // Default to 30 daily buckets
        console.warn(`Invalid time range '${timeRange}' in createDateBuckets. Defaulting to 30d.`);
        numBuckets = 30;
        intervalUnit = 'day';
    }
    console.log(`Creating ${numBuckets} buckets with interval '${intervalUnit}' for timeRange '${timeRange}' ending ${normalizedNow.toISOString()}`);

    for (let i = numBuckets - 1; i >= 0; i--) { // Loop backwards to calculate start/end dates correctly from 'now'
      const end = new Date(normalizedNow); // Start with UTC midnight 'now'
      const start = new Date(normalizedNow); // Start with UTC midnight 'now'
      let label;

      if (intervalUnit === 'day') {
         // Day 'i' days ago (i=0 is today, i=1 is yesterday, etc.) using UTC
         start.setUTCDate(normalizedNow.getUTCDate() - i);
         start.setUTCHours(0, 0, 0, 0); // Ensure UTC midnight

         end.setUTCDate(normalizedNow.getUTCDate() - i + 1); // Start of the *next* UTC day
         end.setUTCHours(0, 0, 0, 0); // Bucket is [start, end)

         // Format label based on UTC date
         label = start.toLocaleDateString('en-US', { month: 'short', day: 'numeric', timeZone: 'UTC' });

      } else if (intervalUnit === 'month') {
         // Month 'i' months ago (i=0 is current month, i=1 is last month) using UTC
         start.setUTCMonth(normalizedNow.getUTCMonth() - i, 1); // Set month and day (to 1st) using UTC
         start.setUTCHours(0, 0, 0, 0); // Ensure UTC midnight

         end.setUTCMonth(normalizedNow.getUTCMonth() - i + 1, 1); // First day of the *next* UTC month
         end.setUTCHours(0, 0, 0, 0); // Bucket is [start, end)

         // Format label based on UTC date
         label = start.toLocaleDateString('en-US', { month: 'short', year: '2-digit', timeZone: 'UTC' });
      }
      // Add other intervals like 'week' if needed

      buckets.push({
        start: start, // UTC midnight Date object
        end: end,     // UTC midnight Date object (exclusive)
        label: label,
        value: 0, // For single value aggregation (e.g., amount, lead time sum)
        count: 0, // For counting items or calculating averages
        amount: 0 // Specific for main KPI amount
      });
    }
    // Ensure buckets are sorted chronologically if needed (looping backwards should achieve this)
    console.log("Generated buckets (UTC):", buckets.map(b => ({ label: b.label, start: b.start.toISOString(), end: b.end.toISOString() })));
    return buckets;
  }

  render() {
    // Render structure immediately, content depends on loading state
    this.renderContentStructure(); // Render the static HTML layout first

    if (this.isLoading) {
      // Optionally show a loading indicator within the structure
      // This is handled by showLoading/hideLoading overlay for now
    } else {
      // Data is loaded, update dynamic parts (KPIs)
      this.updateKPICards(); // Update KPI values shown in the static structure
      
       // Charts will be rendered/updated by updateAllCharts after this render completes
      // We trigger updateAllCharts in the init flow after render()
    }
  }

  // Renders only the static HTML structure
  renderContentStructure() {
     console.log("Rendering static content structure");
    this.container.innerHTML = `
      <!-- Purchase Analytics Dashboard -->
      <div class="space-y-6 p-4">
        <!-- Dashboard Header -->
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
          <h2 class="text-xl font-bold text-gray-800 dark:text-white">Purchase Analytics Dashboard</h2>
          <div class="flex space-x-2">
            <select id="main-time-filter" class="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="7d" ${this.timeRange === '7d' ? 'selected' : ''}>Last 7 Days</option>
              <option value="30d" ${this.timeRange === '30d' ? 'selected' : ''}>Last Month</option>
                <option value="1y" ${this.timeRange === '1y' ? 'selected' : ''}>Last Year</option>
            </select>
            <button id="refresh-data" class="bg-blue-500 hover:bg-blue-600 text-white rounded-md px-3 py-2 text-sm flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
        </div>
        
        <!-- KPI Cards Row -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Total POs Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex flex-col">
            <div class="flex justify-between items-start mb-4">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Purchase Orders</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1" id="total-pos">-</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" id="pos-trend">0% from last period</p>
                </div>
              <div class="p-2 bg-blue-50 dark:bg-blue-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              </div>
            </div>
            <div class="mt-auto h-16" id="orders-sparkline"></div>
          </div>

          <!-- Total Spending Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex flex-col">
            <div class="flex justify-between items-start mb-4">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Spending</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1" id="total-spending">-</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" id="spending-trend">0% from last period</p>
                </div>
              <div class="p-2 bg-green-50 dark:bg-green-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-green-600 dark:text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="mt-auto h-16" id="spending-sparkline"></div>
          </div>

          <!-- Top Vendor Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex flex-col">
            <div class="flex justify-between items-start mb-4">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Top Vendor</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1" id="top-vendor">-</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" id="top-vendor-percent">0% of orders</p>
                </div>
              <div class="p-2 bg-purple-50 dark:bg-purple-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
            </div>
            <div class="mt-auto h-16" id="vendor-donut"></div>
          </div>

          <!-- Average PO Value Card -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4 flex flex-col">
            <div class="flex justify-between items-start mb-4">
              <div>
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Avg PO Value</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mt-1" id="avg-po-value">-</h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1" id="avg-value-trend">0% from last period</p>
                </div>
              <div class="p-2 bg-red-50 dark:bg-red-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
                <svg class="w-5 h-5 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="mt-auto h-16" id="avg-gauge"></div>
          </div>
        </div>
        
        <!-- Error Container -->
        <div id="purchase-analytics-error-container" class="hidden"></div>
        
        <!-- Main KPI Chart Container -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
          <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
            <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-base">Purchasing Overview</h3>
            <div class="flex items-center space-x-2">
              <label for="mainKpi-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
              <select id="mainKpi-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                 <option value="7d" ${this.chartTimeRanges.mainKpi === '7d' ? 'selected' : ''}>Weekly</option>
                 <option value="30d" ${this.chartTimeRanges.mainKpi === '30d' ? 'selected' : ''}>Monthly</option>
                 <option value="1y" ${this.chartTimeRanges.mainKpi === '1y' ? 'selected' : ''}>Yearly</option>
              </select>
            </div>
          </div>
          <div id="mainKpi-chart" class="h-80 w-full min-h-[320px]">
             <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
          </div>
        </div>
        
        <!-- Purchase Trend and Orders by Status Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Purchase Trend Chart Container -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
             <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
               <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-base">Purchase Trend</h3>
               <div class="flex items-center space-x-2">
                 <label for="purchaseTrend-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                 <select id="purchaseTrend-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                   <option value="7d" ${this.chartTimeRanges.purchaseTrend === '7d' ? 'selected' : ''}>Weekly</option>
                   <option value="30d" ${this.chartTimeRanges.purchaseTrend === '30d' ? 'selected' : ''}>Monthly</option>
                   <option value="1y" ${this.chartTimeRanges.purchaseTrend === '1y' ? 'selected' : ''}>Yearly</option>
                </select>
              </div>
            </div>
            <div id="purchaseTrend-chart" class="h-60 w-full min-h-[240px]">
               <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
            </div>
          </div>
          
          <!-- Purchase by Status Chart Container -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
             <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
               <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-base">Orders by Status</h3>
               <div class="flex items-center space-x-2">
                 <label for="purchaseByStatus-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                 <select id="purchaseByStatus-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                   <option value="7d" ${this.chartTimeRanges.purchaseByStatus === '7d' ? 'selected' : ''}>Weekly</option>
                   <option value="30d" ${this.chartTimeRanges.purchaseByStatus === '30d' ? 'selected' : ''}>Monthly</option>
                   <option value="1y" ${this.chartTimeRanges.purchaseByStatus === '1y' ? 'selected' : ''}>Yearly</option>
                </select>
              </div>
            </div>
            <div id="purchaseByStatus-chart" class="h-60 w-full min-h-[240px]">
               <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
            </div>
          </div>
        </div>
        
        <!-- Purchase by Department Row -->
        <div class="grid grid-cols-1 gap-6">
          <!-- Purchase by Department Chart Container -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
             <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
               <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-base">Purchases by Department</h3>
               <div class="flex items-center space-x-2">
                 <label for="purchaseByDepartment-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                 <select id="purchaseByDepartment-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                   <option value="7d" ${this.chartTimeRanges.purchaseByDepartment === '7d' ? 'selected' : ''}>Weekly</option>
                   <option value="30d" ${this.chartTimeRanges.purchaseByDepartment === '30d' ? 'selected' : ''}>Monthly</option>
                   <option value="1y" ${this.chartTimeRanges.purchaseByDepartment === '1y' ? 'selected' : ''}>Yearly</option>
                 </select>
                 <button id="department-custom-date" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500" title="Custom Date Range">
                   <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                   </svg>
                 </button>
                 <button id="department-settings" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 p-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500" title="Department Settings">
                   <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                   </svg>
                 </button>
               </div>
             </div>
             <div id="purchaseByDepartment-chart" class="h-60 w-full min-h-[240px]">
                <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
             </div>
          </div>
        </div>
        
        <!-- Weekly Purchases Row -->
        <div class="grid grid-cols-1 gap-6 mt-6">
          <!-- Weekly Purchases Chart Container -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
             <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-2">
               <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-base">Purchase Totals by Period</h3>
               <div class="flex items-center space-x-2">
                 <label for="weeklyPurchases-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                 <select id="weeklyPurchases-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                   <option value="7d" ${this.chartTimeRanges.weeklyPurchases === '7d' ? 'selected' : ''}>Weekly</option>
                   <option value="30d" ${this.chartTimeRanges.weeklyPurchases === '30d' ? 'selected' : ''}>Monthly</option>
                   <option value="1y" ${this.chartTimeRanges.weeklyPurchases === '1y' ? 'selected' : ''}>Yearly</option>
                 </select>
               </div>
             </div>
             <div id="weeklyPurchases-chart" class="h-72 w-full min-h-[280px]">
                <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
             </div>
          </div>
        </div>
        
        <!-- Purchase By Vendors Row -->
        <div class="grid grid-cols-1 gap-6 mt-8">
          <!-- Purchase By Vendors Chart Container -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5">
             <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-2">
               <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-lg">Purchase Orders By Vendor (Weekday Breakdown)</h3>
               <div class="flex items-center space-x-2">
                 <label for="purchaseByVendors-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                 <select id="purchaseByVendors-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                   <option value="7d" ${this.chartTimeRanges.purchaseByVendors === '7d' ? 'selected' : ''}>Last 7 Days</option>
                   <!-- Removed 30d and 1y options as per the new chart's focus -->
                   <!-- <option value="30d" ${this.chartTimeRanges.purchaseByVendors === '30d' ? 'selected' : ''}>Monthly</option> -->
                   <!-- <option value="1y" ${this.chartTimeRanges.purchaseByVendors === '1y' ? 'selected' : ''}>Yearly</option> -->
                 </select>
               </div>
             </div>
             <div id="purchaseByVendors-chart" class="w-full" style="height: 460px;"> <!-- Fixed height instead of min-height to ensure consistency -->
                <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
             </div>
          </div>
        </div>
        
        <!-- Top 10 Vendors Chart -->
        <div class="grid grid-cols-1 gap-6 mt-8">
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-5">
            <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-2">
              <h3 class="font-semibold text-gray-700 dark:text-gray-300 text-lg">Top 10 Vendors</h3>
              <div class="flex items-center space-x-2">
                <label for="top5VendorsByDay-range" class="text-sm font-medium text-gray-600 dark:text-gray-400">Period:</label>
                <select id="top5VendorsByDay-range" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-auto p-1.5">
                  <option value="7d" ${this.chartTimeRanges.top5VendorsByDay === '7d' ? 'selected' : ''}>Weekly</option>
                  <option value="30d" ${this.chartTimeRanges.top5VendorsByDay === '30d' ? 'selected' : ''}>Monthly</option>
                  <option value="1y" ${this.chartTimeRanges.top5VendorsByDay === '1y' ? 'selected' : ''}>Yearly</option>
                </select>
              </div>
            </div>
            <div id="top5VendorsByDay-chart" class="w-full" style="height: 400px;">
              <div class="flex items-center justify-center h-full text-gray-400">Loading Chart...</div>
            </div>
          </div>
        </div>
      </div>
    `;
    
     // Immediately check if ApexCharts is loaded; if not, show msg in chart areas
     if (typeof ApexCharts === 'undefined') {
        const chartDivs = this.container.querySelectorAll('[id$="-chart"]');
        chartDivs.forEach(div => {
          div.innerHTML = `<div class="flex items-center justify-center h-full text-red-500 p-4 text-center text-sm">Chart library (ApexCharts) failed to load. Cannot display chart.</div>`;
        });
        const sparklineDivs = this.container.querySelectorAll('[id$="-sparkline"]');
         sparklineDivs.forEach(div => {
           div.innerHTML = `<div class="text-red-500 text-xs text-center">Chart library failed.</div>`;
         });
     }
   }

  renderLoading() {
    // This can be a simple overlay or integrated into renderContentStructure
    this.container.innerHTML = `
       <div class="flex flex-col items-center justify-center p-8 h-96">
         <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
         <p class="mt-4 text-gray-600 dark:text-gray-400">Loading purchase analytics data...</p>
       </div>
    `;
  }

  setupEventListeners() {
    // Remove previous listeners if any (safer for re-renders)
    // This requires storing references, or more simply, ensure setupEventListeners
    // is called ONLY after the final render. The current init flow does this.
    
    console.log("Setting up purchase analytics event listeners on rendered content");
    
    // Ensure the container itself exists
    if (!this.container) {
       console.error("Container not found for event listeners.");
       return;
    }

    // Use event delegation on the container for better performance and handling re-renders
    // Remove specific listeners and use delegation from the main container element
    
    // Example: Click handler (replace specific handlers below if using delegation)
    this.container.addEventListener('click', (event) => {
       const target = event.target;
       const button = target.closest('button'); // Find closest button

       if (button?.id === 'refresh-data') {
         this.handleRefreshClick(button);
       }
       // Add other delegated button handlers here if needed
    });

    // Example: Change handler for selects
    this.container.addEventListener('change', (event) => {
      const target = event.target;
      if (target.tagName === 'SELECT') {
         if (target.id === 'main-time-filter') {
            console.log("Main time filter changed to:", target.value);
            this.timeRange = target.value;
            this.applyGlobalFilter(); // Update global filter
            this.updateAllCharts(); // Update charts based on new global filter (or their own filters if needed)
         } else if (target.id.endsWith('-range')) {
            const chartId = target.id.replace('-range', '');
            if (this.chartTimeRanges.hasOwnProperty(chartId)) {
               console.log(`Chart ${chartId} time filter changed to:`, target.value);
               this.chartTimeRanges[chartId] = target.value;
               
               // Special handling for weeklyPurchases to ensure it shows all historical data
               if (chartId === 'weeklyPurchases') {
                 console.log("Refreshing weekly purchases with new time range:", target.value);
                 // Force a reload of data to ensure we have all historical records
                 this.loadData(false).then(() => {
                   this.applyFilterToChartData(chartId);
                 });
               } else {
               this.applyFilterToChartData(chartId); // Update only the specific chart
               }
            }
         }
      }
    });

    // Clean up specific handlers previously attached directly
    // (No need to add them again if using delegation above)

    /* // Main time filter change handler - Replaced by delegation
     const mainTimeFilter = this.container.querySelector('#main-time-filter');
     if (mainTimeFilter) { ... } */

    /* // Refresh button click handler - Replaced by delegation
     const refreshButton = this.container.querySelector('#refresh-data');
     if (refreshButton) { ... } */

    /* // Individual chart range selectors - Replaced by delegation
     const chartRangeSelectors = [ ... ];
     chartRangeSelectors.forEach(selector => { ... }); */

    // Add department settings handler using event delegation
    this.container.addEventListener('click', (event) => {
      const target = event.target;
      const settingsButton = target.closest('#department-settings');
      
      if (settingsButton) {
        this.showDepartmentSettingsModal();
      }
    });

    // Add event listener for department custom date range button
    const departmentCustomDateBtn = document.getElementById('department-custom-date');
    if (departmentCustomDateBtn) {
      departmentCustomDateBtn.addEventListener('click', () => {
        this.showCustomDateRangeModal('purchaseByDepartment');
      });
    }
  }
  
   // Specific handler for refresh button logic (called by delegate)
   async handleRefreshClick(buttonElement) {
       console.log("Refresh button clicked");
       buttonElement.disabled = true;
       const textSpan = buttonElement.querySelector('span'); // Find the text span
       const originalText = textSpan ? textSpan.textContent : 'Refresh'; // Store original text
       buttonElement.innerHTML = `
          <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
         <span>Refreshing...</span>
       `;

       try {
          // Reload data from IndexedDB (forceRefresh=false implicit in loadData)
          await this.loadData();
          // Re-render necessary parts or update charts
          this.render(); // Re-render the structure
          await new Promise(resolve => setTimeout(resolve, 50)); // Allow DOM time
          this.updateAllCharts(); // Update charts with potentially new data
          // this.updateKPICards(); // updateAllCharts calls this now after filtering
          console.log("Data refreshed successfully.");
           // Optional: show success feedback
           this.showTemporaryMessage("Data refreshed from local storage.", "success");
       } catch (error) {
          console.error("Error during refresh:", error);
          this.showError("Failed to refresh data: " + error.message);
       } finally {
          buttonElement.disabled = false;
          // Restore original button icon and text
          buttonElement.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
             <span>${originalText}</span>
           `;
       }
   }
   
    // Helper to show temporary messages (e.g., for refresh success)
    showTemporaryMessage(message, type = "info") {
        const container = document.getElementById('purchase-analytics-error-container');
        if (!container) return;

        const colorClasses = {
            success: "bg-green-100 border-green-500 text-green-700",
            info: "bg-blue-100 border-blue-500 text-blue-700",
            warning: "bg-yellow-100 border-yellow-500 text-yellow-700",
            error: "bg-red-100 border-red-500 text-red-700"
        };

        const messageDiv = document.createElement('div');
        messageDiv.className = `border-l-4 p-3 mb-4 text-sm ${colorClasses[type] || colorClasses.info}`;
        messageDiv.textContent = message;
        
        container.prepend(messageDiv); // Add message to the top

        // Remove the message after a few seconds
        setTimeout(() => {
            messageDiv.style.opacity = '0';
            messageDiv.style.transition = 'opacity 0.5s ease-out';
            setTimeout(() => messageDiv.remove(), 500); // Remove from DOM after fade
        }, 3000); // Display for 3 seconds
    }

    // Updated showError to place message in a dedicated container
    showError(message) {
        const container = document.getElementById('purchase-analytics-error-container');
        if (!container) {
          // Fallback if container not found (should not happen with renderContentStructure)
          console.error("Error container not found! Message:", message);
          this.container.insertAdjacentHTML('afterbegin', `<div class="p-4 bg-red-100 text-red-700">${message}</div>`);
          return;
        }
        
        // Clear previous errors first
        this.hideError();

        const errorDiv = document.createElement('div');
        errorDiv.id = 'purchase-analytics-error'; // Keep ID for potential dismissal logic
        errorDiv.className = 'bg-red-100 border-l-4 border-red-500 text-red-700 p-3 mb-4 text-sm flex justify-between items-center';
        errorDiv.innerHTML = `
          <span>${message}</span>
          <button class="text-red-500 hover:text-red-700" onclick="this.parentNode.remove()">
             <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
          </button>
        `;
        container.appendChild(errorDiv);
    }

    // Updated hideError to target the specific container
    hideError() {
        const errorDiv = this.container.querySelector('#purchase-analytics-error'); // Query within container
        if (errorDiv) {
            errorDiv.remove();
        }
  }
  
  // Helper methods for styling
  getStatusColor(status) {
    if (!status) return '#6b7280'; // Default gray
    
    const statusLower = status.toLowerCase();
    
    if (statusLower.includes('open')) {
      return '#3b82f6'; // blue
    } else if (statusLower.includes('closed') || statusLower.includes('received')) {
      return '#10b981'; // green
    } else if (statusLower.includes('canceled') || statusLower.includes('cancelled') || statusLower.includes('rejected')) {
      return '#ef4444'; // red
    } else if (statusLower.includes('hold')) {
      return '#f59e0b'; // yellow/amber
    } else if (statusLower.includes('pending') || statusLower.includes('awaiting')) {
      return '#8b5cf6'; // purple
    } else {
      return '#6b7280'; // gray
    }
  }

  // --- IndexedDB Data Retrieval Methods ---

  async getPurchaseOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      if (!this.dbReady) {
        reject(new Error("Database not initialized"));
        return;
      }

      try {
        console.log("Opening transaction to fetch purchase orders");
        const transaction = this.db.transaction([this.poStoreName], "readonly");
        const store = transaction.objectStore(this.poStoreName);
        
        const request = store.getAll();
        
        request.onerror = (event) => {
          console.error("Error fetching purchase orders from IndexedDB:", event.target.error);
          reject(new Error("Failed to fetch purchase orders from database"));
        };
        
        request.onsuccess = (event) => {
          const purchaseOrders = event.target.result;
          console.log(`Successfully fetched ${purchaseOrders.length} purchase orders from IndexedDB`);
          
          // Process the data - convert YYYY-MM-DD strings to UTC midnight Date objects
          const processedPOs = purchaseOrders.map(po => {
            try {
              // Helper to parse YYYY-MM-DD string into UTC midnight Date
              const parseStoredDate = (dateStr) => {
                if (!dateStr || typeof dateStr !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                   return null;
                }
                const date = new Date(`${dateStr}T00:00:00.000Z`);
                return isNaN(date.getTime()) ? null : date;
              };

              // Handle date conversions using the parser
              po.date = parseStoredDate(po.date);
              po.promisedDate = parseStoredDate(po.promisedDate);
              po.actualDelivery = parseStoredDate(po.actualDelivery);
              po.lastModified = parseStoredDate(po.lastModified);
              
              // Ensure total and convertedTotal are numbers
              po.total = parseFloat(po.total) || 0;
              
              if (po.convertedTotal !== undefined) {
                po.convertedTotal = parseFloat(po.convertedTotal) || 0;
              } else {
                // If convertedTotal doesn't exist, use total as fallback
                po.convertedTotal = po.total;
              }

              return po;
            } catch (error) {
              console.error("Error processing purchase order:", error, po);
              return po; // Return original PO if processing fails
            }
          });
          
          resolve(processedPOs);
        };
        
      } catch (error) {
        console.error("Error in getPurchaseOrdersFromIndexedDB:", error);
        reject(error);
      }
    });
  }

  async getVendorsFromIndexedDB() {
    return new Promise((resolve, reject) => {
      if (!this.vendorDbReady) {
        return reject(new Error("Vendors database is not ready."));
      }
       if (!this.vendorDb) {
        return reject(new Error("Vendor DB connection is null."));
      }

      try {
          const transaction = this.vendorDb.transaction([this.vendorStoreName], "readonly");
          const store = transaction.objectStore(this.vendorStoreName);
          const getAllRequest = store.getAll();

          getAllRequest.onsuccess = () => {
            const vendors = getAllRequest.result;
            // Add any necessary parsing for vendor data here if needed (e.g., dates)
            vendors.forEach(vendor => {
                try {
                    vendor.lastModified = vendor.lastModified ? new Date(vendor.lastModified) : null;
                    // Ensure leadTime and onTimeDelivery are numbers
                    vendor.leadTime = parseFloat(vendor.leadTime) || 0;
                    vendor.onTimeDelivery = parseFloat(vendor.onTimeDelivery) || 0;
                } catch(e) {
                    console.warn(`Error parsing data for Vendor ID ${vendor.id || vendor.vendorId}:`, e, vendor);
                    vendor.leadTime = 0;
                    vendor.onTimeDelivery = 0;
                }
            });
            console.log(`Retrieved and parsed ${vendors.length} vendors.`);
            resolve(vendors);
          };

          getAllRequest.onerror = (event) => {
            console.error("Error retrieving vendors from DB:", event.target.error);
            reject(new Error("Failed to retrieve vendors from database"));
          };

          transaction.onerror = (event) => {
              console.error("Read transaction error (Vendors):", event.target.error);
              reject(new Error("Database transaction failed for Vendors."));
          };
          transaction.oncomplete = () => {
              console.log("Read transaction for Vendors completed.");
              // this.vendorDb.close(); // Closing here might be premature
          };
      } catch(e) {
          console.error("Error creating Vendor transaction:", e);
          reject(e);
      }
    });
  }

  // --- End IndexedDB Data Retrieval ---

  // Add this method to handle the department categorization modal
  showDepartmentSettingsModal() {
    // Apply the same time filter as used by the chart
    const chartTimeRange = this.chartTimeRanges['purchaseByDepartment'] || this.timeRange;
    
    // Get filtered data for the current time range
    const filteredData = this.filterDataByTimeRange(this.purchaseOrders, chartTimeRange, 'purchaseByDepartment');
    
    // Find uncategorized POs within the filtered data
    const uncategorizedPOs = filteredData.filter(po => {
      // Get department from description
      const department = this.findDepartmentForPO(po);
      return department === 'Uncategorized';
    });
    
    // Find categorized POs within the filtered data
    const categorizedPOs = filteredData.filter(po => {
      const department = this.findDepartmentForPO(po);
      return department !== 'Uncategorized';
    });
    
    // Create departments list for dropdown
    const departments = Object.keys(window.departmentKeywordsCache || {});
    departments.sort(); // Sort alphabetically
    
    // Create a map to track selected departments for each PO
    if (!window.tempCategorizations) {
      window.tempCategorizations = new Map();
    }
    
    // Helper function to format date
    const formatDate = (dateString) => {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      return date instanceof Date && !isNaN(date) 
        ? date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
        : 'N/A';
    };
    
    // Helper function to format currency
    const formatCurrency = (amount) => {
      if (amount === undefined || amount === null || isNaN(amount)) return 'N/A';
      return this.currencyFormatter.format(amount);
    };
    
    // Helper function to format time range for display
    const getTimeRangeDisplayName = (range) => {
      switch(range) {
        case '7d': return 'Weekly (7 days)';
        case '30d': return 'Monthly (30 days)';
        case '1y': return 'Yearly (12 months)';
        default: 
          if (this.customDateRanges && this.customDateRanges['purchaseByDepartment']) {
            const start = new Date(this.customDateRanges['purchaseByDepartment'].start);
            const end = new Date(this.customDateRanges['purchaseByDepartment'].end);
            return `Custom (${formatDate(start)} - ${formatDate(end)})`;
          }
          return 'Custom Period';
      }
    };
    
    // Build modal HTML with tabs
    const modalHTML = `
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Department Categorization</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Time period: ${getTimeRangeDisplayName(chartTimeRange)}
            </p>
          </div>
          <button id="close-dept-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <!-- Tab Navigation -->
        <div class="mb-4 border-b border-gray-200 dark:border-gray-700">
          <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
            <li class="mr-2" role="presentation">
              <button id="uncategorized-tab" class="inline-block p-4 rounded-t-lg border-b-2 border-blue-500 text-blue-500 active" role="tab" aria-selected="true">
                Uncategorized POs (${uncategorizedPOs.length})
              </button>
            </li>
            <li class="mr-2" role="presentation">
              <button id="categorized-tab" class="inline-block p-4 rounded-t-lg border-b-2 border-transparent hover:text-gray-600 hover:border-gray-300" role="tab" aria-selected="false">
                Categorized POs (${categorizedPOs.length})
              </button>
            </li>
          </ul>
        </div>
        
        <!-- Tab Content -->
        <div class="tab-content" style="max-height: calc(70vh - 180px); overflow-y: auto;">
          <!-- Uncategorized POs Tab -->
          <div id="uncategorized-content" class="tab-pane active" role="tabpanel">
        <div class="mb-4">
          <p class="text-sm text-gray-600 dark:text-gray-400">
                ${uncategorizedPOs.length} purchase orders need categorization in this time period. Assign them to departments below.
          </p>
        </div>
        
        ${uncategorizedPOs.length === 0 ? `
          <div class="text-center py-4">
                <p class="text-green-500">All purchase orders in this time period have been categorized!</p>
          </div>
        ` : `
              <div class="overflow-y-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
                <tr>
                  <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">PO #</th>
                  <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                  <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                  <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Department</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    ${uncategorizedPOs.map((po, index) => {
                      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
                      return `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100">${po.orderNbr || po.id || 'Unknown'}</td>
                    <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${formatDate(po.date || po.orderDate || po.createdDate)}</td>
                        <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${formatCurrency(amount)}</td>
                    <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${po.description || 'No description'}</td>
                    <td class="px-2 py-2 text-sm">
                      <select id="dept-select-${index}" class="dept-select bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1.5" data-po-id="${po.id}">
                        <option value="Uncategorized" selected>Select department...</option>
                        ${departments.map(dept => `<option value="${dept}">${dept}</option>`).join('')}
                      </select>
                    </td>
                  </tr>
                    `}).join('')}
              </tbody>
            </table>
          </div>
        `}
          </div>
          
          <!-- Categorized POs Tab -->
          <div id="categorized-content" class="tab-pane hidden" role="tabpanel">
            <div class="mb-4">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                ${categorizedPOs.length} purchase orders have been categorized in this time period. You can review and change their department assignments below.
              </p>
            </div>
            
            ${categorizedPOs.length === 0 ? `
              <div class="text-center py-4">
                <p class="text-yellow-500">No purchase orders have been categorized yet in this time period!</p>
              </div>
            ` : `
              <div class="overflow-y-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
                    <tr>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">PO #</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                      <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Department</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    ${categorizedPOs.map((po, index) => {
                      const currentDept = this.findDepartmentForPO(po);
                      const amount = po.convertedTotal !== undefined ? po.convertedTotal : (po.total || 0);
                      return `
                      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td class="px-2 py-2 text-sm text-gray-900 dark:text-gray-100">${po.orderNbr || po.id || 'Unknown'}</td>
                        <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${formatDate(po.date || po.orderDate || po.createdDate)}</td>
                        <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${formatCurrency(amount)}</td>
                        <td class="px-2 py-2 text-sm text-gray-500 dark:text-gray-400">${po.description ? po.description.replace(/\[DEPT:\s+([^\]]+)\]/i, '') : 'No description'}</td>
                        <td class="px-2 py-2 text-sm">
                          <select id="cat-dept-select-${index}" class="dept-select bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-1.5" data-po-id="${po.id}">
                            <option value="Uncategorized">Uncategorized</option>
                            ${departments.map(dept => `<option value="${dept}" ${dept === currentDept ? 'selected' : ''}>${dept}</option>`).join('')}
                          </select>
                        </td>
                      </tr>
                    `}).join('')}
                  </tbody>
                </table>
              </div>
            `}
          </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
          <button id="close-dept-settings" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-md">
            Cancel
          </button>
          <button id="save-all-dept" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
            Save All
          </button>
        </div>
      </div>
    `;
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
    modalOverlay.id = 'department-settings-modal';
    
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-3xl flex flex-col';
    modalContainer.style.maxHeight = '90vh'; // Limit height to 90% of viewport height
    modalContainer.style.overflowY = 'hidden'; // Container shouldn't scroll, just the content
    modalContainer.innerHTML = modalHTML;
    
    // Add to DOM
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Ensure the modal is properly positioned within viewport
    this.positionModal(modalContainer);
    
    // Set up tab functionality
    const uncategorizedTab = modalContainer.querySelector('#uncategorized-tab');
    const categorizedTab = modalContainer.querySelector('#categorized-tab');
    const uncategorizedContent = modalContainer.querySelector('#uncategorized-content');
    const categorizedContent = modalContainer.querySelector('#categorized-content');
    
    uncategorizedTab.addEventListener('click', () => {
      // Activate uncategorized tab
      uncategorizedTab.classList.add('border-blue-500', 'text-blue-500');
      uncategorizedTab.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
      uncategorizedTab.setAttribute('aria-selected', 'true');
      uncategorizedContent.classList.remove('hidden');
      uncategorizedContent.classList.add('active');
      
      // Deactivate categorized tab
      categorizedTab.classList.remove('border-blue-500', 'text-blue-500');
      categorizedTab.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
      categorizedTab.setAttribute('aria-selected', 'false');
      categorizedContent.classList.add('hidden');
      categorizedContent.classList.remove('active');
    });
    
    categorizedTab.addEventListener('click', () => {
      // Activate categorized tab
      categorizedTab.classList.add('border-blue-500', 'text-blue-500');
      categorizedTab.classList.remove('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
      categorizedTab.setAttribute('aria-selected', 'true');
      categorizedContent.classList.remove('hidden');
      categorizedContent.classList.add('active');
      
      // Deactivate uncategorized tab
      uncategorizedTab.classList.remove('border-blue-500', 'text-blue-500');
      uncategorizedTab.classList.add('border-transparent', 'hover:text-gray-600', 'hover:border-gray-300');
      uncategorizedTab.setAttribute('aria-selected', 'false');
      uncategorizedContent.classList.add('hidden');
      uncategorizedContent.classList.remove('active');
    });
    
    // Add event listeners for dropdowns to track changes
    const selects = modalContainer.querySelectorAll('.dept-select');
    selects.forEach(select => {
      const poId = select.getAttribute('data-po-id');
      
      // Initialize with existing value if already set
      if (window.manualPOCategorization && window.manualPOCategorization.has(poId)) {
        select.value = window.manualPOCategorization.get(poId);
      }
      
      // Track changes in the temp map
      select.addEventListener('change', () => {
        window.tempCategorizations.set(poId, {
          department: select.value,
          poId: poId,
          element: select
        });
      });
    });
    
    // Add event listeners for buttons
    const closeButtons = modalOverlay.querySelectorAll('#close-dept-modal, #close-dept-settings');
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Clear temp categorizations
        window.tempCategorizations.clear();
        document.body.removeChild(modalOverlay);
      });
    });
    
    // Handle save all button
    const saveAllBtn = modalContainer.querySelector('#save-all-dept');
    saveAllBtn.addEventListener('click', () => {
      // Collect both uncategorized and categorized POs for processing
      const allPOs = [...uncategorizedPOs, ...categorizedPOs];
      this.saveAllCategorizations(allPOs, modalOverlay);
    });
    
    // Handle ESC key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        window.tempCategorizations.clear();
        if (document.body.contains(modalOverlay)) {
          document.body.removeChild(modalOverlay);
        }
      }
    }, { once: true });
  }
  
  // Helper to position modal within viewport
  positionModal(modalElement) {
    // Make sure modal doesn't exceed viewport height
    const viewportHeight = window.innerHeight;
    
    // Set a maximum height for the modal
    modalElement.style.maxHeight = 'calc(90vh)';
    
    // Handle overflow content with scrolling
    const modalBody = modalElement.querySelector('.tab-content');
    if (modalBody) {
      // Make the content area scrollable
      modalBody.style.overflow = 'auto';
    }
  }
  
  // Save all categorizations at once
  saveAllCategorizations(allPOs, modalOverlay) {
    // Initialize manual categorization map if not exists
    if (!window.manualPOCategorization) {
      window.manualPOCategorization = new Map();
    }
    
    // Check if there are any changes to save
    if (!window.tempCategorizations || window.tempCategorizations.size === 0) {
      // If no changes, just close the modal
      document.body.removeChild(modalOverlay);
      return;
    }
    
    // Process all temporary categorizations
    let changesMade = false;
    window.tempCategorizations.forEach((data, poId) => {
      if (data.department) {
        // Find the current department for this PO
        const po = allPOs.find(p => p.id === poId);
        if (po) {
          const currentDept = this.findDepartmentForPO(po);
          
          // If there's a change in department assignment
          if (currentDept !== data.department) {
            // If setting to "Uncategorized", remove from manual categorizations
            if (data.department === 'Uncategorized') {
              window.manualPOCategorization.delete(poId);
              
              // Remove department tag from description if present
              if (po.description) {
                po.description = po.description.replace(/\[DEPT:\s+([^\]]+)\]/i, '').trim();
              }
            } else {
              // Save to persistent map for non-Uncategorized departments
        window.manualPOCategorization.set(poId, data.department);
        
              // Update description with new department tag
          const deptTag = `[DEPT: ${data.department}]`;
          if (po.description) {
                // Remove any existing department tag first
                po.description = po.description.replace(/\[DEPT:\s+([^\]]+)\]/i, '').trim();
                // Then add the new tag
              po.description = `${deptTag} ${po.description}`;
          } else {
            po.description = deptTag;
          }
        }
        
        changesMade = true;
          }
        }
      }
    });
    
    // Clear temp categorizations
    window.tempCategorizations.clear();
    
    if (changesMade) {
      // Save to localStorage for persistence
      this.saveManualCategorizationsToStorage();
      
      // Also save to IndexedDB if possible
      this.updateCategorizedPOsInIndexedDB();
      
      // Refresh the chart to show updated data
      this.applyFilterToChartData('purchaseByDepartment');
      
      // Show success notification if notification system exists
      if (this.notificationSystem) {
      this.notificationSystem.addNotification("Department categorizations saved successfully", "success");
      } else {
        // Fallback to showing a message in the console
        console.log("Department categorizations saved successfully");
      }
    }
    
    // Close the modal
    document.body.removeChild(modalOverlay);
  }
  
  // Add method to save categorizations to localStorage
  saveManualCategorizationsToStorage() {
    try {
      // Convert Map to array of entries for JSON serialization
      const serializedMap = JSON.stringify(Array.from(window.manualPOCategorization.entries()));
      localStorage.setItem('manualPOCategorizations', serializedMap);
      console.log(`Saved ${window.manualPOCategorization.size} manual categorizations to localStorage`);
    } catch (error) {
      console.error('Error saving manual categorizations:', error);
      this.notificationSystem.addNotification("Warning: Failed to save categorizations permanently", "warning");
    }
  }
  
  // Update the IndexedDB with categorized POs for long-term persistence
  async updateCategorizedPOsInIndexedDB() {
    try {
      if (!window.manualPOCategorization) {
        window.manualPOCategorization = new Map();
      }
      
      const db = await this.initPurchaseOrdersDB();
      if (!db) {
        console.error("Failed to initialize IndexedDB");
        return;
      }
      
      const transaction = db.transaction('purchaseOrders', 'readwrite');
      const store = transaction.objectStore('purchaseOrders');
      
      // Get all PO IDs from the main data array for processing
      const allPOIds = this.purchaseOrders.map(po => po.id);
      
      // Process all POs that need updating in IndexedDB
      for (const poId of allPOIds) {
        try {
          // Get the current record
          const po = await store.get(poId);
          if (!po) continue;
          
          // Check if this PO has a manual categorization
          if (window.manualPOCategorization.has(poId)) {
            const department = window.manualPOCategorization.get(poId);
            
            // Add department tag to description
            const deptTag = `[DEPT: ${department}]`;
            if (po.description) {
              // Remove any existing tag first
              po.description = po.description.replace(/\[DEPT:\s+([^\]]+)\]/i, '').trim();
              // Then add the new tag
                po.description = `${deptTag} ${po.description}`;
            } else {
              po.description = deptTag;
            }
            
            // Also store the department directly in a field for easier access
            po.department = department;
            
            // Update the record
            await store.put(po);
            console.log(`Updated PO ${poId} with department ${department} in IndexedDB`);
          } 
          // If the PO previously had a department but now is uncategorized
          else if (po.department || (po.description && po.description.match(/\[DEPT:\s+([^\]]+)\]/i))) {
            // Remove department tag from description
            if (po.description) {
              po.description = po.description.replace(/\[DEPT:\s+([^\]]+)\]/i, '').trim();
            }
            
            // Remove the department field
            delete po.department;
            
            // Update the record
            await store.put(po);
            console.log(`Removed department from PO ${poId} in IndexedDB`);
          }
        } catch (poError) {
          console.error(`Error updating PO ${poId}:`, poError);
        }
      }
      
      console.log('Completed IndexedDB updates for categorized POs');
    } catch (error) {
      console.error('Error updating categorized POs in IndexedDB:', error);
    }
  }
  
  // Add method to load saved categorizations from localStorage
  loadManualCategorizations() {
    try {
      const savedCategorizations = localStorage.getItem('manualPOCategorizations');
      if (savedCategorizations) {
        window.manualPOCategorization = new Map(JSON.parse(savedCategorizations));
        console.log(`Loaded ${window.manualPOCategorization.size} manual categorizations from localStorage`);
      } else {
        window.manualPOCategorization = new Map();
      }
    } catch (error) {
      console.error('Error loading manual categorizations:', error);
      window.manualPOCategorization = new Map();
    }
  }
  
  // Update the findDepartmentForPO to check for department tags in description
  findDepartmentForPO(po) {
    // First check manual categorization
    if (window.manualPOCategorization && window.manualPOCategorization.has(po.id)) {
      return window.manualPOCategorization.get(po.id);
    }
    
    // Next check for department tag in description
    if (po.description) {
      const deptTagMatch = po.description.match(/\[DEPT:\s+([^\]]+)\]/i);
      if (deptTagMatch && deptTagMatch[1]) {
        return deptTagMatch[1].trim();
      }
    }
    
    // Otherwise use keyword matching
    if (!po.description) return 'Uncategorized';
    
    const keywordsMap = window.departmentKeywordsCache || {};
    const descLower = po.description.toLowerCase();
    
    // Score each department based on keyword matches
    const scores = {};
    
    Object.entries(keywordsMap).forEach(([dept, keywords]) => {
      scores[dept] = 0;
      
      if (Array.isArray(keywords)) {
        keywords.forEach(keyword => {
          // Skip invalid keywords
          if (!keyword || typeof keyword !== 'string') return;
          
          const keywordLower = keyword.toLowerCase();
          // Exact match gets higher score
          if (descLower.includes(keywordLower)) {
            // Longer keyword matches = higher score
            scores[dept] += keywordLower.length; 
          }
        });
      }
    });
    
    // Find department with highest score
    let maxScore = 0;
    let maxDept = 'Uncategorized';
    
    Object.entries(scores).forEach(([dept, score]) => {
      if (score > maxScore) {
        maxScore = score;
        maxDept = dept;
      }
    });
    
    return maxScore > 0 ? maxDept : 'Uncategorized';
  }

  // Method to show custom date range modal
  showCustomDateRangeModal(chartId) {
    // Create default date range values (30 days)
    const today = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(today.getDate() - 30);
    
    // Format dates for input fields
    const formatDateForInput = (date) => {
      return date.toISOString().split('T')[0];
    };
    
    // Get current custom date range if exists
    const customDateRange = this.customDateRanges ? this.customDateRanges[chartId] : null;
    const startDate = customDateRange ? formatDateForInput(new Date(customDateRange.start)) : formatDateForInput(defaultStartDate);
    const endDate = customDateRange ? formatDateForInput(new Date(customDateRange.end)) : formatDateForInput(today);
    
    // Build modal HTML
    const modalHTML = `
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Custom Date Range</h3>
          <button id="close-date-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="mb-6">
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            Select a custom date range to filter purchase orders:
          </p>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="start-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
              <input type="date" id="start-date" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" value="${startDate}">
            </div>
            <div>
              <label for="end-date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
              <input type="date" id="end-date" class="bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5" value="${endDate}">
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button id="reset-date-range" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white px-4 py-2 rounded-md">
            Reset
          </button>
          <button id="apply-date-range" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
            Apply
          </button>
        </div>
      </div>
    `;
    
    // Create modal overlay
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
    modalOverlay.id = 'custom-date-range-modal';
    
    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] overflow-hidden flex flex-col';
    modalContainer.innerHTML = modalHTML;
    
    // Add to DOM
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Center the modal
    this.positionModal(modalContainer);
    
    // Add event listeners
    const closeBtn = modalContainer.querySelector('#close-date-modal');
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(modalOverlay);
    });
    
    // Add event listener for reset button
    const resetBtn = modalContainer.querySelector('#reset-date-range');
    resetBtn.addEventListener('click', () => {
      // Reset to default 30-day range
      const startDateInput = modalContainer.querySelector('#start-date');
      const endDateInput = modalContainer.querySelector('#end-date');
      
      startDateInput.value = formatDateForInput(defaultStartDate);
      endDateInput.value = formatDateForInput(today);
      
      // Reset the stored custom date range
      if (this.customDateRanges) {
        delete this.customDateRanges[chartId];
      }
      
      // Switch back to predefined range in dropdown
      const rangeSelect = document.getElementById(`${chartId}-range`);
      if (rangeSelect) {
        rangeSelect.value = '30d';
        this.chartTimeRanges[chartId] = '30d';
      }
      
      // Update the chart
      this.applyFilterToChartData(chartId);
      
      // Close modal
      document.body.removeChild(modalOverlay);
    });
    
    // Add event listener for apply button
    const applyBtn = modalContainer.querySelector('#apply-date-range');
    applyBtn.addEventListener('click', () => {
      const startDateInput = modalContainer.querySelector('#start-date');
      const endDateInput = modalContainer.querySelector('#end-date');
      
      const startDate = new Date(startDateInput.value);
      const endDate = new Date(endDateInput.value);
      
      // Validate dates
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        alert("Please select valid dates");
        return;
      }
      
      if (startDate > endDate) {
        alert("Start date cannot be after end date");
        return;
      }
      
      // Store the custom date range
      if (!this.customDateRanges) {
        this.customDateRanges = {};
      }
      
      this.customDateRanges[chartId] = {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      };
      
      // Update the dropdown to show custom
      const rangeSelect = document.getElementById(`${chartId}-range`);
      if (rangeSelect) {
        // Add custom option if not exists
        if (!rangeSelect.querySelector('option[value="custom"]')) {
          const customOption = document.createElement('option');
          customOption.value = 'custom';
          customOption.textContent = 'Custom';
          rangeSelect.appendChild(customOption);
        }
        
        rangeSelect.value = 'custom';
        this.chartTimeRanges[chartId] = 'custom';
      }
      
      // Update the chart
      this.applyFilterToChartData(chartId);
      
      // Close modal
      document.body.removeChild(modalOverlay);
    });
    
    // Handle ESC key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        if (document.body.contains(modalOverlay)) {
          document.body.removeChild(modalOverlay);
        }
      }
    }, { once: true });
  }

  // Renamed for clarity
  applyGlobalFilter() {
    // Apply global time filter to all purchase orders for KPIs
    this.filteredPurchaseOrders = this.filterDataByTimeRange(this.purchaseOrders, this.timeRange);
    
    console.log(`Applied global filter (${this.timeRange}): ${this.filteredPurchaseOrders.length} of ${this.purchaseOrders.length} records for KPIs.`);
  }
} 