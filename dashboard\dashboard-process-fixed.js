// dashboard-process.js - Process and Preview functionality
import * as DashboardActions from "./dashboard-actions.js"
import { setupShipOrder } from "./dashboard-shiporder.js"

// Set up all process buttons
export function setupProcessButtons(dashboard) {
 setupFetchData(dashboard)
 setupShProcess(dashboard)
 setupPoProcess(dashboard)
 setupSoProcess(dashboard)
 setupPartLookup(dashboard)
 setupValidateData(dashboard)
 setupSaveProcess(dashboard)
 setupScheduleProcess(dashboard)
 setupShipOrder(dashboard) // Add ship order setup
}

// Fetch Data button
function setupFetchData(dashboard) {
 const fetchDataBtn = dashboard.container.querySelector('[data-action="fetchData"]')
 if (fetchDataBtn) {
   fetchDataBtn.addEventListener("click", async () => {
     try {
       fetchDataBtn.disabled = true
       fetchDataBtn.classList.add("opacity-50", "cursor-not-allowed")

       const result = await DashboardActions.fetchData()

       if (result === "Data fetched successfully") {
         dashboard.addNotification("Data ready for processing", "success")
       }
     } catch (error) {
       console.error("Error in fetchData:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       fetchDataBtn.disabled = false
       fetchDataBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// SH Process button
function setupShProcess(dashboard) {
 const shProcessBtn = dashboard.container.querySelector('[data-action="shProcess"]')
 if (shProcessBtn) {
   shProcessBtn.addEventListener("click", async () => {
     try {
       shProcessBtn.disabled = true
       shProcessBtn.classList.add("opacity-50", "cursor-not-allowed")

       const result = await DashboardActions.shProcess()

       // Save preview data to storage
       dashboard.savePreviewData(result)

       // Switch to preview table
       showPreviewTable(dashboard, result)

       dashboard.addNotification("Shipment processed successfully", "success")
     } catch (error) {
       console.error("Error in shProcess:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       shProcessBtn.disabled = false
       shProcessBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// PO Process button
function setupPoProcess(dashboard) {
 const poProcessBtn = dashboard.container.querySelector('[data-action="poProcess"]')
 if (poProcessBtn) {
   poProcessBtn.addEventListener("click", async () => {
     try {
       poProcessBtn.disabled = true
       poProcessBtn.classList.add("opacity-50", "cursor-not-allowed")

       const result = await DashboardActions.poProcess()

       // Save preview data to storage
       dashboard.savePreviewData(result)

       // Switch to preview table
       showPreviewTable(dashboard, result)

       dashboard.addNotification("Purchase order processed successfully", "success")
     } catch (error) {
       console.error("Error in poProcess:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       poProcessBtn.disabled = false
       poProcessBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// SO Process button
function setupSoProcess(dashboard) {
 const soProcessBtn = dashboard.container.querySelector('[data-action="soProcess"]')
 if (soProcessBtn) {
   soProcessBtn.addEventListener("click", async () => {
     try {
       soProcessBtn.disabled = true
       soProcessBtn.classList.add("opacity-50", "cursor-not-allowed")

       const result = await DashboardActions.soProcess()

       // Save preview data to storage
       dashboard.savePreviewData(result)

       // Switch to preview table
       showPreviewTable(dashboard, result)

       dashboard.addNotification("Sales order processed successfully", "success")
     } catch (error) {
       console.error("Error in soProcess:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       soProcessBtn.disabled = false
       soProcessBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// Part Lookup button
function setupPartLookup(dashboard) {
 const partLookupBtn = dashboard.container.querySelector('[data-action="partLookup"]')
 if (partLookupBtn) {
   partLookupBtn.addEventListener("click", async () => {
     try {
       // Check if preview table is visible
       const previewTable = dashboard.container.querySelector("#previewTable")
       if (!previewTable || previewTable.classList.contains("hidden")) {
         dashboard.addNotification("Please process an order first to look up parts.", "warning")
         return
       }

       partLookupBtn.disabled = true
       partLookupBtn.classList.add("opacity-50", "cursor-not-allowed")

       // Get the current preview data
       const currentData = dashboard.currentPreviewData
       
       if (!currentData || !currentData.parts || currentData.parts.length === 0) {
         dashboard.addNotification("No parts available to look up.", "warning")
         return
       }

       // Show loading notification
       dashboard.addNotification("Looking up part details...", "info")

       // Perform the part lookup
       const updatedData = await DashboardActions.partLookup(currentData)
       
       // Save the updated data
       dashboard.savePreviewData(updatedData)
       
       // Refresh the preview table
       showPreviewTable(dashboard, updatedData)

       // Count how many parts were successfully found
       const partsWithData = updatedData.parts.filter(part => part.masterPartData).length
       
       if (partsWithData > 0) {
         dashboard.addNotification(`Part lookup completed successfully. Found details for ${partsWithData} of ${updatedData.parts.length} parts.`, "success")
       } else {
         dashboard.addNotification("Part lookup completed but no matching parts found in the database.", "warning")
       }
     } catch (error) {
       console.error("Error in partLookup:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       partLookupBtn.disabled = false
       partLookupBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// Validate Data button - UPDATED
function setupValidateData(dashboard) {
 const validateDataBtn = dashboard.container.querySelector('[data-action="validateData"]')
 if (validateDataBtn) {
   validateDataBtn.addEventListener("click", async () => {
     try {
       // Check if preview table is visible
       const previewTable = dashboard.container.querySelector("#previewTable")
       if (!previewTable || previewTable.classList.contains("hidden")) {
         dashboard.addNotification("Please process an order first to validate data.", "warning")
         return
       }

       validateDataBtn.disabled = true
       validateDataBtn.classList.add("opacity-50", "cursor-not-allowed")

       // Make all fields in the preview table editable
       makePreviewTableEditable(dashboard)
       
       // Make shipment info editable too
       makeShipmentInfoEditable(dashboard)

       dashboard.addNotification("You can now edit and validate all fields. Make your corrections and then save.", "info")
     } catch (error) {
       console.error("Error in validateData:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       validateDataBtn.disabled = false
       validateDataBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// Function to make all fields in the preview table editable
function makePreviewTableEditable(dashboard) {
 // Make Order Details fields editable
 const orderDetailsSection = dashboard.container.querySelector("#previewTable .bg-gray-50:nth-child(1)")
 if (orderDetailsSection) {
   // Find all static text elements in order details and make them editable
   const orderDetailItems = orderDetailsSection.querySelectorAll(".flex")
   orderDetailItems.forEach((item, index) => {
     const textElement = item.querySelector(".text-xs.text-gray-600")
     if (textElement) {
       const originalText = textElement.textContent
       const fieldLabel = item.querySelector(".text-xs.text-gray-700")
       const fieldName = fieldLabel ? fieldLabel.textContent.toLowerCase().replace(/\s+/g, '_') : `field_${index}`
       
       const inputElement = document.createElement("input")
       inputElement.type = "text"
       inputElement.value = originalText
       inputElement.className = "text-xs w-full border border-blue-300 rounded-md px-1 py-0.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400"
       inputElement.dataset.field = fieldName
       
       textElement.innerHTML = ""
       textElement.appendChild(inputElement)
     }
   })
 }

 // Make Package Info fields editable (they're already inputs, just enable them)
 const packageInfoInputs = dashboard.container.querySelectorAll("#previewTable input, #previewTable select")
 packageInfoInputs.forEach(input => {
   input.disabled = false
   input.classList.add("border-blue-300")
   
   // Add a subtle highlight effect
   input.classList.add("focus:ring-1", "focus:ring-blue-400", "focus:border-blue-400")
   
   // Mark this input as editable for later collection
   input.dataset.editable = "true"
 })

 // Make Part Details table cells editable
 const partDetailsTable = dashboard.container.querySelector("#previewTable table")
 if (partDetailsTable) {
   // First, make sure all expandable rows are visible
   const toggleButtons = partDetailsTable.querySelectorAll('.toggle-part-details')
   toggleButtons.forEach(button => {
     const partRow = button.closest('tr')
     const detailsRow = partRow.nextElementSibling
     
     // Open any hidden detail rows to make them editable
     if (detailsRow && detailsRow.classList.contains('part-details-row') && detailsRow.classList.contains('hidden')) {
       detailsRow.classList.remove('hidden')
       partRow.classList.add('bg-blue-50')
     }
   })
   
   // Now make all rows editable
   const tableRows = partDetailsTable.querySelectorAll("tbody tr:not(.part-details-row)")
   tableRows.forEach((row, rowIndex) => {
     // Skip the Action cell (last cell)
     const cells = Array.from(row.querySelectorAll("td"))
     const mainCells = cells.slice(0, cells.length - 1)
     
     mainCells.forEach((cell, cellIndex) => {
       const originalText = cell.textContent
       const inputElement = document.createElement("input")
       inputElement.type = "text"
       inputElement.value = originalText
       inputElement.className = "text-xs w-full border border-blue-300 rounded-md px-1 py-0.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400"
       
       // Add data attributes to identify which part and field this is
       inputElement.dataset.partIndex = rowIndex
       
       // Map cell index to field name
       const fieldNames = ["inventoryID", "description", "shippedQuantity", "lotSerialNumber"]
       if (cellIndex < fieldNames.length) {
         inputElement.dataset.field = fieldNames[cellIndex]
       }
       
       cell.innerHTML = ""
       cell.appendChild(inputElement)
     })
     
     // Find part details row if it exists
     const detailsRow = row.nextElementSibling
     if (detailsRow && detailsRow.classList.contains('part-details-row')) {
       const detailsContainer = detailsRow.querySelector('.part-details-container')
       if (detailsContainer) {
         // Make each detail field editable
         const detailItems = detailsContainer.querySelectorAll('.part-detail-item')
         detailItems.forEach(item => {
           const label = item.querySelector('.part-detail-label')
           const value = item.querySelector('.part-detail-value')
           
           if (value && label) {
             const labelText = label.textContent.trim()
             // Convert label to a property name that matches the original JSON format
             const fieldName = labelText.replace(/:/g, '')
             
             const originalText = value.textContent.trim()
             
             const inputElement = document.createElement("input")
             inputElement.type = "text"
             inputElement.value = originalText
             inputElement.className = "text-xs w-full border border-blue-300 rounded-md px-1 py-0.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400"
             inputElement.dataset.partIndex = rowIndex
             inputElement.dataset.field = `masterPartData.${fieldName}`
             
             value.innerHTML = ""
             value.appendChild(inputElement)
           }
         })
       }
     }
   })
 }

 // Add a "Save Validation" button at the top of the preview
 const previewContainer = dashboard.container.querySelector("#previewTableContainer")
 if (previewContainer) {
   // Remove any existing save button first
   const existingSaveBtn = document.getElementById("saveValidationBtn")
   if (existingSaveBtn) {
     existingSaveBtn.remove()
   }
   
   const saveValidationBtn = document.createElement("button")
   saveValidationBtn.id = "saveValidationBtn"
   saveValidationBtn.className = "mb-4 px-3 py-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center justify-center shadow-sm focus:outline-none"
   saveValidationBtn.innerHTML = `
     <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
       <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
     </svg>
     <span>Save Validated Data</span>
   `
   
   // Add event listener to save validated data
   saveValidationBtn.addEventListener("click", () => {
     // Collect all the edited values and update the data
     const updatedData = collectValidatedData(dashboard)
     
     // Update the dashboard's current preview data
     dashboard.savePreviewData(updatedData)
     
     // Show success message
     dashboard.addNotification("Data validation saved successfully. All changes will be applied to future operations.", "success")
     
     // Refresh the preview table with the updated data
     showPreviewTable(dashboard, updatedData)
   })
   
   // Insert at the beginning of the container
   previewContainer.insertBefore(saveValidationBtn, previewContainer.firstChild)
 }
}

// Make shipment info editable
function makeShipmentInfoEditable(dashboard) {
  // Find the shipment info container
  const shipmentInfoContainer = dashboard.container.querySelector("#shipmentInfoDetails");
  if (!shipmentInfoContainer) return;
  
  // Make sure the shipment info is visible
  shipmentInfoContainer.classList.remove("hidden");
  
  // Update the toggle button icon
  const toggleButton = dashboard.container.querySelector("#toggleShipmentInfoBtn");
  if (toggleButton) {
    toggleButton.innerHTML = `
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
      </svg>
    `;
  }
  
  // Find all field values in the shipment info
  const shipmentInfoFields = shipmentInfoContainer.querySelectorAll(".flex-col");
  shipmentInfoFields.forEach(field => {
    const label = field.querySelector("span:first-child");
    const value = field.querySelector("span:last-child");
    
    if (!label || !value) return;
    
    const fieldName = label.textContent.trim();
    const originalText = value.textContent.trim();
    
    // Create field name for data attribute
    const dataField = "shipmentInfo." + fieldName.toLowerCase().replace(/\s+/g, '_');
    
    // Create an input element
    const inputElement = document.createElement("input");
    inputElement.type = "text";
    inputElement.value = originalText;
    inputElement.className = "text-xs w-full border border-blue-300 rounded-md px-1.5 py-0.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400";
    inputElement.dataset.field = dataField;
    
    // Replace the static text with the input
    value.innerHTML = "";
    value.appendChild(inputElement);
  });
}

// Function to collect all validated data from the form
function collectValidatedData(dashboard) {
 // Start with a copy of the current preview data
 let updatedData = JSON.parse(JSON.stringify(dashboard.currentPreviewData || {}))
 
 // Update Order Details
 const orderDetailsSection = dashboard.container.querySelector("#previewTable .bg-gray-50:nth-child(1)")
 if (orderDetailsSection) {
   const orderDetailInputs = orderDetailsSection.querySelectorAll("input[data-field]")
   orderDetailInputs.forEach(input => {
     const fieldName = input.dataset.field
     if (fieldName === 'customer_name') {
       updatedData.customer = input.value
     } else if (fieldName === 'reference_number') {
       updatedData.reference = input.value
     } else if (fieldName === 'order_type') {
       updatedData.orderType = input.value
     } else if (fieldName === 'order_number') {
       updatedData.orderNumber = input.value
     }
   })
 }
 
 // Update Package Info
 const shipmentMethod = document.getElementById("shipmentMethod")
 const packageQty = document.getElementById("packageQty")
 const packageType = document.getElementById("packageType")
 const packageDims = document.getElementById("packageDims")
 const packageWeight = document.getElementById("packageWeight")
 const packageCost = document.getElementById("packageCost")
 const carrier = document.getElementById("carrier")
 const trackingNo = document.getElementById("trackingNo")
 const packageNoteText = document.getElementById("packageNoteText")
 
 // Create or update package info in the data
 if (!updatedData.packageInfo) {
   updatedData.packageInfo = {}
 }
 
 if (shipmentMethod) updatedData.packageInfo.shipmentMethod = shipmentMethod.value
 if (packageQty) updatedData.packageInfo.quantity = packageQty.value
 if (packageType) updatedData.packageInfo.type = packageType.value
 if (packageDims) updatedData.packageInfo.dimensions = packageDims.value
 if (packageWeight) updatedData.packageInfo.weight = packageWeight.value
 if (packageCost) updatedData.packageInfo.cost = packageCost.value
 if (carrier) updatedData.packageInfo.carrier = carrier.value
 if (trackingNo) updatedData.packageInfo.trackingNumber = trackingNo.value
 if (packageNoteText && packageNoteText.value) updatedData.packageInfo.note = packageNoteText.value
 
 // Save tracking URL for this carrier if available
 if (carrier && trackingNo && trackingNo.value) {
   const baseUrl = trackingUrls[carrier.value];
   if (baseUrl) {
     // Set up the final tracking URL
     let trackingUrl = baseUrl;
     
     // Handle different URL formats based on carrier
     if (["FedEx", "UPS", "DHL", "Loomis", "Purolator"].includes(carrier.value)) {
       // For standard carriers using ship24.com
       trackingUrl = "https://www.ship24.com/tracking?p=" + trackingNo.value;
     } else if (carrier.value === "Manitoulin" && baseUrl.includes("probill=")) {
       // For Manitoulin's special format
       trackingUrl = baseUrl + trackingNo.value;
     } else {
       // For custom carriers - use exact URL format from the user input
       // First check if there are placeholders in the URL
       if (baseUrl.includes("{tracking}")) {
         // Replace the placeholder with the tracking number
         trackingUrl = baseUrl.replace("{tracking}", trackingNo.value);
       } else if (baseUrl.includes("?") && !baseUrl.endsWith("?")) {
         // URL already has parameters
         trackingUrl = baseUrl + (baseUrl.includes("=") ? "&" : "") + "tracking=" + trackingNo.value;
       } else if (baseUrl.endsWith("?")) {
         // URL ends with ? - append tracking directly
         trackingUrl = baseUrl + trackingNo.value;
       } else if (baseUrl.endsWith("/")) {
         // URL ends with / - append tracking directly
         trackingUrl = baseUrl + trackingNo.value;
       } else {
         // Otherwise add as a path segment
         trackingUrl = baseUrl + "/" + trackingNo.value;
       }
     }
     
     // Store the full tracking URL
     updatedData.packageInfo.trackingUrl = trackingUrl;
   }
 }
 
 // Update Part Details
 const partDetailsTable = dashboard.container.querySelector("#previewTable table")
 if (partDetailsTable && updatedData.parts) {
   // Create a mapping of field name replacements to ensure they match the JSON format
   const fieldMapping = {
     'Country of Origin': 'Country of Origin',
     'HS Code': 'HS Code',
     'Supplier': 'Supplier',
     'Supplier PN': 'Supplier P/N',
     'UOM': 'UOM',
     'Manufacturer': 'Manufacturer',
     'Manufacturer  Address': 'Manufacturer & Address',
     'Customs Description': 'Customs Description',
     'USMCACASMA': 'USMCA/CASMA',
     'Default Price': 'Default Price',
     'Preference Criteria': 'Preference Criteria',
     'NMFC Code': 'NMFC Code'
   }
     
   // Handle all inputs (both main part fields and master data fields)
   const partInputs = partDetailsTable.querySelectorAll("input[data-part-index][data-field]")
   partInputs.forEach(input => {
     const partIndex = parseInt(input.dataset.partIndex, 10)
     const fieldName = input.dataset.field
     
     // Skip invalid indices
     if (isNaN(partIndex) || partIndex < 0 || partIndex >= updatedData.parts.length) {
       return
     }
     
     // Check if this is a master part data field
     if (fieldName.startsWith('masterPartData.')) {
       // Extract the specific property name
       let propName = fieldName.split('.')[1]
       
       // Apply field mapping if exists
       if (fieldMapping[propName]) {
         propName = fieldMapping[propName]
       }
       
       // Make sure the part exists at this index and has masterPartData
       if (updatedData.parts[partIndex]) {
         if (!updatedData.parts[partIndex].masterPartData) {
           updatedData.parts[partIndex].masterPartData = {}
         }
         // Set the value in the masterPartData object
         updatedData.parts[partIndex].masterPartData[propName] = input.value
       }
     } 
     // Otherwise, it's a regular part field
     else if (updatedData.parts[partIndex] && fieldName) {
       updatedData.parts[partIndex][fieldName] = input.value
     }
   })
 }
 
 // Collect shipment info data from inputs
 const shipmentInfoInputs = dashboard.container.querySelectorAll("input[data-field^='shipmentInfo.']");
 if (shipmentInfoInputs.length > 0) {
   // Create the shipmentInfo object if it doesn't exist
   if (!updatedData.shipmentInfo) {
     updatedData.shipmentInfo = {};
   }
   
   // Process each input field
   shipmentInfoInputs.forEach(input => {
     const fieldName = input.dataset.field.split('.')[1]; // Get the part after shipmentInfo.
     
     // Map field names to the actual property names
     const fieldMapping = {
       'company_name': 'companyName',
       'attention': 'attention',
       'phone': 'phone',
       'email': 'email',
       'address_line_1': 'addressLine1',
       'address_line_2': 'addressLine2',
       'city': 'city',
       'country': 'country',
       'state': 'state',
       'postal_code': 'postalCode',
       'shipping_method': 'shippingMethod',
       'shipping_terms': 'shippingTerms',
       'shipment_description': 'shipmentDescription',
       'shipment_date': 'shipmentDate'
     };
     
     // Find the matching property name or use the original
     const propertyName = fieldMapping[fieldName] || fieldName;
     
     // Update the property
     updatedData.shipmentInfo[propertyName] = input.value;
   });
 }
 
 console.log("Updated data after validation:", updatedData)
 return updatedData
}

// Save Process button
function setupSaveProcess(dashboard) {
 const saveProcessBtn = dashboard.container.querySelector('[data-action="saveProcess"]')
 if (saveProcessBtn) {
   saveProcessBtn.addEventListener("click", async () => {
     try {
       if (!dashboard.currentPreviewData) {
         dashboard.addNotification("No data available to save.", "warning")
         return
       }
       
       // Check if there are parts in the shipment
       if (!dashboard.currentPreviewData.parts || dashboard.currentPreviewData.parts.length === 0) {
         const confirmEmpty = confirm("This shipment has no parts. Are you sure you want to save it?");
         if (!confirmEmpty) {
           dashboard.addNotification("Save operation cancelled.", "info");
           return;
         }
       }

       saveProcessBtn.disabled = true
       saveProcessBtn.classList.add("opacity-50", "cursor-not-allowed")

       // Pass the current preview data directly to the function
       const result = await DashboardActions.saveProcess(dashboard.currentPreviewData)

       dashboard.addNotification(`Save completed: ${result.message}`, "success")
       // Make sure we're passing the current preview data to updateHistory
       dashboard.updateHistory("saveProcess", "Process saved", dashboard.currentPreviewData)
     } catch (error) {
       console.error("Error in saveProcess:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
       dashboard.updateHistory("saveProcess", `Error: ${error.message}`)
     } finally {
       saveProcessBtn.disabled = false
       saveProcessBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// Schedule Process button
function setupScheduleProcess(dashboard) {
 const scheduleProcessBtn = dashboard.container.querySelector('[data-action="scheduleProcess"]')
 if (scheduleProcessBtn) {
   scheduleProcessBtn.addEventListener("click", async () => {
     try {
       if (!dashboard.currentPreviewData) {
         dashboard.addNotification("No data available to schedule.", "warning")
         return
       }

       scheduleProcessBtn.disabled = true
       scheduleProcessBtn.classList.add("opacity-50", "cursor-not-allowed")

       // Pass the current preview data directly to the function
       const result = await DashboardActions.scheduleProcess(dashboard.currentPreviewData)
       
       // Show the calendar modal using the dashboard's scheduling system
       dashboard.showCalendarModal(result)
       
       // No history update here - we'll only update history if the user confirms in the modal
       dashboard.addNotification("Please select date and time to schedule", "info")
     } catch (error) {
       console.error("Error in scheduleProcess:", error)
       dashboard.addNotification(`Error: ${error.message}`, "danger")
     } finally {
       scheduleProcessBtn.disabled = false
       scheduleProcessBtn.classList.remove("opacity-50", "cursor-not-allowed")
     }
   })
 }
}

// Helper function to toggle part details
function togglePartDetails(event, partId) {
  const partRow = event.target.closest('tr');
  if (!partRow) return;

  // Find or create the details row
  let detailsRow = partRow.nextElementSibling;
  
  // Check if we already have a details row
  if (detailsRow && detailsRow.classList.contains('part-details-row')) {
    // Toggle visibility
    if (detailsRow.classList.contains('hidden')) {
      detailsRow.classList.remove('hidden');
      partRow.classList.add('bg-blue-50');
    } else {
      detailsRow.classList.add('hidden');
      partRow.classList.remove('bg-blue-50');
    }
  } else {
    // We don't have a details row yet - might be loading or missing data
    console.log(`No details row found for part ${partId}`);
  }
}

// Function to fix history table widths
function fixHistoryTableWidths() {
  // Find the history table
  const historyTable = document.querySelector("#historySection table");
  if (!historyTable) return;
  
  // Make container scrollable if needed
  const tableContainer = historyTable.parentElement;
  if (tableContainer) {
    tableContainer.style.overflowX = "auto";
    tableContainer.style.maxWidth = "100%";
  }
  
  // Set fixed layout for table to respect column widths
  historyTable.style.tableLayout = "fixed";
  historyTable.style.width = "100%";
  
  // Set column widths directly on the table headers
  const headers = historyTable.querySelectorAll("th");
  if (headers.length >= 6) {
    // Adjust these widths to fit your content
    headers[0].style.width = "80px";  // User Type
    headers[1].style.width = "100px"; // Customer
    headers[2].style.width = "80px"; // Reference - space for 6 digits
    headers[3].style.width = "90px";  // Date
    headers[4].style.width = "80px";  // Status
    headers[5].style.width = "110px"; // Actions
  }
  
  // Add text truncation to cells to prevent overflow
  const rows = historyTable.querySelectorAll("tbody tr");
  rows.forEach(row => {
    const cells = row.querySelectorAll("td");
    cells.forEach((cell, index) => {
      // Apply truncation to all cells except the Actions column (last one)
      if (index < cells.length - 1) {
        cell.style.whiteSpace = "nowrap";
        cell.style.overflow = "hidden";
        cell.style.textOverflow = "ellipsis";
        
        // Add tooltip for full text
        const cellText = cell.textContent.trim();
        if (cellText.length > 15) {
          cell.title = cellText;
        }
      }
    });
  });
}

// Show the preview table with the provided data
export async function showPreviewTable(dashboard, data) {
 const historySection = dashboard.container.querySelector("#historySection")
 const previewTable = dashboard.container.querySelector("#previewTable")

 if (historySection && previewTable) {
   // Save the preview data for other actions to access
   dashboard.savePreviewData(data)

   // Apply glass effect to the container
   dashboard.container.classList.add("bg-gradient-to-br", "from-white/30", "to-gray-100/30", "backdrop-blur-md")

   // Apply transition
   historySection.style.transition = "opacity 0.2s ease-in-out"
   historySection.style.opacity = "0"

   setTimeout(() => {
     // Hide history after fade out
     historySection.classList.add("hidden")
     historySection.style.opacity = "1"

     // Show preview with fade-in
     previewTable.classList.remove("hidden")
     previewTable.style.transition = "opacity 0.2s ease-in-out"
     previewTable.style.opacity = "0"

     // Generate preview HTML
     const previewContainer = dashboard.container.querySelector("#previewTableContainer")
     if (previewContainer) {
       previewContainer.innerHTML = createPreviewTableHTML(data)

       // Set up all functionality
       setupShipmentMethodLogic(dashboard)
       setupTrackingButtonLogic(dashboard)
       setupNoteToggleFunctionality(dashboard)
       setupPartDetailsToggle(dashboard)
       setupShipmentInfoToggle(dashboard) // Add the new toggle setup
       
       // Find and set up buttons
       const backToHistoryBtn = dashboard.container.querySelector("#backToHistoryBtn")
       const printBtn = dashboard.container.querySelector("#printBtn")

       // Set up Back to History button
       if (backToHistoryBtn) {
         backToHistoryBtn.addEventListener("click", () => {
           showHistoryTable(dashboard)
         })
       }

       // Set up Print button - use the dashboard's export system
       if (printBtn) {
         printBtn.addEventListener("click", () => {
           dashboard.printPreviewData(dashboard.currentPreviewData)
         })
       }

       // Add a slight delay before fading in to ensure DOM updates complete
       setTimeout(() => {
         previewTable.style.opacity = "1"
       }, 50)
     }
   }, 200)
 }
}

// Setup shipment info toggle functionality
function setupShipmentInfoToggle(dashboard) {
  console.log("Setting up shipment info toggle functionality");

  const toggleButton = dashboard.container.querySelector("#toggleShipmentInfoBtn");
  const shipmentInfoDetails = dashboard.container.querySelector("#shipmentInfoDetails");

  if (!toggleButton || !shipmentInfoDetails) {
    console.error("Shipment info toggle elements not found");
    return;
  }

  toggleButton.addEventListener("click", () => {
    console.log("Toggle shipment info button clicked");
    shipmentInfoDetails.classList.toggle("hidden");
    
    // Change button icon depending on state
    if (shipmentInfoDetails.classList.contains("hidden")) {
      toggleButton.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      `;
    } else {
      toggleButton.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
        </svg>
      `;
    }
  });
}

// Setup part details toggle
function setupPartDetailsToggle(dashboard) {
  const toggleButtons = document.querySelectorAll('.toggle-part-details');
  const deleteButtons = document.querySelectorAll('.delete-part-btn');
  
  // Setup toggle functionality
  toggleButtons.forEach(button => {
    button.addEventListener('click', (event) => {
      const partId = button.getAttribute('data-part-id');
      togglePartDetails(event, partId);
    });
  });
  
  // Setup delete functionality
  deleteButtons.forEach(button => {
    button.addEventListener('click', (event) => {
      event.stopPropagation(); // Prevent triggering other row events
      const partIndex = parseInt(button.getAttribute('data-part-index'), 10);
      deletePartFromPreview(dashboard, partIndex);
    });
  });
}

// Show the history table - Updated to call fixHistoryTableWidths
export function showHistoryTable(dashboard) {
 const historySection = dashboard.container.querySelector("#historySection")
 const previewTable = dashboard.container.querySelector("#previewTable")

 if (historySection && previewTable) {
   // Apply transition
   previewTable.style.transition = "opacity 0.15s ease-in-out"
   previewTable.style.opacity = "0"

   setTimeout(() => {
     // Hide preview after fade out
     previewTable.classList.add("hidden")
     previewTable.style.opacity = "1"

     // Show history with fade-in
     historySection.classList.remove("hidden")
     historySection.style.transition = "opacity 0.15s ease-in-out"
     historySection.style.opacity = "0"

     // Refresh history data
     dashboard.loadHistoryData()
     
     // Fix table widths after loading data
     setTimeout(fixHistoryTableWidths, 100);

     // Fade in the history content
     setTimeout(() => {
       historySection.style.opacity = "1"

       // Remove glass effect from container when returning to history
       dashboard.container.classList.remove("bg-gradient-to-br", "from-white/30", "to-gray-100/30", "backdrop-blur-md")
     }, 50)
   }, 150)
 }
}

// Create Preview Table HTML function - UPDATED to include shipment details
function createPreviewTableHTML(data) {
 if (!data || !data.parts) {
   return `
    <div class="p-3 text-center text-gray-500 italic bg-white/70 backdrop-blur-md rounded-lg">
      No preview data available. Please process an order first.
    </div>
  `
 }

 // Get package info if available
 const packageInfo = data.packageInfo || {}
 
 // Get shipment info if available
 const shipmentInfo = data.shipmentInfo || {}

 return `
<div class="space-y-4">
  <!-- Order Details Section -->
  <div class="p-4 bg-gray-50 rounded-xl">
    <h3 class="text-xs font-semibold mb-3 text-gray-700">Order Details</h3>
    <div class="grid grid-cols-1 md:grid-cols-4 gap-3">
      <!-- Customer Name -->
      <div class="flex">
        <div class="h-full">
          <div class="bg-blue-500 rounded-md h-8 w-8 flex items-center justify-center border-0 shadow-none outline-none">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-2 overflow-hidden">
          <div class="text-xs text-gray-700">Customer Name</div>
          <div class="text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis max-w-[140px]">${data.customer || "N/A"}</div>
        </div>
      </div>

      <!-- Reference Number -->
      <div class="flex">
        <div class="h-full">
          <div class="bg-green-500 rounded-md h-8 w-8 flex items-center justify-center border-0 shadow-none outline-none">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-2 overflow-hidden">
          <div class="text-xs text-gray-700">Reference Number</div>
          <div class="text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis max-w-[140px]">${data.reference || "N/A"}</div>
        </div>
      </div>

      <!-- Order Type -->
      <div class="flex">
        <div class="h-full">
          <div class="bg-purple-500 rounded-md h-8 w-8 flex items-center justify-center border-0 shadow-none outline-none">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
          </div>
        </div>
        <div class="ml-2 overflow-hidden">
          <div class="text-xs text-gray-700">Order Type</div>
          <div class="text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis max-w-[140px]">${data.orderType || "N/A"}</div>
        </div>
      </div>

      <!-- Order Number -->
      <div class="flex">
        <div class="h-full">
          <div class="bg-red-600 rounded-md h-8 w-8 flex items-center justify-center border-0 shadow-none outline-none">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
            </svg>
          </div>
        </div>
        <div class="ml-2 overflow-hidden">
          <div class="text-xs text-gray-700">Order Number</div>
          <div class="text-xs text-gray-600 whitespace-nowrap overflow-hidden text-ellipsis max-w-[140px]">${data.orderNumber || "N/A"}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Shipment Details Section (formerly Package Info) -->
  <div class="p-4 bg-gray-50 rounded-xl">
    <!-- Shipment Details header with Shipment Method dropdown -->
    <div class="flex justify-between items-center mb-3">
      <div class="flex items-center">
        <h3 class="text-xs font-semibold text-gray-700">Shipment Details</h3>
        <button id="toggleShipmentInfoBtn" class="ml-2 p-1 text-blue-600 hover:text-blue-800 focus:outline-none">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
      </div>
      <div class="flex items-center gap-2">
        <label for="shipmentMethod" class="text-xs text-gray-600">Shipment Method</label>
        <select id="shipmentMethod" name="shipmentMethod" class="text-xs h-7 w-28 border border-gray-300 rounded-md bg-white px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" ${packageInfo.shipmentMethod ? `value="${packageInfo.shipmentMethod}"` : ''}>
          <option value="prepaid" ${packageInfo.shipmentMethod === 'prepaid' ? 'selected' : ''}>Prepaid</option>
          <option value="collect" ${packageInfo.shipmentMethod === 'collect' ? 'selected' : ''}>Collect</option>
          <option value="thirdParty" ${packageInfo.shipmentMethod === 'thirdParty' ? 'selected' : ''}>Third Party</option>
          <option value="courier" ${packageInfo.shipmentMethod === 'courier' ? 'selected' : ''}>Courier</option>
          <option value="customer" ${packageInfo.shipmentMethod === 'customer' ? 'selected' : ''}>Customer Pickup</option>
        </select>
      </div>
    </div>

    <!-- Detailed Shipment Information (collapsible) -->
    <div id="shipmentInfoDetails" class="mb-4 hidden bg-blue-50 p-3 rounded-lg">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        <!-- Company Name -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Company Name</span>
          <span class="text-xs text-gray-900">${shipmentInfo.companyName || 'N/A'}</span>
        </div>
        
        <!-- Attention -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Attention</span>
          <span class="text-xs text-gray-900">${shipmentInfo.attention || 'N/A'}</span>
        </div>
        
        <!-- Phone -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Phone</span>
          <span class="text-xs text-gray-900">${shipmentInfo.phone || 'N/A'}</span>
        </div>
        
        <!-- Email -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Email</span>
          <span class="text-xs text-gray-900">${shipmentInfo.email || 'N/A'}</span>
        </div>
        
        <!-- Address Line 1 -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Address Line 1</span>
          <span class="text-xs text-gray-900">${shipmentInfo.addressLine1 || 'N/A'}</span>
        </div>
        
        <!-- Address Line 2 -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Address Line 2</span>
          <span class="text-xs text-gray-900">${shipmentInfo.addressLine2 || 'N/A'}</span>
        </div>
        
        <!-- City -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">City</span>
          <span class="text-xs text-gray-900">${shipmentInfo.city || 'N/A'}</span>
        </div>
        
        <!-- State -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">State</span>
          <span class="text-xs text-gray-900">${shipmentInfo.state || 'N/A'}</span>
        </div>
        
        <!-- Postal Code -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Postal Code</span>
          <span class="text-xs text-gray-900">${shipmentInfo.postalCode || 'N/A'}</span>
        </div>
        
        <!-- Country -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Country</span>
          <span class="text-xs text-gray-900">${shipmentInfo.country || 'N/A'}</span>
        </div>
        
        <!-- Shipping Method -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Shipping Method</span>
          <span class="text-xs text-gray-900">${shipmentInfo.shippingMethod || 'N/A'}</span>
        </div>
        
        <!-- Shipping Terms -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Shipping Terms</span>
          <span class="text-xs text-gray-900">${shipmentInfo.shippingTerms || 'N/A'}</span>
        </div>
        
        <!-- Shipment Date -->
        <div class="flex flex-col">
          <span class="text-xs font-medium text-gray-700">Shipment Date</span>
          <span class="text-xs text-gray-900">${shipmentInfo.shipmentDate || 'N/A'}</span>
        </div>
        
        <!-- Shipment Description (spans all columns) -->
        <div class="flex flex-col col-span-3">
          <span class="text-xs font-medium text-gray-700">Shipment Description</span>
          <span class="text-xs text-gray-900">${shipmentInfo.shipmentDescription || 'N/A'}</span>
        </div>
      </div>
    </div>

    <!-- Line 1: Physical package attributes and cost -->
    <div class="grid grid-cols-12 gap-2 mb-3">
      <!-- Qty -->
      <div class="col-span-2">
        <label for="packageQty" class="block text-xs text-gray-600 mb-1">Qty</label>
        <input type="number" id="packageQty" name="packageQty" min="1" max="999" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" value="${packageInfo.quantity || '1'}">
      </div>

      <!-- Type -->
      <div class="col-span-2">
        <label for="packageType" class="block text-xs text-gray-600 mb-1">Type</label>
        <select id="packageType" name="packageType" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400">
          <option value="Box" ${packageInfo.type === 'Box' ? 'selected' : ''}>Box</option>
          <option value="Bag" ${packageInfo.type === 'Bag' ? 'selected' : ''}>Bag</option>
          <option value="Crate" ${packageInfo.type === 'Crate' ? 'selected' : ''}>Crate</option>
          <option value="Skid" ${packageInfo.type === 'Skid' ? 'selected' : ''}>Skid</option>
          <option value="Building" ${packageInfo.type === 'Building' ? 'selected' : ''}>Building</option>
          <option value="B Case" ${packageInfo.type === 'B Case' ? 'selected' : ''}>B Case</option>
          <option value="Envelope" ${packageInfo.type === 'Envelope' ? 'selected' : ''}>Envelope</option>
          <option value="N/A" ${packageInfo.type === 'N/A' ? 'selected' : ''}>N/A</option>
        </select>
      </div>

      <!-- Dims -->
      <div class="col-span-4">
        <label for="packageDims" class="block text-xs text-gray-600 mb-1">Dims</label>
        <input type="text" id="packageDims" name="packageDims" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" placeholder="30x25x20 inch" value="${packageInfo.dimensions || ''}">
      </div>

     <!-- Weight -->
<div class="col-span-2">
  <label for="packageWeight" class="block text-xs text-gray-600 mb-1">Weight</label>
  <input type="number" id="packageWeight" name="packageWeight" min="0" max="999" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" placeholder="20 lbs" value="${packageInfo.weight || ''}">
</div>

      <!-- Cost -->
      <div id="costContainer" class="col-span-2">
        <label for="packageCost" class="block text-xs text-gray-600 mb-1">Cost</label>
        <input type="text" id="packageCost" name="packageCost" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" placeholder="$20" value="${packageInfo.cost || ''}">
      </div>
    </div>

    <!-- Line 2: Carrier and tracking - Aligned inputs and buttons -->
    <div class="grid grid-cols-12 gap-2 mb-2">
      <!-- Carrier dropdown -->
      <div class="col-span-3">
        <label for="carrier" class="block text-xs text-gray-600 mb-1">Carrier</label>
        <select id="carrier" name="carrier" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400">
          <option value="FedEx" ${packageInfo.carrier === 'FedEx' ? 'selected' : ''}>FedEx</option>
          <option value="Loomis" ${packageInfo.carrier === 'Loomis' ? 'selected' : ''}>Loomis</option>
          <option value="Purolator" ${packageInfo.carrier === 'Purolator' ? 'selected' : ''}>Purolator</option>
          <option value="DHL" ${packageInfo.carrier === 'DHL' ? 'selected' : ''}>DHL</option>
          <option value="UPS" ${packageInfo.carrier === 'UPS' ? 'selected' : ''}>UPS</option>
          <option value="HiWay9" ${packageInfo.carrier === 'HiWay9' ? 'selected' : ''}>HiWay9</option>
          <option value="Manitoulin" ${packageInfo.carrier === 'Manitoulin' ? 'selected' : ''}>Manitoulin</option>
          <option value="Rosenau" ${packageInfo.carrier === 'Rosenau' ? 'selected' : ''}>Rosenau</option>
          <option value="Willys" ${packageInfo.carrier === 'Willys' ? 'selected' : ''}>Willy's</option>
          <option value="JazooExpress" ${packageInfo.carrier === 'JazooExpress' ? 'selected' : ''}>Jazoo Express</option>
          <option value="Kindersley" ${packageInfo.carrier === 'Kindersley' ? 'selected' : ''}>Kindersley</option>
          <option value="BREckels" ${packageInfo.carrier === 'BREckels' ? 'selected' : ''}>B&R Eckel's</option>
          <option value="Grimshaw" ${packageInfo.carrier === 'Grimshaw' ? 'selected' : ''}>Grimshaw</option>
          <!-- Add custom carriers from localStorage -->
          ${getCustomCarriersOptions(packageInfo.carrier)}
          <!-- Add the new options -->
          <option value="DropOff" ${packageInfo.carrier === 'DropOff' ? 'selected' : ''}>Drop Off</option>
          <option value="None" ${packageInfo.carrier === 'None' ? 'selected' : ''}>None</option>
          <option value="AddCarrier" ${packageInfo.carrier === 'AddCarrier' ? 'selected' : ''}>Add Carrier</option>
        </select>
      </div>

      <!-- Tracking Number -->
      <div class="col-span-5">
        <label for="trackingNo" class="block text-xs text-gray-600 mb-1">Tracking No</label>
        <input type="text" id="trackingNo" name="trackingNo" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" placeholder="Enter tracking number" value="${packageInfo.trackingNumber || ''}">
      </div>

      <!-- Track button - Aligned with inputs -->
      <div class="col-span-2">
        <label class="block text-xs text-gray-600 mb-1 opacity-0">Action</label>
        <button id="trackButton" class="h-7 w-full px-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center justify-center shadow-none focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <span class="text-xs">Track</span>
        </button>
      </div>

      <!-- Add Note button - Aligned with inputs -->
      <div class="col-span-2">
        <label class="block text-xs text-gray-600 mb-1 opacity-0">Action</label>
        <button id="toggleNoteBtn" class="h-7 w-full px-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center justify-center shadow-none focus:outline-none">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          <span class="text-xs">Add Note</span>
        </button>
      </div>
    </div>

    <!-- Line 3: Note input (hidden by default) -->
    <div id="packageNote" class="w-full ${packageInfo.note ? '' : 'hidden'} mb-1">
      <div class="flex items-center">
        <input type="text" id="packageNoteText" class="text-xs h-7 w-full border border-gray-300 rounded-md px-1.5 focus:outline-none focus:ring-1 focus:ring-blue-400 focus:border-blue-400" placeholder="Add package note" value="${packageInfo.note || ''}">
        <button id="deleteNoteBtn" class="ml-1.5 px-1.5 h-7 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-150 ${packageInfo.note ? '' : 'hidden'} focus:outline-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Part Details Section - Fixed column widths with specific pixel values -->
  <div class="p-4 bg-gray-50 rounded-xl">
    <h3 class="text-xs font-semibold mb-3 text-gray-700">Part Details</h3>
    <div class="bg-white rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        ${data.parts && data.parts.length > 0 ? `
        <table class="w-full table-fixed divide-y divide-gray-200 text-xs">
          <colgroup>
            <col width="90px"> <!-- Inventory ID column -->
            <col width="230px"> <!-- Description column - specified fixed width -->
            <col width="50px"> <!-- Qty column -->
            <col width="100px"> <!-- Serial Nbr column -->
            <col width="70px"> <!-- Action column (merged Details and Delete) -->
          </colgroup>
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inventory ID</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
              <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Nbr</th>
              <th scope="col" class="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            ${data.parts.map((part, index) => {
              const hasMasterData = part.masterPartData ? true : false;
              
              // Create the main part row
              let rowHtml = `
              <tr class="hover:bg-gray-50 transition-colors duration-150 ${hasMasterData ? 'cursor-pointer' : ''}">
                <td class="px-3 py-2 text-xs text-gray-900 truncate">${part.inventoryID}</td>
                <td class="px-3 py-2 text-xs text-gray-900 truncate">${part.description}</td>
                <td class="px-3 py-2 text-xs text-gray-900 text-center">${part.shippedQuantity}</td>
                <td class="px-3 py-2 text-xs text-gray-900 truncate">${part.lotSerialNumber}</td>
                <td class="px-3 py-2 text-center">
                  <div class="flex justify-center items-center space-x-2">
                    ${hasMasterData ? 
                      `<button class="toggle-part-details p-1 text-blue-600 hover:text-blue-800 focus:outline-none" data-part-id="${part.inventoryID}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                      </button>` : 
                      `<div class="w-6"></div>`
                    }
                    <button class="delete-part-btn p-1 text-red-600 hover:text-red-800 focus:outline-none" data-part-index="${index}">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>`;
              
              // Create details row if master part data exists
              if (hasMasterData) {
                const masterData = part.masterPartData;
                
                // Create detail fields in a responsive grid layout
                let detailFields = '';
                
                // Create fields for each property we want to display
                const fieldsToShow = [
                  { key: 'Country of Origin', value: masterData['Country of Origin'] || 'N/A' },
                  { key: 'HS Code', value: masterData['HS Code'] || 'N/A' },
                  { key: 'Supplier', value: masterData['Supplier'] || 'N/A' },
                  { key: 'Supplier P/N', value: masterData['Supplier P/N'] || 'N/A' },
                  { key: 'UOM', value: masterData['UOM'] || 'N/A' },
                  { key: 'Manufacturer', value: masterData['Manufacturer'] || 'N/A' },
                  { key: 'Manufacturer & Address', value: masterData['Manufacturer & Address'] || 'N/A' },
                  { key: 'Customs Description', value: masterData['Customs Description'] || 'N/A' },
                  { key: 'USMCA/CASMA', value: masterData['USMCA/CASMA'] || 'N/A' },
                  { key: 'Default Price', value: masterData['Default Price'] || 'N/A' },
                  { key: 'Preference Criteria', value: masterData['Preference Criteria'] || 'N/A' },
                  { key: 'NMFC Code', value: masterData['NMFC Code'] || 'N/A' }
                ];
                
                fieldsToShow.forEach(field => {
                  detailFields += `
                    <div class="part-detail-item px-2 py-1">
                      <div class="part-detail-label text-xs font-medium text-gray-700">${field.key}</div>
                      <div class="part-detail-value text-xs text-gray-900">${field.value}</div>
                    </div>
                  `;
                });
                
                // Add the details row
                rowHtml += `
                <tr class="part-details-row hidden">
                  <td colspan="5" class="px-0 py-0 border-t-0">
                    <div class="bg-blue-50 p-3 part-details-container">
                      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                        ${detailFields}
                      </div>
                    </div>
                  </td>
                </tr>`;
              }
              
              return rowHtml;
            }).join("")}
          </tbody>
        </table>
        ` : `
        <div class="p-6 text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No parts in shipment</h3>
          <p class="mt-1 text-sm text-gray-500">All parts have been removed from this shipment.</p>
        </div>
        `}
      </div>
    </div>
  </div>
</div>
`
}

// Setup note toggle functionality
function setupNoteToggleFunctionality(dashboard) {
 console.log("Setting up note toggle functionality")

 const toggleNoteBtn = dashboard.container.querySelector("#toggleNoteBtn")
 const packageNote = dashboard.container.querySelector("#packageNote")
 const packageNoteText = dashboard.container.querySelector("#packageNoteText")
 const deleteNoteBtn = dashboard.container.querySelector("#deleteNoteBtn")

 if (!toggleNoteBtn || !packageNote || !packageNoteText || !deleteNoteBtn) {
   console.error("Note elements not found")
   return
 }

 console.log("All note elements found, setting up event listeners")

 toggleNoteBtn.addEventListener("click", () => {
   console.log("Toggle note button clicked")
   packageNote.classList.toggle("hidden")

   if (packageNote.classList.contains("hidden")) {
     toggleNoteBtn.innerHTML =
       '<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg><span class="text-xs">Add Note</span>'
   } else {
     toggleNoteBtn.innerHTML =
       '<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg><span class="text-xs">Cancel</span>'
     packageNoteText.focus()
   }
 })

 packageNoteText.addEventListener("input", () => {
   deleteNoteBtn.classList.toggle("hidden", !packageNoteText.value.trim())
 })

 deleteNoteBtn.addEventListener("click", () => {
   packageNoteText.value = ""
   deleteNoteBtn.classList.add("hidden")
 })

 console.log("Note functionality setup complete")
}

// Setup shipment method logic
function setupShipmentMethodLogic(dashboard) {
 console.log("Setting up shipment method logic")

 const shipmentMethodSelect = dashboard.container.querySelector("#shipmentMethod")
 const costContainer = dashboard.container.querySelector("#costContainer")

 if (!shipmentMethodSelect || !costContainer) {
   console.error("Shipment method elements not found")
   return
 }

 const updateShipmentMethod = () => {
   const method = shipmentMethodSelect.value

   if (method === "collect") {
     costContainer.classList.add("opacity-50", "pointer-events-none")
     const costInput = costContainer.querySelector("#packageCost")
     if (costInput) {
       costInput.value = ""
       costInput.placeholder = "N/A for collect"
     }
   } else if (method === "customer") {
     costContainer.classList.remove("opacity-50")
     costContainer.classList.add("pointer-events-none")
     const costInput = costContainer.querySelector("#packageCost")
     if (costInput) {
       costInput.value = "$0"
       costInput.classList.add("bg-gray-50")
     }
   } else {
     costContainer.classList.remove("opacity-50", "pointer-events-none")
     const costInput = costContainer.querySelector("#packageCost")
     if (costInput) {
       costInput.classList.remove("bg-gray-50")
       costInput.placeholder = "$20"
     }
   }
 }

 shipmentMethodSelect.addEventListener("change", updateShipmentMethod)
 updateShipmentMethod()
}

// Setup tracking button logic
function setupTrackingButtonLogic(dashboard) {
 console.log("Setting up tracking button logic")

 const trackButton = dashboard.container.querySelector("#trackButton")
 const trackingNoInput = dashboard.container.querySelector("#trackingNo")
 const carrierSelect = dashboard.container.querySelector("#carrier")

 if (!trackButton || !trackingNoInput || !carrierSelect) {
   console.error("Tracking elements not found")
   return
 }

 const trackingUrls = {
   FedEx: "https://www.ship24.com/tracking?p=",
   Loomis: "https://www.ship24.com/tracking?p=",
   Purolator: "https://www.ship24.com/tracking?p=",
   DHL: "https://www.ship24.com/tracking?p=",
   UPS: "https://www.ship24.com/tracking?p=",
   HiWay9: "https://www.hi-way9.com/",
   Manitoulin: "https://www.mtdirect.ca/MANITOULIN/pages/PROBILL?output=5&probill=",
   Rosenau: "https://www.rosenau.ca/track-shipment/",
   Willys: "https://www.hi-way9.com/",
   JazooExpress: "http://jazoocourier.com/",
   Kindersley: "https://www.kindersleytransport.com/",
   BREckels: "http://trace.breckels.com/ImagingDB/top/interface-degama/webTraceRoot/index.php",
   Grimshaw: "https://www.hi-way9.com/",
   DropOff: "",
   None: ""
 }
 
 // Load custom carrier tracking URLs from localStorage
 try {
   const customTrackingUrls = JSON.parse(localStorage.getItem('customTrackingUrls') || '{}');
   Object.assign(trackingUrls, customTrackingUrls);
 } catch (e) {
   console.error("Error loading custom tracking URLs", e);
 }

 carrierSelect.addEventListener("change", () => {
   const carrier = carrierSelect.value
   
   // Handle the "Add Carrier" special option
   if (carrier === "AddCarrier") {
     // Prompt user for custom carrier name
     const carrierName = prompt("Enter the carrier name:");
     
     if (carrierName && carrierName.trim() !== "") {
       // Prompt for tracking URL with improved help text
       const trackingUrl = prompt(
         "Enter the tracking URL for this carrier:\n\n" +
         "Examples:\n" +
         "• https://www.tforce.com/{tracking} - Use {tracking} placeholder\n" +
         "• https://example.com/track?num=\n" +
         "• https://example.com/tracking/\n\n" +
         "The tracking number will be appended to this URL or replace {tracking}.", 
         "https://www.tforce.com/{tracking}"
       );
       
       // Save the custom carrier
       const customCarriers = JSON.parse(localStorage.getItem('customCarriers') || '[]');
       if (!customCarriers.includes(carrierName)) {
         customCarriers.push(carrierName);
         localStorage.setItem('customCarriers', JSON.stringify(customCarriers));
       }
       
       // Save the tracking URL if provided
       if (trackingUrl) {
         const customTrackingUrls = JSON.parse(localStorage.getItem('customTrackingUrls') || '{}');
         customTrackingUrls[carrierName] = trackingUrl;
         localStorage.setItem('customTrackingUrls', JSON.stringify(customTrackingUrls));
         trackingUrls[carrierName] = trackingUrl;
       }
       
       // Rebuild the dropdown to include the new carrier
       const currentOptions = Array.from(carrierSelect.options).map(opt => ({
         value: opt.value,
         text: opt.text,
         selected: false
       }));
       
       // Remove "Add Carrier" option (we'll add it back at the end)
       const optionsWithoutAddCarrier = currentOptions.filter(opt => opt.value !== "AddCarrier");
       
       // Add the new carrier option
       optionsWithoutAddCarrier.push({
         value: carrierName,
         text: carrierName,
         selected: true
       });
       
       // Add "Add Carrier" option back
       optionsWithoutAddCarrier.push({
         value: "AddCarrier",
         text: "➕ Add Carrier",
         selected: false
       });
       
       // Rebuild the select element
       carrierSelect.innerHTML = '';
       optionsWithoutAddCarrier.forEach(opt => {
         const option = document.createElement('option');
         option.value = opt.value;
         option.text = opt.text;
         option.selected = opt.selected;
         carrierSelect.appendChild(option);
       });
     } else {
       // User cancelled or entered empty string, default back to FedEx
       carrierSelect.value = "FedEx";
     }
   }
   
   // Update placeholder and button state
   const selectedCarrier = carrierSelect.value
   
   if (selectedCarrier === "FedEx" || selectedCarrier === "UPS" || selectedCarrier === "DHL") {
     trackingNoInput.placeholder = "Enter tracking number"
   } else if (selectedCarrier === "Manitoulin") {
     trackingNoInput.placeholder = "Enter bill number"
   } else if (selectedCarrier === "DropOff" || selectedCarrier === "None") {
     trackingNoInput.placeholder = "No tracking required"
     trackingNoInput.value = "";
   } else {
     trackingNoInput.placeholder = "Enter tracking number"
   }
   updateTrackButtonState()
 })

 const updateTrackButtonState = () => {
   const trackingNo = trackingNoInput.value.trim()
   const carrier = carrierSelect.value
   
   // Disable button for Drop Off and None carriers, or if there's no tracking number
   const isDisabled = !trackingNo || carrier === "DropOff" || carrier === "None"
   
   trackButton.disabled = isDisabled
   if (isDisabled) {
     trackButton.classList.add("opacity-50", "cursor-not-allowed")
   } else {
     trackButton.classList.remove("opacity-50", "cursor-not-allowed")
   }
 }

 trackButton.addEventListener("click", (e) => {
   e.preventDefault()
   const carrier = carrierSelect.value
   const trackingNo = trackingNoInput.value.trim()

   if (!trackingNo || !carrier) return

   // If carrier is Drop Off or None, don't open tracking
   if (carrier === "DropOff" || carrier === "None") {
     dashboard.addNotification("No tracking available for this carrier option", "info")
     return
   }

   // Get tracking URL for this carrier
   const baseUrl = trackingUrls[carrier]
   
   // If no tracking URL is available, show notification
   if (!baseUrl) {
     dashboard.addNotification(`No tracking URL configured for ${carrier}`, "warning")
     return
   }

   // Set up the final tracking URL
   let trackingUrl = baseUrl
   
   // Handle different URL formats based on carrier
   if (["FedEx", "UPS", "DHL", "Loomis", "Purolator"].includes(carrier)) {
     // For standard carriers using ship24.com
     trackingUrl = "https://www.ship24.com/tracking?p=" + trackingNo
   } else if (carrier === "Manitoulin" && baseUrl.includes("probill=")) {
     // For Manitoulin's special format
     trackingUrl = baseUrl + trackingNo
   } else {
     // For custom carriers - use exact URL format from the user input
     // First check if there are placeholders in the URL
     if (baseUrl.includes("{tracking}")) {
       // Replace the placeholder with the tracking number
       trackingUrl = baseUrl.replace("{tracking}", trackingNo)
     } else if (baseUrl.includes("?") && !baseUrl.endsWith("?")) {
       // URL already has parameters
       trackingUrl = baseUrl + (baseUrl.includes("=") ? "&" : "") + "tracking=" + trackingNo
     } else if (baseUrl.endsWith("?")) {
       // URL ends with ? - append tracking directly
       trackingUrl = baseUrl + trackingNo
     } else if (baseUrl.endsWith("/")) {
       // URL ends with / - append tracking directly
       trackingUrl = baseUrl + trackingNo
     } else {
       // Otherwise add as a path segment
       trackingUrl = baseUrl + "/" + trackingNo
     }
   }

   console.log("Opening tracking URL:", trackingUrl);

   // Save tracking info to dashboard state
   const updatedData = {
     ...dashboard.currentPreviewData,
     packageInfo: {
       ...dashboard.currentPreviewData.packageInfo,
       trackingNo,
       carrier,
       trackingUrl // Save the full tracking URL to be used by other components
     }
   }
   dashboard.currentPreviewData = updatedData

   // Open tracking url in new tab
   window.open(trackingUrl, "_blank")
 })

 trackingNoInput.addEventListener("input", updateTrackButtonState)
 updateTrackButtonState()
}

// Helper function to get custom carriers from localStorage
function getCustomCarriersOptions(currentCarrier) {
 const customCarriers = localStorage.getItem('customCarriers') || '[]';
 const carriers = JSON.parse(customCarriers);

 let options = '';
 carriers.forEach(carrier => {
   options += `<option value="${carrier}" ${currentCarrier === carrier ? 'selected' : ''}>${carrier}</option>`;
 });

 return options;
}

// Function to delete a part from the preview
function deletePartFromPreview(dashboard, partIndex) {
  // Get current data
  const currentData = { ...dashboard.currentPreviewData };
  
  if (!currentData || !currentData.parts || !Array.isArray(currentData.parts)) {
    dashboard.addNotification("Cannot delete part: Invalid data structure", "danger");
    return;
  }
  
  // Check if index is valid
  if (partIndex < 0 || partIndex >= currentData.parts.length) {
    dashboard.addNotification("Cannot delete part: Invalid part index", "danger");
    return;
  }
  
  // Get part details for notification
  const partToDelete = currentData.parts[partIndex];
  const partId = partToDelete.inventoryID;
  const partDesc = partToDelete.description;
  
  // Confirm deletion
  if (confirm(`Are you sure you want to remove part ${partId} (${partDesc}) from this shipment?`)) {
    // Remove the part
    currentData.parts.splice(partIndex, 1);
    
    // Save the updated data
    dashboard.savePreviewData(currentData);
    
    // Refresh the preview table
    showPreviewTable(dashboard, currentData);
    
    // Show success notification
    dashboard.addNotification(`Part ${partId} has been removed from the shipment`, "success");
  }
}

// Then directly define the initProcessEvents function without re-importing setupShipOrder
export function initProcessEvents(dashboard) {
  // Call all the setup functions for each button/action
  setupFetchData(dashboard);
  setupShProcess(dashboard);
  setupPoProcess(dashboard);
  setupSoProcess(dashboard);
  setupPartLookup(dashboard);
  setupValidateData(dashboard);
  setupSaveProcess(dashboard);
  setupClearForm(dashboard);
  setupPrintBtn(dashboard);
  setupBackToHistoryBtn(dashboard);
  setupTrackingInfo(dashboard);
  setupShipmentInfoToggle(dashboard);
  setupShipOrder(dashboard);
}