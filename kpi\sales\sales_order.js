// Sales Order component for Sales KPI Dashboard
export class SalesOrderComponent {
  constructor(container) {
    this.container = container;
    this.salesOrders = [];
    this.filteredSalesOrders = [];
    this.currentPage = 1;
    this.pageSize = 10;
    this.sortField = 'orderNbr';
    this.sortDirection = 'desc';
    this.searchTerm = '';
    this.isLoading = false;
    this.dbName = 'salesKpiDb';
    this.storeName = 'salesOrders';
    
    // Import the ConnectionManager
    try {
      import('../../core/connection.js').then(module => {
        this.connectionManager = module.connectionManager;
      }).catch(error => {
        console.error('Error importing ConnectionManager:', error);
      });
    } catch (error) {
      console.error('Error importing ConnectionManager:', error);
    }
  }

  async init() {
    try {
      // Initialize the database
      await this.initDatabase();
      
      // Render the initial UI
      this.render();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load sales order data
      await this.loadSalesOrderData(true); // Force refresh on first load
    } catch (error) {
      console.error('Error initializing Sales Order component:', error);
      this.showError('Failed to initialize the Sales Order component. Please try again later.');
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName, 1);
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create salesOrders object store if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.log('Creating salesOrders store in IndexedDB');
            const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
            store.createIndex('orderNbr', 'orderNbr', { unique: true });
            store.createIndex('date', 'date', { unique: false });
            store.createIndex('status', 'status', { unique: false });
            store.createIndex('customerID', 'customerID', { unique: false });
          } else {
            console.log('SalesOrders store already exists in IndexedDB');
          }
          
          // Also make sure we have a customers store for lookup
          if (!db.objectStoreNames.contains('customers')) {
            console.log('Creating customers store in IndexedDB from sales_order.js');
            const customerStore = db.createObjectStore('customers', { keyPath: 'id' });
            customerStore.createIndex('customerID', 'customerID', { unique: true });
            customerStore.createIndex('customerName', 'customerName', { unique: false });
          } else {
            console.log('Customers store already exists in IndexedDB');
          }
        };
        
        request.onsuccess = (event) => {
          console.log('IndexedDB connection established successfully for SalesOrder');
          resolve(event.target.result);
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB:', event.target.error);
          // Instead of rejecting, resolve with null to allow fallback to mock data
          console.log('Falling back to mock data due to IndexedDB error');
          resolve(null);
        };
      } catch (error) {
        console.error('Exception in initDatabase:', error);
        // Resolve with null instead of rejecting to allow component to continue
        resolve(null);
      }
    });
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold">Sales Orders</h2>
          <div class="flex items-center space-x-2">
            <div class="relative">
              <input 
                type="text" 
                id="salesOrderSearch" 
                placeholder="Search sales orders..." 
                class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 pl-9 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
              <div class="absolute left-3 top-2.5 text-gray-400">
                <i class="fas fa-search"></i>
              </div>
              <button id="clearSearchBtn" class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hidden">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <button id="dateRangeBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Date Range">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </button>
            <button id="refreshSalesOrdersBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Refresh Data">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </button>
            <button id="exportSalesOrdersBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Export Data">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
            </button>
            <button id="settingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 38px; width: 38px;" title="Settings">
              <svg style="height: 18px; width: 18px;" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Table View -->
        <div id="tableView" class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderNbr">
                    <div class="flex items-center">
                      Order #
                      <span class="sort-icon ml-1"><i class="fas fa-sort-down"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="date">
                    <div class="flex items-center">
                      Date
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="customerName">
                    <div class="flex items-center">
                      Customer
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="status">
                    <div class="flex items-center">
                      Status
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="orderTotal">
                    <div class="flex items-center">
                      Total Amount
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="itemCount">
                    <div class="flex items-center">
                      Items
                      <span class="sort-icon ml-1"><i class="fas fa-sort"></i></span>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody id="salesOrdersTableBody" class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <!-- Table content will be dynamically loaded -->
              </tbody>
            </table>
          </div>
          
          <!-- Loading Indicator -->
          <div id="salesOrdersLoadingIndicator" class="flex justify-center items-center p-10 hidden">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
          </div>
          
          <!-- Empty State -->
          <div id="salesOrdersEmptyState" class="flex flex-col justify-center items-center p-10 hidden">
            <i class="fas fa-file-invoice-dollar text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
            <p class="text-gray-500 dark:text-gray-400">No sales order data available</p>
            <button id="loadSalesOrdersBtn" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Load Sales Orders
            </button>
          </div>
          
          <!-- Error State -->
          <div id="salesOrdersErrorState" class="hidden p-6">
            <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
              <div class="flex items-center">
                <div class="flex-shrink-0 text-red-500 dark:text-red-400">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="ml-3">
                  <p id="errorMessage" class="text-sm text-red-800 dark:text-red-200">
                    Error loading sales order data.
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Pagination -->
          <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
              <button id="mobilePagePrev" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Previous
              </button>
              <button id="mobilePageNext" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                Next
              </button>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                  Showing
                  <span id="paginationStart" class="font-medium">1</span>
                  to
                  <span id="paginationEnd" class="font-medium">10</span>
                  of
                  <span id="paginationTotal" class="font-medium">0</span>
                  results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button id="pageFirst" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">First</span>
                    <i class="fas fa-angle-double-left"></i>
                  </button>
                  <button id="pagePrev" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Previous</span>
                    <i class="fas fa-angle-left"></i>
                  </button>
                  <span id="pageNumbers" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                    1 of 1
                  </span>
                  <button id="pageNext" class="relative inline-flex items-center px-2 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Next</span>
                    <i class="fas fa-angle-right"></i>
                  </button>
                  <button id="pageLast" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <span class="sr-only">Last</span>
                    <i class="fas fa-angle-double-right"></i>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Show loading indicator initially
    this.showLoading();
  }

  setupEventListeners() {
    // Table sorting
    const tableHeaders = this.container.querySelectorAll('th[data-sort]');
    tableHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        this.sortTable(field);
      });
    });
    
    // Search input
    const searchInput = this.container.querySelector('#salesOrderSearch');
    if (searchInput) {
      searchInput.addEventListener('input', () => {
        this.searchTerm = searchInput.value;
        this.updateClearButton();
        this.applySearch();
      });
    }
    
    // Clear search button
    const clearSearchBtn = this.container.querySelector('#clearSearchBtn');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        const searchInput = this.container.querySelector('#salesOrderSearch');
        if (searchInput) {
          searchInput.value = '';
          this.searchTerm = '';
          this.updateClearButton();
          this.applySearch();
        }
      });
    }
    
    // Date range button
    const dateRangeBtn = this.container.querySelector('#dateRangeBtn');
    if (dateRangeBtn) {
      dateRangeBtn.addEventListener('click', () => this.showDateRangePicker());
    }
    
    // Refresh button
    const refreshBtn = this.container.querySelector('#refreshSalesOrdersBtn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadSalesOrderData(true));
    }
    
    // Export button
    const exportBtn = this.container.querySelector('#exportSalesOrdersBtn');
    if (exportBtn) {
      exportBtn.addEventListener('click', () => this.exportSalesOrderData());
    }
    
    // Settings button
    const settingsBtn = this.container.querySelector('#settingsBtn');
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.showSettings());
    }
    
    // Load sales orders button (on empty state)
    const loadSalesOrdersBtn = this.container.querySelector('#loadSalesOrdersBtn');
    if (loadSalesOrdersBtn) {
      loadSalesOrdersBtn.addEventListener('click', () => this.loadSalesOrderData(true));
    }
    
    // Pagination buttons
    const paginationButtons = {
      first: this.container.querySelector('#pageFirst'),
      prev: this.container.querySelector('#pagePrev'),
      next: this.container.querySelector('#pageNext'),
      last: this.container.querySelector('#pageLast'),
      mobilePrev: this.container.querySelector('#mobilePagePrev'),
      mobileNext: this.container.querySelector('#mobilePageNext')
    };
    
    if (paginationButtons.first) {
      paginationButtons.first.addEventListener('click', () => this.goToPage(1));
    }
    
    if (paginationButtons.prev) {
      paginationButtons.prev.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    }
    
    if (paginationButtons.next) {
      paginationButtons.next.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    }
    
    if (paginationButtons.last) {
      paginationButtons.last.addEventListener('click', () => this.goToPage(this.getTotalPages()));
    }
    
    if (paginationButtons.mobilePrev) {
      paginationButtons.mobilePrev.addEventListener('click', () => this.goToPage(this.currentPage - 1));
    }
    
    if (paginationButtons.mobileNext) {
      paginationButtons.mobileNext.addEventListener('click', () => this.goToPage(this.currentPage + 1));
    }
  }

  async loadSalesOrderData(forceRefresh = false) {
    try {
      this.showLoading();
      
      // If not forcing refresh, try to load from IndexedDB first
      if (!forceRefresh) {
        try {
          const cachedOrders = await this.getSalesOrdersFromIndexedDB();
          if (cachedOrders && cachedOrders.length > 0) {
            console.log(`Loaded ${cachedOrders.length} sales orders from IndexedDB`);
            this.salesOrders = cachedOrders;
            await this.lookupCustomerNames();
            this.applySearch(); // This will set filteredSalesOrders and render the table
            return;
          }
        } catch (dbError) {
          console.warn('Error loading from IndexedDB, will try API or fallback to mock data:', dbError);
          // Continue with other methods if IndexedDB fails
        }
      }
      
      // Get connection status to check if connected to Acumatica
      if (!this.connectionManager) {
        // Try to import again if it failed the first time
        try {
          const module = await import('../../core/connection.js');
          this.connectionManager = module.connectionManager;
        } catch (error) {
          console.error('Error importing ConnectionManager on refresh:', error);
          console.warn('Generating mock data for demo purposes due to ConnectionManager import failure.');
          const mockOrders = this.generateMockSalesOrderData();
          this.salesOrders = mockOrders;
          await this.lookupCustomerNames();
          this.applySearch();
          this.showError('Using generated data. Connection error: Connection manager not available.');
          return;
        }
      }
      
      // Check if connected to Acumatica
      const connectionStatus = this.connectionManager.getConnectionStatus();
      if (!connectionStatus.acumatica.isConnected) {
        console.warn('Not connected to Acumatica. Generating mock data for demo purposes.');
        const mockOrders = this.generateMockSalesOrderData();
        this.salesOrders = mockOrders;
        await this.lookupCustomerNames();
        this.applySearch();
        this.showError('Not connected to Acumatica. Using generated data for demo purposes.');
        return;
      }
      
      // Get Acumatica instance from connection manager
      const instance = connectionStatus.acumatica.instance;
      if (!instance) {
        console.warn('Acumatica instance URL not found. Generating mock data for demo purposes.');
        const mockOrders = this.generateMockSalesOrderData();
        this.salesOrders = mockOrders;
        await this.lookupCustomerNames();
        this.applySearch();
        this.showError('Acumatica instance URL not found. Using generated data for demo purposes.');
        return;
      }
      
      try {
        // Make the API call to get sales orders
        const salesOrderData = await this.fetchAcumaticaSalesOrders(instance);
        
        if (!salesOrderData || !salesOrderData.length) {
          console.warn('No sales order data received from Acumatica. Generating mock data for demo purposes.');
          const mockOrders = this.generateMockSalesOrderData();
          this.salesOrders = mockOrders;
          await this.lookupCustomerNames();
          this.applySearch();
          this.showError('No sales order data received from Acumatica. Using generated data for demo purposes.');
          return;
        }
        
        // Parse and store sales order data
        const parsedOrders = this.parseAcumaticaSalesOrders(salesOrderData);
        
        // Store in IndexedDB for future use
        try {
          await this.storeSalesOrdersInIndexedDB(parsedOrders);
        } catch (dbError) {
          console.warn('Failed to store sales orders in IndexedDB:', dbError);
          // Continue anyway, just won't be cached
        }
        
        this.salesOrders = parsedOrders;
        await this.lookupCustomerNames();
        this.applySearch(); // This will filter and render the table
      } catch (error) {
        console.error('Error fetching sales orders from Acumatica:', error);
        
        // If API call fails, try to load from IndexedDB as a fallback
        try {
          const cachedOrders = await this.getSalesOrdersFromIndexedDB();
          if (cachedOrders && cachedOrders.length > 0) {
            this.salesOrders = cachedOrders;
            await this.lookupCustomerNames();
            this.applySearch();
            this.showError(`Using cached data. Failed to refresh: ${error.message}`);
            return;
          }
        } catch (dbError) {
          console.warn('Failed to fall back to cached data:', dbError);
          // Continue to mock data generation
        }
        
        // If no cached data, show error and generate mock data for demo
        console.warn('No cached data available. Generating mock data for demo purposes.');
        const mockOrders = this.generateMockSalesOrderData();
        this.salesOrders = mockOrders;
        await this.lookupCustomerNames();
        this.applySearch();
        this.showError(`Using generated data. API error: ${error.message}`);
      }
    } catch (error) {
      console.error('Error loading sales order data:', error);
      // Final fallback to mock data if everything else fails
      const mockOrders = this.generateMockSalesOrderData();
      this.salesOrders = mockOrders;
      await this.lookupCustomerNames();
      this.applySearch();
      this.showError(`Failed to load sales order data. Using mock data. Error: ${error.message}`);
    }
  }

  // Fetch sales orders from Acumatica API
  async fetchAcumaticaSalesOrders(instance) {
    // Construct the API URL
    const apiUrl = `${instance}/entity/default/22.200.001/SalesOrder?$expand=Details,Shipments&$filter=Date ge datetimeoffset'2024-01-01T00:00:00Z' and Date lt datetimeoffset'2026-01-01T00:00:00Z'`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies in the request
      });
      
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`API call failed with status ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Acumatica API request failed:', error);
      throw error;
    }
  }

  // Parse Acumatica sales order data into a format that works with our UI
  parseAcumaticaSalesOrders(salesOrderData) {
    return salesOrderData.map(order => {
      // Extract line items
      const details = order.Details || [];
      const itemCount = details.length;
      
      // Calculate totals if needed
      const orderTotal = order.OrderTotal?.value || 0;
      
      // Extract the date
      const orderDate = order.Date?.value ? new Date(order.Date.value) : new Date();
      
      // Extract the requested date
      const requestedDate = order.RequestedOn?.value ? new Date(order.RequestedOn.value) : null;
      
      // Extract shipments
      const shipments = (order.Shipments || []).map(shipment => ({
        id: shipment.id || `SH${Math.random().toString(36).substr(2, 9)}`,
        shipmentNbr: shipment.ShipmentNbr?.value || 'N/A',
        shipmentDate: shipment.ShipmentDate?.value ? new Date(shipment.ShipmentDate.value) : null,
        shipmentType: shipment.ShipmentType?.value || 'Shipment',
        invoiceNbr: shipment.InvoiceNbr?.value || 'N/A',
        invoiceType: shipment.InvoiceType?.value || 'N/A',
        status: shipment.Status?.value || 'Unknown',
        shippedQty: shipment.ShippedQty?.value || 0,
        shippedWeight: shipment.ShippedWeight?.value || 0,
        shippedVolume: shipment.ShippedVolume?.value || 0
      }));
      
      // Extract the values we need
      return {
        id: order.id || order.NoteID?.value || `SO${Math.random().toString(36).substr(2, 9)}`,
        orderNbr: order.OrderNbr?.value || 'N/A',
        orderType: order.OrderType?.value || 'SO',
        date: orderDate,
        formattedDate: orderDate.toLocaleDateString(),
        status: order.Status?.value || 'Unknown',
        customerID: order.CustomerID?.value || 'N/A',
        customerName: 'Loading...', // Will be filled in later by customer lookup
        customerOrder: order.CustomerOrder?.value || '',
        contactID: order.ContactID?.value || '',
        orderTotal: orderTotal,
        formattedTotal: this.formatCurrency(orderTotal, order.CurrencyID?.value || 'USD'),
        currencyID: order.CurrencyID?.value || 'USD',
        baseCurrencyID: order.BaseCurrencyID?.value || 'USD',
        itemCount: itemCount,
        description: order.Description?.value || '',
        note: order.note?.value || '',
        willCall: order.WillCall?.value || false,
        taxTotal: order.TaxTotal?.value || 0,
        hold: order.Hold?.value || false,
        project: order.Project?.value || '',
        location: order.LocationID?.value || '',
        branch: order.Branch?.value || '',
        approved: order.Approved?.value || false,
        creditHold: order.CreditHold?.value || false,
        paymentMethod: order.PaymentMethod?.value || '',
        paymentRef: order.PaymentRef?.value || '',
        externalRef: order.ExternalRef?.value || '',
        createdDate: order.CreatedDate?.value ? new Date(order.CreatedDate.value) : null,
        lastModified: order.LastModified?.value ? new Date(order.LastModified.value) : null,
        effectiveDate: order.EffectiveDate?.value ? new Date(order.EffectiveDate.value) : null,
        requestedDate: requestedDate,
        requestedDateFormatted: requestedDate ? requestedDate.toLocaleDateString() : 'N/A',
        shipments: shipments,
        details: details.map(item => ({
          id: item.id || `SOLN${Math.random().toString(36).substr(2, 9)}`,
          lineNbr: item.LineNbr?.value || 0,
          inventoryID: item.InventoryID?.value || '',
          description: item.LineDescription?.value || '',
          lineType: item.LineType?.value || '',
          quantity: item.OrderQty?.value || 0,
          openQty: item.OpenQty?.value || 0,
          qtyOnShipments: item.QtyOnShipments?.value || 0,
          unitPrice: item.UnitPrice?.value || 0,
          unitCost: item.UnitCost?.value || 0,
          extendedPrice: item.ExtendedPrice?.value || 0,
          amount: item.Amount?.value || 0,
          discountPercent: item.DiscountPercent?.value || 0,
          discountAmount: item.DiscountAmount?.value || 0,
          discountCode: item.DiscountCode?.value || '',
          discountedUnitPrice: item.DiscountedUnitPrice?.value || 0,
          unbilledAmount: item.UnbilledAmount?.value || 0,
          warehouse: item.WarehouseID?.value || '',
          uom: item.UOM?.value || '',
          note: item.note?.value || '',
          completed: item.Completed?.value || false,
          requestedOn: item.RequestedOn?.value ? new Date(item.RequestedOn.value) : null,
          shipOn: item.ShipOn?.value ? new Date(item.ShipOn.value) : null,
          account: item.Account?.value || '',
          taxCategory: item.TaxCategory?.value || '',
          taxZone: item.TaxZone?.value || ''
        })),
        // Store the full original data for detailed view
        rawData: order
      };
    });
  }

  async getSalesOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          
          // Check if the store exists before trying to access it
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.warn(`Store ${this.storeName} does not exist in database ${this.dbName}`);
            resolve([]);
            return;
          }
          
          try {
            const transaction = db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const getAllRequest = store.getAll();
            
            getAllRequest.onsuccess = () => {
              resolve(getAllRequest.result);
            };
            
            getAllRequest.onerror = (error) => {
              console.error('Error getting all sales orders:', error);
              resolve([]); // Resolve with empty array instead of rejecting
            };
          } catch (transactionError) {
            console.error('Transaction error in getSalesOrdersFromIndexedDB:', transactionError);
            resolve([]); // Resolve with empty array instead of rejecting
          }
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB in getSalesOrdersFromIndexedDB:', event.target.error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
      } catch (error) {
        console.error('Exception in getSalesOrdersFromIndexedDB:', error);
        resolve([]); // Resolve with empty array instead of rejecting
      }
    });
  }

  async storeSalesOrdersInIndexedDB(salesOrders) {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          
          // Check if the store exists
          if (!db.objectStoreNames.contains(this.storeName)) {
            console.warn(`Store ${this.storeName} does not exist, cannot store sales orders`);
            resolve();
            return;
          }
          
          try {
            const transaction = db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            
            // Clear existing data
            store.clear();
            
            // Add new data
            salesOrders.forEach(order => {
              store.add(order);
            });
            
            transaction.oncomplete = () => {
              resolve();
            };
            
            transaction.onerror = (error) => {
              console.error('Error storing sales orders in IndexedDB:', error);
              resolve(); // Resolve anyway to avoid blocking the UI
            };
          } catch (transactionError) {
            console.error('Transaction error in storeSalesOrdersInIndexedDB:', transactionError);
            resolve(); // Resolve anyway to avoid blocking the UI
          }
        };
        
        request.onerror = (event) => {
          console.error('Error opening IndexedDB in storeSalesOrdersInIndexedDB:', event.target.error);
          resolve(); // Resolve anyway to avoid blocking the UI
        };
      } catch (error) {
        console.error('Exception in storeSalesOrdersInIndexedDB:', error);
        resolve(); // Resolve anyway to avoid blocking the UI
      }
    });
  }

  async lookupCustomerNames() {
    // Get customer ID to name mapping from IndexedDB
    try {
      const db = await this.openDatabase();
      
      // If db is null, skip the lookup and use basic customer info
      if (!db) {
        console.warn('Database not available for customer name lookup');
        this.salesOrders = this.salesOrders.map(order => {
          return {
            ...order,
            customerName: `Customer ${order.customerID}`
          };
        });
        return;
      }
      
      const customers = await this.getCustomersFromDB(db);
      
      // Create a lookup map
      const customerMap = {};
      customers.forEach(customer => {
        customerMap[customer.customerID] = customer.customerName;
      });
      
      // Update salesOrders with customer names
      this.salesOrders = this.salesOrders.map(order => {
        return {
          ...order,
          customerName: customerMap[order.customerID] || `Customer ${order.customerID}`
        };
      });
    } catch (error) {
      console.error('Error looking up customer names:', error);
      // If lookup fails, just use the customer ID
      this.salesOrders = this.salesOrders.map(order => {
        return {
          ...order,
          customerName: `Customer ${order.customerID}`
        };
      });
    }
  }

  async openDatabase() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.dbName);
        
        request.onsuccess = event => resolve(event.target.result);
        
        request.onerror = event => {
          console.error('Error opening database in openDatabase:', event.target.error);
          // Return null instead of rejecting to allow fallback
          resolve(null);
        };
      } catch (error) {
        console.error('Exception in openDatabase:', error);
        resolve(null);
      }
    });
  }

  async getCustomersFromDB(db) {
    return new Promise((resolve, reject) => {
      try {
        if (!db) {
          console.warn('Database is null in getCustomersFromDB');
          resolve([]);
          return;
        }
        
        if (!db.objectStoreNames.contains('customers')) {
          console.warn('Customers store not found in DB');
          resolve([]);
          return;
        }
        
        const transaction = db.transaction(['customers'], 'readonly');
        const store = transaction.objectStore('customers');
        const request = store.getAll();
        
        request.onsuccess = () => {
          resolve(request.result);
        };
        
        request.onerror = (error) => {
          console.error('Error in getCustomersFromDB request:', error);
          resolve([]); // Resolve with empty array instead of rejecting
        };
      } catch (error) {
        console.error('Error in getCustomersFromDB:', error);
        resolve([]); // Resolve with empty array instead of rejecting
      }
    });
  }

  formatCurrency(amount, currencyCode = 'USD') {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: currencyCode
    }).format(amount);
  }

  // Generate mock sales order data for demo purposes
  generateMockSalesOrderData() {
    const statusOptions = ['Open', 'Closed', 'Cancelled', 'On Hold', 'Credit Hold', 'Pending Approval'];
    const orders = [];
    
    for (let i = 1; i <= 100; i++) {
      const orderNbr = '00' + (1000 + i).toString();
      const randomStatus = statusOptions[Math.floor(Math.random() * statusOptions.length)];
      const randomAmount = Math.floor(Math.random() * 1000000) / 100;
      const randomDate = new Date(2024, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      const randomItemCount = Math.floor(Math.random() * 10) + 1;
      const customerId = 'CUST' + (Math.floor(Math.random() * 100) + 1).toString().padStart(3, '0');
      
      orders.push({
        id: 'SO' + i.toString().padStart(5, '0'),
        orderNbr: orderNbr,
        orderType: 'SO',
        date: randomDate,
        formattedDate: randomDate.toLocaleDateString(),
        status: randomStatus,
        customerID: customerId,
        customerName: 'Loading...',
        orderTotal: randomAmount,
        formattedTotal: this.formatCurrency(randomAmount),
        itemCount: randomItemCount,
        description: `Mock order ${orderNbr}`,
        note: `This is a mock order for demonstration purposes.`,
        willCall: Math.random() > 0.8,
        taxTotal: randomAmount * 0.1,
        hold: randomStatus === 'On Hold' || randomStatus === 'Credit Hold',
        branch: 'MAIN',
        requestedDate: new Date(randomDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000),
        requestedDateFormatted: new Date(randomDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        details: Array(randomItemCount).fill(0).map((_, j) => ({
          id: `SOLN${i}_${j}`,
          lineNbr: j + 1,
          inventoryID: `ITEM-${Math.floor(Math.random() * 1000) + 1}`,
          description: `Item description for line ${j + 1}`,
          quantity: Math.floor(Math.random() * 10) + 1,
          unitPrice: Math.floor(Math.random() * 10000) / 100,
          extendedPrice: Math.floor(Math.random() * 100000) / 100,
          discountPercent: Math.floor(Math.random() * 20),
          discountAmount: Math.floor(Math.random() * 5000) / 100,
          warehouse: 'MAIN'
        }))
      });
    }
    
    return orders;
  }

  sortTable(field, toggleDirection = true) {
    // If the same field is clicked, toggle sort direction
    if (field === this.sortField && toggleDirection) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      // Default direction based on field type
      if (field === 'orderTotal' || field === 'itemCount' || field === 'date') {
        this.sortDirection = 'desc'; // Default to descending for numeric and date fields
      } else {
        this.sortDirection = 'asc'; // Default to ascending for text fields
      }
    }
    
    // Update column headers to show sort direction
    const tableHeaders = this.container.querySelectorAll('th[data-sort]');
    tableHeaders.forEach(header => {
      const sortField = header.getAttribute('data-sort');
      const sortIcon = header.querySelector('.sort-icon i');
      
      if (sortIcon) {
        if (sortField === this.sortField) {
          sortIcon.className = this.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        } else {
          sortIcon.className = 'fas fa-sort';
        }
      }
    });
    
    // Apply search/filter without calling applySearch (which would create a circular call)
    const searchTerm = this.searchTerm.toLowerCase();
    
    // Filter sales orders based on search term
    this.filteredSalesOrders = this.salesOrders.filter(order => {
      if (!searchTerm) return true;
      
      return (
        order.orderNbr.toLowerCase().includes(searchTerm) ||
        order.customerName.toLowerCase().includes(searchTerm) ||
        order.status.toLowerCase().includes(searchTerm) ||
        (order.description && order.description.toLowerCase().includes(searchTerm)) ||
        (order.formattedTotal && order.formattedTotal.includes(searchTerm))
      );
    });
    
    // Sort filtered orders directly
    this.filteredSalesOrders.sort((a, b) => {
      let valueA = a[this.sortField];
      let valueB = b[this.sortField];
      
      // Handle special cases for date sorting
      if (this.sortField === 'date') {
        valueA = new Date(valueA);
        valueB = new Date(valueB);
      }
      
      // Handle numeric sorting
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle date sorting
      if (valueA instanceof Date && valueB instanceof Date) {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle string sorting
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortDirection === 'asc' 
          ? valueA.localeCompare(valueB) 
          : valueB.localeCompare(valueA);
      }
      
      return 0;
    });
    
    // Go to first page and update the table
    this.currentPage = 1;
    this.updateTable();
  }

  applySearch() {
    const searchTerm = this.searchTerm.toLowerCase();
    
    // Filter sales orders based on search term
    this.filteredSalesOrders = this.salesOrders.filter(order => {
      if (!searchTerm) return true;
      
      return (
        order.orderNbr.toLowerCase().includes(searchTerm) ||
        order.customerName.toLowerCase().includes(searchTerm) ||
        order.status.toLowerCase().includes(searchTerm) ||
        (order.description && order.description.toLowerCase().includes(searchTerm)) ||
        (order.formattedTotal && order.formattedTotal.includes(searchTerm))
      );
    });
    
    // Apply sorting directly without calling sortTable (which would create an infinite loop)
    this.filteredSalesOrders.sort((a, b) => {
      let valueA = a[this.sortField];
      let valueB = b[this.sortField];
      
      // Handle special cases for date sorting
      if (this.sortField === 'date') {
        valueA = new Date(valueA);
        valueB = new Date(valueB);
      }
      
      // Handle numeric sorting
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle date sorting
      if (valueA instanceof Date && valueB instanceof Date) {
        return this.sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
      }
      
      // Handle string sorting
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return this.sortDirection === 'asc' 
          ? valueA.localeCompare(valueB) 
          : valueB.localeCompare(valueA);
      }
      
      return 0;
    });
    
    // Reset to first page
    this.currentPage = 1;
    
    // Update the table
    this.updateTable();
  }

  updateClearButton() {
    const clearButton = this.container.querySelector('#clearSearchBtn');
    if (clearButton) {
      clearButton.classList.toggle('hidden', !this.searchTerm);
    }
  }

  updateTable() {
    // Avoid operating if the container is not available
    if (!this.container) {
      console.error('Container not available for updating table');
      return;
    }
    
    const tableBody = this.container.querySelector('#salesOrdersTableBody');
    if (!tableBody) {
      console.error('Table body element not found');
      return;
    }
    
    try {
      // Calculate pagination
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      const pagedOrders = this.filteredSalesOrders.slice(start, end);
      
      // Clear loading and error states
      this.hideLoading();
      this.hideError();
      
      // Show empty state if no results
      if (this.filteredSalesOrders.length === 0) {
        this.showEmptyState();
        this.updatePagination();
        return;
      }
      
      // Hide empty state if we have results
      this.hideEmptyState();
      
      // Generate table rows - use a DocumentFragment for better performance
      const fragment = document.createDocumentFragment();
      
      // Clear previous content
      tableBody.innerHTML = '';
      
      // Add new rows
      pagedOrders.forEach(order => {
        const row = document.createElement('tr');
        
        // Get status class for color coding
        const statusClass = this.getStatusClass(order.status);
        
        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="text-sm font-medium text-gray-900 dark:text-white">${order.orderNbr}</div>
              <div class="ml-2 text-xs text-gray-500 dark:text-gray-400">${order.orderType}</div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${order.formattedDate}</td>
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900 dark:text-white">${order.customerName}</div>
            <div class="text-xs text-gray-500 dark:text-gray-400">${order.customerID}</div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
              ${order.status}
            </span>
            ${order.hold ? `<span class="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">Hold</span>` : ''}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${order.formattedTotal}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${order.itemCount}</td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button data-order-id="${order.id}" class="view-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
              <i class="fas fa-eye"></i>
            </button>
            <button data-order-id="${order.id}" class="print-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
              <i class="fas fa-print"></i>
            </button>
          </td>
        `;
        
        fragment.appendChild(row);
      });
      
      // Append all rows at once
      tableBody.appendChild(fragment);
      
      // Add event listeners to action buttons
      const viewButtons = tableBody.querySelectorAll('.view-order');
      viewButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const orderId = button.getAttribute('data-order-id');
          this.viewOrderDetails(orderId);
        });
      });
      
      const printButtons = tableBody.querySelectorAll('.print-order');
      printButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const orderId = button.getAttribute('data-order-id');
          this.printOrder(orderId);
        });
      });
      
      // Update pagination
      this.updatePagination();
    } catch (error) {
      console.error('Error updating table:', error);
      this.showError('An error occurred while updating the table: ' + error.message);
    }
  }

  getStatusClass(status) {
    status = status.toLowerCase();
    switch (status) {
      case 'open':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'closed':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'on hold':
      case 'credit hold':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'pending approval':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  updatePagination() {
    const totalItems = this.filteredSalesOrders.length;
    const totalPages = this.getTotalPages();
    
    // Update page numbers display
    const pageNumbers = this.container.querySelector('#pageNumbers');
    if (pageNumbers) {
      pageNumbers.textContent = `${this.currentPage} of ${totalPages}`;
    }
    
    // Update item count display
    const start = totalItems === 0 ? 0 : (this.currentPage - 1) * this.pageSize + 1;
    const end = Math.min(start + this.pageSize - 1, totalItems);
    
    const paginationStart = this.container.querySelector('#paginationStart');
    const paginationEnd = this.container.querySelector('#paginationEnd');
    const paginationTotal = this.container.querySelector('#paginationTotal');
    
    if (paginationStart) paginationStart.textContent = start;
    if (paginationEnd) paginationEnd.textContent = end;
    if (paginationTotal) paginationTotal.textContent = totalItems;
    
    // Disable/enable navigation buttons
    const isFirstPage = this.currentPage === 1;
    const isLastPage = this.currentPage === totalPages || totalPages === 0;
    
    const pageFirstBtn = this.container.querySelector('#pageFirst');
    const pagePrevBtn = this.container.querySelector('#pagePrev');
    const pageNextBtn = this.container.querySelector('#pageNext');
    const pageLastBtn = this.container.querySelector('#pageLast');
    const mobilePagePrevBtn = this.container.querySelector('#mobilePagePrev');
    const mobilePageNextBtn = this.container.querySelector('#mobilePageNext');
    
    if (pageFirstBtn) pageFirstBtn.disabled = isFirstPage;
    if (pagePrevBtn) pagePrevBtn.disabled = isFirstPage;
    if (pageNextBtn) pageNextBtn.disabled = isLastPage;
    if (pageLastBtn) pageLastBtn.disabled = isLastPage;
    if (mobilePagePrevBtn) mobilePagePrevBtn.disabled = isFirstPage;
    if (mobilePageNextBtn) mobilePageNextBtn.disabled = isLastPage;
    
    // Apply visual indication for disabled buttons
    [pageFirstBtn, pagePrevBtn, pageNextBtn, pageLastBtn, mobilePagePrevBtn, mobilePageNextBtn].forEach(btn => {
      if (btn) {
        btn.classList.toggle('opacity-50', btn.disabled);
        btn.classList.toggle('cursor-not-allowed', btn.disabled);
      }
    });
  }

  viewOrderDetails(orderId) {
    // Find the order in our data
    const order = this.salesOrders.find(o => o.id === orderId);
    if (!order) {
      alert('Order details not found');
      return;
    }
    
    // Create a modal to display order details
    const detailsModal = document.createElement('div');
    detailsModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    detailsModal.id = 'orderDetailsModal';
    
    // Create modal content with order details
    detailsModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center p-3 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            ${order.orderType} Order ${order.orderNbr} 
            <span class="inline-flex ml-2 items-center px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(order.status)}">
              ${order.status}
            </span>
            ${order.willCall ? '<span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">Will Call</span>' : ''}
          </h3>
          <button id="closeDetailsBtn" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="overflow-y-auto p-3 flex-grow">
          <!-- Order Info -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <h4 class="font-medium text-gray-900 dark:text-white mb-2">Order Information</h4>
              <div class="space-y-1">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Order Number</span>
                  <p class="text-gray-900 dark:text-white">${order.orderNbr}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Order Type</span>
                  <p class="text-gray-900 dark:text-white">${order.orderType}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Date</span>
                  <p class="text-gray-900 dark:text-white">${order.formattedDate}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Requested Date</span>
                  <p class="text-gray-900 dark:text-white">${order.requestedDateFormatted}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Status</span>
                  <p class="text-gray-900 dark:text-white">
                    <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${this.getStatusClass(order.status)}">
                      ${order.status}
                    </span>
                  </p>
                </div>
                ${order.customerOrder ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Customer PO</span>
                  <p class="text-gray-900 dark:text-white">${order.customerOrder}</p>
                </div>
                ` : ''}
                ${order.createdDate ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Created Date</span>
                  <p class="text-gray-900 dark:text-white">${new Date(order.createdDate).toLocaleDateString()}</p>
                </div>
                ` : ''}
                ${order.lastModified ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Last Modified</span>
                  <p class="text-gray-900 dark:text-white">${new Date(order.lastModified).toLocaleDateString()}</p>
                </div>
                ` : ''}
              </div>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <h4 class="font-medium text-gray-900 dark:text-white mb-2">Customer Information</h4>
              <div class="space-y-1">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Customer</span>
                  <p class="text-gray-900 dark:text-white">${order.customerName}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Customer ID</span>
                  <p class="text-gray-900 dark:text-white">${order.customerID}</p>
                </div>
                ${order.contactID ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Contact ID</span>
                  <p class="text-gray-900 dark:text-white">${order.contactID}</p>
                </div>
                ` : ''}
                ${order.location ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Location</span>
                  <p class="text-gray-900 dark:text-white">${order.location}</p>
                </div>
                ` : ''}
                ${order.branch ? `
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Branch</span>
                  <p class="text-gray-900 dark:text-white">${order.branch}</p>
                </div>
                ` : ''}
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Project</span>
                  <p class="text-gray-900 dark:text-white">${order.project || 'N/A'}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Will Call</span>
                  <p class="text-gray-900 dark:text-white">${order.willCall ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <h4 class="font-medium text-gray-900 dark:text-white mb-2">Order Totals</h4>
              <div class="space-y-1">
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Order Total</span>
                  <p class="text-gray-900 dark:text-white font-bold">${order.formattedTotal}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Tax</span>
                  <p class="text-gray-900 dark:text-white">${this.formatCurrency(order.taxTotal, order.currencyID)}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Currency</span>
                  <p class="text-gray-900 dark:text-white">${order.currencyID || 'USD'}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Item Count</span>
                  <p class="text-gray-900 dark:text-white">${order.itemCount}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Approval Status</span>
                  <p class="text-gray-900 dark:text-white">${order.approved ? 'Approved' : 'Not Approved'}</p>
                </div>
                <div>
                  <span class="text-sm text-gray-500 dark:text-gray-400">Hold Status</span>
                  <p class="text-gray-900 dark:text-white">
                    ${order.hold ? '<span class="text-red-600 dark:text-red-400">On Hold</span>' : 'No Hold'}
                    ${order.creditHold ? ' <span class="text-red-600 dark:text-red-400">(Credit Hold)</span>' : ''}
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Shipments Section (if any) -->
          ${order.shipments && order.shipments.length > 0 ? `
          <div class="mb-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Shipments</h4>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead>
                  <tr>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Shipment #</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Date</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Type</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Invoice #</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Status</th>
                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Qty</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                  ${order.shipments.map(shipment => `
                    <tr>
                      <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">${shipment.shipmentNbr}</td>
                      <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${shipment.shipmentDate ? new Date(shipment.shipmentDate).toLocaleDateString() : 'N/A'}</td>
                      <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${shipment.shipmentType}</td>
                      <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${shipment.invoiceNbr}</td>
                      <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                        <span class="px-2 py-1 text-xs rounded-full ${this.getStatusClass(shipment.status)}">
                          ${shipment.status}
                        </span>
                      </td>
                      <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${shipment.shippedQty}</td>
                    </tr>
                  `).join('')}
                </tbody>
              </table>
            </div>
          </div>
          ` : ''}
          
          <!-- Notes Section -->
          <div class="mb-4">
            <h4 class="font-medium text-gray-900 dark:text-white mb-2">Notes</h4>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <p class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">${order.note || 'No notes'}</p>
            </div>
          </div>
          
          <!-- Line Items -->
          <h4 class="font-medium text-gray-900 dark:text-white mb-2">Line Items</h4>
          <div class="overflow-x-auto bg-gray-50 dark:bg-gray-700 rounded-lg">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
              <thead>
                <tr>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Line</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Item</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Description</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Qty</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Price</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Discount</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total</th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                ${order.details.map(item => `
                  <tr>
                    <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${item.lineNbr}</td>
                    <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">${item.inventoryID}</td>
                    <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                      ${item.description}
                      ${item.note ? `<div class="mt-1 text-xs italic text-gray-500 dark:text-gray-400">${item.note}</div>` : ''}
                    </td>
                    <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${item.quantity} ${item.uom || ''}</td>
                    <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">${this.formatCurrency(item.unitPrice, order.currencyID)}</td>
                    <td class="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                      ${item.discountPercent > 0 ? 
                        `${item.discountPercent}% ${item.discountCode ? `(${item.discountCode})` : ''} 
                        (${this.formatCurrency(item.discountAmount, order.currencyID)})` 
                        : 'None'}
                    </td>
                    <td class="px-4 py-2 text-sm font-medium text-gray-900 dark:text-white">${this.formatCurrency(item.extendedPrice, order.currencyID)}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>
        
        <div class="border-t border-gray-200 dark:border-gray-700 p-3 flex justify-end space-x-3">
          <button id="closeOrderBtn" class="py-1 px-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            Close
          </button>
          <button id="printOrderBtn" class="py-1 px-3 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
            Print Order
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(detailsModal);
    
    // Add event listeners
    const closeBtn = document.getElementById('closeDetailsBtn');
    const closeBtnBottom = document.getElementById('closeOrderBtn');
    const printBtn = document.getElementById('printOrderBtn');
    
    const closeModal = () => {
      document.body.removeChild(detailsModal);
    };
    
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (closeBtnBottom) closeBtnBottom.addEventListener('click', closeModal);
    
    if (printBtn) {
      printBtn.addEventListener('click', () => {
        closeModal();
        this.printOrder(orderId);
      });
    }
  }

  printOrder(orderId) {
    // Find the order in our data
    const order = this.salesOrders.find(o => o.id === orderId);
    if (!order) {
      alert('Order details not found');
      return;
    }
    
    // Calculate subtotal
    const subtotal = order.orderTotal - order.taxTotal;
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    
    // Make sure the window was created successfully
    if (!printWindow) {
      alert('Please allow pop-ups for this site to print orders');
      return;
    }
    
    printWindow.document.write(`
      <html>
      <head>
        <title>${order.orderType} Order #${order.orderNbr}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
          .header { display: flex; justify-content: space-between; margin-bottom: 20px; }
          .company-info { font-weight: bold; font-size: 24px; }
          .order-info { text-align: right; }
          .section { margin-bottom: 20px; }
          .section-title { font-weight: bold; margin-bottom: 5px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
          th { text-align: left; padding: 8px; border-bottom: 2px solid #ddd; background-color: #f8f8f8; }
          td { padding: 8px; border-bottom: 1px solid #ddd; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          .total-row { font-weight: bold; background-color: #f8f8f8; }
          .badge { display: inline-block; padding: 3px 6px; border-radius: 12px; font-size: 12px; font-weight: bold; }
          .badge-success { background-color: #d4edda; color: #155724; }
          .badge-warning { background-color: #fff3cd; color: #856404; }
          .badge-danger { background-color: #f8d7da; color: #721c24; }
          .badge-info { background-color: #d1ecf1; color: #0c5460; }
          .badge-default { background-color: #e9ecef; color: #343a40; }
          .will-call { background-color: #cce5ff; color: #004085; padding: 3px 6px; border-radius: 12px; font-size: 12px; font-weight: bold; }
          .notes { background-color: #f8f9fa; padding: 10px; border-left: 3px solid #ddd; margin-bottom: 15px; }
          .line-note { font-style: italic; font-size: 12px; color: #666; margin-top: 3px; }
          .col-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
          .col-3 { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; }
          .info-box { background-color: #f8f9fa; padding: 15px; border-radius: 5px; }
          @media print {
            @page { margin: 0.5cm; }
            body { font-size: 12pt; }
            .badge, .will-call { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
            .info-box { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-info">
            Acumatica
          </div>
          <div class="order-info">
            <h2>${order.orderType} ORDER #${order.orderNbr}</h2>
            <div>Date: ${order.formattedDate}</div>
            <div>Status: 
              <span class="badge ${this.getStatusPrintClass(order.status)}">${order.status}</span>
              ${order.willCall ? '<span class="will-call">Will Call</span>' : ''}
            </div>
            ${order.customerOrder ? `<div>Customer PO: ${order.customerOrder}</div>` : ''}
          </div>
        </div>
        
        <div class="col-3">
          <div class="info-box">
            <div class="section-title">Customer Information</div>
            <div><strong>Customer:</strong> ${order.customerName}</div>
            <div><strong>ID:</strong> ${order.customerID}</div>
            ${order.contactID ? `<div><strong>Contact ID:</strong> ${order.contactID}</div>` : ''}
            ${order.branch ? `<div><strong>Branch:</strong> ${order.branch}</div>` : ''}
            ${order.location ? `<div><strong>Location:</strong> ${order.location}</div>` : ''}
            <div><strong>Project:</strong> ${order.project || 'N/A'}</div>
          </div>
          
          <div class="info-box">
            <div class="section-title">Order Details</div>
            <div><strong>Order Date:</strong> ${order.formattedDate}</div>
            <div><strong>Requested Date:</strong> ${order.requestedDateFormatted}</div>
            ${order.createdDate ? `<div><strong>Created:</strong> ${new Date(order.createdDate).toLocaleDateString()}</div>` : ''}
            ${order.lastModified ? `<div><strong>Last Modified:</strong> ${new Date(order.lastModified).toLocaleDateString()}</div>` : ''}
            <div><strong>Hold:</strong> ${order.hold ? 'Yes' : 'No'}${order.creditHold ? ' (Credit Hold)' : ''}</div>
          </div>
          
          <div class="info-box">
            <div class="section-title">Financial Information</div>
            <div><strong>Currency:</strong> ${order.currencyID}</div>
            <div><strong>Subtotal:</strong> ${this.formatCurrency(subtotal, order.currencyID)}</div>
            <div><strong>Tax:</strong> ${this.formatCurrency(order.taxTotal, order.currencyID)}</div>
            <div><strong>Total:</strong> ${order.formattedTotal}</div>
            <div><strong>Approved:</strong> ${order.approved ? 'Yes' : 'No'}</div>
          </div>
        </div>
        
        ${order.note ? `
        <div class="section">
          <div class="section-title">Order Notes</div>
          <div class="notes">${order.note}</div>
        </div>
        ` : ''}
        
        ${order.shipments && order.shipments.length > 0 ? `
        <div class="section">
          <div class="section-title">Shipments</div>
          <table>
            <thead>
              <tr>
                <th>Shipment #</th>
                <th>Date</th>
                <th>Type</th>
                <th>Invoice #</th>
                <th>Status</th>
                <th>Qty</th>
              </tr>
            </thead>
            <tbody>
              ${order.shipments.map(shipment => `
                <tr>
                  <td>${shipment.shipmentNbr}</td>
                  <td>${shipment.shipmentDate ? new Date(shipment.shipmentDate).toLocaleDateString() : 'N/A'}</td>
                  <td>${shipment.shipmentType}</td>
                  <td>${shipment.invoiceNbr}</td>
                  <td><span class="badge ${this.getStatusPrintClass(shipment.status)}">${shipment.status}</span></td>
                  <td>${shipment.shippedQty}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        ` : ''}
        
        <div class="section">
          <div class="section-title">Line Items</div>
          <table>
            <thead>
              <tr>
                <th>Line</th>
                <th>Item</th>
                <th>Description</th>
                <th>Qty</th>
                <th>UOM</th>
                <th>Price</th>
                <th>Discount</th>
                <th>Extended</th>
              </tr>
            </thead>
            <tbody>
              ${order.details.map(item => `
                <tr>
                  <td>${item.lineNbr}</td>
                  <td>${item.inventoryID}</td>
                  <td>
                    ${item.description}
                    ${item.note ? `<div class="line-note">${item.note}</div>` : ''}
                  </td>
                  <td>${item.quantity}</td>
                  <td>${item.uom || ''}</td>
                  <td>${this.formatCurrency(item.unitPrice, order.currencyID)}</td>
                  <td>${item.discountPercent > 0 ? 
                    `${item.discountPercent}% ${item.discountCode ? `(${item.discountCode})` : ''}` : '-'}</td>
                  <td>${this.formatCurrency(item.extendedPrice, order.currencyID)}</td>
                </tr>
              `).join('')}
              <tr class="total-row">
                <td colspan="6"></td>
                <td>Subtotal:</td>
                <td>${this.formatCurrency(subtotal, order.currencyID)}</td>
              </tr>
              <tr class="total-row">
                <td colspan="6"></td>
                <td>Tax:</td>
                <td>${this.formatCurrency(order.taxTotal, order.currencyID)}</td>
              </tr>
              <tr class="total-row">
                <td colspan="6"></td>
                <td>Total:</td>
                <td>${order.formattedTotal}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <div class="footer">
          <p>This is a printed copy of ${order.orderType} order #${order.orderNbr}. Generated on ${new Date().toLocaleString()}</p>
        </div>
        
        <script>
          // Use setTimeout to ensure the document is fully loaded before printing
          setTimeout(function() {
            window.print();
          }, 500);
        </script>
      </body>
      </html>
    `);
    
    printWindow.document.close();
  }
  
  // Helper method for print styling
  getStatusPrintClass(status) {
    status = status.toLowerCase();
    switch (status) {
      case 'open':
      case 'active':
        return 'badge-success';
      case 'closed':
      case 'completed':
      case 'invoiced':
        return 'badge-info';
      case 'cancelled':
        return 'badge-danger';
      case 'on hold':
      case 'credit hold':
        return 'badge-warning';
      case 'pending approval':
        return 'badge-warning';
      default:
        return 'badge-default';
    }
  }

  exportSalesOrderData() {
    try {
      const dataStr = JSON.stringify(this.filteredSalesOrders, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileName = `sales_orders_${new Date().toISOString().slice(0, 10)}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileName);
      linkElement.click();
    } catch (error) {
      console.error('Error exporting sales order data:', error);
      alert('Failed to export sales order data. Please try again.');
    }
  }

  showDateRangePicker() {
    // This is a placeholder for future date range picker implementation
    console.log('Date range picker will be implemented here');
    
    // For now, just show a simple alert
    alert('Date range selection feature coming soon');
  }

  showSettings() {
    // This is a placeholder for future settings modal implementation
    console.log('Settings modal will be implemented here');
    
    // Create a settings modal dialog
    const settingsModal = document.createElement('div');
    settingsModal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    settingsModal.id = 'settingsModal';
    
    settingsModal.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sales Order Settings</h3>
          <button id="closeSettingsBtn" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Items per page</label>
            <select id="pageSizeSelect" class="block w-full rounded-md border border-gray-300 dark:border-gray-600 py-2 px-3 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="5">5</option>
              <option value="10" selected>10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Default sort field</label>
            <select id="defaultSortSelect" class="block w-full rounded-md border border-gray-300 dark:border-gray-600 py-2 px-3 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="orderNbr" selected>Order Number</option>
              <option value="date">Date</option>
              <option value="customerName">Customer</option>
              <option value="status">Status</option>
              <option value="orderTotal">Order Total</option>
            </select>
          </div>
          
          <div class="flex items-center">
            <input type="checkbox" id="autoRefreshCheckbox" class="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500">
            <label for="autoRefreshCheckbox" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Auto-refresh data (every 5 minutes)
            </label>
          </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
          <button id="cancelSettingsBtn" class="py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            Cancel
          </button>
          <button id="saveSettingsBtn" class="py-2 px-4 border border-transparent rounded-md text-sm text-white bg-blue-600 hover:bg-blue-700">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(settingsModal);
    
    // Add event listeners for the modal buttons
    const closeBtn = document.getElementById('closeSettingsBtn');
    const cancelBtn = document.getElementById('cancelSettingsBtn');
    const saveBtn = document.getElementById('saveSettingsBtn');
    
    const closeModal = () => {
      document.body.removeChild(settingsModal);
    };
    
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);
    
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        // Get values from form controls
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        const defaultSortSelect = document.getElementById('defaultSortSelect');
        const autoRefreshCheckbox = document.getElementById('autoRefreshCheckbox');
        
        if (pageSizeSelect) {
          this.pageSize = parseInt(pageSizeSelect.value, 10);
        }
        
        if (defaultSortSelect) {
          this.sortField = defaultSortSelect.value;
          this.sortDirection = 'desc';
        }
        
        // Apply settings
        this.applySearch(); // This will update the table with new settings
        
        // Save settings to localStorage (could be implemented in the future)
        console.log('Settings saved:', {
          pageSize: this.pageSize,
          sortField: this.sortField,
          autoRefresh: autoRefreshCheckbox ? autoRefreshCheckbox.checked : false
        });
        
        closeModal();
      });
    }
  }

  getTotalPages() {
    return Math.max(1, Math.ceil(this.filteredSalesOrders.length / this.pageSize));
  }

  goToPage(page) {
    const totalPages = this.getTotalPages();
    this.currentPage = Math.max(1, Math.min(page, totalPages));
    this.updateTable();
  }

  // Display state methods
  showLoading() {
    const loadingIndicator = this.container.querySelector('#salesOrdersLoadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.classList.remove('hidden');
    }
    
    this.hideEmptyState();
    this.hideError();
    this.isLoading = true;
  }

  hideLoading() {
    const loadingIndicator = this.container.querySelector('#salesOrdersLoadingIndicator');
    if (loadingIndicator) {
      loadingIndicator.classList.add('hidden');
    }
    this.isLoading = false;
  }

  showEmptyState() {
    const emptyState = this.container.querySelector('#salesOrdersEmptyState');
    if (emptyState) {
      emptyState.classList.remove('hidden');
    }
  }

  hideEmptyState() {
    const emptyState = this.container.querySelector('#salesOrdersEmptyState');
    if (emptyState) {
      emptyState.classList.add('hidden');
    }
  }

  showError(message) {
    const errorState = this.container.querySelector('#salesOrdersErrorState');
    const errorMessage = this.container.querySelector('#errorMessage');
    
    if (errorState) {
      errorState.classList.remove('hidden');
    }
    
    if (errorMessage) {
      errorMessage.textContent = message;
    }
    
    this.hideLoading();
    this.hideEmptyState();
  }

  hideError() {
    const errorState = this.container.querySelector('#salesOrdersErrorState');
    if (errorState) {
      errorState.classList.add('hidden');
    }
  }
} 