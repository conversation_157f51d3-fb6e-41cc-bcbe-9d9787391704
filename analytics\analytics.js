import { create<PERSON><PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON>, create<PERSON><PERSON><PERSON><PERSON>, createStatsGrid, createCustomerShipmentChart } from "./simple-charts.js"
import { HistoryTable } from "./analytics-history.js"

export class AnalyticsComponent {
  constructor(container) {
    this.container = container;
    this.historyData = [];
    this.lastUpdate = null;
    this.updateInterval = null;
    this.historyTable = null;
    this.chartFilters = {
      dailyFreightCost: 'week',
      shipmentVolume: 'week',
      shipmentByDay: 'week',
      shipmentByCarrier: 'month',
      carrierCost: 'month',
      topProducts: 'month',
      topDestination: 'month',
      costByCompany: 'month',
      customerShipments: 'month',
      extraMetrics: 'month'
    };
    this.isInitialized = false;
    this.isDataLoaded = false;
    
    // Create an observer to detect when component is visible
    this.visibilityObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.isDataLoaded) {
          this.onContentVisible();
        }
      });
    });
    
    // Start observing the container
    if (this.container) {
      this.visibilityObserver.observe(this.container);
    }
  }

  /**
   * This method is triggered when component becomes visible
   * Ensures data is loaded immediately upon visibility
   */
  onContentVisible() {
    if (!this.isDataLoaded) {
      console.log("Container is now visible, ensuring data is loaded");
      this.loadDataAndRender();
      this.isDataLoaded = true;
    }
  }

  /**
   * CRITICAL: Main initialization method with robust loading sequence
   */
  async init() {
    console.log("Analytics component initializing...");
    
    // Set initial loading state
    this.container.innerHTML = `
      <div class="p-2 bg-gray-50 rounded-xl shadow-sm flex items-center justify-center h-32">
        <div class="text-center">
          <svg class="animate-spin h-6 w-6 mx-auto text-blue-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p class="text-sm text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    `;
    
    // Start data loading process
    this.loadDataAndRender();
    
    // Set up hourly auto-update
    this.updateInterval = setInterval(() => {
      this.fetchHistoryData()
        .then(data => {
          if (data && data.length > 0) {
            this.updateCharts();
          }
        })
        .catch(error => console.error("Auto-update error:", error));
    }, 60 * 60 * 1000); // 1 hour
    
    this.isInitialized = true;
    console.log("Analytics component initialization complete");
  }

  /**
   * CRITICAL: Core data loading and rendering sequence
   * This ensures data is always available for rendering
   */
  async loadDataAndRender() {
    console.log("Starting data loading sequence");
    
    // Step 1: Try to load cached data from storage
    const dataLoadedFromStorage = await this.loadDataFromStorage();
    
    // Step 2: If we have data from storage, render immediately
    if (dataLoadedFromStorage && this.historyData.length > 0) {
      console.log("Data loaded from storage, rendering charts");
      this.render();
      this.isDataLoaded = true;
    }
    
    // Step 3: Always fetch fresh data in the background
    try {
      console.log("Fetching fresh data from API");
      const freshData = await this.fetchHistoryData();
      
      // Step 4: If we got fresh data and we didn't render yet, render now
      if (freshData && freshData.length > 0) {
        if (!this.isDataLoaded) {
          console.log("First-time data load from API, rendering charts");
          this.render();
          this.isDataLoaded = true;
        } else {
          // Otherwise just update the existing charts
          console.log("Updating charts with fresh data");
          this.updateCharts();
        }
      }
    } catch (error) {
      console.error("Error fetching fresh data:", error);
      
      // Step 5: If API fetch failed but we haven't rendered yet,
      // render with whatever data we have (even if empty)
      if (!this.isDataLoaded) {
        console.log("API fetch failed, rendering with existing data");
        this.render();
        this.isDataLoaded = true;
      }
    }
  }

  /**
   * Improved data loading from storage
   */
  async loadDataFromStorage() {
    console.log("Attempting to load data from storage");
    
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['analyticsHistoryData', 'analyticsLastUpdate'], (result) => {
          if (chrome.runtime.lastError) {
            console.error('Chrome storage error:', chrome.runtime.lastError);
            resolve(false);
            return;
          }
          
          if (result && result.analyticsHistoryData && Array.isArray(result.analyticsHistoryData) && result.analyticsHistoryData.length > 0) {
            this.historyData = result.analyticsHistoryData;
            this.lastUpdate = result.analyticsLastUpdate ? new Date(result.analyticsLastUpdate) : new Date();
            console.log(`✅ Successfully loaded ${this.historyData.length} records from chrome storage`);
            resolve(true);
          } else {
            console.log('⚠️ No valid data found in chrome storage');
            resolve(false);
          }
        });
      } else {
        try {
          const storedData = localStorage.getItem('analyticsHistoryData');
          const storedDate = localStorage.getItem('analyticsLastUpdate');
          
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            if (Array.isArray(parsedData) && parsedData.length > 0) {
              this.historyData = parsedData;
              this.lastUpdate = storedDate ? new Date(storedDate) : new Date();
              console.log(`✅ Successfully loaded ${this.historyData.length} records from localStorage`);
              resolve(true);
            } else {
              console.log('⚠️ Invalid data format in localStorage');
              resolve(false);
            }
          } else {
            console.log('⚠️ No data found in localStorage');
            resolve(false);
          }
        } catch (e) {
          console.error('Error loading from localStorage:', e);
          resolve(false);
        }
      }
    });
  }

  /**
   * Improved data fetching with robust error handling
   */
  async fetchHistoryData() {
    console.log("Fetching fresh history data from API");
    try {
      const apiEndpoint = "https://sheetdb.io/api/v1/ygn268dcacru7";
      // Add cache-busting parameter
      const cacheBuster = `_cb=${Date.now()}`;
      const url = `${apiEndpoint}?${cacheBuster}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Authorization": "Bearer tgive5whsoypqz9f6lq7ggno3xkamhp1dhqhc9ed",
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache"
        }
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const newData = await response.json();
      
      if (!Array.isArray(newData)) {
        throw new Error("API returned non-array data");
      }
      
      console.log(`✅ Successfully fetched ${newData.length} records from API`);
      
      if (newData.length > 0) {
        // Update our data only if we got valid data
        this.historyData = newData;
        this.lastUpdate = new Date();
        
        // Save to storage immediately
        this.saveDataToStorage();
      } else {
        console.warn("API returned empty data array");
      }
      
      return this.historyData;
    } catch (error) {
      console.error("❌ Error fetching history data:", error);
      return this.historyData; // Return existing data
    }
  }

  /**
   * Improved storage saving with verification
   */
  saveDataToStorage() {
    if (!this.historyData || !Array.isArray(this.historyData) || this.historyData.length === 0) {
      console.warn("No valid data to save to storage");
      return;
    }

    console.log(`Saving ${this.historyData.length} records to storage`);
    
    // Prepare data for storage
    const storageData = {
      analyticsHistoryData: this.historyData,
      analyticsLastUpdate: this.lastUpdate.toISOString()
    };
    
    // For debugging
    const dataSize = new Blob([JSON.stringify(storageData)]).size;
    console.log(`Data size for storage: ${(dataSize / 1024).toFixed(2)} KB`);

    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.set(storageData, () => {
        if (chrome.runtime.lastError) {
          console.error("❌ Error saving to chrome storage:", chrome.runtime.lastError);
        } else {
          console.log(`✅ Successfully saved ${this.historyData.length} records to chrome storage`);
          // Verify save succeeded
          this.verifySavedData();
        }
      });
    } else {
      try {
        localStorage.setItem('analyticsHistoryData', JSON.stringify(this.historyData));
        localStorage.setItem('analyticsLastUpdate', this.lastUpdate.toISOString());
        console.log(`✅ Successfully saved ${this.historyData.length} records to localStorage`);
        // Verify save succeeded
        this.verifySavedData();
      } catch (e) {
        console.error('❌ Error saving to localStorage:', e);
      }
    }
  }

  /**
   * Verify data was actually saved to storage
   */
  verifySavedData() {
    setTimeout(() => {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['analyticsHistoryData'], (result) => {
          if (result && result.analyticsHistoryData && Array.isArray(result.analyticsHistoryData)) {
            console.log(`✓ Verified ${result.analyticsHistoryData.length} records in storage`);
          } else {
            console.error("❌ Verification failed - data not found in chrome storage after save");
          }
        });
      } else {
        try {
          const storedData = localStorage.getItem('analyticsHistoryData');
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            if (Array.isArray(parsedData)) {
              console.log(`✓ Verified ${parsedData.length} records in localStorage`);
            }
          } else {
            console.error("❌ Verification failed - data not found in localStorage after save");
          }
        } catch (e) {
          console.error('Error verifying localStorage data:', e);
        }
      }
    }, 500); // Small delay to ensure storage operation completed
  }

  render() {
    this.container.innerHTML = `
      <div class="p-3 bg-gray-50 rounded-xl shadow-sm">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-bold text-gray-800">Analytics Dashboard</h2>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-500 mr-1">Last updated: ${this.lastUpdate ? this.lastUpdate.toLocaleTimeString() : 'Never'}</span>
            <button id="refreshAnalytics" class="px-2 py-1 bg-blue-500 text-white text-xs rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
            <button id="exportAnalytics" class="px-2 py-1 bg-green-500 text-white text-xs rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
              Export
            </button>
            <button id="fullscreenAnalytics" class="px-2 py-1 bg-purple-500 text-white text-xs rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
              </svg>
              Fullscreen
            </button>
          </div>
        </div>

        <!-- KPI Cards Row - IMPROVED with consistent height and better alignment -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
          <!-- Shipment Volume KPI -->
          <div class="bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200 border border-gray-100 h-32 flex flex-col justify-between">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-xs font-medium text-gray-500">Total Shipments</p>
                <p class="text-lg font-bold text-gray-800 mt-1" id="totalShipmentsKPI">0</p>
              </div>
              <div class="p-2 bg-blue-50 rounded-lg">
                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
                </svg>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1" id="shipmentsChangeKPI">0% from last period</p>
          </div>
          
          <!-- Freight Cost KPI - RENAMED to "Total Cost" for better alignment -->
          <div class="bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200 border border-gray-100 h-32 flex flex-col justify-between">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-xs font-medium text-gray-500">Total Cost</p>
                <p class="text-lg font-bold text-gray-800 mt-1" id="totalFreightKPI">$0.00</p>
              </div>
              <div class="p-2 bg-green-50 rounded-lg">
                <svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1" id="freightChangeKPI">0% from last period</p>
          </div>
          
          <!-- Top Carrier KPI -->
          <div class="bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200 border border-gray-100 h-32 flex flex-col justify-between">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-xs font-medium text-gray-500">Top Carrier</p>
                <p class="text-lg font-bold text-gray-800 mt-1" id="topCarrierKPI">-</p>
              </div>
              <div class="p-2 bg-yellow-50 rounded-lg">
                <svg class="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM19 17a2 2 0 11-4 0 2 2 0 014 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16V6a1 1 0 00-1-1H4a1 1 0 00-1 1v10a1 1 0 001 1h1m8-1a1 1 0 01-1 1H9m4-1V8a1 1 0 011-1h2.586a1 1 0 01.707.293l3.414 3.414a1 1 0 01.293.707V16a1 1 0 01-1 1h-1m-6-1a1 1 0 001 1h1M5 17a2 2 0 104 0m-4 0a2 2 0 114 0m6 0a2 2 0 104 0m-4 0a2 2 0 114 0" />
                </svg>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1" id="carrierPercentKPI">0% of shipments</p>
          </div>
          
          <!-- Top Destination KPI -->
          <div class="bg-white p-3 rounded-lg shadow-sm hover:shadow transition-shadow duration-200 border border-gray-100 h-32 flex flex-col justify-between">
            <div class="flex items-start justify-between">
              <div>
                <p class="text-xs font-medium text-gray-500">Top Destination</p>
                <p class="text-lg font-bold text-gray-800 mt-1" id="topDestinationKPI">-</p>
              </div>
              <div class="p-2 bg-purple-50 rounded-lg">
                <svg class="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <p class="text-xs text-gray-500 mt-1" id="destinationPercentKPI">0% of shipments</p>
          </div>
        </div>

        <!-- Additional Metrics Section -->
        <div class="bg-white p-3 rounded-lg shadow-sm mb-4 border border-gray-100">
          <div class="flex justify-between items-center mb-3">
            <h3 class="text-sm font-semibold text-gray-700">Shipping Metrics</h3>
            <select id="extraMetricsFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
              <option value="day">Today</option>
              <option value="week">This Week</option>
              <option value="month" selected>This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
          </div>
          <div id="extraMetricsSection" class="w-full h-32 overflow-hidden"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
          <!-- Daily Shipment Cost Tracker -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Freight Cost</h3>
              <select id="freightCostFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="week" selected>Last 7 days</option>
                <option value="month">Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">Last 365 days</option>
              </select>
            </div>
            <div id="dailyFreightCostChart" class="w-full h-52 overflow-hidden"></div>
          </div>
          
          <!-- Shipment Volume Trends -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Shipment Volume</h3>
              <select id="shipmentVolumeFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="week" selected>Weekly</option>
                <option value="month">Monthly</option>
                <option value="quarter">Quarterly</option>
              </select>
            </div>
            <div id="shipmentVolumeChart" class="w-full h-52 overflow-hidden"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
          <!-- Shipment by Weekday - FIXED -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Shipments by Day of Week</h3>
              <select id="shipmentByDayFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="week" selected>Recent</option>
                <option value="month">Last 30 days</option>
                <option value="quarter">Last 90 days</option>
              </select>
            </div>
            <div id="shipmentByDayChart" class="w-full h-52 overflow-hidden"></div>
          </div>
          
          <!-- Shipment by Carrier -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Shipments by Carrier</h3>
              <select id="shipmentByCarrierFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="month" selected>Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">Last 365 days</option>
              </select>
            </div>
            <div id="shipmentByCarrierChart" class="w-full h-52 overflow-hidden"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
          <!-- Shipping Cost by Carrier -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Cost by Carrier</h3>
              <select id="carrierCostFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="month" selected>Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">Last 365 days</option>
              </select>
            </div>
            <div id="carrierCostChart" class="w-full h-48 overflow-hidden"></div>
          </div>
          
          <!-- Carrier Utilization -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Carrier Utilization</h3>
            <div id="carrierUtilizationChart" class="w-full h-48 overflow-hidden"></div>
          </div>
          
          <!-- Parts Shipped -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Top Products</h3>
              <select id="topProductsFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="month" selected>Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">All Time</option>
              </select>
            </div>
            <div id="topProductsChart" class="w-full h-48 overflow-hidden"></div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
          <!-- Shipments per Customer -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Shipments by Customer</h3>
              <select id="customerShipmentsFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="month" selected>Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">Last 365 days</option>
              </select>
            </div>
            <div id="customerShipmentsChart" class="w-full h-48 overflow-hidden"></div>
          </div>
          
          <!-- Top Destinations - REPLACED with Country Distribution Pie Chart -->
          <div class="bg-white p-3 rounded-lg shadow-sm border border-gray-100">
            <div class="flex justify-between items-center mb-2">
              <h3 class="text-sm font-semibold text-gray-700">Country Distribution</h3>
              <select id="topDestinationFilter" class="text-xs bg-gray-50 border border-gray-300 rounded-md px-2 py-1">
                <option value="month" selected>Last 30 days</option>
                <option value="quarter">Last 90 days</option>
                <option value="year">Last 365 days</option>
              </select>
            </div>
            <div id="topDestinationChart" class="w-full h-48 overflow-hidden"></div>
          </div>
        </div>

        <!-- Recent History Table -->
        <div class="bg-white p-3 rounded-lg shadow-sm mb-4 border border-gray-100">
          <div class="flex justify-between items-center mb-2">
            <h3 class="text-sm font-semibold text-gray-700">Recent Shipping History</h3>
          </div>
          <div id="historyTableContainer" class="w-full overflow-x-auto max-h-60"></div>
        </div>
      </div>
    `;

    this.setupEventListeners();
    this.renderCharts();
    this.updateKPICards();
    this.renderExtraMetrics();
    
    // Initialize history table
    this.historyTable = new HistoryTable(
      document.getElementById('historyTableContainer'),
      this.historyData.slice(0, 10) // Show most recent 10 items
    );
    this.historyTable.render();
  }

  /**
   * Update KPI card metrics with calculated values
   */
  updateKPICards() {
    if (!this.historyData || this.historyData.length === 0) {
      return;
    }

    // Calculate time periods
    const now = new Date();
    const thirtyDaysAgo = new Date(now);
    thirtyDaysAgo.setDate(now.getDate() - 30);
    const sixtyDaysAgo = new Date(now);
    sixtyDaysAgo.setDate(now.getDate() - 60);

    // Filter data for current and previous periods
    const currentPeriodData = this.historyData.filter(item => {
      const date = new Date(item.Date);
      return date >= thirtyDaysAgo && date <= now;
    });

    const previousPeriodData = this.historyData.filter(item => {
      const date = new Date(item.Date);
      return date >= sixtyDaysAgo && date < thirtyDaysAgo;
    });

    // 1. Total Shipments KPI
    const totalShipments = currentPeriodData.length;
    const previousTotalShipments = previousPeriodData.length;
    const shipmentChange = previousTotalShipments > 0 
      ? ((totalShipments - previousTotalShipments) / previousTotalShipments * 100).toFixed(1)
      : 0;
    
    document.getElementById('totalShipmentsKPI').textContent = totalShipments.toLocaleString();
    
    const shipmentsChangeElement = document.getElementById('shipmentsChangeKPI');
    shipmentsChangeElement.textContent = `${shipmentChange > 0 ? '+' : ''}${shipmentChange}% from last period`;
    shipmentsChangeElement.className = `text-xs ${shipmentChange > 0 ? 'text-green-500' : shipmentChange < 0 ? 'text-red-500' : 'text-gray-500'} mt-1`;

    // 2. Total Freight Cost KPI
    const totalFreight = currentPeriodData.reduce((sum, item) => {
      const cost = parseFloat(item["Freight Cost"]) || 0;
      return sum + cost;
    }, 0);
    
    const previousTotalFreight = previousPeriodData.reduce((sum, item) => {
      const cost = parseFloat(item["Freight Cost"]) || 0;
      return sum + cost;
    }, 0);
    
    const freightChange = previousTotalFreight > 0 
      ? ((totalFreight - previousTotalFreight) / previousTotalFreight * 100).toFixed(1) 
      : 0;
    
    document.getElementById('totalFreightKPI').textContent = `$${totalFreight.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}`;
    
    const freightChangeElement = document.getElementById('freightChangeKPI');
    freightChangeElement.textContent = `${freightChange > 0 ? '+' : ''}${freightChange}% from last period`;
    freightChangeElement.className = `text-xs ${freightChange > 0 ? 'text-red-500' : freightChange < 0 ? 'text-green-500' : 'text-gray-500'} mt-1`;

    // 3. Top Carrier KPI
    const carrierCounts = {};
    currentPeriodData.forEach(item => {
      const carrier = item.Carrier || 'Unknown';
      carrierCounts[carrier] = (carrierCounts[carrier] || 0) + 1;
    });
    
    let topCarrier = 'N/A';
    let topCarrierCount = 0;
    
    Object.entries(carrierCounts).forEach(([carrier, count]) => {
      if (count > topCarrierCount && carrier !== 'Unknown' && carrier !== 'N/A') {
        topCarrier = carrier;
        topCarrierCount = count;
      }
    });
    
    const topCarrierPercent = totalShipments > 0 
      ? ((topCarrierCount / totalShipments) * 100).toFixed(1) 
      : 0;
    
    document.getElementById('topCarrierKPI').textContent = topCarrier;
    document.getElementById('carrierPercentKPI').textContent = `${topCarrierPercent}% of shipments`;

    // 4. Top Destination KPI
    const destinationCounts = {};
    currentPeriodData.forEach(item => {
      const country = item.Country || 'Unknown';
      destinationCounts[country] = (destinationCounts[country] || 0) + 1;
    });
    
    let topDestination = 'N/A';
    let topDestinationCount = 0;
    
    Object.entries(destinationCounts).forEach(([country, count]) => {
      if (count > topDestinationCount && country !== 'Unknown' && country !== 'N/A') {
        topDestination = country;
        topDestinationCount = count;
      }
    });
    
    const topDestinationPercent = totalShipments > 0 
      ? ((topDestinationCount / totalShipments) * 100).toFixed(1) 
      : 0;
    
    document.getElementById('topDestinationKPI').textContent = topDestination;
    document.getElementById('destinationPercentKPI').textContent = `${topDestinationPercent}% of shipments`;
  }

  /**
   * Render the extra metrics section (average weight, avg cost, etc.)
   */
  renderExtraMetrics() {
    const container = document.getElementById('extraMetricsSection');
    if (!container) return;
    
    // Get time filtered data based on selected period
    const timeFilteredData = this.getTimeFilteredData(this.chartFilters.extraMetrics);
    
    // Calculate metrics
    const totalShipments = timeFilteredData.length;
    
    // Average package weight
    let totalWeight = 0;
    let weightCount = 0;
    let heaviestPackage = 0;
    
    // Freight costs
    let totalFreight = 0;
    let costCount = 0;
    let highestCost = 0;
    
    timeFilteredData.forEach(item => {
      // Process weight
      let weight = parseFloat(item["Package Weight"]) || 0;
      if (weight > 0) {
        totalWeight += weight;
        weightCount++;
        if (weight > heaviestPackage) {
          heaviestPackage = weight;
        }
      }
      
      // Process cost
      let cost = parseFloat(item["Freight Cost"]) || 0;
      if (cost > 0) {
        totalFreight += cost;
        costCount++;
        if (cost > highestCost) {
          highestCost = cost;
        }
      }
    });
    
    // Calculate averages
    const avgWeight = weightCount > 0 ? (totalWeight / weightCount) : 0;
    const avgCost = costCount > 0 ? (totalFreight / costCount) : 0;
    
    // Create stats array for the grid
    const stats = [
      {
        title: 'Average Package Weight',
        value: `${avgWeight.toFixed(2)} lbs`,
        icon: 'weight',
        color: '#3B82F6'
      },
      {
        title: 'Average Freight Cost',
        value: `$${avgCost.toFixed(2)}`,
        icon: 'dollar',
        color: '#10B981'
      },
      {
        title: 'Total Freight Cost',
        value: `$${totalFreight.toFixed(2)}`,
        icon: 'dollar',
        color: '#F59E0B'
      },
      {
        title: 'Heaviest Package',
        value: `${heaviestPackage.toFixed(2)} lbs`,
        icon: 'weight',
        color: '#8B5CF6'
      },
      {
        title: 'Most Expensive Shipment',
        value: `$${highestCost.toFixed(2)}`,
        icon: 'dollar',
        color: '#EF4444'
      },
      {
        title: 'Total Shipments',
        value: totalShipments.toString(),
        icon: 'box',
        color: '#EC4899'
      }
    ];
    
    // Render the stats grid
    container.innerHTML = createStatsGrid(
      stats,
      container.clientWidth,
      container.clientHeight,
      true // compact for extension view
    );
  }

  setupEventListeners() {
    const refreshButton = document.getElementById('refreshAnalytics');
    if (refreshButton) {
      refreshButton.addEventListener('click', async () => {
        refreshButton.disabled = true;
        refreshButton.innerHTML = `
          <svg class="w-3 h-3 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Updating...
        `;
        
        try {
          await this.fetchHistoryData();
          this.updateCharts();
          this.updateKPICards();
          this.renderExtraMetrics();
          console.log("Manual refresh completed successfully");
        } catch (error) {
          console.error("Error during manual refresh:", error);
        }
        
        refreshButton.disabled = false;
        refreshButton.innerHTML = `
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Refresh
        `;
      });
    }
    
    // Export button event listener
    const exportButton = document.getElementById('exportAnalytics');
    if (exportButton) {
      exportButton.addEventListener('click', () => this.exportAnalyticsReport());
    }
    
    // Fullscreen button event listener
    const fullscreenButton = document.getElementById('fullscreenAnalytics');
    if (fullscreenButton) {
      fullscreenButton.addEventListener('click', () => this.openFullscreenView());
    }
    
    // Add filter change event listeners for all charts
    this.setupFilterListeners('freightCostFilter', 'dailyFreightCost', this.renderDailyFreightCostChart.bind(this));
    this.setupFilterListeners('shipmentVolumeFilter', 'shipmentVolume', this.renderShipmentVolumeChart.bind(this));
    this.setupFilterListeners('shipmentByDayFilter', 'shipmentByDay', this.renderShipmentByDayChart.bind(this));
    this.setupFilterListeners('shipmentByCarrierFilter', 'shipmentByCarrier', this.renderShipmentByCarrierChart.bind(this));
    this.setupFilterListeners('carrierCostFilter', 'carrierCost', this.renderCarrierCostChart.bind(this));
    this.setupFilterListeners('topProductsFilter', 'topProducts', this.renderTopProductsChart.bind(this));
    this.setupFilterListeners('topDestinationFilter', 'topDestination', this.renderTopDestinationChart.bind(this));
    this.setupFilterListeners('customerShipmentsFilter', 'customerShipments', this.renderCustomerShipmentsChart.bind(this));
    this.setupFilterListeners('extraMetricsFilter', 'extraMetrics', this.renderExtraMetrics.bind(this));
  }

  setupFilterListeners(elementId, filterKey, renderFunction) {
    const filterElement = document.getElementById(elementId);
    if (filterElement) {
      filterElement.addEventListener('change', (e) => {
        this.chartFilters[filterKey] = e.target.value;
        renderFunction();
      });
    }
  }
  
  // IMPROVED Export analytics as PDF/image with better formatting and page breaks
  exportAnalyticsReport() {
    // Create a temporary div to hold the report content
    const reportContainer = document.createElement('div');
    reportContainer.className = 'analytics-report';
    reportContainer.style.width = '210mm'; // A4 width
    reportContainer.style.margin = '0 auto';
    reportContainer.style.padding = '20mm';
    reportContainer.style.backgroundColor = 'white';
    reportContainer.style.fontFamily = 'Arial, sans-serif';
    reportContainer.style.boxSizing = 'border-box';
    
    // Add report header
    reportContainer.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px; border-bottom: 2px solid #e5e7eb; padding-bottom: 15px;">
        <h1 style="font-size: 24px; font-weight: bold; color: #111827;">Shipping Analytics Report</h1>
        <div>
          <p style="margin: 0 0 5px 0; font-size: 14px;">Generated: ${new Date().toLocaleString()}</p>
          <p style="margin: 0; font-size: 14px;">Data updated: ${this.lastUpdate ? this.lastUpdate.toLocaleString() : 'Unknown'}</p>
        </div>
      </div>

      <!-- KPI summary section -->
      <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 30px;">
        <div style="background-color: #f3f4f6; border-radius: 8px; padding: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div>
              <p style="font-size: 14px; color: #6b7280; margin: 0 0 5px 0;">Total Shipments</p>
              <p style="font-size: 24px; font-weight: bold; color: #111827; margin: 0;">${document.getElementById('totalShipmentsKPI').textContent}</p>
              <p style="font-size: 12px; color: #6b7280; margin: 5px 0 0 0;">${document.getElementById('shipmentsChangeKPI').textContent}</p>
            </div>
          </div>
        </div>
        
        <div style="background-color: #f3f4f6; border-radius: 8px; padding: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div>
              <p style="font-size: 14px; color: #6b7280; margin: 0 0 5px 0;">Total Cost</p>
              <p style="font-size: 24px; font-weight: bold; color: #111827; margin: 0;">${document.getElementById('totalFreightKPI').textContent}</p>
              <p style="font-size: 12px; color: #6b7280; margin: 5px 0 0 0;">${document.getElementById('freightChangeKPI').textContent}</p>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Add extra metrics section
    reportContainer.innerHTML += `
      <div style="margin-bottom: 30px;">
        <h2 style="font-size: 18px; color: #111827; margin-bottom: 15px;">Shipping Metrics (${this.chartFilters.extraMetrics})</h2>
        <div style="height: 150px;">
          ${document.getElementById('extraMetricsSection').innerHTML}
        </div>
      </div>
    `;
    
    // Clone all charts with consistent sizes for A4 paper
    const chartContainers = this.container.querySelectorAll('[id$="Chart"]');
    
    // First row of charts (2 columns)
    let chartSection = '<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 30px;">';
    
    // Add Freight Cost and Shipment Volume charts
    const costChart = document.getElementById('dailyFreightCostChart');
    const volumeChart = document.getElementById('shipmentVolumeChart');
    
    // Get chart titles
    const costTitle = costChart.closest('.bg-white').querySelector('h3').textContent;
    const volumeTitle = volumeChart.closest('.bg-white').querySelector('h3').textContent;
    
    // Create high-quality versions for printing
    const costChartData = this.getTimeFilteredCostData(this.chartFilters.dailyFreightCost);
    const costChartValues = costChartData.map(item => item.cost);
    const costLabels = costChartData.map(item => {
      const date = new Date(item.date);
      return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
    });
    
    const volumeData = this.getGroupedShipmentVolume(this.chartFilters.shipmentVolume);
    const volumeBarData = volumeData.map(item => ({
      label: item.label,
      value: item.count
    }));
    
    chartSection += `
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">${costTitle}</h2>
        ${createLineChart(costChartValues, 350, 200, '#3B82F6', costLabels, false)}
      </div>
      
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">${volumeTitle}</h2>
        ${createBarChart(volumeBarData, 350, 200, ['#60A5FA', '#3B82F6', '#2563EB', '#1D4ED8'], false, false)}
      </div>
    `;
    
    chartSection += '</div>';
    
    // Second row of charts (2 columns)
    chartSection += '<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 30px;">';
    
    // Add Days of Week and Carrier charts
    const daysChart = document.getElementById('shipmentByDayChart');
    const carrierChart = document.getElementById('shipmentByCarrierChart');
    
    const daysTitle = daysChart.closest('.bg-white').querySelector('h3').textContent;
    const carrierTitle = carrierChart.closest('.bg-white').querySelector('h3').textContent;
    
    // Create high-quality versions for printing
    const daysData = this.getShipmentsByDayOfWeek(this.chartFilters.shipmentByDay);
    const daysBarData = daysData.map(item => ({
      label: item.day,
      value: item.count
    }));
    
    const carriersData = this.getShipmentsByCarrier(this.chartFilters.shipmentByCarrier);
    const carriersBarData = carriersData.map(item => ({
      label: item.carrier,
      value: item.count
    }));
    
    chartSection += `
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">${daysTitle}</h2>
        ${createBarChart(daysBarData, 350, 200, ['#A78BFA', '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6', '#4C1D95', '#4338CA'], false, false)}
      </div>
      
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">${carrierTitle}</h2>
        ${createBarChart(carriersBarData, 350, 200, ['#F59E0B', '#D97706', '#B45309', '#92400E', '#78350F'], true, false)}
      </div>
    `;
    
    chartSection += '</div>';
    
    // Third row (with page break before)
    chartSection += '<div class="pagebreak" style="page-break-before: always;"></div>';
    chartSection += '<div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 30px; margin-top: 30px;">';
    
    // Add Customer and Country Distribution charts
    const customerData = this.getCustomerShipments(this.chartFilters.customerShipments, 10);
    const countryData = this.getCountryDistribution(this.chartFilters.topDestination);
    
    chartSection += `
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">Shipments by Customer</h2>
        ${createCustomerShipmentChart(customerData, 350, 300, ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'], false)}
      </div>
      
      <div style="background-color: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
        <h2 style="font-size: 16px; margin: 0 0 10px 0; color: #374151;">Country Distribution</h2>
        ${createPieChart(countryData, 350, 300, ['#8B5CF6', '#6D28D9', '#5B21B6', '#4C1D95', '#EF4444', '#F59E0B'], false)}
      </div>
    `;
    
    chartSection += '</div>';
    
    reportContainer.innerHTML += chartSection;
    
    // Add table data (improved styling)
    const tableData = this.historyData.slice(0, 20);
    let tableHtml = `
      <h2 style="font-size: 20px; margin: 30px 0 15px 0; color: #111827; border-bottom: 1px solid #e5e7eb; padding-bottom: 10px;">Recent Shipping History</h2>
      <table style="width: 100%; border-collapse: collapse; font-size: 13px; margin-bottom: 30px;">
        <thead>
          <tr style="background-color: #f3f4f6;">
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">User</th>
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">Reference</th>
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">Customer</th>
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">Carrier</th>
            <th style="padding: 10px; text-align: left; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">Date</th>
            <th style="padding: 10px; text-align: right; border-bottom: 1px solid #e5e7eb; font-weight: 600; color: #374151;">Cost</th>
          </tr>
        </thead>
        <tbody>
    `;
    
    tableData.forEach((item, index) => {
      const cost = parseFloat(item["Freight Cost"]) || 0;
      tableHtml += `
        <tr style="background-color: ${index % 2 === 0 ? 'white' : '#f9fafb'};">
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item["User Name"] || 'Unknown'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item["Reference Number"] || 'N/A'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item["Customer Name"] || 'N/A'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item["Carrier"] || 'N/A'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb;">${item["Date"] || 'N/A'}</td>
          <td style="padding: 10px; border-bottom: 1px solid #e5e7eb; text-align: right;">$${cost.toFixed(2)}</td>
        </tr>
      `;
    });
    
    tableHtml += `
        </tbody>
      </table>
      
      <div style="text-align: center; margin-top: 30px; font-size: 12px; color: #6b7280;">
        <p>Generated by Shipping Analytics Dashboard</p>
      </div>
    `;
    
    reportContainer.innerHTML += tableHtml;
    
    // Add to document temporarily
    document.body.appendChild(reportContainer);
    
    // IMPROVED: Use setTimeout to ensure all charts are fully rendered before printing
    setTimeout(() => {
      // Open in a new window with improved print CSS
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>Shipping Analytics Report</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                margin: 0; 
                padding: 0;
                background-color: white;
              }
              .analytics-report {
                background-color: white;
                box-sizing: border-box;
              }
              @page {
                size: A4;
                margin: 0;
              }
              @media print {
                .analytics-report {
                  width: 210mm;
                  padding: 20mm;
                  box-sizing: border-box;
                }
                .pagebreak {
                  page-break-before: always;
                  margin-top: 40px;
                }
                /* Ensure background colors print */
                * {
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                }
              }
            </style>
          </head>
          <body>
            ${reportContainer.outerHTML}
            <script>
              window.onload = function() {
                // Delay printing to ensure all chart SVGs are fully rendered
                setTimeout(function() {
                  window.print();
                }, 1000);
              }
            </script>
          </body>
          </html>
        `);
        printWindow.document.close();
      }
      
      // Remove temp container
      document.body.removeChild(reportContainer);
    }, 500);
  }
  
  // Open fullscreen view in new tab
  openFullscreenView() {
    // Save current data to storage to ensure the new window has access
    this.saveDataToStorage();
    
    // Also save current filter settings
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.set({
        'analyticsChartFilters': this.chartFilters
      }, () => {
        if (chrome.runtime.lastError) {
          console.error('Error saving chart filters:', chrome.runtime.lastError);
        }
      });
    } else {
      try {
        localStorage.setItem('analyticsChartFilters', JSON.stringify(this.chartFilters));
      } catch (e) {
        console.error('Error saving chart filters to localStorage:', e);
      }
    }
    
    const popupUrl = chrome.runtime.getURL('analytics/fullscreen.html');
    chrome.tabs.create({ url: popupUrl });
  }

  renderCharts() {
    if (!this.historyData || this.historyData.length === 0) {
      console.log("No data available for charts");
      return;
    }

    this.renderDailyFreightCostChart();
    this.renderShipmentVolumeChart();
    this.renderShipmentByDayChart();
    this.renderShipmentByCarrierChart();
    this.renderCarrierCostChart();
    this.renderCarrierUtilizationChart();
    this.renderTopProductsChart();
    this.renderTopDestinationChart();
    this.renderCustomerShipmentsChart();
  }

  updateCharts() {
    this.renderCharts();
    
    // Update the history table with new data
    if (this.historyTable) {
      this.historyTable.updateData(this.historyData.slice(0, 10));
    }
    
    // Update KPI cards
    this.updateKPICards();
    
    // Update extra metrics
    this.renderExtraMetrics();
    
    // Update last updated time
    const lastUpdatedEl = this.container.querySelector('.text-xs.text-gray-500');
    if (lastUpdatedEl) {
      lastUpdatedEl.textContent = `Last updated: ${this.lastUpdate.toLocaleTimeString()}`;
    }
  }

  renderDailyFreightCostChart() {
    const chartContainer = document.getElementById('dailyFreightCostChart');
    if (!chartContainer) return;
    
    // Get appropriate time-filtered data based on selected filter
    const timeFilteredData = this.getTimeFilteredCostData(this.chartFilters.dailyFreightCost);
    
    // Format for line chart
    const chartData = timeFilteredData.map(item => item.cost);
    
    // Format labels with short date formats to prevent overlap
    const labels = timeFilteredData.map(item => {
      const date = new Date(item.date);
      // For week view, show day of week
      if (this.chartFilters.dailyFreightCost === 'week') {
        return date.toLocaleDateString(undefined, { weekday: 'short' });
      }
      // For month view, show day and month
      else if (this.chartFilters.dailyFreightCost === 'month') {
        return date.toLocaleDateString(undefined, { day: 'numeric', month: 'short' });
      }
      // For quarter or year, show month only or month/year
      else {
        return date.toLocaleDateString(undefined, { month: 'short', year: '2-digit' });
      }
    });
    
    // Create a more specialized line chart with labels
    chartContainer.innerHTML = createLineChart(
      chartData, 
      chartContainer.clientWidth, 
      chartContainer.clientHeight, 
      '#3B82F6',
      labels,
      true // compact mode for extension
    );
  }

  renderShipmentVolumeChart() {
    const chartContainer = document.getElementById('shipmentVolumeChart');
    if (!chartContainer) return;
    
    // Get grouped shipment volume data based on selected filter
    const shipmentVolume = this.getGroupedShipmentVolume(this.chartFilters.shipmentVolume);
    
    // Format for bar chart
    const barData = shipmentVolume.map(item => ({
      label: item.label,
      value: item.count
    }));
    
    chartContainer.innerHTML = createBarChart(
      barData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#60A5FA', '#3B82F6', '#2563EB', '#1D4ED8'],
      false, // vertical bars
      true // compact mode for extension
    );
  }

  // FIXED: Complete rewrite of the shipment by day of week chart to accurately
  // calculate distribution based on actual dates
  renderShipmentByDayChart() {
    const chartContainer = document.getElementById('shipmentByDayChart');
    if (!chartContainer) return;
    
    // Get shipments by day of week with improved calculation
    const shipmentsByDay = this.getShipmentsByDayOfWeek(this.chartFilters.shipmentByDay);
    
    // Format for bar chart
    const barData = shipmentsByDay.map(item => ({
      label: item.day,
      value: item.count,
      tooltip: `${item.day}: ${item.count} shipments`
    }));
    
    chartContainer.innerHTML = createBarChart(
      barData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#A78BFA', '#8B5CF6', '#7C3AED', '#6D28D9', '#5B21B6', '#4C1D95', '#4338CA'],
      false, // vertical bars
      true // compact mode
    );
  }

  renderShipmentByCarrierChart() {
    const chartContainer = document.getElementById('shipmentByCarrierChart');
    if (!chartContainer) return;
    
    // Get shipments by carrier
    const shipmentsByCarrier = this.getShipmentsByCarrier(this.chartFilters.shipmentByCarrier);
    
    // Format for bar chart
    const barData = shipmentsByCarrier.map(item => ({
      label: this.shortenCarrierName(item.carrier),
      value: item.count,
      tooltip: `${item.carrier}: ${item.count} shipments`
    }));
    
    chartContainer.innerHTML = createBarChart(
      barData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#F59E0B', '#D97706', '#B45309', '#92400E', '#78350F'],
      true, // horizontal
      true // compact mode
    );
  }

  renderCarrierCostChart() {
    const chartContainer = document.getElementById('carrierCostChart');
    if (!chartContainer) return;
    
    // Get carrier costs filtered by time
    const carrierCosts = this.getTimeFilteredCarrierCosts(this.chartFilters.carrierCost);
    
    // Format for horizontal bar chart
    const barData = carrierCosts.map(item => ({
      label: this.shortenCarrierName(item.carrier), // Shorten carrier names
      value: Math.round(item.avgCost * 100) / 100, // Round to 2 decimal places
      tooltip: `${item.carrier}: $${item.avgCost.toFixed(2)} avg. per shipment`
    }));
    
    chartContainer.innerHTML = createBarChart(
      barData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#F59E0B', '#D97706', '#B45309', '#92400E', '#78350F'],
      true, // horizontal
      true // compact mode
    );
  }

  renderCarrierUtilizationChart() {
    const chartContainer = document.getElementById('carrierUtilizationChart');
    if (!chartContainer) return;
    
    // Get carrier utilization data
    const carrierUtilization = this.calculateCarrierUtilization();
    
    // Format for pie chart - only use top carriers and group others
    let pieData = [];
    const topCarriers = carrierUtilization.slice(0, 4); // Top 4 carriers
    
    if (carrierUtilization.length > 4) {
      // Calculate sum of remaining carriers
      const otherCarriersSum = carrierUtilization.slice(4).reduce((sum, item) => sum + item.percentage, 0);
      
      // Add top carriers
      pieData = topCarriers.map(item => ({
        label: this.shortenCarrierName(item.carrier),
        value: item.percentage,
        tooltip: `${item.carrier}: ${item.percentage}% (${item.count} shipments)`
      }));
      
      // Add "Others" category
      if (otherCarriersSum > 0) {
        pieData.push({
          label: 'Others',
          value: otherCarriersSum,
          tooltip: `Other carriers: ${Math.round(otherCarriersSum)}%`
        });
      }
    } else {
      pieData = carrierUtilization.map(item => ({
        label: this.shortenCarrierName(item.carrier),
        value: item.percentage,
        tooltip: `${item.carrier}: ${item.percentage}% (${item.count} shipments)`
      }));
    }
    
    chartContainer.innerHTML = createPieChart(
      pieData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#EF4444', '#F97316', '#F59E0B', '#10B981', '#3B82F6'],
      true // compact mode
    );
  }

  renderTopProductsChart() {
    const chartContainer = document.getElementById('topProductsChart');
    if (!chartContainer) return;
    
    // Get top products filtered by time period
    const topProducts = this.getTimeFilteredTopProducts(this.chartFilters.topProducts, 6);
    
    // Format for horizontal bar chart
    const barData = topProducts.map(item => ({
      label: this.shortenProductId(item.id),
      value: item.count,
      tooltip: `${item.id}: Shipped ${item.count} times`
    }));
    
    chartContainer.innerHTML = createBarChart(
      barData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#10B981', '#059669', '#047857', '#065F46', '#064E3B', '#022C22'],
      true, // horizontal
      true // compact mode
    );
  }

  // REPLACED: Instead of destination chart, use a pie chart for country distribution
  renderTopDestinationChart() {
    const chartContainer = document.getElementById('topDestinationChart');
    if (!chartContainer) return;
    
    // Get country distribution data
    const countryDistribution = this.getCountryDistribution(this.chartFilters.topDestination);
    
    // Create pie chart with the country distribution data
    chartContainer.innerHTML = createPieChart(
      countryDistribution,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#8B5CF6', '#6D28D9', '#5B21B6', '#4C1D95', '#EF4444', '#F59E0B', '#3B82F6'],
      true // compact mode
    );
  }

  renderCustomerShipmentsChart() {
    const chartContainer = document.getElementById('customerShipmentsChart');
    if (!chartContainer) return;
    
    // Get customer shipment data
    const customerData = this.getCustomerShipments(this.chartFilters.customerShipments, 6);
    
    // Use the customer shipment chart
    chartContainer.innerHTML = createCustomerShipmentChart(
      customerData,
      chartContainer.clientWidth,
      chartContainer.clientHeight,
      ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'],
      true // compact mode
    );
  }
  
  // DATA PROCESSING METHODS
  
  getTimeFilteredData(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'day':
        // Today only
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        // This week (starting from Monday)
        const day = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
        startDate.setDate(diff);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        // This month
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'quarter':
        // This quarter
        const quarter = Math.floor(today.getMonth() / 3);
        startDate.setMonth(quarter * 3, 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'year':
        // This year
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      default:
        // Default to this month
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
    }
    
    // Filter records by date
    return this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
  }
  
  getTimeFilteredCostData(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by date and calculate costs
    const dailyCosts = {};
    
    filteredRecords.forEach(record => {
      const date = record.Date;
      let cost = parseFloat(record["Freight Cost"]) || 0;
      if (isNaN(cost)) cost = 0;
      
      if (!dailyCosts[date]) {
        dailyCosts[date] = { date: date, cost: 0, count: 0 };
      }
      
      dailyCosts[date].cost += cost;
      dailyCosts[date].count += 1;
    });
    
    // Convert to array and sort by date
    return Object.values(dailyCosts)
      .sort((a, b) => new Date(a.date) - new Date(b.date));
  }
  
  getGroupedShipmentVolume(groupBy) {
    // Get date range based on group
    const today = new Date();
    let startDate = new Date();
    let format = {};
    
    switch (groupBy) {
      case 'week':
        startDate.setDate(today.getDate() - 7 * 8); // Last 8 weeks
        format = { month: 'short', day: 'numeric' };
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 12); // Last 12 months
        format = { month: 'short', year: '2-digit' };
        break;
      case 'quarter':
        startDate.setFullYear(today.getFullYear() - 2); // Last 8 quarters (2 years)
        format = { month: 'short', year: 'numeric' };
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
        format = { month: 'short', day: 'numeric' };
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by period
    const groupedData = {};
    
    filteredRecords.forEach(record => {
      let groupKey;
      const recordDate = new Date(record.Date);
      
      switch (groupBy) {
        case 'week':
          // Group by week - use Monday as start of week
          const weekStart = new Date(recordDate);
          const day = recordDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
          const diff = recordDate.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
          weekStart.setDate(diff);
          groupKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          // Group by month
          groupKey = `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'quarter':
          // Group by quarter
          const quarter = Math.floor(recordDate.getMonth() / 3) + 1;
          groupKey = `${recordDate.getFullYear()}-Q${quarter}`;
          break;
        default:
          groupKey = record.Date;
      }
      
      if (!groupedData[groupKey]) {
        let label = '';
        
        // Create human-readable label
        switch (groupBy) {
          case 'week':
            const weekStart = new Date(groupKey);
            label = `${weekStart.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}`;
            break;
          case 'month':
            const [year, month] = groupKey.split('-');
            label = new Date(parseInt(year), parseInt(month) - 1, 1).toLocaleDateString(undefined, { month: 'short', year: '2-digit' });
            break;
          case 'quarter':
            label = groupKey;
            break;
          default:
            label = new Date(groupKey).toLocaleDateString(undefined, format);
        }
        
        groupedData[groupKey] = { 
          key: groupKey, 
          label: label, 
          count: 0 
        };
      }
      
      groupedData[groupKey].count += 1;
    });
    
    // Convert to array and sort by key
    return Object.values(groupedData)
      .sort((a, b) => a.key.localeCompare(b.key));
  }

  // FIXED: Completely rewritten to correctly calculate day of week distribution
  getShipmentsByDayOfWeek(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'week':
        startDate.setDate(today.getDate() - 14); // Last 2 weeks
        break;
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      default:
        startDate.setDate(today.getDate() - 14); // Default to 2 weeks
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Map day names to their full names for better readability
    const dayNames = [
      { dayIndex: 1, day: 'Monday' },
      { dayIndex: 2, day: 'Tuesday' },
      { dayIndex: 3, day: 'Wednesday' },
      { dayIndex: 4, day: 'Thursday' },
      { dayIndex: 5, day: 'Friday' },
      { dayIndex: 6, day: 'Saturday' },
      { dayIndex: 0, day: 'Sunday' }
    ];
    
    // Initialize the counts array with all days set to 0
    const dayCounts = dayNames.map(item => ({ ...item, count: 0 }));
    
    // Count shipments by day of week using the date object's getDay method
    filteredRecords.forEach(record => {
      if (!record.Date) return;
      try {
        const recordDate = new Date(record.Date);
        const dayOfWeek = recordDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
        
        // Find the corresponding day in our array and increment its count
        const dayIndex = dayCounts.findIndex(item => item.dayIndex === dayOfWeek);
        if (dayIndex !== -1) {
          dayCounts[dayIndex].count += 1;
        }
      } catch (error) {
        console.error(`Error processing date: ${record.Date}`, error);
      }
    });
    
    // Sort by day of week (Monday first)
    return dayCounts
      .sort((a, b) => {
        // Special sorting to put Monday first (index 1)
        const indexA = a.dayIndex === 0 ? 7 : a.dayIndex;
        const indexB = b.dayIndex === 0 ? 7 : b.dayIndex;
        return indexA - indexB;
      })
      .map(item => ({
        day: item.day,
        count: item.count
      }));
  }

  getShipmentsByCarrier(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by carrier
    const carrierCounts = {};
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      
      if (!carrierCounts[carrier]) {
        carrierCounts[carrier] = 0;
      }
      
      carrierCounts[carrier] += 1;
    });
    
    // Convert to array and sort by count (descending)
    return Object.entries(carrierCounts)
      .filter(([carrier]) => carrier !== 'Unknown' && carrier !== 'N/A')
      .map(([carrier, count]) => ({ carrier, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 6); // Top 6 carriers
  }
  
  getTimeFilteredCarrierCosts(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by carrier and calculate costs
    const carrierData = {};
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      let cost = parseFloat(record["Freight Cost"]) || 0;
      if (isNaN(cost)) cost = 0;
      
      if (!carrierData[carrier]) {
        carrierData[carrier] = { carrier: carrier, totalCost: 0, count: 0 };
      }
      
      carrierData[carrier].totalCost += cost;
      carrierData[carrier].count += 1;
    });
    
    // Calculate average costs and format
    return Object.values(carrierData)
      .map(item => ({
        carrier: item.carrier,
        totalCost: item.totalCost,
        avgCost: item.count > 0 ? item.totalCost / item.count : 0,
        count: item.count
      }))
      .filter(item => item.carrier !== 'Unknown' && item.carrier !== 'N/A')
      .sort((a, b) => b.avgCost - a.avgCost)
      .slice(0, 5); // Top 5 carriers
  }
  
  calculateCarrierUtilization() {
    // Count shipments by carrier in the last 90 days
    const carrierCounts = {};
    let totalShipments = 0;
    
    // Get records from last 90 days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90);
    
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate;
    });
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      
      if (!carrierCounts[carrier]) {
        carrierCounts[carrier] = 0;
      }
      
      carrierCounts[carrier] += 1;
      totalShipments += 1;
    });
    
    // Calculate percentages
    return Object.keys(carrierCounts)
      .filter(carrier => carrier !== 'Unknown' && carrier !== 'N/A')
      .map(carrier => ({
        carrier: carrier,
        count: carrierCounts[carrier],
        percentage: Math.round((carrierCounts[carrier] / totalShipments) * 100)
      }))
      .sort((a, b) => b.count - a.count);
  }
  
  getTimeFilteredTopProducts(timeFilter, limit = 5) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        // For "All Time", we don't filter by date, so no need to set startDate
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date (skip filtering if "All Time" is selected)
    const filteredRecords = timeFilter === 'year' 
      ? this.historyData
      : this.historyData.filter(record => {
          if (!record.Date) return false;
          const recordDate = new Date(record.Date);
          return recordDate >= startDate && recordDate <= today;
        });
    
    // Parse all inventory IDs and count occurrences
    const productCounts = {};
    
    filteredRecords.forEach(record => {
      if (!record["Inventory ID"]) return;
      
      // Split the comma-separated inventory IDs
      const inventoryIds = record["Inventory ID"].split(",").map(id => id.trim());
      
      inventoryIds.forEach(id => {
        if (!id || id === 'N/A') return;
        
        if (!productCounts[id]) {
          productCounts[id] = 0;
        }
        
        productCounts[id] += 1;
      });
    });
    
    // Convert to array and sort
    return Object.keys(productCounts)
      .map(id => ({
        id: id,
        count: productCounts[id]
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  // NEW: Country distribution function for pie chart
  getCountryDistribution(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by country
    const countryCounts = {};
    let totalShipments = 0;
    
    filteredRecords.forEach(record => {
      const country = record.Country || 'Unknown';
      if (country !== 'Unknown' && country !== 'N/A') {
        countryCounts[country] = (countryCounts[country] || 0) + 1;
        totalShipments++;
      }
    });
    
    // Convert to array, calculate percentages, and format for pie chart
    const countryData = Object.entries(countryCounts)
      .map(([country, count]) => ({
        label: country,
        value: Math.round((count / totalShipments) * 100),
        tooltip: `${country}: ${count} shipments (${Math.round((count / totalShipments) * 100)}%)`
      }))
      .sort((a, b) => b.value - a.value);
    
    // Limit to top countries and group others
    if (countryData.length > 6) {
      const topCountries = countryData.slice(0, 5);
      const otherCountries = countryData.slice(5);
      
      const otherValue = otherCountries.reduce((sum, item) => sum + item.value, 0);
      const otherCount = otherCountries.reduce((sum, country) => {
        // Extract the count from the tooltip (format: "Country: X shipments (Y%)")
        const match = country.tooltip.match(/: (\d+) shipment/);
        return sum + (match ? parseInt(match[1]) : 0);
      }, 0);
      
      topCountries.push({
        label: 'Other Countries',
        value: otherValue,
        tooltip: `Other Countries: ${otherCount} shipments (${otherValue}%)`
      });
      
      return topCountries;
    }
    
    return countryData;
  }

  getCustomerShipments(timeFilter, limit = 6) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by customer
    const customerCounts = {};
    
    filteredRecords.forEach(record => {
      const customer = record["Customer Name"] || record["Company Name"] || 'Unknown';
      
      if (!customerCounts[customer]) {
        customerCounts[customer] = 0;
      }
      
      customerCounts[customer] += 1;
    });
    
    // Convert to array and sort by count
    return Object.entries(customerCounts)
      .filter(([customer]) => customer !== 'Unknown' && customer !== 'N/A')
      .map(([customer, count]) => ({ customer, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }
  
  // Helper methods for formatting
  shortenCarrierName(name) {
    // Handle common carrier names to make them fit better in charts
    const carrierMap = {
      'FedEx': 'FedEx',
      'UPS': 'UPS',
      'USPS': 'USPS',
      'Federal Express': 'FedEx',
      'United Parcel Service': 'UPS',
      'Postal Service': 'USPS',
      'DHL': 'DHL',
      'Purolator': 'Purolator',
      'Manitoulin': 'Manitoulin',
      'Loomis': 'Loomis',
      'JazooExpress': 'Jazoo',
      'Rosenau': 'Rosenau',
    };
    
    return carrierMap[name] || (name.length > 8 ? name.substring(0, 7) + '...' : name);
  }
  
  shortenProductId(id) {
    // Shorten product IDs for display
    if (!id) return '';
    
    // If the ID has a dash, keep the part before the dash
    if (id.includes('-')) {
      const parts = id.split('-');
      return parts[0].length > 5 ? parts[0].substring(0, 5) + '...' : parts[0];
    }
    
    // Otherwise shorten to first 8 chars
    return id.length > 7 ? id.substring(0, 7) + '...' : id;
  }

  destroyComponent() {
    // Stop the observer
    if (this.visibilityObserver) {
      this.visibilityObserver.disconnect();
    }
    
    // Clear update interval
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    
    console.log("Analytics component destroyed");
  }
}