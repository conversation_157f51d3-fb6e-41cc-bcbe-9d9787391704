<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Project Map - Fullscreen View</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Use absolute paths to ensure resources load correctly -->
  <link href="./mapbox-gl.css" rel="stylesheet">
  <script src="./mapbox-gl.js"></script>
  <style>
    body, html { 
      margin: 0; 
      padding: 0; 
      height: 100vh; 
      width: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      overflow: hidden;
    }
    #map { 
      position: absolute; 
      top: 0; 
      bottom: 0; 
      width: 100%; 
      height: 100%;
    }
    .map-overlay {
      position: absolute;
      top: 10px;
      left: 10px;
      background: white;
      padding: 10px;
      border-radius: 4px;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
      z-index: 10;
      max-width: 300px;
      max-height: 80%;
      overflow-y: auto;
    }
    .header-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
    }
    .map-title {
      font-weight: bold;
      margin-bottom: 0;
      font-size: 18px;
      flex: 1;
    }
    #closeButton {
      margin-left: 10px;
      padding: 4px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .search-container {
      margin: 8px 0;
      width: 100%;
    }
    .search-input {
      width: 100%;
      padding: 6px 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    .project-item {
      padding: 8px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
    }
    .project-item:hover {
      background: #f0f0f0;
    }
    .popup-content h3 {
      margin-top: 0;
      margin-bottom: 5px;
    }
    .popup-content img {
      max-width: 100%;
      border-radius: 4px;
      margin: 5px 0;
    }
    .dark-mode {
      background-color: #1a1a1a;
      color: white;
    }
    .dark-mode .map-overlay {
      background-color: #2d2d2d;
      color: white;
    }
    .dark-mode .project-item {
      border-color: #444;
    }
    .dark-mode .project-item:hover {
      background: #444;
    }
    .dark-mode .search-input {
      background-color: #333;
      border-color: #555;
      color: white;
    }
    .location-btn {
      position: absolute;
      bottom: 80px;
      right: 10px;
      background: white;
      border: none;
      border-radius: 4px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 10px rgba(0,0,0,0.2);
      cursor: pointer;
      z-index: 5;
    }
    .dark-mode .location-btn {
      background-color: #2d2d2d;
      color: white;
    }
    
    /* Loading indicator */
    .loading-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 20;
    }
    .dark-mode .loading-container {
      background-color: rgba(26, 26, 26, 0.8);
    }
    .loading-spinner {
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <!-- Loading indicator -->
  <div id="loadingContainer" class="loading-container">
    <div class="loading-spinner"></div>
  </div>

  <div id="map"></div>
  <div class="map-overlay">
    <div class="header-container">
      <div class="map-title">Project Locations</div>
      <button id="closeButton" class="text-gray-500 hover:text-gray-700" title="Close Fullscreen View">
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="search-container">
      <input type="text" id="projectSearch" class="search-input" placeholder="Search projects...">
    </div>
    <div id="project-list"></div>
  </div>
  
  <!-- Location button -->
  <button id="locationButton" class="location-btn" title="Show my location">
    <i class="fas fa-location-arrow"></i>
  </button>
  
  <!-- Debug script moved to external file -->
  <script src="./map-debug.js"></script>
  
  <!-- Load the map fullscreen script -->
  <script src="./map-fullscreen.js"></script>
  
  <!-- Button handlers script -->
  <script src="./map-handlers.js"></script>
</body>
</html> 