<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sales Order Parsing Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Sales Order Parsing Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-lg font-semibold mb-4">Test Data</h2>
            <button id="testParsingBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Test Parsing Logic
            </button>
            <button id="testModalBtn" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 ml-2">
                Test Modal
            </button>
        </div>

        <div id="results" class="bg-white rounded-lg shadow p-6">
            <h2 class="text-lg font-semibold mb-4">Results</h2>
            <div id="output"></div>
        </div>
    </div>

    <script>
        // Sample API data based on your example
        const sampleApiData = [
            {
                "id": "d4ff0f26-da1c-ee11-8354-027fe675bf28",
                "rowNumber": 1,
                "OrderNbr": { "value": "006151" },
                "Status": { "value": "Back Order" },
                "OrderType": { "value": "SO" },
                "OrderTotal": { "value": 60284.44 },
                "OrderedQty": { "value": 13.0 },
                "CreatedDate": { "value": "2023-07-07T00:00:00+00:00" },
                "ShippingDate": { "value": "2023-07-07T00:00:00+00:00" },
                "inventoryitem": [
                    {
                        "id": "e3a58032-da1c-ee11-8354-027fe675bf28",
                        "rowNumber": 1,
                        "inventorybom": [
                            {
                                "id": "0b69f6e7-2698-46c1-bcae-185a874ab2f5",
                                "rowNumber": 1,
                                "InventoryID": { "value": "331SDS" },
                                "ProductionNbr": { "value": "SYS000652" }
                            }
                        ],
                        "InventoryID": {},
                        "LineDescription": { "value": "H2S Analyzer, Dual Sensing, Class 1, Div. 2, Second Gen" },
                        "Quantity": { "value": 1.0 },
                        "UnitPrice": { "value": 22540.0 },
                        "UOM": { "value": "EACH" }
                    },
                    {
                        "id": "6eb4141f-6e22-ee11-8354-027fe675bf28",
                        "rowNumber": 3,
                        "inventorybom": [
                            {
                                "id": "4d9dbc71-5331-4acb-b0c0-d2bae4e4fe99",
                                "rowNumber": 1,
                                "InventoryID": { "value": "1100399" },
                                "ProductionNbr": {}
                            }
                        ],
                        "InventoryID": { "value": "1100399" },
                        "LineDescription": { "value": "Probe, direct drive, A+ Genie 760, SS, 1\" NPT process connection" },
                        "Quantity": { "value": 1.0 },
                        "UnitPrice": { "value": 5160.0 },
                        "UOM": { "value": "EACH" }
                    }
                ]
            }
        ];

        // Test parsing function (simplified version of the actual parsing logic)
        function testParseAcumaticaSalesOrders(salesOrderData) {
            return salesOrderData.map(order => {
                const id = order.id || `so-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
                const orderNbr = order.OrderNbr?.value || '';
                const status = order.Status?.value || '';
                const orderType = order.OrderType?.value || '';
                const orderTotal = parseFloat(order.OrderTotal?.value || 0);
                const orderedQty = parseFloat(order.OrderedQty?.value || 0);

                const lineItems = [];
                let totalBOMItems = 0;

                if (Array.isArray(order.inventoryitem) && order.inventoryitem.length > 0) {
                    order.inventoryitem.forEach(item => {
                        const quantity = parseFloat(item.Quantity?.value || 0);
                        const unitPrice = parseFloat(item.UnitPrice?.value || 0);
                        const extAmount = quantity * unitPrice;

                        // Process BOM items
                        const bomItems = [];
                        let bomInventoryID = '';
                        if (Array.isArray(item.inventorybom) && item.inventorybom.length > 0) {
                            item.inventorybom.forEach(bom => {
                                let productionNbr = '';
                                if (bom.ProductionNbr?.value) {
                                    productionNbr = bom.ProductionNbr.value.trim();
                                }

                                let bomInvID = '';
                                if (bom.InventoryID?.value) {
                                    bomInvID = bom.InventoryID.value.trim();
                                }

                                if (!bomInventoryID && bomInvID) {
                                    bomInventoryID = bomInvID;
                                }

                                bomItems.push({
                                    id: bom.id || `bom-${id}-${Math.random().toString(36).substring(2, 10)}`,
                                    InventoryID: bomInvID,
                                    ProductionNbr: productionNbr,
                                    rowNumber: bom.rowNumber || 0
                                });

                                if (productionNbr || bomInvID) {
                                    totalBOMItems++;
                                }
                            });
                        }

                        // Extract inventory ID
                        let inventoryID = '';
                        let isFromBOM = false;

                        if (item.InventoryID?.value) {
                            inventoryID = item.InventoryID.value.trim();
                        }

                        // If no inventory ID from main field, use BOM inventory ID
                        if (!inventoryID && bomInventoryID) {
                            inventoryID = bomInventoryID;
                            isFromBOM = true;
                        }

                        if (!inventoryID) {
                            inventoryID = 'N/A';
                        }

                        const lineItem = {
                            id: item.id || `line-${id}-${Math.random().toString(36).substring(2, 10)}`,
                            rowNumber: item.rowNumber || 0,
                            InventoryID: inventoryID,
                            IsFromBOM: isFromBOM,
                            OriginalInventoryID: item.InventoryID?.value || '',
                            BOMInventoryID: bomInventoryID,
                            LineDescription: item.LineDescription?.value || '',
                            Quantity: quantity,
                            UnitPrice: unitPrice,
                            UOM: item.UOM?.value || '',
                            ExtAmount: extAmount,
                            BOMItems: bomItems,
                            BOMCount: bomItems.length
                        };

                        lineItems.push(lineItem);
                    });
                }

                return {
                    id,
                    OrderNbr: orderNbr,
                    Status: status,
                    OrderType: orderType,
                    OrderTotal: orderTotal,
                    OrderedQty: orderedQty,
                    LineItems: lineItems,
                    LineItemCount: lineItems.length,
                    TotalBOMItems: totalBOMItems
                };
            });
        }

        document.getElementById('testParsingBtn').addEventListener('click', () => {
            // Test the parsing logic directly
            const parsedData = testParseAcumaticaSalesOrders(sampleApiData);

            console.log('Parsed data:', parsedData);

            const output = document.getElementById('output');
            output.innerHTML = `
                <h3 class="font-semibold mb-2">Parsed Sales Orders:</h3>
                <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto">${JSON.stringify(parsedData, null, 2)}</pre>
            `;
        });

        document.getElementById('testModalBtn').addEventListener('click', () => {
            // Create a test order
            const testOrder = {
                id: "test-order",
                OrderNbr: "TEST001",
                Status: "Open",
                OrderType: "SO",
                OrderTotal: 1000.00,
                OrderedQty: 2.0,
                LineItemCount: 2,
                TotalBOMItems: 1,
                CreatedDate: { year: 2023, month: 7, day: 7 },
                ShippingDate: { year: 2023, month: 7, day: 15 },
                LineItems: [
                    {
                        id: "line1",
                        InventoryID: "331SDS",
                        IsFromBOM: true,
                        BOMInventoryID: "331SDS",
                        LineDescription: "Test Item with BOM ID",
                        Quantity: 1.0,
                        UnitPrice: 500.0,
                        UOM: "EACH",
                        ExtAmount: 500.0,
                        BOMItems: [
                            {
                                id: "bom1",
                                InventoryID: "331SDS",
                                ProductionNbr: "SYS000652"
                            }
                        ],
                        BOMCount: 1
                    },
                    {
                        id: "line2",
                        InventoryID: "1100399",
                        IsFromBOM: false,
                        OriginalInventoryID: "1100399",
                        LineDescription: "Regular inventory item",
                        Quantity: 1.0,
                        UnitPrice: 500.0,
                        UOM: "EACH",
                        ExtAmount: 500.0,
                        BOMItems: [],
                        BOMCount: 0
                    }
                ]
            };

            // Show the modal using the test function
            testShowOrderDetails(testOrder);
        });

        // Test modal function
        function testShowOrderDetails(order) {
            const escapeHtml = (text) => {
                if (typeof text !== 'string') return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            const formatCurrency = (amount) => {
                if (typeof amount !== 'number') return '$0.00';
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(amount);
            };

            const formatDate = (dateObj) => {
                if (!dateObj || !dateObj.year) return 'N/A';
                try {
                    const date = new Date(dateObj.year, dateObj.month - 1, dateObj.day);
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                    });
                } catch (error) {
                    return 'Invalid Date';
                }
            };

            const modalHtml = `
                <div id="orderDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-10 mx-auto p-6 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
                        <div class="flex items-center justify-between pb-3 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">
                                Sales Order Details - ${escapeHtml(order.OrderNbr)}
                            </h3>
                            <button id="closeOrderModal" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <div class="mt-4">
                            <!-- Line Items Table -->
                            <div class="mb-4">
                                <h4 class="text-lg font-medium text-gray-900 mb-3">Line Items</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Extended</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Production</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            ${order.LineItems.map(item => `
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                        <div class="flex items-center space-x-2">
                                                            <span>${escapeHtml(item.InventoryID)}</span>
                                                            ${item.IsFromBOM ? `
                                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                                                    <i class="fas fa-cogs mr-1"></i>
                                                                    BOM ID
                                                                </span>
                                                            ` : item.OriginalInventoryID ? `
                                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                    <i class="fas fa-box mr-1"></i>
                                                                    Inventory
                                                                </span>
                                                            ` : ''}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        <div class="max-w-xs truncate" title="${escapeHtml(item.LineDescription)}">
                                                            ${escapeHtml(item.LineDescription)}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${item.Quantity.toFixed(2)} ${escapeHtml(item.UOM)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${formatCurrency(item.UnitPrice)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${formatCurrency(item.ExtAmount)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${item.BOMCount > 0 ? `
                                                            <div class="space-y-1">
                                                                ${item.BOMItems.map(bom => `
                                                                    <div class="flex items-center space-x-2">
                                                                        ${bom.ProductionNbr ? `
                                                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                                                <i class="fas fa-industry mr-1"></i>
                                                                                ${escapeHtml(bom.ProductionNbr)}
                                                                            </span>
                                                                        ` : `
                                                                            <span class="text-xs text-gray-500">No Production #</span>
                                                                        `}
                                                                    </div>
                                                                `).join('')}
                                                            </div>
                                                        ` : `
                                                            <span class="text-xs text-gray-500">No BOM</span>
                                                        `}
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end pt-4 border-t border-gray-200">
                            <button id="closeOrderModalBtn" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Add event listeners for closing modal
            const closeModal = () => {
                const modal = document.getElementById('orderDetailsModal');
                if (modal) {
                    document.removeEventListener('keydown', handleEscape);
                    modal.remove();
                    console.log('Modal closed and removed from DOM');
                }
            };

            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    console.log('Escape key pressed');
                    closeModal();
                }
            };

            // Set up event listeners with delay
            setTimeout(() => {
                console.log('Setting up modal event listeners...');

                const closeHeaderBtn = document.getElementById('closeOrderModal');
                console.log('Header close button found:', !!closeHeaderBtn);
                if (closeHeaderBtn) {
                    closeHeaderBtn.onclick = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Close header button clicked');
                        closeModal();
                    };
                    console.log('Header close button listener added');
                }

                const closeFooterBtn = document.getElementById('closeOrderModalBtn');
                console.log('Footer close button found:', !!closeFooterBtn);
                if (closeFooterBtn) {
                    closeFooterBtn.onclick = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Close footer button clicked');
                        closeModal();
                    };
                    console.log('Footer close button listener added');
                }

                const modalElement = document.getElementById('orderDetailsModal');
                console.log('Modal element found:', !!modalElement);
                if (modalElement) {
                    modalElement.onclick = (e) => {
                        if (e.target === modalElement) {
                            console.log('Backdrop clicked');
                            closeModal();
                        }
                    };
                    console.log('Backdrop click listener added');
                }

                document.addEventListener('keydown', handleEscape);
                console.log('All modal event listeners set up successfully');
            }, 200);
        }
    </script>
</body>
</html>
