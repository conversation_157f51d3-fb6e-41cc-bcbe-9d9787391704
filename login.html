<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Envent Bridge</title>
    <style>
    body, html {
        width: 800px;
        height: 600px;
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        overflow: hidden;
        background-color: white;
        color: black;
    }
    .slide-container {
        width: 100%;
        height: 100%;
        display: flex;
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
        background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
        position: relative;
        overflow: hidden;
    }
    .background-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        pointer-events: none;
    }
    .shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(0, 102, 255, 0.1), rgba(0, 102, 255, 0.05));
        animation: float 20s infinite;
    }
    .shape:nth-child(1) {
        width: 300px;
        height: 300px;
        top: -150px;
        right: -150px;
    }
    .shape:nth-child(2) {
        width: 200px;
        height: 200px;
        bottom: -100px;
        left: -100px;
        animation-delay: -5s;
    }
    .dots-pattern {
        position: absolute;
        width: 100%;
        height: 100%;
        background-image: radial-gradient(#0066ff 1px, transparent 1px);
        background-size: 30px 30px;
        opacity: 0.1;
    }
    @keyframes float {
        0%, 100% { transform: translate(0, 0); }
        25% { transform: translate(10px, -10px); }
        50% { transform: translate(0, -20px); }
        75% { transform: translate(-10px, -10px); }
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .slide {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1.5rem;
        box-sizing: border-box;
        position: relative;
        z-index: 1;
    }
    .welcome-content {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1);
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        padding: 2rem;
        box-sizing: border-box;
        border: 1px solid rgba(255, 255, 255, 0.5);
    }
    .badge {
        display: inline-block;
        padding: 4px 12px;
        background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
        color: white;
        border-radius: 9999px;
        font-size: 12px;
        margin-bottom: 1rem;
        width: fit-content;
        box-shadow: 0 2px 4px rgba(0, 102, 255, 0.2);
    }
    .welcome-grid {
        display: grid;
        grid-template-columns: 1fr 1.2fr;
        gap: 2rem;
        align-items: center;
        flex-grow: 1;
        margin: 1rem 0;
    }
    .welcome-text {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    .title-group {
        margin-bottom: 0.5rem;
    }
    h1 {
        font-size: 1.75rem;
        line-height: 1.2;
        margin: 0 0 0.25rem 0;
        font-weight: bold;
        white-space: nowrap;
        background: linear-gradient(135deg, #000000 0%, #333333 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .subtitle {
        font-size: 1rem;
        color: #6b7280;
        margin: 0;
        font-weight: 500;
    }
    .feature-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
    }
    .feature-item {
        display: flex;
        gap: 0.75rem;
        align-items: flex-start;
        padding: 0.75rem;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 8px;
        border: 1px solid rgba(0, 102, 255, 0.1);
        transition: all 0.3s ease;
        animation: slideIn 0.5s ease forwards;
        opacity: 0;
    }
    .feature-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 102, 255, 0.1);
        background: rgba(255, 255, 255, 0.8);
    }
    .feature-item:nth-child(1) { animation-delay: 0.1s; }
    .feature-item:nth-child(2) { animation-delay: 0.2s; }
    .feature-item:nth-child(3) { animation-delay: 0.3s; }
    @keyframes slideIn {
        from { opacity: 0; transform: translateX(-20px); }
        to { opacity: 1; transform: translateX(0); }
    }
    .feature-icon {
        color: #0066ff;
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        filter: drop-shadow(0 2px 4px rgba(0, 102, 255, 0.2));
    }
    .feature-text h3 {
        font-size: 0.875rem;
        margin: 0 0 0.125rem 0;
        color: #111827;
    }
    .feature-text p {
        font-size: 0.75rem;
        color: #6b7280;
        margin: 0;
    }
    .welcome-image {
        display: flex;
        align-items: center;
        justify-content: center;
        animation: fadeInRight 0.5s ease forwards 0.4s, bounce 3s ease-in-out infinite 1s;
        opacity: 0;
        position: relative;
    }
    .welcome-image::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, rgba(0, 102, 255, 0.1) 0%, transparent 70%);
        z-index: -1;
    }
    @keyframes fadeInRight {
        from { opacity: 0; transform: translateX(20px); }
        to { opacity: 1; transform: translateX(0); }
    }
    @keyframes bounce {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
    }
    .welcome-image img {
        max-width: 100%;
        height: auto;
        filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
    }
    .get-started-button {
        align-self: flex-end;
        padding: 0.75rem 2rem;
        background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 102, 255, 0.2);
        position: relative;
        overflow: hidden;
    }
    .get-started-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 102, 255, 0.3);
    }
    .get-started-button::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: 0.5s;
    }
    .get-started-button:hover::after {
        left: 100%;
    }
    /* Login form styles */
    .login-content {
        background-color: white;
        padding: 2rem;
        border-radius: 16px;
        width: 360px;
        box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1);
    }
    .login-title {
        font-size: 1.75rem;
        color: #0066ff;
        margin-bottom: 0.5rem;
        text-align: center;
    }
    .login-subtitle {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    form {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    input {
        padding: 0.75rem;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: border-color 0.3s ease;
    }
    input:focus {
        outline: none;
        border-color: #0066ff;
    }
    .checkbox-container {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.75rem;
        color: #6b7280;
    }
    .checkbox-container input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #0066ff;
    }
    .login-button {
        padding: 0.75rem;
        background-color: #99c2ff;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        cursor: not-allowed;
        transition: background-color 0.3s ease, transform 0.3s ease;
    }
    .login-button.active {
        background-color: #0066ff;
        cursor: pointer;
    }
    .login-button.active:hover {
        background-color: #0052cc;
        transform: translateY(-2px);
    }
    .social-login {
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin-top: 1.5rem;
    }
    .social-login-button {
        display: flex;
        align-items: center;
        background: #f3f4f6;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .social-login-button:hover {
        background: #e5e7eb;
        transform: translateY(-2px);
    }
    .social-login-icon {
        width: 24px;
        height: 24px;
        margin-right: 0.5rem;
    }
    /* Profile selection styles */
    .avatar-preview {
    width: 60px;
    height: 60px;
    margin: 0 auto 1rem;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #0066ff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.avatar-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 1rem;
    margin-bottom: 1rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    background: #f3f4f6;
    border-radius: 8px;
    scrollbar-width: thin;
    scrollbar-color: #0066ff #f3f4f6;
}

.profile-grid::-webkit-scrollbar {
    width: 8px;
}

.profile-grid::-webkit-scrollbar-track {
    background: #f3f4f6;
    border-radius: 4px;
}

.profile-grid::-webkit-scrollbar-thumb {
    background-color: #0066ff;
    border-radius: 4px;
    border: 2px solid #f3f4f6;
}

.profile-option {
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 0.5rem;
    background: white;
}

.profile-option:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 102, 255, 0.1);
}

.profile-option.selected {
    border-color: #0066ff;
    background-color: rgba(0, 102, 255, 0.1);
}

.profile-option img {
    width: 100%;
    aspect-ratio: 1;
    border-radius: 50%;
    object-fit: cover;
}

#showMoreAvatars,
#selectAvatarButton {
    width: 100%;
    margin-bottom: 0.5rem;
    background-color: #0066ff;
    color: white;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

#showMoreAvatars:hover,
#selectAvatarButton:hover {
    background-color: #0052cc;
    transform: translateY(-1px);
}

#selectAvatarButton:disabled {
    background-color: #99c2ff;
    cursor: not-allowed;
    transform: none;
}

.dialog-content {
    width: 400px;
    max-width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    padding: 1.5rem;
}

.dialog-content h2 {
    text-align: center;
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
}

.dialog-content h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #6b7280;
    font-size: 1rem;
}
    .dialog {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }
    .dialog-content {
        background-color: white;
        padding: 2rem;
        border-radius: 16px;
        width: 400px;
        max-width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    .notification {
        background-color: #0066ff;
        color: white;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin-top: 1rem;
        text-align: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        font-size: 0.875rem;
    }
    .or-divider {
        display: flex;
        align-items: center;
        text-align: center;
        margin: 1.5rem 0;
        color: #6b7280;
        font-size: 0.875rem;
    }
    .or-divider::before,
    .or-divider::after {
        content: '';
        flex: 1;
        border-bottom: 1px solid #e5e7eb;
    }
    .or-divider::before {
        margin-right: .5em;
    }
    .or-divider::after {
        margin-left: .5em;
    }
}
</style>
</head>
<body>
    <div class="slide-container">
        <div class="background-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="dots-pattern"></div>
        </div>
        <div class="slide" id="welcomeSlide">
            <div class="welcome-content">
                <div class="badge">Envent Engineering</div>
                <div class="welcome-grid">
                    <div class="welcome-text">
                        <div class="title-group">
                            <h1>Welcome to Envent Bridge!</h1>
                            <p class="subtitle">Enhanced Data Management</p>
                        </div>
                        <div class="feature-list">
                            <div class="feature-item">
                                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                                <div class="feature-text">
                                    <h3>Effortless Control</h3>
                                    <p>Your data, simplified. Manage workflows in just a few clicks.</p>
                                </div>
                            </div>
                            <div class="feature-item">
                                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                                <div class="feature-text">
                                    <h3>Built for Speed</h3>
                                    <p>Process shipments and orders faster than ever. Stability guaranteed.</p>
                                </div>
                            </div>
                            <div class="feature-item">
                                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="20 6 9 17 4 12"></polyline>
                                </svg>
                                <div class="feature-text">
                                    <h3>Designed to Delight</h3>
                                    <p>Clean, modern tools that make complex tasks feel simple.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="welcome-image">
                        <img src="images/login1.png" alt="Data Management Illustration">
                    </div>
                </div>
                <button class="get-started-button" id="getStartedButton">Get Started</button>
            </div>
        </div>
    </div>
    <div id="loginDialog" class="dialog">
        <div class="dialog-content">
            <h2 class="login-title">Envent Bridge</h2>
            <p class="login-subtitle">Powered by Envent Engineering Ltd</p>
            <form id="loginForm">
                <input type="email" id="email" placeholder="Email" required>
                <input type="password" id="password" placeholder="Password" required>
                <div class="checkbox-container">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">I agree to the <a href="terms.html" target="_blank" style="color: #0066ff; text-decoration: none;">Terms and Conditions</a></label>
                </div>
                <button type="submit" id="loginButton" class="login-button" disabled>Log In</button>
            </form>
            <div class="or-divider">or</div>
            <div class="social-login">
                <button class="social-login-button" id="outlookLogin">
                    <img src="images/icons/outlook.png" alt="Outlook" class="social-login-icon">
                    <span>Outlook</span>
                </button>
                <button class="social-login-button" id="teamsLogin">
                    <img src="images/icons/teams.png" alt="Teams" class="social-login-icon">
                    <span>Teams</span>
                </button>
            </div>
            <div id="loginNotification" class="notification"></div>
        </div>
    </div>
    <div id="profileDialog" class="dialog">
        <div class="dialog-content">
            <h2>Choose Your Profile</h2>
            <div class="profile-grid" id="profileGrid">
                <!-- Profile options will be dynamically added here -->
            </div>
        </div>
    </div>
    <div id="notification" class="notification"></div>
    <script src="core/login.js"></script>
</body>
</html>


