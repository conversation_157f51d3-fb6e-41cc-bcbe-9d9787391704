<!DOCTYPE html>
<html>
<head>
    <title>Avatar Selector</title>
    <style>
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .avatar-card {
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
        }
        .avatar-card.selected {
            border-color: #4CAF50;
            transform: scale(1.1);
        }
        .avatar-img {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        #selectedAvatar {
            max-width: 200px;
            margin: 20px auto;
            display: block;
        }
    </style>
</head>
<body>
    <h1>Choose Your Avatar</h1>
    <div class="gallery" id="gallery"></div>
    <img id="selectedAvatar" src="" alt="Selected Avatar">
    
    <script>
        const avatars = Array.from({length: 100}, (_, i) => `avatars/avatar_${i+1}.jpg`);
        
        function createGallery() {
            const gallery = document.getElementById('gallery');
            
            avatars.forEach((src, index) => {
                const card = document.createElement('div');
                card.className = 'avatar-card';
                card.innerHTML = `
                    <img src="${src}" class="avatar-img" data-id="${index + 1}" 
                         alt="Avatar ${index + 1}">
                `;
                
                card.addEventListener('click', () => {
                    document.querySelectorAll('.avatar-card').forEach(c => c.classList.remove('selected'));
                    card.classList.add('selected');
                    document.getElementById('selectedAvatar').src = src;
                });
                
                gallery.appendChild(card);
            });
        }

        // Initialize gallery
        createGallery();
    </script>
</body>
</html>