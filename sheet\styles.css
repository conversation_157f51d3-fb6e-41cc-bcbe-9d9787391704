/* Modern Google Sheets-like UI styling */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Inter', 'Roboto', 'Segoe UI', Arial, sans-serif;
}

:root {
    /* Main colors */
    --primary-color: #1a73e8;
    --primary-light: #e8f0fe;
    --primary-dark: #0b57d0;
    --text-color: #202124;
    --text-secondary: #5f6368;
    --border-color: #dadce0;
    --hover-color: #f1f3f4;
    --active-color: #e8f0fe;
    --bg-color: #ffffff;
    --bg-secondary: #f8f9fa;
    --grid-lines: #e0e0e0;
    --selection-bg: rgba(26, 115, 232, 0.1);

    /* Size variables */
    --toolbar-height: 40px;
    --header-height: 56px;
    --formula-height: 36px;
    --tab-height: 36px;
    --footer-height: 36px;
    --cell-width: 120px;
    --cell-height: 25px;
    --row-header-width: 50px;
    --col-header-height: 25px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

body {
    background-color: var(--bg-secondary);
    color: var(--text-color);
    height: 100vh;
    overflow: hidden;
    font-size: 14px;
    line-height: 1.5;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-color);
    box-shadow: var(--shadow-md);
}

/* Header Styles */
.app-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-color);
    box-shadow: var(--shadow-sm);
}

.header-left, .header-right {
    display: flex;
    align-items: center;
}

.document-controls {
    margin-left: 16px;
}

.app-logo {
    font-size: 22px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.app-logo i {
    font-size: 24px;
}

.sheet-title {
    font-size: 16px;
    border: none;
    outline: none;
    padding: 6px 8px;
    width: 240px;
    border-radius: 4px;
    transition: background-color var(--transition-fast);
    font-weight: 500;
}

.sheet-title:hover, .sheet-title:focus {
    background-color: var(--hover-color);
}

.header-buttons {
    display: flex;
    margin-top: 6px;
}

.header-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    margin-right: 8px;
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-secondary);
    transition: background-color var(--transition-fast);
}

.header-btn:hover {
    background-color: var(--hover-color);
}

.share-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.share-btn:hover {
    background-color: var(--primary-dark);
}

.user-avatar {
    margin-left: 16px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.user-avatar:hover {
    transform: scale(1.05);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Toolbar Styles */
.toolbar {
    height: var(--toolbar-height);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--border-color);
    gap: 16px;
    background-color: var(--bg-color);
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 2px;
    position: relative;
}

.toolbar-group:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    height: 20px;
    width: 1px;
    background-color: var(--border-color);
}

.toolbar-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    position: relative;
}

.toolbar-btn:hover {
    background-color: var(--hover-color);
    color: var(--text-color);
}

.toolbar-btn.active {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.toolbar-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: 2px;
}

.format-select {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    font-size: 14px;
    min-width: 140px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%235f6368' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    padding-right: 30px;
    transition: border-color var(--transition-fast);
}

.format-select:hover, .format-select:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Sheet Tabs */
.sheet-tabs {
    display: flex;
    height: var(--tab-height);
    align-items: center;
    padding: 0;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    overflow-x: auto;
    scrollbar-width: thin;
}

.sheet-tabs::-webkit-scrollbar {
    height: 3px;
}

.sheet-tabs::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 3px;
}

.tab {
    padding: 10px 16px;
    cursor: pointer;
    font-size: 13px;
    min-width: 100px;
    text-align: center;
    position: relative;
    transition: all var(--transition-fast);
    border-right: 1px solid var(--border-color);
    user-select: none;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
}

.tab.active {
    background-color: var(--bg-color);
    color: var(--primary-color);
    font-weight: 500;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.tab:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.05);
}

.add-sheet {
    width: 40px;
    min-width: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary-color);
}

/* Spreadsheet Container */
.spreadsheet-container {
    flex: 1;
    display: grid;
    grid-template-columns: var(--row-header-width) 1fr;
    grid-template-rows: var(--col-header-height) 1fr;
    overflow: auto;
    position: relative;
    background-color: var(--bg-color);
}

.spreadsheet-container::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.spreadsheet-container::-webkit-scrollbar-track {
    background-color: var(--bg-secondary);
}

.spreadsheet-container::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 10px;
    border: 2px solid var(--bg-secondary);
}

.spreadsheet-container::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

.column-headers {
    grid-column: 2;
    grid-row: 1;
    display: flex;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--grid-lines);
}

.row-headers {
    grid-column: 1;
    grid-row: 2;
    display: flex;
    flex-direction: column;
    position: sticky;
    left: 0;
    z-index: 2;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--grid-lines);
}

.spreadsheet {
    grid-column: 2;
    grid-row: 2;
    display: grid;
    overflow: auto;
    background-color: var(--bg-color);
}

.column-header, .row-header {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    background-color: var(--bg-secondary);
    user-select: none;
}

.column-header {
    width: var(--cell-width);
    height: var(--col-header-height);
    border-right: 1px solid var(--grid-lines);
    position: relative;
}

.column-header::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 100%;
    background-color: var(--grid-lines);
    z-index: 1;
}

.row-header {
    width: var(--row-header-width);
    height: var(--cell-height);
    border-bottom: 1px solid var(--grid-lines);
    position: relative;
}

.row-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: var(--grid-lines);
    z-index: 1;
}

.cell {
    width: var(--cell-width);
    height: var(--cell-height);
    border-right: 1px solid var(--grid-lines);
    border-bottom: 1px solid var(--grid-lines);
    padding: 3px 6px;
    outline: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    transition: background-color 0.1s ease;
    font-size: 13px;
}

.cell:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.02);
}

.cell.active {
    z-index: 1;
    outline: 2px solid var(--primary-color);
    outline-offset: -1px;
}

.cell.selected {
    background-color: var(--selection-bg);
}

/* Formula Bar */
.formula-bar {
    height: var(--formula-height);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

.cell-reference {
    width: 60px;
    padding: 4px 8px;
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
    border-right: 1px solid var(--border-color);
    font-weight: 600;
}

.formula-input-container {
    display: flex;
    align-items: center;
    flex-grow: 1;
    margin-left: 8px;
}

.formula-fx {
    font-size: 14px;
    color: var(--text-secondary);
    padding: 0 8px;
    font-weight: 500;
}

.formula-input {
    border: none;
    outline: none;
    padding: 8px;
    width: 100%;
    font-size: 14px;
    border-radius: 4px;
    transition: background-color var(--transition-fast);
}

.formula-input:hover, .formula-input:focus {
    background-color: var(--hover-color);
}

/* Footer */
.app-footer {
    height: var(--footer-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.footer-left, .footer-right {
    display: flex;
    align-items: center;
}

.footer-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
    transition: background-color var(--transition-fast);
}

.footer-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-color);
}

.sheet-info {
    font-size: 14px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
    margin-right: 16px;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.sheet-info:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Selection styling */
.selection-marker {
    position: absolute;
    border: 2px solid var(--primary-color);
    background-color: var(--selection-bg);
    pointer-events: none;
    z-index: 1;
    box-shadow: 0 0 0 1px white;
}

.selection-handle {
    position: absolute;
    width: 6px;
    height: 6px;
    background-color: var(--primary-color);
    border: 1px solid white;
    right: -4px;
    bottom: -4px;
    cursor: crosshair;
    z-index: 2;
}

/* Modal Dialog Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 10;
    overflow: auto;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: var(--bg-color);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: modalFadeIn 0.3s ease-out;
    transform-origin: center;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 18px;
    color: var(--text-color);
    font-weight: 600;
}

.close-btn {
    font-size: 22px;
    color: var(--text-secondary);
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.close-btn:hover {
    background-color: var(--hover-color);
    color: var(--text-color);
}

.modal-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 130px);
}

.modal-footer {
    padding: 16px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    border-top: 1px solid var(--border-color);
}

.db-selection, 
.store-selection,
.import-options {
    margin-bottom: 24px;
}

.db-selection label,
.store-selection label,
.import-options label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--text-color);
}

.db-select {
    width: 100%;
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    font-size: 14px;
    margin-bottom: 8px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%235f6368' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 36px;
    transition: border-color var(--transition-fast);
}

.db-select:hover, .db-select:focus {
    border-color: var(--primary-color);
    outline: none;
}

.db-select:disabled {
    background-color: var(--bg-secondary);
    cursor: not-allowed;
    opacity: 0.7;
}

.option {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.option input[type="checkbox"] {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 3px;
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.option input[type="checkbox"]:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.option input[type="checkbox"]:checked::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.option input[type="checkbox"]:focus {
    outline: 2px solid var(--primary-light);
    outline-offset: 1px;
}

.option label {
    font-weight: normal;
    cursor: pointer;
    margin-bottom: 0;
    user-select: none;
}

.import-preview {
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 16px;
    background-color: var(--bg-secondary);
}

.import-preview h3 {
    font-size: 15px;
    margin-bottom: 12px;
    color: var(--text-color);
    font-weight: 500;
}

.preview-container {
    max-height: 220px;
    overflow-y: auto;
    overflow-x: auto;
    background-color: var(--bg-color);
    border-radius: 4px;
    box-shadow: var(--shadow-sm);
}

.preview-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.preview-container::-webkit-scrollbar-track {
    background-color: var(--bg-secondary);
}

.preview-container::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 8px;
    border: 2px solid var(--bg-secondary);
}

.no-preview {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    padding: 24px;
}

.preview-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
}

.preview-table th {
    background-color: var(--bg-secondary);
    text-align: left;
    padding: 10px;
    font-weight: 500;
    border: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1;
}

.preview-table td {
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.preview-table tr:nth-child(even) {
    background-color: var(--bg-secondary);
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.primary-btn:hover {
    background-color: var(--primary-dark);
}

.primary-btn:disabled {
    background-color: #c6d4f1;
    cursor: not-allowed;
    box-shadow: none;
}

.cancel-btn {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.cancel-btn:hover {
    background-color: var(--hover-color);
    color: var(--text-color);
}

/* Tooltip */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    padding: 6px 10px;
    background-color: #333;
    color: white;
    font-size: 12px;
    white-space: nowrap;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
    z-index: 10;
}

[data-tooltip]:after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border-width: 4px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    pointer-events: none;
    z-index: 10;
}

[data-tooltip]:hover:before,
[data-tooltip]:hover:after {
    opacity: 1;
    visibility: visible;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #202124;
        --bg-secondary: #303134;
        --text-color: #e8eaed;
        --text-secondary: #9aa0a6;
        --border-color: #444746;
        --grid-lines: #3c4043;
        --hover-color: #3c4043;
        --primary-light: rgba(26, 115, 232, 0.2);
        --selection-bg: rgba(26, 115, 232, 0.15);
    }
    
    .preview-container::-webkit-scrollbar-track,
    .spreadsheet-container::-webkit-scrollbar-track {
        background-color: var(--bg-color);
    }
    
    .preview-container::-webkit-scrollbar-thumb,
    .spreadsheet-container::-webkit-scrollbar-thumb {
        background-color: #666;
        border: 2px solid var(--bg-color);
    }
    
    .format-select,
    .db-select {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%239aa0a6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    }
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .header-buttons {
        display: none;
    }
    
    .toolbar-group:nth-child(n+3) {
        display: none;
    }
    
    .sheet-title {
        width: 160px;
    }
    
    .cell {
        --cell-width: 100px;
    }
    
    .formula-bar {
        --formula-height: 44px;
    }
    
    .modal-content {
        width: 95%;
    }
}

/* Context Menu */
.context-menu {
    position: absolute;
    display: none;
    background-color: var(--bg-color);
    border-radius: 6px;
    box-shadow: var(--shadow-lg);
    min-width: 180px;
    padding: 6px 0;
    z-index: 100;
    font-size: 13px;
    border: 1px solid var(--border-color);
}

.context-menu-item {
    padding: 8px 16px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
    transition: background-color var(--transition-fast);
}

.context-menu-item:hover {
    background-color: var(--hover-color);
}

.context-menu-item.disabled {
    color: var(--text-secondary);
    opacity: 0.6;
    cursor: default;
}

.context-menu-item.disabled:hover {
    background-color: transparent;
}

.context-menu-item i {
    font-size: 14px;
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

.context-menu-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 6px 0;
}

/* Resize Handles */
.resize-handle {
    position: absolute;
    z-index: 3;
    transition: background-color var(--transition-fast);
}

.resize-handle.horizontal {
    cursor: col-resize;
    width: 4px;
    height: 100%;
    right: 0;
    top: 0;
}

.resize-handle.vertical {
    cursor: row-resize;
    height: 4px;
    width: 100%;
    bottom: 0;
    left: 0;
}

.resize-handle:hover {
    background-color: var(--primary-color);
}

/* Theme toggle button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: background-color var(--transition-fast), color var(--transition-fast);
}

.theme-toggle:hover {
    background-color: var(--hover-color);
    color: var(--text-color);
}

/* Dark Mode Support */
body.dark-mode {
    --bg-color: #202124;
    --bg-secondary: #303134;
    --text-color: #e8eaed;
    --text-secondary: #9aa0a6;
    --border-color: #444746;
    --grid-lines: #3c4043;
    --hover-color: #3c4043;
    --primary-light: rgba(26, 115, 232, 0.2);
    --selection-bg: rgba(26, 115, 232, 0.15);
}

/* Referenced cells */
.cell.referenced {
    background-color: rgba(255, 196, 0, 0.2);
}

/* Preview table styling */
.preview-table .null-value {
    color: #999;
    font-style: italic;
}

.preview-table .boolean-value {
    color: var(--primary-color);
    font-weight: 500;
}

.preview-table .number-value {
    color: #1967d2;
}

.preview-table .array-value,
.preview-table .object-value {
    color: #188038;
    font-family: monospace;
    font-size: 12px;
}

.preview-footer {
    text-align: center;
    padding: 8px;
    color: var(--text-secondary);
    font-style: italic;
    border-top: 1px solid var(--border-color);
}

/* Notification Toast */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: var(--shadow-lg);
    font-size: 14px;
    max-width: 350px;
    z-index: 1000;
    transform: translateY(100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.info {
    background-color: var(--primary-color);
    color: white;
}

.notification.success {
    background-color: #188038;
    color: white;
}

.notification.warning {
    background-color: #f29900;
    color: white;
}

.notification.error {
    background-color: #d93025;
    color: white;
}

/* Loading indicator */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Mobile optimizations */
@media screen and (max-width: 768px) {
    :root {
        --cell-width: 100px;
        --cell-height: 28px;
        --formula-height: 44px;
    }
    
    .header-buttons {
        display: none;
    }
    
    .toolbar-group:nth-child(n+3) {
        display: none;
    }
    
    .sheet-title {
        width: 160px;
    }
    
    .formula-bar {
        flex-wrap: wrap;
    }
    
    .cell-reference {
        width: 60px;
    }
    
    .formula-input-container {
        flex-basis: 100%;
        margin-left: 0;
        margin-top: 4px;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .notification {
        left: 20px;
        right: 20px;
        max-width: none;
    }
} 