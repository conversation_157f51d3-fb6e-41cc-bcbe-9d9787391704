// analytics-fullscreen-print.js
// Enhanced printing functionality for the dashboard

export class PrintManager {
  constructor(dashboard) {
    this.dashboard = dashboard;
    this.printQueue = new Set(); // Store chart IDs to print
    this.isPreparingPrint = false;
    this.activeTabId = null; // Store the active tab during printing
    this.printSettings = {
      orientation: 'landscape',
      paperSize: 'letter',
      includeKPIs: true,
      includeDataTable: false,
      chartSize: 'auto'
    };
    this.initialized = false;
  }

  /**
   * Initialize print manager
   */
  init() {
    // Initialize print settings and UI
    this.initialized = true;
    console.log('PrintManager initialized');
    
    // Add print controls to each chart
    this.addPrintControlsToCharts();
    
    // Add print-specific CSS
    this.addPrintStyles();
    
    // Listen for native print events
    this.listenForNativePrint();

    // Check if settings panel is available and inject settings
    this.injectPrintSettings();

    // Prevent default print shortcuts
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
        console.log("Ctrl+P / Cmd+P intercepted by PrintManager");
        e.preventDefault();
        e.stopPropagation();
        
        this.showPrintDialog();
        
        return false;
      }
    }, true);
  }

  /**
   * Inject print settings into the settings panel
   */
  injectPrintSettings(container) {
    // Use the dedicated print settings container if available, otherwise use the provided container
    const settingsContainer = container || document.getElementById('printSettingsContainer');
    if (!settingsContainer) return;
    
    console.log('Injecting print settings into settings panel');
    
    // Connect existing form controls with PrintManager
    const paperSizeSelect = document.getElementById('printPaperSize');
    const orientationSelect = document.getElementById('printOrientation');
    const includeAllCheckbox = document.getElementById('printIncludeAll');
    const includeTableCheckbox = document.getElementById('printIncludeTable');
    const printButton = document.getElementById('printFromSettings');
    
    // Set initial values from PrintManager settings
    if (paperSizeSelect) {
      paperSizeSelect.value = this.printSettings.paperSize;
      paperSizeSelect.addEventListener('change', (e) => {
        this.printSettings.paperSize = e.target.value;
      });
    }
    
    if (orientationSelect) {
      orientationSelect.value = this.printSettings.orientation;
      orientationSelect.addEventListener('change', (e) => {
        this.printSettings.orientation = e.target.value;
      });
    }
    
    if (includeAllCheckbox) {
      includeAllCheckbox.checked = this.printSettings.includeKPIs;
      includeAllCheckbox.addEventListener('change', (e) => {
        this.printSettings.includeKPIs = e.target.checked;
      });
    }
    
    if (includeTableCheckbox) {
      includeTableCheckbox.checked = this.printSettings.includeDataTable;
      includeTableCheckbox.addEventListener('change', (e) => {
        this.printSettings.includeDataTable = e.target.checked;
      });
    }
    
    if (printButton) {
      printButton.addEventListener('click', () => {
        this.executePrint();
      });
    }
  }

  /**
   * Hook into the existing print button
   */
  hookPrintButton() {
    // This method is no longer needed as we're handling the button click in analytics-fullscreen.js
    console.log('PrintManager: Print button handling moved to main dashboard code');
    return;
  }

  /**
   * Add print controls to each chart
   */
  addPrintControlsToCharts() {
    // Add print controls to chart containers that don't have them
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
      // Skip if already has print controls
      if (container.querySelector('.print-control')) return;
      
      // Get chart element
      const chartElement = container.querySelector('[id$="Chart"]');
      if (!chartElement) return;
      
      // Get chart title
      const title = container.querySelector('h3')?.textContent || 'Chart';
      
      // Create print control
      const printControl = document.createElement('button');
      printControl.className = 'print-control absolute top-2 right-10 bg-gray-100 hover:bg-gray-200 rounded p-1 text-gray-600 hover:text-gray-800 transition-colors duration-150 opacity-0 group-hover:opacity-100 z-10 no-print';
      printControl.innerHTML = `
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
        </svg>
      `;
      printControl.title = "Print this chart";
      
      // Add click handler
      printControl.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.printSingleChart(container);
      });
      
      // Make container position relative if not already
      if (getComputedStyle(container).position === 'static') {
        container.style.position = 'relative';
      }
      
      // Add group class for hover effects
      container.classList.add('group');
      
      // Add controls to container
      container.appendChild(printControl);
    });
  }

  /**
   * Select or deselect all charts
   */
  selectAllCharts(selected) {
    console.log(`${selected ? 'Selecting' : 'Deselecting'} all charts`);
    
    // Get checkboxes from the chart selector in settings panel
    const checkboxes = document.querySelectorAll('#chartSelectorList .print-select-checkbox');
    
    console.log(`Found ${checkboxes.length} checkboxes to update`);
    
    // Clear print queue if deselecting all
    if (!selected) {
      this.printQueue.clear();
    }
    
    checkboxes.forEach(checkbox => {
      // Update checkbox state
      checkbox.checked = selected;
      
      // Find the chart ID
      let chartId;
      if (checkbox.id && checkbox.id.startsWith('print_')) {
        // Checkbox format: print_chartId
        chartId = checkbox.id.substring(6);
        
        // Add to print queue if selected
        if (selected && chartId) {
          this.printQueue.add(chartId);
        }
      }
    });
    
    // Update the count display
    this.updateSelectedCount();
    
    // Show feedback
    this.showNotification(
      selected ? 'All charts selected for printing' : 'All charts deselected'
    );
    
    console.log(`Print queue now contains ${this.printQueue.size} charts`);
  }

  /**
   * Update the selected charts count display
   */
  updateSelectedCount() {
    // Update count in the settings panel
    const countElement = document.getElementById('chartPrintCount');
    if (countElement) {
      const total = document.querySelectorAll('.chart-container').length;
      const selected = this.printQueue.size;
      
      if (selected === 0) {
        countElement.textContent = 'No charts selected';
        countElement.className = 'mt-2 text-xs text-red-500';
      } else if (selected === total) {
        countElement.textContent = 'All charts selected';
        countElement.className = 'mt-2 text-xs text-green-500';
      } else {
        countElement.textContent = `${selected} of ${total} charts selected`;
        countElement.className = 'mt-2 text-xs text-blue-500';
      }
    }
  }

  /**
   * Print selected charts
   */
  printSelectedCharts() {
    if (this.printQueue.size === 0) {
      this.showNotification('No charts selected for printing', 'error');
      return;
    }
    
    this.showPrintDialog();
  }

  /**
   * Show notification message
   */
  showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notification = document.getElementById('printNotification');
    if (!notification) {
      notification = document.createElement('div');
      notification.id = 'printNotification';
      notification.className = 'fixed top-4 right-4 max-w-md z-50 transform transition-all duration-500 translate-y-[-100%] opacity-0';
      document.body.appendChild(notification);
    }
    
    // Set appearance based on type
    let bgColor, textColor, icon;
    switch(type) {
      case 'error':
        bgColor = 'bg-red-50 border-red-200';
        textColor = 'text-red-800';
        icon = `<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`;
        break;
      case 'success':
        bgColor = 'bg-green-50 border-green-200';
        textColor = 'text-green-800';
        icon = `<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`;
        break;
      default: // info
        bgColor = 'bg-blue-50 border-blue-200';
        textColor = 'text-blue-800';
        icon = `<svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`;
    }
    
    // Set content
    notification.className = `fixed top-4 right-4 max-w-md z-50 p-4 rounded-lg border ${bgColor} ${textColor} shadow-sm transition-all duration-500`;
    notification.innerHTML = `
      <div class="flex">
        <div class="flex-shrink-0">
          ${icon}
            </div>
        <div class="ml-3">
          <p class="text-sm font-medium">${message}</p>
          </div>
        </div>
      `;
      
    // Show notification
    setTimeout(() => {
      notification.style.transform = 'translateY(0)';
      notification.style.opacity = '1';
    }, 10);
    
    // Hide after delay
      setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(-100%)';
    }, 3000);
  }

  /**
   * Add print styles
   */
  addPrintStyles() {
    // Create style element if it doesn't exist
    let styleElement = document.getElementById('printManagerStyles');
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'printManagerStyles';
      document.head.appendChild(styleElement);
    }
    
    // Apply settings from form to print settings object
    const paperSizeSelect = document.getElementById('printPaperSize');
    if (paperSizeSelect) {
      this.printSettings.paperSize = paperSizeSelect.value;
    }
    
    const orientationSelect = document.getElementById('printOrientation');
    if (orientationSelect) {
      this.printSettings.orientation = orientationSelect.value;
    }
    
    const includeAllCheckbox = document.getElementById('printIncludeAll');
    if (includeAllCheckbox) {
      this.printSettings.includeKPIs = includeAllCheckbox.checked;
    }
    
    const includeTableCheckbox = document.getElementById('printIncludeTable');
    if (includeTableCheckbox) {
      this.printSettings.includeDataTable = includeTableCheckbox.checked;
    }
    
    // Set print styles
    styleElement.textContent = this.getPrintStylesText();
  }

  /**
   * Get print styles CSS text
   */
  getPrintStylesText() {
    // Convert chart IDs to CSS selectors
    let chartSelectors = '';
    
    // Find all chart containers
    document.querySelectorAll('.chart-container').forEach(container => {
      const chartEl = container.querySelector('[id$="Chart"]');
      if (chartEl && !this.printQueue.has(chartEl.id)) {
        // This chart is not in the print queue, so hide it
        chartSelectors += `#${chartEl.id}, #${chartEl.id} + *, #${chartEl.id} ~ *, .chart-container:has(#${chartEl.id}) { display: none !important; }\n`;
      }
    });
  
    return `
      /* Print styles for Shipping Analytics Dashboard */
      @media print {
        /* General print settings */
        @page {
          size: ${this.printSettings.paperSize} ${this.printSettings.orientation};
          margin: 2cm;
        }
        
        body {
          background-color: white !important;
          color: black !important;
          font-family: 'Inter', Arial, sans-serif !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        
        /* Hide non-printable elements */
        .no-print, nav, footer, button:not(.print-included), 
        #dashboardSettingsPanel, #settingsPanel, 
        .chart-controls, .print-selection-panel {
          display: none !important;
        }
        
        /* Dashboard styling */
        .dashboard-container {
          padding: 0 !important;
          margin: 0 !important;
          max-width: none !important;
        }
        
        /* Improve chart appearance */
        .chart-container {
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          margin-bottom: 30px !important;
          box-shadow: none !important;
          border: 1px solid #e5e7eb !important;
          padding: 15px !important;
          max-height: none !important;
        }
        
        /* KPI card styling */
        .kpi-card {
          box-shadow: none !important;
          border: 1px solid #e5e7eb !important;
          ${this.printSettings.includeKPIs ? '' : 'display: none !important;'}
        }
        
        /* Data table styling */
        #historyTableContainer {
          width: 100% !important;
          overflow: visible !important;
          ${this.printSettings.includeDataTable ? '' : 'display: none !important;'}
        }
        
        /* Tab content visibility */
        .tab-content {
          display: block !important;
          opacity: 1 !important;
          visibility: visible !important;
        }
        
        /* Page breaks */
        .tab-content {
          page-break-before: always !important;
        }
        
        .tab-content:first-of-type {
          page-break-before: avoid !important;
        }
        
        /* Ensure proper chart rendering */
        .apexcharts-canvas {
          width: 100% !important;
          height: auto !important;
        }
        
        svg {
          max-width: 100% !important;
          height: auto !important;
        }
        
        /* Chart size adjustment based on settings */
        ${this.printSettings.chartSize === 'full' ? `
          .chart-container {
            width: 100% !important;
          }
        ` : ''}
        
        ${this.printSettings.chartSize === 'half' ? `
          .chart-container {
            width: 50% !important;
            float: left !important;
          }
          .chart-container:nth-child(odd) {
            clear: left !important;
          }
        ` : ''}
        
        /* Hide unselected charts */
        ${chartSelectors}
      }
    `;
  }

  /**
   * Show print dialog with options
   */
  showPrintDialog() {
    console.log("PrintManager.showPrintDialog() called");
    
    // Open the settings panel and scroll to print settings
    const settingsPanel = document.getElementById('dashboardSettingsPanel');
    const printSettings = document.getElementById('printSettingsContainer');
    
    if (settingsPanel) {
      // Show settings panel
      settingsPanel.classList.remove('translate-x-full');
      
      // Scroll to print settings section with a slight delay to ensure panel is visible
      if (printSettings) {
    setTimeout(() => {
          printSettings.scrollIntoView({ behavior: 'smooth' });
      }, 300);
      }
    } else {
      console.error("Settings panel not found, falling back to browser print");
      window.print();
    }
  }
  
  /**
   * Execute the print with current settings
   */
  async executePrint() {
    console.log("Executing print with settings:", this.printSettings);
    
    try {
    // Close the settings panel if it's open
    const settingsPanel = document.getElementById('dashboardSettingsPanel');
      if (settingsPanel) {
        settingsPanel.classList.add('translate-x-full');
      }
      
      // Show print preparation message
      this.showNotification('Preparing charts for printing...', 'info');
      
      // Create a hidden print frame if it doesn't exist
      let printFrame = document.getElementById('printFrame');
      if (!printFrame) {
        printFrame = document.createElement('iframe');
        printFrame.id = 'printFrame';
        printFrame.style.position = 'fixed';
        printFrame.style.right = '-9999px';
        printFrame.style.bottom = '-9999px';
        printFrame.style.width = '1000px';
        printFrame.style.height = '800px';
        printFrame.style.border = 'none';
        document.body.appendChild(printFrame);
      }
      
      // Prepare print content by collecting selected charts
      await this.preparePrint();
      
      // Get all charts marked for printing
      const chartsToPrint = document.querySelectorAll('.chart-container.print-me');
      console.log(`Found ${chartsToPrint.length} charts to print`);
      
      if (chartsToPrint.length === 0) {
        this.showNotification('No charts selected for printing', 'error');
        this.cleanupAfterPrint();
      return;
    }
    
      // Create simple HTML for printing
      const printContent = `
      <!DOCTYPE html>
      <html>
      <head>
          <title>Dashboard Report</title>
        <style>
            body { font-family: system-ui, -apple-system, sans-serif; padding: 20px; }
            .print-header { text-align: center; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 1px solid #ddd; }
            .chart-container { page-break-inside: avoid; margin-bottom: 30px; }
            .chart-title { font-size: 16px; font-weight: bold; margin-bottom: 10px; }
          @media print {
              @page { size: ${this.printSettings.orientation === 'landscape' ? 'landscape' : 'portrait'}; }
              body { padding: 0; margin: 1cm; }
          }
        </style>
      </head>
      <body>
          <div class="print-header">
            <h1>Analytics Dashboard Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>
          </div>
          <div class="print-content">
            ${Array.from(chartsToPrint).map((chart, index) => {
              const title = chart.querySelector('h3')?.textContent || `Chart ${index+1}`;
              return `
                <div class="chart-container">
                  <div class="chart-title">${title}</div>
                  <img src="${this.captureChartImage(chart)}" style="max-width: 100%;">
        </div>
              `;
            }).join('')}
          </div>
      </body>
      </html>
      `;
      
      // Write to the iframe
      const frameDoc = printFrame.contentDocument || printFrame.contentWindow.document;
      frameDoc.open();
      frameDoc.write(printContent);
      frameDoc.close();
      
      // Wait for images to load
      setTimeout(() => {
        // Trigger print
        printFrame.contentWindow.print();
        
        // Clean up
        setTimeout(() => {
          this.cleanupAfterPrint();
        }, 500);
      }, 1000);
    } catch (error) {
      console.error("Error during print execution:", error);
      this.showNotification('Error preparing print: ' + error.message, 'error');
      this.cleanupAfterPrint(); // Ensure cleanup happens even on error
    }
  }
  
  /**
   * Capture chart as image data URL
   */
  captureChartImage(chartContainer) {
    try {
      // Try to get chart SVG
      const chartElement = chartContainer.querySelector('[id$="Chart"]');
      if (!chartElement) return '';
      
      // Check if it's an ApexChart
      if (chartElement.__apexChart) {
        // Get chart data URL from ApexCharts
        return chartElement.__apexChart.exports.getSvgString();
      }
      
      // Fallback: try to get SVG directly
      const svg = chartContainer.querySelector('svg');
      if (svg) {
        const svgData = new XMLSerializer().serializeToString(svg);
        return 'data:image/svg+xml;base64,' + btoa(svgData);
      }
      
      // Fallback: create screenshot using html2canvas (if available)
      if (typeof html2canvas === 'function') {
        html2canvas(chartContainer).then(canvas => {
          return canvas.toDataURL('image/png');
        });
      }
      
      // Ultimate fallback
      return '';
    } catch (error) {
      console.error('Error capturing chart image:', error);
      return '';
    }
  }

  /**
   * Print a single chart
   */
  printSingleChart(chartContainer) {
    // Save current print queue
    const originalPrintQueue = new Set(this.printQueue);
    
    // Clear queue and add only this chart
    this.printQueue.clear();
    
    const chartElement = chartContainer.querySelector('[id$="Chart"]');
    if (!chartElement) {
      this.showNotification('No chart found to print', 'error');
      // Restore original queue
      this.printQueue = originalPrintQueue;
      return;
    }
    
    // Add this chart to queue
    this.printQueue.add(chartElement.id);
    
    // Show notification
    this.showNotification(`Printing "${chartContainer.querySelector('h3')?.textContent || 'Chart'}"`);
    
    // Print
    this.showPrintDialog();
    
    // Restore original queue after printing
    setTimeout(() => {
      this.printQueue = originalPrintQueue;
      this.updateSelectedCount();
    }, 1500);
  }

  /**
   * Listen for native print events
   */
  listenForNativePrint() {
    window.addEventListener('beforeprint', () => {
      if (!this.isPreparingPrint) {
        // Handle direct print requests like Ctrl+P
        this.preparePrint();
      }
    });
    
    window.addEventListener('afterprint', () => {
      this.cleanupAfterPrint();
    });
  }

  /**
   * Prepare for printing
   */
  async preparePrint() {
    if (this.isPreparingPrint) return;
    
    this.isPreparingPrint = true;
    
    try {
      // Add loading overlay
      const loadingOverlay = document.createElement('div');
      loadingOverlay.id = 'printLoadingOverlay';
      loadingOverlay.className = 'fixed inset-0 bg-white bg-opacity-80 z-50 flex items-center justify-center';
      loadingOverlay.innerHTML = `
        <div class="text-center">
          <div class="loading-spinner w-12 h-12 mb-4 mx-auto"></div>
          <p class="text-gray-700">Preparing charts for printing...</p>
        </div>
      `;
      document.body.appendChild(loadingOverlay);
      
      // Store the active tab before changing visibility
      const activeTab = document.querySelector('.tab-button.active');
      if (activeTab) {
        this.activeTabId = activeTab.getAttribute('data-tab');
      }
      
      // Get all chart containers
      const chartContainers = document.querySelectorAll('.chart-container');
      
      // Check if we have any charts selected
      if (this.printQueue.size === 0) {
        // If no charts specifically selected, select all visible ones
        chartContainers.forEach(container => {
          const chartElement = container.querySelector('[id$="Chart"]');
          if (chartElement && !container.classList.contains('hidden')) {
            this.printQueue.add(chartElement.id);
          }
        });
      }
      
      // Add print-me class to selected charts
      chartContainers.forEach(container => {
      const chartElement = container.querySelector('[id$="Chart"]');
        if (chartElement && this.printQueue.has(chartElement.id)) {
          container.classList.add('print-me');
      } else {
          container.classList.remove('print-me');
      }
    });
    
      // Add print-me to KPI cards if enabled
    const kpiCards = document.querySelectorAll('.kpi-card');
      if (this.printSettings.includeKPIs) {
        kpiCards.forEach(card => card.classList.add('print-me'));
      } else {
        kpiCards.forEach(card => card.classList.remove('print-me'));
      }
      
      // Add print-me to data table if enabled
      const dataTable = document.getElementById('historyTableContainer');
      if (dataTable && this.printSettings.includeDataTable) {
        dataTable.closest('.chart-container')?.classList.add('print-me');
      } else if (dataTable) {
        dataTable.closest('.chart-container')?.classList.remove('print-me');
      }
      
      // Make sure all selected charts are visible by making all tabs active for printing
      document.querySelectorAll('.tab-content').forEach(tab => {
        // Store current active state
        if (tab.classList.contains('active')) {
          tab.classList.add('active-before-print');
        }
        // Make all tabs active for printing
        tab.classList.add('active');
      });
      
      // Wait a moment for charts to render properly
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Remove loading overlay
      const overlay = document.getElementById('printLoadingOverlay');
      if (overlay) {
        document.body.removeChild(overlay);
      }
      
      console.log("Print preparation complete");
      return true;
      } catch (error) {
      console.error('Error preparing print:', error);
      this.isPreparingPrint = false;
      
      // Clean up any overlay
      const overlay = document.getElementById('printLoadingOverlay');
      if (overlay) {
        document.body.removeChild(overlay);
      }
      
      throw error;
    }
  }

  /**
   * Clean up after printing
   */
  cleanupAfterPrint() {
    if (!this.isPreparingPrint) return;
    
    try {
      // Restore tab visibility - only show the previously active tab
      document.querySelectorAll('.tab-content').forEach(tab => {
        if (!tab.classList.contains('active-before-print')) {
          tab.classList.remove('active');
        }
        tab.classList.remove('active-before-print');
      });
      
      // If we stored an active tab ID, reactivate that tab
      if (this.activeTabId) {
        const tabButton = document.querySelector(`.tab-button[data-tab="${this.activeTabId}"]`);
        if (tabButton && typeof tabButton.click === 'function') {
          tabButton.click();
        }
        this.activeTabId = null;
      }
      
      // Remove print-me classes
      document.querySelectorAll('.print-me').forEach(element => {
        element.classList.remove('print-me');
      });
    } finally {
      this.isPreparingPrint = false;
    }
  }
}

// Initialize PrintManager when document is ready
function initPrintManager() {
  // Create global PrintManager instance
  window.printManager = new PrintManager();
  
  // Initialize
  window.printManager.init();
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', initPrintManager);

// Handle case where DOMContentLoaded already fired
if (document.readyState === 'interactive' || document.readyState === 'complete') {
  initPrintManager();
} 