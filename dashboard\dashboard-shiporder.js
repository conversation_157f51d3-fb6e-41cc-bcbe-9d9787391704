// dashboard-shiporder.js - Ship Order functionality
import { connectionManager } from "../core/connection.js";
// Replace incorrect import with dashboard usage
// import { showNotification } from "../core/notifications.js";

// Initialize the Ship Order module
export function initializeShipOrder(dashboard) {
  console.log("Initializing Ship Order module");
  return new ShipOrderSystem(dashboard);
}

class ShipOrderSystem {
  constructor(dashboard) {
    this.dashboard = dashboard;
    this.currentData = null;
    this.shipmentType = "package"; // Default to package (ShipWave)
  }

  // Show the Ship Order modal with form
  async showShipOrderModal(orderData) {
    this.currentData = orderData || this.dashboard.currentPreviewData;
    
    if (!this.currentData) {
      this.dashboard.addNotification("No order data available. Please process an order first.", "warning");
      return;
    }

    // Create modal container
    const modal = document.createElement("div");
    modal.id = "shipOrderModal";
    modal.className = "fixed inset-0 bg-black bg-opacity-50 z-50 overflow-auto flex";
    
    // Get shipment info from current data if available
    const shipmentInfo = this.currentData.shipmentInfo || {};
    const packageInfo = this.currentData.packageInfo || {};
    
    // Set default from information
    const fromInfo = {
      name: "Envent Engineering Ltd.",
      attn: "N/A",
      address: "2721 Hopewell Pl NE",
      suite: "N/A",
      city: "Calgary",
      state: "AB - Alberta",
      postalCode: "T1Y 7J7",
      country: "CA - Canada",
      phone: "************",
      openFrom: "08:00",
      openUntil: "16:00",
      locationType: "Warehouse"
    };
    
    // Get destination info from current data
    const toInfo = {
      name: this.currentData.customer || "",
      attn: shipmentInfo.attention || "",
      address: shipmentInfo.addressLine1 || "",
      suite: shipmentInfo.addressLine2 || "",
      city: shipmentInfo.city || "",
      state: shipmentInfo.state || "",
      postalCode: shipmentInfo.postalCode || "",
      country: shipmentInfo.country || "Canada",
      phone: shipmentInfo.phone || "",
    };

    // Create modal content with proper spacing for 800x600px frame
    modal.innerHTML = `
      <div class="m-auto" style="padding: 50px 70px;">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-[560px] max-h-[420px] flex flex-col">
          <!-- Header -->
          <div class="p-2 px-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Ship Order</h2>
            <button id="closeShipOrderBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Content - Scrollable -->
          <div class="flex-grow p-1 px-3 overflow-y-auto">
            <form id="shipOrderForm" class="space-y-1.5">
              <!-- Shipment Type Selection -->
              <div class="bg-gray-50 dark:bg-gray-700 p-1 rounded-lg">
                <div class="flex items-center justify-between">
                  <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200">Shipment Type</h3>
                  <div class="relative">
                    <select id="shipmentType" class="block pl-2 pr-8 py-0.5 text-xs border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 rounded-md">
                      <option value="package" selected>Package (ShipWave)</option>
                      <option value="freight">Freight (Freight Sample)</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- Address Sections with Swap Button -->
              <div class="flex justify-between items-center mb-0.5">
                <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200">Address Information</h3>
                <button type="button" id="swapAddressBtn" class="text-xs bg-blue-500 text-white px-2 py-1.5 rounded-md hover:bg-blue-600">
                  <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                  Swap Addresses
                </button>
              </div>

              <div class="grid grid-cols-2 gap-1.5">
                <!-- From Information -->
                <div class="bg-gray-50 dark:bg-gray-700 p-1 rounded-lg">
                  <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200 mb-0.5">From Information</h3>
                  <div class="space-y-0.5">
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Company Name</label>
                      <input type="text" id="fromName" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.name}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Attention/Contact Name</label>
                      <input type="text" id="fromAttn" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.attn}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Address Line 1</label>
                      <input type="text" id="fromAddress" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.address}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Address Line 2/Suite</label>
                      <input type="text" id="fromSuite" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.suite}">
                    </div>
                    <div class="grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">City</label>
                        <input type="text" id="fromCity" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.city}">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">State/Province</label>
                        <input type="text" id="fromState" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.state}">
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Postal Code</label>
                        <input type="text" id="fromPostalCode" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.postalCode}">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Country</label>
                        <input type="text" id="fromCountry" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.country}">
                      </div>
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Phone</label>
                      <input type="text" id="fromPhone" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.phone}">
                    </div>
                    <div class="freight-only hidden grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Open From (HH:MM)</label>
                        <input type="text" id="fromOpenFrom" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.openFrom}">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Open Until (HH:MM)</label>
                        <input type="text" id="fromOpenUntil" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${fromInfo.openUntil}">
                      </div>
                    </div>
                    <div class="freight-only hidden">
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Location Type</label>
                      <select id="fromLocationType" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                        <option value="Warehouse" selected>Warehouse</option>
                        <option value="Business">Business</option>
                        <option value="Residence">Residence</option>
                      </select>
                    </div>
                    <div class="package-only">
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Is Commercial</label>
                      <select id="fromCommercial" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                        <option value="true" selected>Yes</option>
                        <option value="false">No</option>
                      </select>
                    </div>
                  </div>
                </div>

                <!-- To Information -->
                <div class="bg-gray-50 dark:bg-gray-700 p-1 rounded-lg">
                  <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200 mb-0.5">To Information</h3>
                  <div class="space-y-0.5">
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Company/Business Name</label>
                      <input type="text" id="toName" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.name}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Attention/Contact Name</label>
                      <input type="text" id="toAttn" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.attn}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Address Line 1</label>
                      <input type="text" id="toAddress" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.address}">
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Address Line 2/Suite</label>
                      <input type="text" id="toSuite" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.suite}">
                    </div>
                    <div class="grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">City</label>
                        <input type="text" id="toCity" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.city}">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">State/Province</label>
                        <input type="text" id="toState" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.state}">
                      </div>
                    </div>
                    <div class="grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Postal Code</label>
                        <input type="text" id="toPostalCode" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.postalCode}">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Country</label>
                        <input type="text" id="toCountry" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.country}">
                      </div>
                    </div>
                    <div>
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Phone</label>
                      <input type="text" id="toPhone" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${toInfo.phone}">
                    </div>
                    <div class="freight-only hidden grid grid-cols-2 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Open From (HH:MM)</label>
                        <input type="text" id="toOpenFrom" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="09:00">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Open Until (HH:MM)</label>
                        <input type="text" id="toOpenUntil" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="17:00">
                      </div>
                    </div>
                    <div class="freight-only hidden">
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Location Type</label>
                      <select id="toLocationType" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                        <option value="Warehouse" selected>Warehouse</option>
                        <option value="Business">Business</option>
                        <option value="Residence">Residence</option>
                      </select>
                    </div>
                    <div class="package-only">
                      <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Is Commercial</label>
                      <select id="toCommercial" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                        <option value="true" selected>Yes</option>
                        <option value="false">No</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Package/Items Details -->
              <div class="bg-gray-50 dark:bg-gray-700 p-1 rounded-lg">
                <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200 mb-0.5">Package/Items Details</h3>
                <div id="packageItems" class="space-y-1.5">
                  <div class="package-item border border-gray-200 p-1 rounded-md">
                    <div class="grid grid-cols-5 gap-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Length</label>
                        <input type="number" class="item-length w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Width</label>
                        <input type="number" class="item-width w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Height</label>
                        <input type="number" class="item-height w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Weight</label>
                        <input type="number" class="item-weight w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="">
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Description</label>
                        <input type="text" class="item-description w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="Item description">
                      </div>
                    </div>
                    <div class="freight-only hidden grid grid-cols-3 gap-1 mt-1">
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Handling Unit Type</label>
                        <select class="item-handling-type w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                          <option value="Pallet" selected>Pallet</option>
                          <option value="Box">Box</option>
                          <option value="Carton">Carton</option>
                          <option value="Crate">Crate</option>
                          <option value="Drum">Drum</option>
                          <option value="Tube">Tube</option>
                        </select>
                      </div>
                      <div>
                        <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Number of Units</label>
                        <input type="number" class="item-handling-units w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="1">
                      </div>
                      <div class="flex gap-1">
                        <div class="flex-1">
                          <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Stackable</label>
                          <select class="item-stackable w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                            <option value="false" selected>No</option>
                            <option value="true">Yes</option>
                          </select>
                        </div>
                        <div class="flex-1">
                          <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Dangerous</label>
                          <select class="item-dangerous w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                            <option value="false" selected>No</option>
                            <option value="true">Yes</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="mt-1">
                  <button type="button" id="addItemBtn" class="text-xs bg-blue-500 text-white px-2 py-1.5 rounded-md hover:bg-blue-600">
                    Add Item
                  </button>
                  <button type="button" id="removeItemBtn" class="text-xs bg-red-500 text-white px-2 py-1.5 rounded-md hover:bg-red-600 ml-1">
                    Remove Item
                  </button>
                </div>
              </div>

              <!-- Additional Options -->
              <div class="bg-gray-50 dark:bg-gray-700 p-1 rounded-lg">
                <h3 class="text-xs font-medium text-gray-700 dark:text-gray-200 mb-0.5">Additional Options</h3>
                <div class="grid grid-cols-3 gap-1">
                  <div>
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Pickup Date</label>
                    <input type="date" id="pickupDate" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${new Date().toISOString().split('T')[0]}">
                  </div>
                  <div>
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Reference</label>
                    <input type="text" id="reference" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" value="${this.currentData.reference || this.currentData.orderNumber || ''}">
                  </div>
                  <div>
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Driver Instructions</label>
                    <textarea id="driverInstructions" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" rows="1"></textarea>
                  </div>
                </div>
                <div class="freight-only hidden mt-1">
                  <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Existing Shipment ID (Optional)</label>
                  <div class="flex items-center gap-1">
                    <input type="text" id="existingShipmentId" class="flex-1 px-1.5 py-0.5 text-xs border border-gray-300 rounded-md" placeholder="Enter shipment ID to fetch quotes">
                    <div class="text-xs text-gray-500">Use this to retrieve quotes for an existing shipment</div>
                  </div>
                </div>
                <div class="grid grid-cols-3 gap-1 mt-1">
                  <div class="package-only">
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Signature Required</label>
                    <select id="signatureRequired" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                      <option value="false" selected>No</option>
                      <option value="true">Yes</option>
                    </select>
                  </div>
                  <div class="package-only">
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Saturday Delivery</label>
                    <select id="saturdayDelivery" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                      <option value="false" selected>No</option>
                      <option value="true">Yes</option>
                    </select>
                  </div>
                  <div class="freight-only hidden">
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Inside Required</label>
                    <select id="specialServiceInside" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                      <option value="false" selected>No</option>
                      <option value="true">Yes</option>
                    </select>
                  </div>
                  <div class="freight-only hidden">
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Lift Gate Required</label>
                    <select id="specialServiceLiftGate" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                      <option value="false" selected>No</option>
                      <option value="true">Yes</option>
                    </select>
                  </div>
                  <div class="freight-only hidden">
                    <label class="block text-xs text-gray-700 dark:text-gray-300 mb-0.5">Appointment Required</label>
                    <select id="specialServiceAppointment" class="w-full px-1.5 py-0.5 text-xs border border-gray-300 rounded-md">
                      <option value="false" selected>No</option>
                      <option value="true">Yes</option>
                    </select>
                  </div>
                </div>
              </div>
            </form>
          </div>
          
          <!-- Footer with buttons -->
          <div class="p-1 px-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-lg flex justify-end space-x-2">
            <button id="cancelShipOrderBtn" class="px-2 py-1.5 text-xs bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
              Cancel
            </button>
            <button id="submitShipOrderBtn" class="px-2 py-1.5 text-xs bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Ship Order
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listeners
    this.setupModalEventListeners(modal);
    
    // Toggle the input fields based on the current shipment type
    this.toggleShipmentTypeFields(this.shipmentType);

    // Return the modal for additional operations if needed
    return modal;
  }

  // Setup event listeners for the modal
  setupModalEventListeners(modal) {
    // Close button
    const closeBtn = modal.querySelector("#closeShipOrderBtn");
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Cancel button
    const cancelBtn = modal.querySelector("#cancelShipOrderBtn");
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Submit button
    const submitBtn = modal.querySelector("#submitShipOrderBtn");
    if (submitBtn) {
      submitBtn.addEventListener("click", () => {
        this.submitShipOrder(modal);
      });
    }
    
    // Shipment type dropdown
    const shipmentTypeSelect = modal.querySelector("#shipmentType");
    if (shipmentTypeSelect) {
      shipmentTypeSelect.addEventListener("change", (e) => {
        this.shipmentType = e.target.value;
        this.toggleShipmentTypeFields(this.shipmentType);
      });
    }
    
    // Add listener for existing shipment ID field
    const existingShipmentId = modal.querySelector("#existingShipmentId");
    if (existingShipmentId) {
      existingShipmentId.addEventListener("input", (e) => {
        if (this.shipmentType === "freight") {
          if (submitBtn) {
            if (e.target.value) {
              submitBtn.textContent = "Fetch Rates";
            } else {
              submitBtn.textContent = "Get Freight Rates";
            }
          }
        }
      });
    }
    
    // Add item button
    const addItemBtn = modal.querySelector("#addItemBtn");
    if (addItemBtn) {
      addItemBtn.addEventListener("click", () => {
        this.addPackageItem(modal);
      });
    }
    
    // Remove item button
    const removeItemBtn = modal.querySelector("#removeItemBtn");
    if (removeItemBtn) {
      removeItemBtn.addEventListener("click", () => {
        this.removeLastPackageItem(modal);
      });
    }
    
    // Add swap addresses button functionality
    const swapAddressBtn = modal.querySelector("#swapAddressBtn");
    if (swapAddressBtn) {
      swapAddressBtn.addEventListener("click", () => {
        this.swapAddresses(modal);
      });
    }
    
    // Close when clicking outside
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }
  
  // New function to swap from and to addresses
  swapAddresses(modal) {
    // Get all from and to fields
    const fromFields = {
      name: modal.querySelector("#fromName").value,
      attn: modal.querySelector("#fromAttn").value,
      address: modal.querySelector("#fromAddress").value,
      suite: modal.querySelector("#fromSuite").value,
      city: modal.querySelector("#fromCity").value,
      state: modal.querySelector("#fromState").value,
      postalCode: modal.querySelector("#fromPostalCode").value,
      country: modal.querySelector("#fromCountry").value,
      phone: modal.querySelector("#fromPhone").value,
      isCommercial: modal.querySelector("#fromCommercial")?.value,
      openFrom: modal.querySelector("#fromOpenFrom")?.value,
      openUntil: modal.querySelector("#fromOpenUntil")?.value,
      locationType: modal.querySelector("#fromLocationType")?.value
    };
    
    const toFields = {
      name: modal.querySelector("#toName").value,
      attn: modal.querySelector("#toAttn").value,
      address: modal.querySelector("#toAddress").value,
      suite: modal.querySelector("#toSuite").value,
      city: modal.querySelector("#toCity").value,
      state: modal.querySelector("#toState").value,
      postalCode: modal.querySelector("#toPostalCode").value,
      country: modal.querySelector("#toCountry").value,
      phone: modal.querySelector("#toPhone").value,
      isCommercial: modal.querySelector("#toCommercial")?.value,
      openFrom: modal.querySelector("#toOpenFrom")?.value,
      openUntil: modal.querySelector("#toOpenUntil")?.value,
      locationType: modal.querySelector("#toLocationType")?.value
    };
    
    // Swap the values
    modal.querySelector("#fromName").value = toFields.name;
    modal.querySelector("#fromAttn").value = toFields.attn;
    modal.querySelector("#fromAddress").value = toFields.address;
    modal.querySelector("#fromSuite").value = toFields.suite;
    modal.querySelector("#fromCity").value = toFields.city;
    modal.querySelector("#fromState").value = toFields.state;
    modal.querySelector("#fromPostalCode").value = toFields.postalCode;
    modal.querySelector("#fromCountry").value = toFields.country;
    modal.querySelector("#fromPhone").value = toFields.phone;
    
    modal.querySelector("#toName").value = fromFields.name;
    modal.querySelector("#toAttn").value = fromFields.attn;
    modal.querySelector("#toAddress").value = fromFields.address;
    modal.querySelector("#toSuite").value = fromFields.suite;
    modal.querySelector("#toCity").value = fromFields.city;
    modal.querySelector("#toState").value = fromFields.state;
    modal.querySelector("#toPostalCode").value = fromFields.postalCode;
    modal.querySelector("#toCountry").value = fromFields.country;
    modal.querySelector("#toPhone").value = fromFields.phone;
    
    // Swap dropdown values if they exist
    if (modal.querySelector("#fromCommercial") && modal.querySelector("#toCommercial")) {
      modal.querySelector("#fromCommercial").value = toFields.isCommercial;
      modal.querySelector("#toCommercial").value = fromFields.isCommercial;
    }
    
    if (modal.querySelector("#fromOpenFrom") && modal.querySelector("#toOpenFrom")) {
      modal.querySelector("#fromOpenFrom").value = toFields.openFrom;
      modal.querySelector("#toOpenFrom").value = fromFields.openFrom;
    }
    
    if (modal.querySelector("#fromOpenUntil") && modal.querySelector("#toOpenUntil")) {
      modal.querySelector("#fromOpenUntil").value = toFields.openUntil;
      modal.querySelector("#toOpenUntil").value = fromFields.openUntil;
    }
    
    if (modal.querySelector("#fromLocationType") && modal.querySelector("#toLocationType")) {
      modal.querySelector("#fromLocationType").value = toFields.locationType;
      modal.querySelector("#toLocationType").value = fromFields.locationType;
    }
    
    // Show notification
    this.dashboard.addNotification("From and To addresses swapped", "info");
  }
  
  // Toggle fields based on shipment type
  toggleShipmentTypeFields(type) {
    const freightOnlyFields = document.querySelectorAll(".freight-only");
    const packageOnlyFields = document.querySelectorAll(".package-only");
    
    if (type === "freight") {
      freightOnlyFields.forEach(field => field.classList.remove("hidden"));
      packageOnlyFields.forEach(field => field.classList.add("hidden"));
      
      // Update submit button text
      const submitBtn = document.querySelector("#submitShipOrderBtn");
      const existingIdField = document.querySelector("#existingShipmentId");
      
      if (submitBtn) {
        if (existingIdField && existingIdField.value) {
          submitBtn.textContent = "Fetch Rates";
        } else {
          submitBtn.textContent = "Get Freight Rates";
        }
      }
    } else {
      freightOnlyFields.forEach(field => field.classList.add("hidden"));
      packageOnlyFields.forEach(field => field.classList.remove("hidden"));
      
      // Reset submit button text for package type
      const submitBtn = document.querySelector("#submitShipOrderBtn");
      if (submitBtn) {
        submitBtn.textContent = "Ship Order";
      }
    }
  }
  
  // Add a new package item
  addPackageItem(modal) {
    const packageItems = modal.querySelector("#packageItems");
    const itemTemplate = packageItems.querySelector(".package-item").cloneNode(true);
    
    // Reset all input values to defaults
    itemTemplate.querySelectorAll("input").forEach(input => {
      if (input.type === "number") {
        if (input.classList.contains("item-length") || 
            input.classList.contains("item-width") || 
            input.classList.contains("item-height")) {
          input.value = "";
        } else if (input.classList.contains("item-weight")) {
          input.value = "";
        } else if (input.classList.contains("item-handling-units")) {
          input.value = "1";
        }
      } else if (input.type === "text" && input.classList.contains("item-description")) {
        input.value = "Item description";
      }
    });
    
    // Add the new item to the DOM
    packageItems.appendChild(itemTemplate);
  }
  
  // Remove the last package item
  removeLastPackageItem(modal) {
    const packageItems = modal.querySelector("#packageItems");
    const items = packageItems.querySelectorAll(".package-item");
    
    // Don't remove the last item
    if (items.length > 1) {
      packageItems.removeChild(items[items.length - 1]);
    } else {
      this.dashboard.addNotification("At least one item is required", "warning");
    }
  }
  
  // Collect form data
  collectFormData(modal) {
    // Shipment type
    const shipmentType = modal.querySelector("#shipmentType").value;
    
    // From information
    const fromInfo = {
      name: modal.querySelector("#fromName").value,
      attn: modal.querySelector("#fromAttn").value,
      address: modal.querySelector("#fromAddress").value,
      suite: modal.querySelector("#fromSuite").value,
      city: modal.querySelector("#fromCity").value,
      state: modal.querySelector("#fromState").value,
      postalCode: modal.querySelector("#fromPostalCode").value,
      country: modal.querySelector("#fromCountry").value,
      phone: modal.querySelector("#fromPhone").value,
      isCommercial: modal.querySelector("#fromCommercial")?.value === "true",
      // Freight specific fields
      openFrom: modal.querySelector("#fromOpenFrom")?.value,
      openUntil: modal.querySelector("#fromOpenUntil")?.value,
      locationType: modal.querySelector("#fromLocationType")?.value
    };
    
    // To information
    const toInfo = {
      name: modal.querySelector("#toName").value,
      attn: modal.querySelector("#toAttn").value,
      address: modal.querySelector("#toAddress").value,
      suite: modal.querySelector("#toSuite").value,
      city: modal.querySelector("#toCity").value,
      state: modal.querySelector("#toState").value,
      postalCode: modal.querySelector("#toPostalCode").value,
      country: modal.querySelector("#toCountry").value,
      phone: modal.querySelector("#toPhone").value,
      isCommercial: modal.querySelector("#toCommercial")?.value === "true",
      // Freight specific fields
      openFrom: modal.querySelector("#toOpenFrom")?.value,
      openUntil: modal.querySelector("#toOpenUntil")?.value,
      locationType: modal.querySelector("#toLocationType")?.value
    };
    
    // Package items
    const itemElements = modal.querySelectorAll(".package-item");
    const items = Array.from(itemElements).map(itemEl => {
      return {
        length: parseFloat(itemEl.querySelector(".item-length").value) || 0,
        width: parseFloat(itemEl.querySelector(".item-width").value) || 0,
        height: parseFloat(itemEl.querySelector(".item-height").value) || 0,
        weight: parseFloat(itemEl.querySelector(".item-weight").value) || 0,
        description: itemEl.querySelector(".item-description").value,
        // Freight specific fields
        handlingUnitType: itemEl.querySelector(".item-handling-type")?.value,
        numberHandlingUnits: parseInt(itemEl.querySelector(".item-handling-units")?.value) || 1,
        isStackable: itemEl.querySelector(".item-stackable")?.value === "true",
        isDangerous: itemEl.querySelector(".item-dangerous")?.value === "true"
      };
    });
    
    // Additional options
    const options = {
      pickupDate: modal.querySelector("#pickupDate").value,
      reference: modal.querySelector("#reference").value,
      driverInstructions: modal.querySelector("#driverInstructions").value,
      // Existing shipment ID (if provided)
      existingShipmentId: modal.querySelector("#existingShipmentId")?.value,
      // Package specific fields
      signatureRequired: modal.querySelector("#signatureRequired")?.value === "true",
      saturdayDelivery: modal.querySelector("#saturdayDelivery")?.value === "true",
      // Freight specific fields
      specialServiceInside: modal.querySelector("#specialServiceInside")?.value === "true",
      specialServiceLiftGate: modal.querySelector("#specialServiceLiftGate")?.value === "true",
      specialServiceAppointment: modal.querySelector("#specialServiceAppointment")?.value === "true"
    };
    
    return {
      shipmentType,
      fromInfo,
      toInfo,
      items,
      options
    };
  }
  
  // Submit the ship order form
  async submitShipOrder(modal) {
    try {
      // Disable submit button to prevent double submission
      const submitBtn = modal.querySelector("#submitShipOrderBtn");
      submitBtn.disabled = true;
      submitBtn.textContent = "Processing...";
      
      // Get form data
      const formData = this.collectFormData(modal);
      
      // Check if we have an existing shipment ID for Freight Sample
      if (this.shipmentType === "freight" && formData.options.existingShipmentId) {
        // Fetch quotes for the existing shipment
        await this.fetchExistingShipment(formData.options.existingShipmentId);
        
        // Close the modal
        modal.remove();
        return;
      }
      
      // Validate the form data
      if (!this.validateFormData(formData)) {
        submitBtn.disabled = false;
        submitBtn.textContent = "Ship Order";
        return;
      }
      
      // Check which API to use based on shipment type
      if (this.shipmentType === "freight") {
        // Use Freight Sample API
        await this.submitToFreightSample(formData);
      } else {
        // Use ShipWave API
        await this.submitToShipWave(formData);
      }
      
      // Close the modal
      modal.remove();
      
      // Show success notification
      this.dashboard.addNotification("Shipment created successfully", "success");
      
    } catch (error) {
      console.error("Error submitting ship order:", error);
      this.dashboard.addNotification(`Error: ${error.message}`, "danger");
      
      // Re-enable the submit button
      const submitBtn = modal.querySelector("#submitShipOrderBtn");
      submitBtn.disabled = false;
      submitBtn.textContent = "Ship Order";
    }
  }
  
  // New function to fetch an existing shipment
  async fetchExistingShipment(shipmentId) {
    // Check if connected to Freight Sample
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.freightSample.isConnected) {
      throw new Error("Not connected to Freight Sample. Please connect in External Connections first.");
    }
    
    try {
      // Display loading notification
      this.dashboard.addNotification(`Fetching shipment ${shipmentId}...`, "info");
      
      // Add a delay to ensure all quotes are ready
      this.dashboard.addNotification("Waiting for all carrier quotes...", "info");
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      
      // Use the fixed token for consistent results
      const apiToken = "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973";
      
      // Request with cache-busting headers to ensure fresh data
      const shipmentResponse = await fetch(`https://api.freightsimpledemo.com/v1/shipments/get-shipment?shipmentId=${shipmentId}`, {
        method: "GET",
        cache: "no-cache",
        headers: {
          "Authorization": `Bearer ${apiToken}`,
          "Cache-Control": "no-cache",
          "Pragma": "no-cache"
        }
      });
      
      console.log("Shipment response status:", shipmentResponse.status);
      
      if (!shipmentResponse.ok) {
        const errorText = await shipmentResponse.text();
        console.error("Error response from API:", errorText);
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(`Failed to get shipment details: ${shipmentResponse.status} - ${errorData.errorMessage || ""}`);
        } catch (parseError) {
          throw new Error(`Failed to get shipment details: ${shipmentResponse.status} - ${errorText.substring(0, 100)}`);
        }
      }
      
      // Get raw response text first for analysis
      const responseText = await shipmentResponse.text();
      console.log("Raw API response:", responseText);
      
      // Then parse as JSON
      const shipmentDetails = JSON.parse(responseText);
      
      // Log complete response
      console.log("Complete API response object:", shipmentDetails);
      console.log(`Shipment status: ${shipmentDetails.shipmentStatus}`);
      
      // Save the shipment ID for later use
      this.currentShipmentId = shipmentId;
      
      // Check if the shipment has quotes
      if (shipmentDetails.quotes && Array.isArray(shipmentDetails.quotes)) {
        const quoteCount = shipmentDetails.quotes.length;
        console.log(`Found ${quoteCount} quotes in response`);
        
        // Log all quotes individually
        if (quoteCount > 0) {
          shipmentDetails.quotes.forEach((quote, i) => {
            console.log(`Quote ${i+1}/${quoteCount}: ${quote.carrierDisplayName} - ${quote.serviceDisplayName} - ${quote.currency} ${quote.price}`);
          });
          
          this.dashboard.addNotification(`Found ${quoteCount} carrier quotes!`, "success");
          
          // Set a flag to indicate we already have quotes (for button text)
          shipmentDetails.hasQuotes = true;
        } else {
          this.dashboard.addNotification(`Shipment found but no quotes available. Status: ${shipmentDetails.shipmentStatus}`, "info");
        }
      } else {
        console.log("No quotes array in response. Status:", shipmentDetails.shipmentStatus);
        this.dashboard.addNotification(`Shipment found. Status: ${shipmentDetails.shipmentStatus}`, "info");
      }
      
      // Store the complete shipment details
      this.currentShipmentDetails = shipmentDetails;
      
      // Show the rates modal with all data
      this.showRatesModal(shipmentDetails);
      
      return {
        success: true,
        message: "Shipment details retrieved successfully",
        shipmentId: shipmentId
      };
    } catch (error) {
      console.error("Error fetching existing shipment:", error);
      throw new Error(`Error fetching shipment: ${error.message}`);
    }
  }
  
  // Validate form data
  validateFormData(formData) {
    let isValid = true;
    let errorMessage = "";
    
    // Check required from fields
    if (!formData.fromInfo.name || !formData.fromInfo.address || !formData.fromInfo.city ||
        !formData.fromInfo.state || !formData.fromInfo.postalCode || !formData.fromInfo.country) {
      errorMessage = "Please fill in all required 'From' address fields";
      isValid = false;
    }
    
    // Check required to fields
    if (!formData.toInfo.name || !formData.toInfo.address || !formData.toInfo.city ||
        !formData.toInfo.state || !formData.toInfo.postalCode || !formData.toInfo.country) {
      errorMessage = "Please fill in all required 'To' address fields";
      isValid = false;
    }
    
    // Check if there are items
    if (!formData.items.length) {
      errorMessage = "At least one package item is required";
      isValid = false;
    }
    
    // Check that each item has valid dimensions
    for (const item of formData.items) {
      if (item.length <= 0 || item.width <= 0 || item.height <= 0 || item.weight <= 0) {
        errorMessage = "All items must have valid dimensions and weight";
        isValid = false;
        break;
      }
    }
    
    // Check pickup date
    if (!formData.options.pickupDate) {
      errorMessage = "Please select a pickup/shipping date";
      isValid = false;
    }
    
    if (!isValid) {
      this.dashboard.addNotification(errorMessage, "warning");
    }
    
    return isValid;
  }
  
  // Submit to Freight Sample API
  async submitToFreightSample(formData) {
    // Check if connected to Freight Sample
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.freightSample.isConnected) {
      throw new Error("Not connected to Freight Sample. Please connect in External Connections first.");
    }
    
    // Format data EXACTLY like the AJAX example - same field order and structure
    const freightData = {
      pickupDate: formData.options.pickupDate,
      pickup: {
        locationType: "Warehouse",
        businessName: formData.fromInfo.name || "Envent Engineering Ltd.",
        addressLine: formData.fromInfo.address || "2721 Hopewell Pl NE",
        addressLine2: formData.fromInfo.suite || "N/A",
        city: formData.fromInfo.city || "Calgary",
        stateOrProvinceCode: this.formatStateCode(formData.fromInfo.state) || "AB",
        postalCode: formData.fromInfo.postalCode || "T1Y 7J7",
        countryCode: "Canada",  // Always use full country name "Canada"
        openFrom: formData.fromInfo.openFrom || "08:00",
        openUntil: formData.fromInfo.openUntil || "16:00",
        notes: formData.options.driverInstructions || "",
        contactName: formData.fromInfo.attn || "N/A",
        contactPhoneNumber: formData.fromInfo.phone || "************",
        specialServiceInsideRequired: Boolean(formData.options.specialServiceInside) || false,
        specialServiceLiftGateRequired: Boolean(formData.options.specialServiceLiftGate) || false
      },
      delivery: {
        locationType: "Warehouse",
        businessName: formData.toInfo.name || "Medallion Energy Services",
        addressLine: formData.toInfo.address || "10945 86th Ave",
        addressLine2: formData.toInfo.suite || "N/A",
        city: formData.toInfo.city || "Grande Prairie",
        stateOrProvinceCode: this.formatStateCode(formData.toInfo.state) || "AB",
        postalCode: formData.toInfo.postalCode || "T8V 8K2",
        countryCode: "Canada",  // Always use full country name "Canada"
        openFrom: formData.toInfo.openFrom || "09:00",
        openUntil: formData.toInfo.openUntil || "17:00",
        notes: formData.options.driverInstructions || "",
        contactName: formData.toInfo.attn || "Donovan Partridge/ Kenny C",
        contactPhoneNumber: formData.toInfo.phone || "************",
        specialServiceInsideRequired: Boolean(formData.options.specialServiceInside) || false,
        specialServiceLiftGateRequired: Boolean(formData.options.specialServiceLiftGate) || false,
        specialServiceAppointmentRequired: Boolean(formData.options.specialServiceAppointment) || false
      },
      lineItems: [
        {
          handlingUnitType: "Pallet",
          numberHandlingUnits: parseInt(formData.items[0].numberHandlingUnits) || 1,
          description: formData.items[0].description || "Item description",
          lengthInches: parseInt(formData.items[0].length) || 30,
          widthInches: parseInt(formData.items[0].width) || 30,
          heightInches: parseInt(formData.items[0].height) || 30,
          weightPerHandlingUnitPounds: parseInt(formData.items[0].weight) || 250,
          temperatureHandling: "NoSpecialHandling",
          isDangerous: Boolean(formData.items[0].isDangerous) || false,
          isStackable: Boolean(formData.items[0].isStackable) || false
        }
      ]
    };
    
    // Log exactly what we're sending to match with AJAX example
    console.log("API REQUEST PAYLOAD:", JSON.stringify(freightData, null, 2));
    
    try {
      // Display loading notification
      this.dashboard.addNotification("Creating shipment...", "info");
      
      // Use the fixed token EXACTLY as in AJAX example
      const apiToken = "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973";
      
      // MAKE THE EXACT SAME REQUEST AS THE AJAX EXAMPLE
      const startQuoteResponse = await fetch("https://api.freightsimpledemo.com/v1/shipments/start-quoting", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiToken}`
        },
        body: JSON.stringify(freightData)
      });
      
      console.log("Start quote response status:", startQuoteResponse.status);
      
      if (!startQuoteResponse.ok) {
        const errorText = await startQuoteResponse.text();
        console.error("Error response from API:", errorText);
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(`Failed to start quoting process: ${startQuoteResponse.status} - ${errorData.errorMessage || ""}`);
        } catch (parseError) {
          throw new Error(`Failed to start quoting process: ${startQuoteResponse.status} - ${errorText.substring(0, 100)}`);
        }
      }
      
      // Get the raw response text first for debugging
      const rawResponse = await startQuoteResponse.text();
      console.log("Raw response:", rawResponse);
      
      // Then parse it as JSON
      const quoteData = JSON.parse(rawResponse);
      console.log("Quote data received:", quoteData);
      
      const shipmentId = quoteData.shipmentId;
      
      if (!shipmentId) {
        throw new Error("No shipment ID returned from the API");
      }
      
      // Save the shipment ID immediately
      this.currentShipmentId = shipmentId;
      
      this.dashboard.addNotification(`Shipment created with ID: ${shipmentId}`, "success");
      
      // Create a basic shipment details object with the ID
      const shipmentDetails = {
        shipmentId: shipmentId,
        shipmentStatus: "QuoteRequested",
        pickupDate: formData.options.pickupDate,
        pickupLocation: {
          businessName: formData.fromInfo.name || "Envent Engineering Ltd.",
          address: {
            city: formData.fromInfo.city || "Calgary",
            stateOrProvinceCode: this.formatStateCode(formData.fromInfo.state) || "AB"
          }
        },
        deliveryLocation: {
          businessName: formData.toInfo.name || "Medallion Energy Services",
          address: {
            city: formData.toInfo.city || "Grande Prairie",
            stateOrProvinceCode: this.formatStateCode(formData.toInfo.state) || "AB"
          }
        }
      };
      
      // Show the rates modal with the basic shipment details - user will need to click "Check Status"
      this.showRatesModal(shipmentDetails);
      
      return {
        success: true,
        message: "Shipment created successfully",
        shipmentId: shipmentId
      };
    } catch (error) {
      console.error("Error in Freight Sample API:", error);
      throw new Error(`Error: ${error.message}`);
    }
  }
  
  // Show rates modal with quotes from the shipment
  showRatesModal(shipmentDetails) {
    // Remove any existing modal
    const existingModal = document.getElementById("ratesModal");
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal container
    const modal = document.createElement("div");
    modal.id = "ratesModal";
    modal.className = "fixed inset-0 bg-black bg-opacity-50 z-50 overflow-auto flex";
    
    // IMPORTANT: Use the quotes array directly from the API response without modification
    const quotes = shipmentDetails.quotes || [];
    const quoteCount = quotes.length;
    
    // Log complete information about the quotes we're displaying
    console.log(`Displaying modal with ${quoteCount} quotes`);
    console.log(`Complete quotes array for display:`, JSON.stringify(quotes));
    
    // Log individual quotes for verification
    if (quoteCount > 0) {
      quotes.forEach((quote, i) => {
        console.log(`Displaying quote ${i+1}/${quoteCount}: ${quote.carrierDisplayName} - ${quote.serviceDisplayName} - ${quote.price}`);
      });
    }
    
    // Sort quotes by price (lowest first) without modifying the original array
    // This is only for display purposes and doesn't affect the stored data
    const sortedQuotes = [...quotes].sort((a, b) => parseFloat(a.price) - parseFloat(b.price));
    
    // Function to ensure logo URLs have .png extension
    const ensureLogoExtension = (url) => {
      if (!url) return null;
      return url.endsWith('.png') ? url : `${url}.png`;
    };
    
    // Create modal content with larger size
    modal.innerHTML = `
      <div class="m-auto" style="padding: 30px 40px;">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-[850px] max-h-[650px] flex flex-col">
          <!-- Header -->
          <div class="p-3 px-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Shipment Information</h2>
            <button id="closeRatesBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Content - Scrollable -->
          <div class="flex-grow p-3 px-4 overflow-y-auto">
            <div class="mb-3">
              <div class="flex justify-between items-start">
                <div>
                  <div class="text-md font-medium text-gray-700">
                    Shipment from ${shipmentDetails.pickupLocation?.businessName || 'Unknown'} to ${shipmentDetails.deliveryLocation?.businessName || 'Unknown'}
                  </div>
                  <div class="text-sm text-gray-500">
                    Pickup date: ${shipmentDetails.pickupDate ? new Date(shipmentDetails.pickupDate).toLocaleDateString() : 'Unknown'}
                  </div>
                </div>
                <div class="text-sm font-semibold px-2 py-1 rounded ${
                  shipmentDetails.shipmentStatus === "Quoted" ? 'bg-green-100 text-green-700' :
                  shipmentDetails.shipmentStatus === "QuoteRequested" ? 'bg-blue-100 text-blue-700' :
                  'bg-gray-100 text-gray-700'
                }">
                  Status: ${shipmentDetails.shipmentStatus || 'Unknown'}
                </div>
              </div>
              <div class="text-sm mt-2 bg-gray-100 p-2 my-2 border rounded flex justify-between items-center">
                <div>
                  <span class="font-bold">Shipment ID:</span> 
                  <span class="font-mono">${shipmentDetails.shipmentId || 'Unknown'}</span>
                </div>
                <button id="copyShipmentId" class="ml-1 text-blue-500 hover:text-blue-700 flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                  Copy ID
                </button>
              </div>
            </div>
            
            <div class="space-y-3" id="quotes-container">
              ${quoteCount > 0 ? 
                `<div class="flex justify-between items-center mb-2">
                  <h3 class="font-medium text-md">Available Shipping Rates (${quoteCount}):</h3>
                  <div class="text-sm text-gray-500">Sorted by lowest price first</div>
                </div>
                <div class="overflow-y-auto max-h-[400px] pr-1">
                  ${sortedQuotes.map((quote, index) => `
                    <div class="rate-option border border-gray-200 rounded-md p-3 hover:bg-gray-50 cursor-pointer mb-3" data-quote-id="${quote.quoteId}">
                      <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-4">
                          <div class="w-16 h-16 flex-shrink-0 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden">
                            ${quote.carrierLogoUrl ? 
                              `<img src="${ensureLogoExtension(quote.carrierLogoUrl)}" alt="${quote.carrierDisplayName}" class="max-w-full max-h-full object-contain">` : 
                              `<div class="text-gray-400 text-md font-medium">${quote.carrierDisplayName.substring(0, 2)}</div>`
                            }
                          </div>
                          <div>
                            <div class="font-medium text-md">${quote.carrierDisplayName}</div>
                            <div class="text-sm text-gray-500">${quote.serviceDisplayName}</div>
                          </div>
                        </div>
                        <div class="text-right">
                          <div class="font-bold text-lg">${quote.currency} ${parseFloat(quote.price).toFixed(2)}</div>
                          <div class="text-sm text-gray-500">
                            ${quote.transitBusinessDays} ${quote.transitBusinessDays === 1 ? 'day' : 'days'} transit time
                          </div>
                          <div class="text-xs text-gray-400">
                            Expected delivery: ${new Date(quote.expectedDeliveryDate).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div class="mt-2 flex justify-end">
                        <button class="select-rate-btn px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700" data-quote-id="${quote.quoteId}">
                          Select This Rate
                        </button>
                      </div>
                    </div>
                  `).join('')}
                </div>` : 
                `<div class="text-center p-4">
                  ${shipmentDetails.shipmentStatus === "QuoteRequested" ? 
                    `<div class="mb-3 text-blue-600 bg-blue-50 p-3 rounded border border-blue-200">
                      <p class="font-medium text-md">Your quote request is being processed.</p>
                      <p class="text-sm mt-1">Please click "Check Status" to get rates for your shipment.</p>
                    </div>` : 
                    shipmentDetails.shipmentStatus === "Quoted" ? 
                    `<div class="mb-3 text-yellow-600 bg-yellow-50 p-3 rounded border border-yellow-200">
                      <p class="font-medium text-md">The shipment has been quoted but no rates are displayed yet.</p>
                      <p class="text-sm mt-1">Please click "Check Status" to retrieve available rates.</p>
                    </div>` : 
                    `<div class="text-gray-500 text-md">No rates available for this shipment.</div>`
                  }
                  
                  <div class="mt-4 text-md text-gray-700">
                    <p class="font-medium">Shipment Information</p>
                    <div class="mt-2 text-sm text-left p-3 border rounded bg-gray-50">
                      <p><span class="font-semibold">Pickup:</span> ${shipmentDetails.pickupLocation?.businessName || 'N/A'}, 
                      ${shipmentDetails.pickupLocation?.address?.city || 'N/A'}, 
                      ${shipmentDetails.pickupLocation?.address?.stateOrProvinceCode || 'N/A'}</p>
                      
                      <p class="mt-1"><span class="font-semibold">Delivery:</span> ${shipmentDetails.deliveryLocation?.businessName || 'N/A'}, 
                      ${shipmentDetails.deliveryLocation?.address?.city || 'N/A'}, 
                      ${shipmentDetails.deliveryLocation?.address?.stateOrProvinceCode || 'N/A'}</p>
                      
                      <p class="mt-1"><span class="font-semibold">Items:</span> 
                      ${shipmentDetails.lineItems ? shipmentDetails.lineItems.length : 0} handling unit(s)</p>
                    </div>
                  </div>
                </div>`
              }
            </div>
          </div>
          
          <!-- Footer with buttons -->
          <div class="p-3 px-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-lg flex justify-end space-x-3">
            <button id="cancelRatesBtn" class="px-3 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
              Close
            </button>
            ${shipmentDetails.shipmentId ? 
              `<button id="retryRatesBtn" class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                ${(quoteCount > 0 || shipmentDetails.hasQuotes) ? 'Show More' : 'Check Status'}
              </button>` : ''}
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Store shipment details for later use
    this.currentShipmentDetails = shipmentDetails;
    this.currentShipmentId = shipmentDetails.shipmentId;
    
    // Set up event listeners for the rates modal
    this.setupRatesModalEventListeners(modal);
    
    // Add copy shipment ID functionality
    const copyBtn = modal.querySelector("#copyShipmentId");
    if (copyBtn) {
      copyBtn.addEventListener("click", () => {
        // Copy the shipment ID to clipboard
        const shipmentId = shipmentDetails.shipmentId;
        if (shipmentId) {
          navigator.clipboard.writeText(shipmentId)
            .then(() => {
              this.dashboard.addNotification("Shipment ID copied to clipboard", "success");
            })
            .catch(err => {
              console.error("Could not copy text: ", err);
            });
        }
      });
    }
  }
  
  // Set up event listeners for the rates modal
  setupRatesModalEventListeners(modal) {
    // Close button
    const closeBtn = modal.querySelector("#closeRatesBtn");
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Cancel button
    const cancelBtn = modal.querySelector("#cancelRatesBtn");
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Retry/Check Status button
    const retryBtn = modal.querySelector("#retryRatesBtn");
    if (retryBtn && this.currentShipmentId) {
      retryBtn.addEventListener("click", async () => {
        try {
          // Save original button text
          const originalButtonText = retryBtn.textContent.trim();
          
          // Show loading state
          retryBtn.textContent = "Checking...";
          retryBtn.disabled = true;
          
          // Show loading notification
          this.dashboard.addNotification("Checking for rates...", "info");
          
          // IMPORTANT: Added a delay to ensure all quotes are ready - similar to Postman behavior
          this.dashboard.addNotification("Waiting for all carriers to respond...", "info");
          await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds before fetching
          
          // Use the fixed API token
          const apiToken = "fs_demo_1a574b_e1b1de8f2fd349429d293f1fae1a1973";
          
          // Make a SINGLE API call to get ALL quotes at once - exactly like Postman
          const shipmentResponse = await fetch(`https://api.freightsimpledemo.com/v1/shipments/get-shipment?shipmentId=${this.currentShipmentId}`, {
            method: "GET",
            headers: {
              "Authorization": `Bearer ${apiToken}`
            },
            // Add cache-busting param to ensure fresh results every time
            cache: "no-cache",
            headers: {
              "Authorization": `Bearer ${apiToken}`,
              "Cache-Control": "no-cache",
              "Pragma": "no-cache"
            }
          });
          
          if (!shipmentResponse.ok) {
            throw new Error(`Failed to get shipment details: ${shipmentResponse.status}`);
          }
          
          // Parse the complete response as text first
          const responseText = await shipmentResponse.text();
          console.log("Raw API response text:", responseText);
          
          // Then parse as JSON
          const shipmentDetails = JSON.parse(responseText);
          
          // Log all details thoroughly
          console.log(`FINAL RESPONSE - Shipment status: ${shipmentDetails.shipmentStatus}`);
          console.log(`FINAL RESPONSE - Complete quotes array:`, shipmentDetails.quotes);
          console.log(`FINAL RESPONSE - Number of quotes found: ${shipmentDetails.quotes?.length || 0}`);
          
          // Always display all returned quotes without filtering
          if (shipmentDetails.quotes && Array.isArray(shipmentDetails.quotes)) {
            const quoteCount = shipmentDetails.quotes.length;
            
            // Log each quote to verify we're processing all of them
            if (quoteCount > 0) {
              shipmentDetails.quotes.forEach((quote, i) => {
                console.log(`Quote ${i+1}/${quoteCount}: ${quote.carrierDisplayName} - ${quote.serviceDisplayName} - ${quote.price}`);
              });
              
              this.dashboard.addNotification(`Found ${quoteCount} carrier quotes!`, "success");
              
              // After finding quotes, next time we'll "Show More"
              if (retryBtn) {
                retryBtn.textContent = "Show More";
              }
            } else {
              this.dashboard.addNotification("No quotes available for this shipment.", "warning");
              
              // Reset the button text
              if (retryBtn) {
                retryBtn.textContent = originalButtonText;
              }
            }
          } else {
            console.warn("No quotes array in API response");
            this.dashboard.addNotification(`No quotes found. Status: ${shipmentDetails.shipmentStatus}`, "warning");
            
            // Reset the button text
            if (retryBtn) {
              retryBtn.textContent = originalButtonText;
            }
          }
          
          // Store the complete shipment details for display
          this.currentShipmentDetails = shipmentDetails;
          
          // Remove the old modal
          modal.remove();
          
          // Show the new modal with the complete data and updated button text
          this.showRatesModal(shipmentDetails);
          
        } catch (error) {
          console.error("Error checking rates:", error);
          this.dashboard.addNotification(`Error checking rates: ${error.message}`, "error");
          retryBtn.textContent = "Check Status";
          retryBtn.disabled = false;
        }
      });
    }
    
    // Rate option selection - highlighting
    const rateOptions = modal.querySelectorAll(".rate-option");
    rateOptions.forEach(option => {
      option.addEventListener("click", event => {
        // Don't trigger when clicking the select button (it has its own handler)
        if (!event.target.closest('.select-rate-btn')) {
          const quoteId = option.dataset.quoteId;
          // Toggle selected class for visual feedback
          rateOptions.forEach(opt => opt.classList.remove("bg-blue-50", "border-blue-300"));
          option.classList.add("bg-blue-50", "border-blue-300");
        }
      });
    });
    
    // Select rate buttons
    const selectButtons = modal.querySelectorAll(".select-rate-btn");
    selectButtons.forEach(button => {
      button.addEventListener("click", async () => {
        try {
          const quoteId = button.dataset.quoteId;
          if (quoteId) {
            // Disable all select buttons to prevent multiple selections
            selectButtons.forEach(btn => {
              btn.disabled = true;
              btn.textContent = "Processing...";
            });
            
            // Select the rate
            await this.selectRate(quoteId);
            
            // Close the modal
            modal.remove();
          }
        } catch (error) {
          console.error("Error selecting rate:", error);
          this.dashboard.addNotification(`Error selecting rate: ${error.message}`, "error");
          
          // Re-enable buttons
          selectButtons.forEach(btn => {
            btn.disabled = false;
            btn.textContent = "Select This Rate";
          });
        }
      });
    });
  }
  
  // Handle rate selection
  async selectRate(quoteId) {
    try {
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.freightSample.isConnected) {
        throw new Error("Not connected to Freight Sample. Please reconnect in External Connections.");
      }
      
      const token = connectionManager.connections.freightSample.token;
      
      this.dashboard.addNotification("Booking shipment with selected carrier...", "info");
      
      // In a real implementation, we would make an API call to book the shipment with the selected quote
      // For this demo, we'll simulate a successful booking
      
      // Close the rates modal
      const modal = document.getElementById("ratesModal");
      if (modal) modal.remove();
      
      // Find the selected quote from our stored shipment details
      const selectedQuote = this.currentShipmentDetails.quotes.find(q => q.quoteId === quoteId);
      
      if (selectedQuote) {
        const carrierName = selectedQuote.carrierDisplayName;
        const price = `${selectedQuote.currency} ${selectedQuote.price.toFixed(2)}`;
        const deliveryDate = new Date(selectedQuote.expectedDeliveryDate).toLocaleDateString();
        
        // Show success notification
        this.dashboard.addNotification(
          `Shipment booked with ${carrierName} for ${price}. Estimated delivery: ${deliveryDate}`, 
          "success"
        );
        
        // Update the current data with shipping information
        if (this.currentData) {
          this.currentData.shippingInfo = {
            carrier: carrierName,
            trackingNumber: `FS-${Math.floor(Math.random() * 1000000)}`,
            cost: price,
            estimatedDelivery: deliveryDate,
            service: selectedQuote.serviceDisplayName,
            shipmentId: this.currentShipmentDetails.shipmentId
          };
          
          // Save the updated data
          this.dashboard.savePreviewData(this.currentData);
          
          // Update history
          this.dashboard.updateHistory("Shipping", `Booked with ${carrierName}`, this.currentData);
        }
      } else {
        throw new Error("Selected quote not found");
      }
    } catch (error) {
      console.error("Error selecting rate:", error);
      this.dashboard.addNotification(`Error booking shipment: ${error.message}`, "danger");
    }
  }
  
  // Submit to ShipWave API (Flagship)
  async submitToShipWave(formData) {
    // Check if connected to ShipWave
    const connectionStatus = connectionManager.getConnectionStatus();
    if (!connectionStatus.shipWave.isConnected) {
      throw new Error("Not connected to ShipWave. Please connect in External Connections first.");
    }
    
    // Get the API key (token)
    const token = connectionManager.connections.shipWave.apiKey;
    if (!token) {
      throw new Error("ShipWave API token not available. Please reconnect in External Connections.");
    }
    
    // Format data for Flagship API exactly as shown in the example
    const shipwaveData = {
      from: {
        name: formData.fromInfo.name || "Envent Engineering Ltd.",
        attn: formData.fromInfo.attn || "Shipping Dept",
        address: formData.fromInfo.address || "2721 Hopewell Pl NE",
        suite: formData.fromInfo.suite || "",
        city: formData.fromInfo.city || "Calgary",
        country: formData.fromInfo.country.substring(0, 2) || "CA", // Extract the country code
        state: this.formatStateCode(formData.fromInfo.state) || "AB",
        postal_code: formData.fromInfo.postalCode || "T1Y7J7",
        phone: formData.fromInfo.phone?.replace(/\D/g, '') || "4034646611", // Remove non-digits
        ext: "",
        department: "Shipping",
        is_commercial: formData.fromInfo.isCommercial || true
      },
      to: {
        name: formData.toInfo.name || "Medallion Energy Services",
        attn: formData.toInfo.attn || "Receiving Dept",
        address: formData.toInfo.address || "10945 86th Ave",
        suite: formData.toInfo.suite || "",
        city: formData.toInfo.city || "Grande Prairie",
        country: formData.toInfo.country.substring(0, 2) || "CA", // Extract the country code
        state: this.formatStateCode(formData.toInfo.state) || "AB",
        postal_code: formData.toInfo.postalCode || "T8V8K2",
        phone: formData.toInfo.phone?.replace(/\D/g, '') || "7805128483", // Remove non-digits
        ext: "",
        department: "Receiving",
        is_commercial: formData.toInfo.isCommercial || true
      },
      packages: {
        items: formData.items.map(item => ({
          width: item.width || 10,
          height: item.height || 10,
          length: item.length || 10,
          weight: item.weight || 25,
          description: item.description || "Item description"
        })),
        units: "imperial",
        type: "package",
        content: "goods"
      },
      payment: {
        payer: "F" // Your Flagship Account
      },
      options: {
        signature_required: formData.options.signatureRequired || false,
        saturday_delivery: formData.options.saturdayDelivery || false,
        shipping_date: formData.options.pickupDate || new Date().toISOString().split('T')[0],
        reference: formData.options.reference || `Shippfiy Order ${Math.floor(Math.random() * 10000)}`,
        driver_instructions: formData.options.driverInstructions || ""
      }
    };
    
    // Optional insurance if needed
    if (formData.options.insurance) {
      shipwaveData.options.insurance = {
        value: parseFloat(formData.options.insurance) || 123.45,
        description: "General industrial goods"
      };
    }
    
    console.log("Flagship API payload:", JSON.stringify(shipwaveData, null, 2));
    
    try {
      this.dashboard.addNotification("Fetching rates from carriers...", "info");
      
      // Make the API call to Flagship
      const response = await fetch("https://api.smartship.io/ship/rates", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-smartship-token": token
        },
        body: JSON.stringify(shipwaveData)
      });
      
      // Log the status code
      console.log(`Flagship API response status: ${response.status}`);
      
      // Handle non-success responses
      if (!response.ok) {
        // Try to parse error response
        const errorResponse = await response.text();
        console.error("Error response from Flagship API:", errorResponse);
        
        try {
          const errorData = JSON.parse(errorResponse);
          if (errorData.errors) {
            // Concatenate all error messages
            const errorMessages = Object.values(errorData.errors)
              .flat()
              .map(err => (typeof err === 'object' ? Object.values(err).join(', ') : err))
              .join('; ');
            throw new Error(`API Error: ${errorMessages}`);
          } else {
            throw new Error(`API Error: ${response.status} - ${response.statusText}`);
          }
        } catch (parseError) {
          throw new Error(`API Error: ${response.status} - ${errorResponse.substring(0, 100)}`);
        }
      }
      
      // Parse the successful response
      const ratesData = await response.json();
      console.log("Flagship API response:", ratesData);
      
      // Process and display the rates
      if (ratesData.content && Array.isArray(ratesData.content) && ratesData.content.length > 0) {
        // Format the rates for display
        const formattedRates = this.formatFlagshipRates(ratesData, shipwaveData);
        
        // Show the rates in the modal
        this.showShipWaveRatesModal(formattedRates);
        
        return {
          success: true,
          message: "Rates retrieved successfully"
        };
      } else {
        // No rates available
        throw new Error("No rates available for this shipment. Please check your shipment details.");
      }
    } catch (error) {
      console.error("Error in Flagship API:", error);
      
      // For demo purposes, if there's an API error, fall back to simulated rates
      if (error.message.includes("API Error") || error.message.includes("Failed to fetch")) {
        this.dashboard.addNotification("API connection failed, showing simulated rates for demo purposes", "warning");
        
        // Generate simulated response data
        const simulatedRates = this.generateSimulatedShipWaveRates(shipwaveData);
        
        // Show the rates in a modal
        this.showShipWaveRatesModal(simulatedRates);
        
        return {
          success: true,
          message: "Simulated rates retrieved successfully"
        };
      }
      
      throw new Error(`Error fetching rates: ${error.message}`);
    }
  }
  
  // Format Flagship API rates response for display
  formatFlagshipRates(ratesData, shipwaveData) {
    const rates = [];
    
    // Process each rate in content array
    ratesData.content.forEach((rate, index) => {
      // Calculate delivery date based on transit time
      const shippingDate = new Date(shipwaveData.options.shipping_date);
      const transitDays = parseInt(rate.service.transit_time) || 1;
      const deliveryDate = new Date(shippingDate);
      deliveryDate.setDate(deliveryDate.getDate() + transitDays);
      
      // Skip weekends for delivery date
      if (deliveryDate.getDay() === 0) deliveryDate.setDate(deliveryDate.getDate() + 1); // Sunday -> Monday
      if (deliveryDate.getDay() === 6) deliveryDate.setDate(deliveryDate.getDate() + 2); // Saturday -> Monday
      
      // Add formatted rate
      rates.push({
        id: `FS-${index + 1}`,
        carrier: rate.service.courier_name,
        service: rate.service.courier_desc,
        price: rate.price.subtotal || 0,
        tax: rate.price.taxes ? Object.values(rate.price.taxes).reduce((sum, tax) => sum + tax, 0) : 0,
        total: rate.price.total || 0,
        currency: "CAD", // Default to CAD
        transitDays: transitDays,
        deliveryDate: deliveryDate.toISOString().split('T')[0],
        estimatedDelivery: rate.service.estimated_delivery_date || deliveryDate.toISOString().split('T')[0],
        // Convert carrier name to lowercase for icon lookup
        logoUrl: `couriers/${rate.service.courier_name.toLowerCase().replace(/\s+/g, '')}`
      });
    });
    
    // Sort by price
    rates.sort((a, b) => a.total - b.total);
    
    // Add any errors or notices
    const messages = [];
    
    // Add any errors
    if (ratesData.errors && Object.keys(ratesData.errors).length > 0) {
      Object.entries(ratesData.errors).forEach(([carrier, errors]) => {
        if (Array.isArray(errors)) {
          errors.forEach(error => {
            if (typeof error === 'object') {
              Object.values(error).forEach(errorMsg => {
                messages.push({
                  type: "error",
                  carrier: carrier,
                  message: errorMsg
                });
              });
            } else {
              messages.push({
                type: "error",
                carrier: carrier,
                message: error
              });
            }
          });
        }
      });
    }
    
    // Add any notices
    if (ratesData.notices && Array.isArray(ratesData.notices)) {
      ratesData.notices.forEach(notice => {
        const parts = notice.split(':');
        if (parts.length >= 2) {
          messages.push({
            type: "notice",
            carrier: parts[0].trim(),
            message: parts.slice(1).join(':').trim()
          });
        } else {
          messages.push({
            type: "notice",
            carrier: "system",
            message: notice
          });
        }
      });
    }
    
    return {
      shipment: {
        from: shipwaveData.from.city,
        to: shipwaveData.to.city,
        shippingDate: shipwaveData.options.shipping_date,
        packages: shipwaveData.packages.items.length
      },
      rates: rates,
      messages: messages
    };
  }

  // Show rates modal for ShipWave (Flagship)
  showShipWaveRatesModal(ratesData) {
    // Remove any existing modal
    const existingModal = document.getElementById("ratesModal");
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal container
    const modal = document.createElement("div");
    modal.id = "ratesModal";
    modal.className = "fixed inset-0 bg-black bg-opacity-50 z-50 overflow-auto flex";
    
    // Create modal content with larger size for better readability
    modal.innerHTML = `
      <div class="m-auto" style="padding: 30px 40px;">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-[850px] max-h-[650px] flex flex-col">
          <!-- Header -->
          <div class="p-3 px-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-t-lg flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Select a Shipping Rate</h2>
            <button id="closeRatesBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <!-- Content - Scrollable -->
          <div class="flex-grow p-3 px-4 overflow-y-auto">
            <div class="mb-3">
              <div class="text-md font-medium text-gray-700 mb-1">
                Shipment from ${ratesData.shipment.from} to ${ratesData.shipment.to}
              </div>
              <div class="text-sm text-gray-500">
                Shipping date: ${new Date(ratesData.shipment.shippingDate).toLocaleDateString()} • ${ratesData.shipment.packages} package(s)
              </div>
            </div>
            
            ${ratesData.messages && ratesData.messages.length > 0 ? `
              <div class="mb-3 border border-yellow-200 bg-yellow-50 rounded-md p-2">
                <div class="text-sm font-medium text-gray-700 mb-1">Notices:</div>
                <ul class="text-xs text-gray-600 list-disc list-inside">
                  ${ratesData.messages.map(msg => `
                    <li><span class="font-medium">${msg.carrier}:</span> ${msg.message}</li>
                  `).join('')}
                </ul>
              </div>
            ` : ''}
            
            <div class="space-y-3 overflow-y-auto max-h-[400px] pr-1">
              ${ratesData.rates.length > 0 ? 
                ratesData.rates.map(rate => `
                  <div class="rate-option border border-gray-200 rounded-md p-3 hover:bg-gray-50 cursor-pointer mb-3" data-rate-id="${rate.id}">
                    <div class="flex justify-between items-center">
                      <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 flex-shrink-0 bg-gray-100 rounded-md flex items-center justify-center overflow-hidden p-2">
                          <img src="images/${rate.logoUrl}.svg" alt="${rate.carrier}" class="max-w-full max-h-full object-contain" 
                               onerror="this.onerror=null; this.src='images/couriers/generic.svg'; this.alt='${rate.carrier}';">
                        </div>
                        <div>
                          <div class="font-medium text-md">${rate.carrier}</div>
                          <div class="text-sm text-gray-500">${rate.service}</div>
                        </div>
                      </div>
                      <div class="text-right">
                        <div class="font-bold text-lg">${rate.currency} ${parseFloat(rate.total).toFixed(2)}</div>
                        <div class="text-sm text-gray-500">
                          ${rate.transitDays} ${rate.transitDays === 1 ? 'day' : 'days'} transit time
                        </div>
                        <div class="text-xs text-gray-400">
                          Delivery by: ${new Date(rate.estimatedDelivery).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                      <button class="select-rate-btn px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700" data-rate-id="${rate.id}">
                        Select This Rate
                      </button>
                    </div>
                  </div>
                `).join('') : 
                `<div class="text-center p-4 bg-gray-50 rounded-md border border-gray-200">
                  <svg class="w-12 h-12 mx-auto text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <div class="text-gray-600 font-medium">No rates available for this shipment</div>
                  <div class="text-gray-500 text-sm mt-1">Please try different shipment details or check with support.</div>
                </div>`
              }
            </div>
          </div>
          
          <!-- Footer with buttons -->
          <div class="p-3 px-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-lg flex justify-end space-x-3">
            <button id="cancelRatesBtn" class="px-3 py-2 text-sm bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300">
              Close
            </button>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Store rate data for later use
    this.currentShipWaveRates = ratesData;
    
    // Add event listeners
    this.setupShipWaveRatesModalEventListeners(modal);
  }
  
  // Setup event listeners for the ShipWave rates modal
  setupShipWaveRatesModalEventListeners(modal) {
    // Close button
    const closeBtn = modal.querySelector("#closeRatesBtn");
    if (closeBtn) {
      closeBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Cancel button
    const cancelBtn = modal.querySelector("#cancelRatesBtn");
    if (cancelBtn) {
      cancelBtn.addEventListener("click", () => {
        modal.remove();
      });
    }
    
    // Select rate buttons
    const selectRateBtns = modal.querySelectorAll(".select-rate-btn");
    selectRateBtns.forEach(btn => {
      btn.addEventListener("click", () => {
        const rateId = btn.getAttribute("data-rate-id");
        this.selectShipWaveRate(rateId);
      });
    });
    
    // Make entire rate cards clickable
    const rateOptions = modal.querySelectorAll(".rate-option");
    rateOptions.forEach(option => {
      option.addEventListener("click", (e) => {
        // Don't trigger if clicking the select button directly
        if (!e.target.classList.contains("select-rate-btn")) {
          const rateId = option.getAttribute("data-rate-id");
          
          // Highlight selected option
          rateOptions.forEach(o => o.classList.remove("border-blue-500", "border-2"));
          option.classList.add("border-blue-500", "border-2");
        }
      });
    });
    
    // Close when clicking outside
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }
  
  // Generate simulated rates for ShipWave when API fails
  generateSimulatedShipWaveRates(shipwaveData) {
    // Calculate basic shipping distance (simplified)
    const fromZip = shipwaveData.from.postal_code;
    const toZip = shipwaveData.to.postal_code;
    
    // Simple algorithm to determine a "distance factor" from postal codes
    const distanceFactor = (fromZip.substring(0, 1) === toZip.substring(0, 1)) ? 1 : 2;
    
    // Calculate total weight and dimensions
    const packages = shipwaveData.packages.items;
    const totalWeight = packages.reduce((sum, pkg) => sum + pkg.weight, 0);
    const totalVolume = packages.reduce((sum, pkg) => sum + (pkg.length * pkg.width * pkg.height), 0);
    
    // Base pricing factors
    const basePricePerKg = 0.5; // $0.50 per kg
    const basePricePerCubicCm = 0.0001; // $0.0001 per cubic cm
    
    // Base date for delivery estimates
    const shippingDate = new Date(shipwaveData.options.shipping_date);
    
    // Generate Canadian carriers for Flagship simulation
    const carriers = [
      {
        name: "Purolator",
        services: [
          {
            name: "Express",
            speedFactor: 1.5,
            priceFactor: 1.8,
            logoUrl: "couriers/purolator"
          },
          {
            name: "Ground",
            speedFactor: 1.0,
            priceFactor: 1.0,
            logoUrl: "couriers/purolator"
          }
        ]
      },
      {
        name: "Canada Post",
        services: [
          {
            name: "Priority",
            speedFactor: 1.8,
            priceFactor: 2.0,
            logoUrl: "couriers/canadapost"
          },
          {
            name: "Expedited Parcel",
            speedFactor: 1.0,
            priceFactor: 0.9,
            logoUrl: "couriers/canadapost" 
          }
        ]
      },
      {
        name: "FedEx",
        services: [
          {
            name: "Priority Overnight",
            speedFactor: 2.0,
            priceFactor: 2.2,
            logoUrl: "couriers/fedex"
          },
          {
            name: "Ground",
            speedFactor: 1.0,
            priceFactor: 1.1,
            logoUrl: "couriers/fedex"
          }
        ]
      },
      {
        name: "DHL",
        services: [
          {
            name: "Express",
            speedFactor: 1.5,
            priceFactor: 1.5,
            logoUrl: "couriers/dhl"
          }
        ]
      }
    ];
    
    // Generate simulated rates
    const rates = [];
    let rateId = 1;
    
    carriers.forEach(carrier => {
      carrier.services.forEach(service => {
        // Calculate transit days
        const transitDays = Math.max(1, Math.round(distanceFactor * 2 / service.speedFactor));
        
        // Calculate delivery date
        const deliveryDate = new Date(shippingDate);
        deliveryDate.setDate(deliveryDate.getDate() + transitDays);
        
        // Skip weekends for delivery date
        if (deliveryDate.getDay() === 0) deliveryDate.setDate(deliveryDate.getDate() + 1); // Sunday -> Monday
        if (deliveryDate.getDay() === 6) deliveryDate.setDate(deliveryDate.getDate() + 2); // Saturday -> Monday
        
        // Calculate base price
        const basePrice = (totalWeight * basePricePerKg) + (totalVolume * basePricePerCubicCm);
        
        // Apply service modifiers
        const price = basePrice * service.priceFactor * distanceFactor;
        
        // Add tax
        const tax = price * 0.05;
        
        rates.push({
          id: `FS-${rateId++}`,
          carrier: carrier.name,
          service: service.name,
          price: price,
          tax: tax,
          total: price + tax,
          currency: "CAD",
          transitDays: transitDays,
          deliveryDate: deliveryDate.toISOString().split('T')[0],
          estimatedDelivery: deliveryDate.toISOString().split('T')[0],
          logoUrl: service.logoUrl
        });
      });
    });
    
    // Sort by price
    rates.sort((a, b) => a.total - b.total);
    
    // Add some simulated notices
    const messages = [
      {
        type: "notice",
        carrier: "Canada Post",
        message: "No Saturday delivery available for this postal code"
      },
      {
        type: "notice",
        carrier: "system",
        message: "Rates are estimates and may change at time of shipping"
      }
    ];
    
    return {
      shipment: {
        from: shipwaveData.from.city,
        to: shipwaveData.to.city,
        shippingDate: shipwaveData.options.shipping_date,
        packages: packages.length
      },
      rates: rates,
      messages: messages
    };
  }
  
  // Handle ShipWave rate selection
  async selectShipWaveRate(rateId) {
    try {
      const connectionStatus = connectionManager.getConnectionStatus();
      if (!connectionStatus.shipWave.isConnected) {
        throw new Error("Not connected to ShipWave. Please reconnect in External Connections.");
      }
      
      this.dashboard.addNotification("Booking shipment with selected carrier...", "info");
      
      // In a real implementation, we would make an API call to book the shipment with the selected rate
      // For this demo, we'll simulate a successful booking
      
      // Close the rates modal
      const modal = document.getElementById("ratesModal");
      if (modal) modal.remove();
      
      // Find the selected rate from our stored rates data
      const selectedRate = this.currentShipWaveRates.rates.find(r => r.id === rateId);
      
      if (selectedRate) {
        const carrierName = selectedRate.carrier;
        const price = `${selectedRate.currency} ${selectedRate.total.toFixed(2)}`;
        const deliveryDate = new Date(selectedRate.deliveryDate).toLocaleDateString();
        
        // Show success notification
        this.dashboard.addNotification(
          `Shipment booked with ${carrierName} for ${price}. Estimated delivery: ${deliveryDate}`, 
          "success"
        );
        
        // Update the current data with shipping information
        if (this.currentData) {
          this.currentData.shippingInfo = {
            carrier: carrierName,
            trackingNumber: `SW-${Math.floor(Math.random() * 1000000)}`,
            cost: price,
            estimatedDelivery: deliveryDate,
            service: selectedRate.service,
            shipmentId: selectedRate.id
          };
          
          // Save the updated data
          this.dashboard.savePreviewData(this.currentData);
          
          // Update history
          this.dashboard.updateHistory("Shipping", `Booked with ${carrierName}`, this.currentData);
        }
      } else {
        throw new Error("Selected rate not found");
      }
    } catch (error) {
      console.error("Error selecting ShipWave rate:", error);
      this.dashboard.addNotification(`Error booking shipment: ${error.message}`, "danger");
    }
  }

  // Test function to verify the API integration
  async testApiIntegration() {
    console.log("Testing API integration");
    this.dashboard.addNotification("Testing API integration...", "info");
    return true;
  }

  // Helper function to format state code
  formatStateCode(state) {
    if (!state) return "AB";  // Default to Alberta
    
    // Handle different formats
    if (state.includes(" - ")) {
      return state.split(" - ")[0].trim();
    } else if (state.includes("-")) {
      return state.split("-")[0].trim();
    } else if (state.length <= 2) {
      return state.trim();
    }
    
    // Default to first 2 chars if nothing else works
    return state.substring(0, 2).trim();
  }
  
  // Helper function to format country code
  formatCountryCode(country) {
    if (!country) return "Canada";  // Default to Canada
    
    // Format for Freight Sample API - must be full country name
    if (country.includes("Canada") || country === "CA") {
      return "Canada";
    } else if (country.includes("United States") || country === "US" || country === "USA") {
      return "UnitedStates";
    }
    
    // Default to Canada if we can't determine
    return "Canada";
  }
}

// Setup the Ship Order button
export function setupShipOrder(dashboard) {
  const shipOrderBtn = dashboard.container.querySelector('[data-action="shipOrder"]');
  if (shipOrderBtn) {
    const shipOrderSystem = initializeShipOrder(dashboard);
    
    shipOrderBtn.addEventListener("click", async () => {
      try {
        // Check if order data is available
        if (!dashboard.currentPreviewData) {
          dashboard.addNotification("No order data available. Please process an order first.", "warning");
          return;
        }
        
        shipOrderBtn.disabled = true;
        shipOrderBtn.classList.add("opacity-50", "cursor-not-allowed");
        
        // Show the ship order modal
        await shipOrderSystem.showShipOrderModal(dashboard.currentPreviewData);
        
      } catch (error) {
        console.error("Error in shipOrder:", error);
        dashboard.addNotification(`Error: ${error.message}`, "danger");
      } finally {
        shipOrderBtn.disabled = false;
        shipOrderBtn.classList.remove("opacity-50", "cursor-not-allowed");
      }
    });
  }
} 