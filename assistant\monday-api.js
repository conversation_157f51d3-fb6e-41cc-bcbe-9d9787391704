// Monday.com API Integration Service
export class MondayApiService {
  constructor(assistant) {
    this.assistant = assistant;
  }

  async setupMondayApi() {
    if (!this.assistant.user) {
      console.warn("No user data available for Monday.com setup");
      return false;
    }
    
    // Extract Monday.com API key
    if (this.assistant.user["Monday API Key"]) {
      this.assistant.mondayApiKey = this.assistant.user["Monday API Key"];
      console.log("Monday.com API key loaded");
    } else {
      console.warn("No Monday API Key found in user data");
      return false;
    }
    
    // Store board configurations for different order types
    let boardsConfigured = 0;
    
    // SH Board Configuration
    if (this.assistant.user["SH- Board ID"]) {
      this.assistant.mondayBoards.SH = {
        boardId: this.assistant.user["SH- Board ID"],
        groupId: this.assistant.user["SH-Group ID"] || null,
        fields: {
          orderNumber: this.assistant.user["SH-Order Number"] || "Order Number",
          orderType: this.assistant.user["SH-Order Type"] || "Order Type",
          customerName: this.assistant.user["SH-Customer Name"] || "Customer Name",
          referenceNumber: this.assistant.user["SH-Reference Number"] || "Reference Number",
          country: this.assistant.user["SH-Country"] || "Country",
          packageQty: this.assistant.user["SH-Package Qty"] || "Package Qty",
          packageType: this.assistant.user["SH-Package Type"] || "Package Type",
          dimensions: this.assistant.user["SH-Dims"] || "Dimensions",
          weight: this.assistant.user["SH-Weight"] || "Weight",
          shipmentMethod: this.assistant.user["SH-Shipment Method"] || "Shipment Method",
          freightCost: this.assistant.user["SH-Freight Cost"] || "Freight Cost",
          description: this.assistant.user["SH-Shipment Description"] || "Description",
          date: this.assistant.user["SH-Date"] || "Date",
          packageNote: this.assistant.user["SH_Package note"] || "Package Note",
          carrier: this.assistant.user["SH-Carrier"] || "Carrier",
          trackingNumber: this.assistant.user["SH-Tracking Number"] || "Tracking Number",
          trackingLink: this.assistant.user["SH-Tracking Link"] || "Tracking Link",
          inventoryId: this.assistant.user["SH-Inventory ID"] || "Inventory ID",
          partDescription: this.assistant.user["SH-Part Description"] || "Part Description",
          shippedQty: this.assistant.user["SH-Shipped Qty"] || "Shipped Qty",
          serialNumber: this.assistant.user["SH-Serial Nbr"] || "Serial Number",
          hsCode: this.assistant.user["SH-HS Code"] || "HS Code",
          countryOfOrigin: this.assistant.user["SH-Country of Origin"] || "Country of Origin"
        }
      };
      console.log(`SH board configuration loaded, ID: ${this.assistant.mondayBoards.SH.boardId}`);
      boardsConfigured++;
    }
    
    // PO Board Configuration
    if (this.assistant.user["PO- Board ID"]) {
      this.assistant.mondayBoards.PO = {
        boardId: this.assistant.user["PO- Board ID"],
        groupId: this.assistant.user["PO-Group ID"] || null,
        fields: {
          referenceNumber: this.assistant.user["PO-Reference Number"] || "Reference Number",
          orderType: this.assistant.user["PO-Order Type"] || "Order Type",
          customerName: this.assistant.user["PO-Customer Name"] || "Customer Name",
          description: this.assistant.user["PO-Shipment Description"] || "Description",
          country: this.assistant.user["PO-Country"] || "Country",
          packageQty: this.assistant.user["PO-Package Qty"] || "Package Qty",
          packageType: this.assistant.user["PO-Package Type"] || "Package Type",
          dimensions: this.assistant.user["PO-Dims"] || "Dimensions",
          weight: this.assistant.user["PO-Weight"] || "Weight",
          shipmentMethod: this.assistant.user["PO-Shipment Method"] || "Shipment Method",
          freightCost: this.assistant.user["PO-Freight Cost"] || "Freight Cost",
          carrier: this.assistant.user["PO-Carrier"] || "Carrier",
          trackingNumber: this.assistant.user["PO-Tracking No"] || "Tracking Number",
          trackingLink: this.assistant.user["PO-Tracking Link"] || "Tracking Link",
          packageNote: this.assistant.user["PO-Package Note"] || "Package Note",
          inventoryId: this.assistant.user["PO-Inventory ID"] || "Inventory ID",
          partDescription: this.assistant.user["PO-Part Description"] || "Part Description",
          partQty: this.assistant.user["PO-Part Qty"] || "Part Qty",
          serialNumber: this.assistant.user["PO-Serial Nbr"] || "Serial Number",
          supplierPartNumber: this.assistant.user["PO-Supplier P/N"] || "Supplier P/N",
          hsCode: this.assistant.user["PO-HS Code"] || "HS Code",
          countryOfOrigin: this.assistant.user["PO-COO"] || "Country of Origin"
        }
      };
      console.log(`PO board configuration loaded, ID: ${this.assistant.mondayBoards.PO.boardId}`);
      boardsConfigured++;
    }
    
    // SO Board Configuration
    if (this.assistant.user["SO- Board ID"]) {
      this.assistant.mondayBoards.SO = {
        boardId: this.assistant.user["SO- Board ID"],
        groupId: this.assistant.user["SO-Group ID"] || null,
        fields: {
          customerName: this.assistant.user["SO-Customer Name"] || "Customer Name",
          salesOrderNumber: this.assistant.user["SO-Sales Order Number"] || "Sales Order Number",
          orderType: this.assistant.user["SO-Order Type"] || "Order Type",
          inventoryId: this.assistant.user["SO-Inventory ID"] || "Inventory ID",
          description: this.assistant.user["SO-Description"] || "Description",
          orderQty: this.assistant.user["SO-Order Qty"] || "Order Qty",
          productionOrderType: this.assistant.user["SO-Production Order Type"] || "Production Order Type",
          productionNumber: this.assistant.user["SO-Production Number"] || "Production Number"
        }
      };
      console.log(`SO board configuration loaded, ID: ${this.assistant.mondayBoards.SO.boardId}`);
      boardsConfigured++;
    }
    
    if (boardsConfigured > 0) {
      console.log(`Monday.com configuration complete, ${boardsConfigured} boards configured`);
      return true;
    } else {
      console.warn("No Monday.com board IDs found in user configuration");
      return false;
    }
  }

  // Only trigger Monday.com search when explicitly requested
  isMondayQuery(message) {
    const mondayKeywords = ['monday', 'monday.com'];
    const normalizedMessage = message.toLowerCase().trim();
    
    // Only return true if "monday" or "monday.com" is mentioned
    return mondayKeywords.some(keyword => normalizedMessage.includes(keyword));
  }

  // Enhanced Monday.com query handler
  async handleMondayQuery(message) {
    // Use the API key from user data if available, otherwise use hardcoded key as fallback
    const apiKey = this.assistant.mondayApiKey || "***********************************************************************************************************************************************************************************************************************************";
    
    // Extract order type and number with improved extraction
    const orderInfo = this.extractMondayOrderInfo(message);
    
    if (!orderInfo.orderNumber) {
      this.assistant.addMessage({
        role: 'assistant',
        content: "Please provide an order number to search for in Monday.com. For example: 'Search Monday.com for order PO12345'",
        timestamp: new Date()
      });
      return;
    }
    
    // Show searching message
    this.assistant.addMessage({
      role: 'assistant',
      content: `Searching Monday.com for ${orderInfo.orderType || ""} ${orderInfo.orderNumber}...`,
      timestamp: new Date()
    });
    
    try {
      // Smarter search strategy based on order type
      let order = null;
      
      if (orderInfo.orderType === "PO") {
        // More efficient PO search - try different formats in parallel
        const searchQueries = [
          orderInfo.orderNumber,
          `PO-${orderInfo.orderNumber}`,
          `PO - ${orderInfo.orderNumber}`
        ];
        
        // Try all search patterns in sequence until we find a result
        for (const searchQuery of searchQueries) {
          order = await this.searchMondayWithRules(
            searchQuery, 
            apiKey, 
            "7708171423", // PO board ID
            "name"
          );
          
          if (order) break;
        }
      } else {
        // SO/SH search strategy
        const paddedNumber = orderInfo.orderNumber.padStart(6, '0');
        
        // Try direct search by reference number first
        order = await this.searchMondayWithRules(
          orderInfo.orderNumber, 
          apiKey, 
          "7708125953", // SO/SH board ID
          "text0__1"
        );
        
        // Try with padded number if not found
        if (!order && paddedNumber !== orderInfo.orderNumber) {
          order = await this.searchMondayWithRules(
            paddedNumber, 
            apiKey, 
            "7708125953", 
            "text0__1"
          );
        }
        
        // Try searching by board item name if still not found
        if (!order) {
          order = await this.searchMondayWithRules(
            orderInfo.orderNumber,
            apiKey,
            "7708125953",
            "name"
          );
        }
        
        // Try with type prefix patterns if all else fails
        if (!order && (orderInfo.orderType === "SH" || orderInfo.orderType === "SO")) {
          order = await this.searchMondayWithRules(
            `${orderInfo.orderType} - ${orderInfo.orderNumber}`, 
            apiKey, 
            "7708125953", 
            "name"
          );
        }
      }
      
      if (order) {
        this.assistant.addMessage({
          role: 'assistant',
          content: {
            type: 'orderTracking',
            order: order
          },
          timestamp: new Date()
        });
        
        // Store in conversation context for smarter follow-ups
        this.assistant.updateConversationContext('mondaySearch', {
          lastSearchedOrder: orderInfo.orderNumber
        });
      } else {
        this.assistant.addMessage({
          role: 'assistant',
          content: `I couldn't find any ${orderInfo.orderType || "order"} matching ${orderInfo.orderNumber} in Monday.com. Please check the order number and try again.`,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error("Error searching Monday.com:", error);
      this.assistant.addMessage({
        role: 'assistant',
        content: "I encountered an error while searching Monday.com. Please try again later.",
        timestamp: new Date()
      });
    }
  }

  // Extract order info from message for Monday search
  extractMondayOrderInfo(message) {
    const normalizedMessage = message.toLowerCase().trim();
    
    // Define patterns to match different order number formats
    // Pattern 1: PO#12345, SO#12345, SH#12345
    // Pattern 2: PO-12345, SO-12345, SH-12345
    // Pattern 3: PO 12345, SO 12345, SH 12345
    // Pattern 4: PO12345, SO12345, SH12345
    const patterns = [
      /\b(po|so|sh)#(\d+)\b/i,
      /\b(po|so|sh)-(\d+)\b/i,
      /\b(po|so|sh)\s+(\d+)\b/i,
      /\b(po|so|sh)(\d+)\b/i
    ];
    
    // Try each pattern
    for (const pattern of patterns) {
      const match = normalizedMessage.match(pattern);
      if (match) {
        return {
          orderType: match[1].toUpperCase(),
          orderNumber: match[2]
        };
      }
    }
    
    // If no pattern with order type, look for just a number
    const numberMatch = normalizedMessage.match(/\border\s+(?:number\s+)?(\d+)\b|\b(\d{4,})\b/i);
    if (numberMatch) {
      // Try to infer order type from context
      let orderType = null;
      if (normalizedMessage.includes("po") || normalizedMessage.includes("purchase")) {
        orderType = "PO";
      } else if (normalizedMessage.includes("so") || normalizedMessage.includes("sales")) {
        orderType = "SO";
      } else if (normalizedMessage.includes("sh") || normalizedMessage.includes("ship")) {
        orderType = "SH";
      } else {
        // Default to SO if no specific type mentioned
        orderType = "SO";
      }
      
      return {
        orderType: orderType,
        orderNumber: numberMatch[1] || numberMatch[2]
      };
    }
    
    return { orderType: null, orderNumber: null };
  }

  // Enhanced Monday.com search function
  async searchMondayWithRules(searchValue, apiKey, boardId, columnId) {
    try {
      console.log(`Searching Monday.com for "${searchValue}" in board ${boardId}, column ${columnId}`);
      
      // Sanitize search value to prevent injection
      const sanitizedSearchValue = searchValue.replace(/["'\\]/g, '\\$&');
      
      // Build GraphQL query with the exact structure provided
      const query = `
        query {
          boards(ids: ${boardId}) {
            items_page(query_params: {
              rules: [{
                column_id: "${columnId}", 
                compare_value: ["${sanitizedSearchValue}"], 
                operator: contains_text
              }]
            }) {
              items {
                id
                name
                column_values {
                  id
                  text
                }
              }
            }
          }
        }
      `;
      
      // Make API request
      const response = await fetch("https://api.monday.com/v2", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": apiKey
        },
        body: JSON.stringify({ query })
      });
      
      if (!response.ok) {
        throw new Error(`Monday.com API error: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Enhanced error handling
      if (data.errors) {
        console.error("Monday.com API errors:", JSON.stringify(data.errors));
        return null;
      }
      
      // Get items from the response
      const items = data.data?.boards[0]?.items_page?.items;
      console.log(`Found ${items?.length || 0} items matching "${searchValue}"`);
      
      if (!items || items.length === 0) {
        return null;
      }
      
      // Format the first item based on board type
      const item = items[0];
      
      // Create column value map for easier access
      const columnValues = {};
      item.column_values.forEach(col => {
        columnValues[col.id] = col.text || "";
      });
      
      // Initialize order based on board type
      let order;
      
      if (boardId === "7708171423") {
        // PO Board
        order = this.parsePOBoardItem(item, columnValues);
      } else {
        // SO/SH Board
        order = this.parseSOBoardItem(item, columnValues);
      }
      
      // Add a single Monday.com link
      order["Monday.com Link"] = `https://enventengineering.monday.com/boards/${boardId}/pulses/${item.id}`;
      
      console.log("Successfully retrieved order from Monday.com:", order);
      return order;
      
    } catch (error) {
      console.error("Error searching Monday.com:", error);
      return null;
    }
  }

  // Parse PO board item response with complete field mapping
  parsePOBoardItem(item, columnValues) {
    // Base order object with PO details
    const order = {
      "Order Type": "PO",
      "Order Number": item.name,
      "Reference Number": item.name,
      "Source": "Monday.com",
      "Status": columnValues["status"] || "Active",
      "Monday Item ID": item.id,
      "boardId": "7708171423"
    };
    
    // Map all known fields from PO board
    this.mapColumnIfExists(columnValues, "text__1", "Vendor Name", order);
    this.mapColumnIfExists(columnValues, "text_mkp4g97s", "Order Type Detail", order);
    this.mapColumnIfExists(columnValues, "text53__1", "Description", order);
    this.mapColumnIfExists(columnValues, "text_mkp1g1t7", "Shipment Method", order);
    this.mapColumnIfExists(columnValues, "text_mkp173gd", "Freight Cost", order);
    this.mapColumnIfExists(columnValues, "text8__1", "Carrier", order);
    this.mapColumnIfExists(columnValues, "text_mkp1w2zs", "Tracking No", order);
    this.mapColumnIfExists(columnValues, "text_mkp1a1sw", "Tracking Link", order);
    this.mapColumnIfExists(columnValues, "text_Mjj6bmnQ", "Notes", order);
    this.mapColumnIfExists(columnValues, "date_Mjj4fRml", "ETA Date", order);
    this.mapColumnIfExists(columnValues, "email_Mjj4Qvzz", "Notice Email", order);
    this.mapColumnIfExists(columnValues, "date4", "Date Received", order);
    this.mapColumnIfExists(columnValues, "person", "Receiver", order);
    this.mapColumnIfExists(columnValues, "text_mkp15v1b", "Country", order);
    this.mapColumnIfExists(columnValues, "text_mkp1gchf", "Package Qty", order);
    this.mapColumnIfExists(columnValues, "text_mkp1z7p5", "Package Type", order);
    this.mapColumnIfExists(columnValues, "text_mkp12w54", "Dimensions", order);
    this.mapColumnIfExists(columnValues, "text_mkp1hpx1", "Weight", order);
    
    return order;
  }

  // Parse SO/SH board item response with complete field mapping
  parseSOBoardItem(item, columnValues) {
    // Determine if it's SO or SH from available data
    let orderType = "SO";
    if (item.name.startsWith("SH")) {
      orderType = "SH";
    } else if (columnValues["text_mkp4njf2"] === "SH") {
      orderType = "SH";
    } else if (columnValues["text_mkp4njf2"] === "SO") {
      orderType = "SO";
    }
    
    // Base order object
    const order = {
      "Order Type": orderType,
      "Source": "Monday.com",
      "Monday Item ID": item.id,
      "boardId": "7708125953"
    };
    
    // Extract order number and reference number
    if (columnValues["text0__1"]) {
      order["Reference Number"] = columnValues["text0__1"];
    }
    
    // Order Number either from the item name or reference number
    order["Order Number"] = item.name || order["Reference Number"] || "N/A";
    
    // Map all fields from SO/SH board with complete mapping
    this.mapColumnIfExists(columnValues, "text_mkp4njf2", "Order Type Detail", order);
    this.mapColumnIfExists(columnValues, "status_18__1", "Status", order);
    this.mapColumnIfExists(columnValues, "text8__1", "Customer Name", order);
    this.mapColumnIfExists(columnValues, "text_mkp13473", "Country", order);
    this.mapColumnIfExists(columnValues, "text86__1", "Payment Terms", order);
    this.mapColumnIfExists(columnValues, "text5__1", "Currency", order);
    this.mapColumnIfExists(columnValues, "text3__1", "Package Qty", order);
    this.mapColumnIfExists(columnValues, "text_mkp1nmw7", "Package Type", order);
    this.mapColumnIfExists(columnValues, "text52__1", "Dimensions", order);
    this.mapColumnIfExists(columnValues, "text_12__1", "Weight", order);
    this.mapColumnIfExists(columnValues, "text_mkp1m1am", "Freight Terms", order);
    this.mapColumnIfExists(columnValues, "text53__1", "Description", order);
    this.mapColumnIfExists(columnValues, "freight_charge8__1", "Freight Cost", order);
    this.mapColumnIfExists(columnValues, "text_mkp1gqg8", "Ship Via", order);
    this.mapColumnIfExists(columnValues, "text_mkp1n5g9", "Ship Date", order);
    this.mapColumnIfExists(columnValues, "person", "Personnel", order);
    this.mapColumnIfExists(columnValues, "long_text2__1", "Comments", order);
    this.mapColumnIfExists(columnValues, "status_19__1", "Shipment Status", order);
    this.mapColumnIfExists(columnValues, "text_mkp0zexp", "Extra Info", order);
    this.mapColumnIfExists(columnValues, "text27__1", "XCF Reference", order);
    this.mapColumnIfExists(columnValues, "text_mkp4pzmh", "Tracking Info", order);
    this.mapColumnIfExists(columnValues, "status__1", "Transit Status", order);
    this.mapColumnIfExists(columnValues, "email85__1", "Notification Email", order);
    
    // Extract tracking number and link from tracking info if available
    if (order["Tracking Info"] && !order["Tracking Link"]) {
      const trackingMatch = order["Tracking Info"].match(/tracking\s*-\s*(https?:\/\/[^\s]+)/i);
      if (trackingMatch) {
        order["Tracking Link"] = trackingMatch[1];
      }
    }
    
    return order;
  }

  // Helper method to safely map column values
  mapColumnIfExists(columnValues, columnId, targetField, orderObject) {
    if (columnValues[columnId] && columnValues[columnId].trim() !== "") {
      orderObject[targetField] = columnValues[columnId];
      return true;
    }
    return false;
  }

  // Improved order tracking display with better formatting
  formatOrderTracking(order) {
    if (!order) {
      return `I couldn't find any information for that order. Please check the order number or try searching by customer name.`;
    }
    
    // Get order type and identifier
    const orderType = order["Order Type"] || "Order";
    const orderNumber = order["Reference Number"] || order["Order Number"] || "N/A";
    
    // Build source indicator (Monday.com or local)
    const sourceIndicator = order["Source"] === "Monday.com" 
      ? `<span class="bg-gray-100 text-gray-800 text-xs font-medium mr-2 px-2 py-0.5 rounded">Monday.com</span>`
      : '';
    
    // Start building the HTML output
    let html = `
      <div class="order-tracking bg-gray-50 p-3 rounded border border-gray-200">
        <h3 class="font-bold text-lg mb-2 flex items-center">
          ${orderType} #${orderNumber} ${sourceIndicator}
        </h3>
    `;

    // Different layouts for different order types
    if (orderType === "PO") {
      html += this.formatPODetails(order);
    } else {
      // SO or SH formatting
      html += this.formatSOSHDetails(order);
    }
    
    // Only add one Monday.com link at the bottom
    if (order["Monday.com Link"]) {
      html += `
        <div class="mt-2 pt-2 border-t border-gray-200 text-xs text-gray-600">
          <a href="${order["Monday.com Link"]}" target="_blank" class="flex items-center">
            <svg class="w-4 h-4 mr-1" viewBox="0 0 24 24" fill="currentColor">
              <path d="M2.6,6.09c0-2.339,1.891-4.23,4.23-4.23s4.23,1.891,4.23,4.23s-1.891,4.23-4.23,4.23 S2.6,8.429,2.6,6.09 M12.729,6.09c0-2.339,1.891-4.23,4.23-4.23s4.23,1.891,4.23,4.23s-1.891,4.23-4.23,4.23S12.729,8.429,12.729,6.09 M12.729,16.219c0-2.339,1.891-4.23,4.23-4.23s4.23,1.891,4.23,4.23s-1.891,4.23-4.23,4.23S12.729,18.558,12.729,16.219 M2.6,16.219 c0-2.339,1.891-4.23,4.23-4.23s4.23,1.891,4.23,4.23s-1.891,4.23-4.23,4.23S2.6,18.558,2.6,16.219"/>
            </svg>
            View in Monday.com
          </a>
        </div>
      `;
    }
    
    // Close the container
    html += `</div>`;
    
    return html;
  }

  // Helper function for formatting PO details
  formatPODetails(order) {
    let html = `<div class="grid grid-cols-2 gap-2 text-sm">`;
    
    // Main information section
    const mainFields = [
      ["Vendor Name", "Vendor"],
      ["Status", "Status"],
      ["Date Received", "Date"],
      ["ETA Date", "ETA"],
      ["Receiver", "Receiver"],
      ["Country", "Country"]
    ];
    
    // Add main fields
    mainFields.forEach(([key, label]) => {
      if (order[key]) {
        html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
      }
    });
    
    // Shipping information
    const shippingFields = [
      ["Shipment Method", "Ship Method"],
      ["Carrier", "Carrier"],
      ["Freight Cost", "Freight Cost"]
    ];
    
    if (shippingFields.some(([key]) => order[key])) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Shipping Information:</div>
        <div class="grid grid-cols-2 gap-2 pl-3">`;
        
      shippingFields.forEach(([key, label]) => {
        if (order[key]) {
          html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
        }
      });
      
      html += `</div></div>`;
    }
    
    // Package details
    const packageFields = [
      ["Package Qty", "Quantity"],
      ["Package Type", "Type"],
      ["Dimensions", "Dimensions"],
      ["Weight", "Weight"]
    ];
    
    if (packageFields.some(([key]) => order[key])) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Package Details:</div>
        <div class="grid grid-cols-2 gap-2 pl-3">`;
        
      packageFields.forEach(([key, label]) => {
        if (order[key]) {
          html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
        }
      });
      
      html += `</div></div>`;
    }
    
    // Tracking information
    if (order["Tracking No"] || order["Tracking Link"] || order["Tracking Info"]) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Tracking Information:</div>
        <div class="pl-3">`;
        
      if (order["Tracking No"]) {
        html += `<div><span class="font-semibold">Tracking Number:</span> 
          ${order["Tracking Link"] ? 
            `<a href="${order["Tracking Link"]}" target="_blank" class="text-blue-600 underline">${order["Tracking No"]}</a>` : 
            order["Tracking No"]}
        </div>`;
      } else if (order["Tracking Info"]) {
        html += `<div><span class="font-semibold">Tracking:</span> ${order["Tracking Info"]}</div>`;
      }
      
      html += `</div></div>`;
    }
    
    // Notes & Description
    if (order["Notes"] || order["Description"]) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">`;
      
      if (order["Notes"]) {
        html += `<div><span class="font-semibold">Notes:</span> ${order["Notes"]}</div>`;
      }
      
      if (order["Description"]) {
        html += `<div class="mt-1"><span class="font-semibold">Description:</span> ${order["Description"]}</div>`;
      }
      
      html += `</div>`;
    }
    
    // Close the grid
    html += `</div>`;
    
    return html;
  }

  // Helper function for formatting SO/SH details
  formatSOSHDetails(order) {
    let html = `<div class="grid grid-cols-2 gap-2 text-sm">`;
    
    // Main information section
    const mainFields = [
      ["Customer Name", "Customer"],
      ["Status", "Status"],
      ["Shipment Status", "Ship Status"],
      ["Transit Status", "Transit Status"],
      ["Ship Date", "Ship Date"],
      ["Country", "Country"],
      ["Currency", "Currency"],
      ["Payment Terms", "Payment Terms"]
    ];
    
    // Add main fields
    mainFields.forEach(([key, label]) => {
      if (order[key]) {
        html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
      }
    });
    
    // Shipping information
    const shippingFields = [
      ["Ship Via", "Ship Via"],
      ["Carrier", "Carrier"],
      ["Freight Terms", "Freight Terms"],
      ["Freight Cost", "Freight Cost"]
    ];
    
    if (shippingFields.some(([key]) => order[key])) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Shipping Information:</div>
        <div class="grid grid-cols-2 gap-2 pl-3">`;
        
      shippingFields.forEach(([key, label]) => {
        if (order[key]) {
          html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
        }
      });
      
      html += `</div></div>`;
    }
    
    // Package details
    const packageFields = [
      ["Package Qty", "Quantity"],
      ["Package Type", "Type"],
      ["Dimensions", "Dimensions"],
      ["Weight", "Weight"]
    ];
    
    if (packageFields.some(([key]) => order[key])) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Package Details:</div>
        <div class="grid grid-cols-2 gap-2 pl-3">`;
        
      packageFields.forEach(([key, label]) => {
        if (order[key]) {
          html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
        }
      });
      
      html += `</div></div>`;
    }
    
    // References
    const referenceFields = [
      ["XCF Reference", "XCF Ref"],
      ["Extra Info", "Additional Info"]
    ];
    
    if (referenceFields.some(([key]) => order[key])) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">References:</div>
        <div class="pl-3">`;
        
      referenceFields.forEach(([key, label]) => {
        if (order[key]) {
          html += `<div><span class="font-semibold">${label}:</span> ${order[key]}</div>`;
        }
      });
      
      html += `</div></div>`;
    }
    
    // Tracking information
    if (order["Tracking Info"] || order["Tracking Link"]) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div class="font-semibold mb-1">Tracking Information:</div>
        <div class="pl-3">`;
        
      if (order["Tracking Info"]) {
        const trackingInfo = order["Tracking Info"];
        const trackingLinkMatch = trackingInfo.match(/(https?:\/\/[^\s]+)/);
        
        if (trackingLinkMatch) {
          const linkText = trackingInfo.replace(trackingLinkMatch[0], '').replace('Tracking -', '').trim();
          html += `<div><span class="font-semibold">Tracking:</span> ${linkText} 
            <a href="${trackingLinkMatch[0]}" target="_blank" class="text-blue-600 underline">View Tracking</a>
          </div>`;
        } else {
          html += `<div><span class="font-semibold">Tracking:</span> ${trackingInfo}</div>`;
        }
      } else if (order["Tracking Link"]) {
        html += `<div><span class="font-semibold">Tracking:</span> 
          <a href="${order["Tracking Link"]}" target="_blank" class="text-blue-600 underline">View Tracking</a>
        </div>`;
      }
      
      html += `</div></div>`;
    }
    
    // Personnel
    if (order["Personnel"]) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">
        <div><span class="font-semibold">Personnel:</span> ${order["Personnel"]}</div>
      </div>`;
    }
    
    // Comments & Description
    if (order["Comments"] || order["Description"]) {
      html += `<div class="col-span-2 mt-2 pt-2 border-t border-gray-200">`;
      
      if (order["Comments"]) {
        html += `<div><span class="font-semibold">Comments:</span> ${order["Comments"]}</div>`;
      }
      
      if (order["Description"]) {
        html += `<div class="mt-1"><span class="font-semibold">Description:</span> ${order["Description"]}</div>`;
      }
      
      html += `</div>`;
    }
    
    // Close the grid
    html += `</div>`;
    
    return html;
  }
}