// Import necessary components from their respective folders
import { DashboardComponent } from "./dashboard/dashboard.js";
import { AnalyticsComponent } from "./analytics/analytics.js";
import { MasterPartsComponent } from "./masterparts/master-parts.js";
import { AssistantComponent } from "./assistant/assistant.js";
import { MapComponent } from "./map/map.js";
import { MainSettingsComponent, toggleDarkMode, loadDarkModePreference } from "./core/settings.js";
import { AdminDashboardComponent } from "./admin/admin-dashboard.js";
import { NotificationSystem } from "./core/notifications.js";
import { SearchSystem } from "./core/search.js";
import { connectionUI } from "./core/connection-ui.js";


// Declare chrome variable if it's not defined globally
const chrome = window.chrome || {}

document.addEventListener("DOMContentLoaded", () => {
  // Get DOM elements
  const userAvatar = document.getElementById("userAvatar")
  const userName = document.getElementById("userName")
  const userInfo = document.getElementById("userInfo")
  const logoutButton = document.getElementById("logoutButton")
  const notificationButton = document.getElementById("notificationButton")
  const searchInput = document.querySelector(".search-input")
  const searchButton = document.querySelector(".search-button")
  const recentActivity = document.getElementById("recentActivity")
  const notificationsList = document.getElementById("notificationsList")
  const tabButtons = document.querySelectorAll(".tab-button")
  const navItems = document.querySelectorAll(".nav-item")
  const actionButtons = document.querySelectorAll(".action-button")
  const dynamicContent = document.getElementById("dynamicContent")
  let currentComponent = null
  
  // Initialize notification and search systems
  const notificationSystem = new NotificationSystem()
  const searchSystem = new SearchSystem()
  
  // Register all components
  const components = {
    dashboard: DashboardComponent,
    analytics: AnalyticsComponent,
    partmaster: MasterPartsComponent,
    assistant: AssistantComponent,
    projectmap: MapComponent,
    settings: MainSettingsComponent,
    admindashboard: AdminDashboardComponent
  }

  // Store the current user role
  let currentUserRole = null;

  // Load user data
  loadUserData()
  
  // Initialize notification system
  initializeNotificationSystem()
  
  // Initialize search system
  initializeSearchSystem()
  
  // Initialize connection system
  initializeConnectionSystem()

  // Section 2: Event Handlers
  logoutButton.addEventListener("click", handleLogout)
  notificationButton.addEventListener("click", handleNotifications)

  // Tab switching
  tabButtons.forEach((button) => {
    button.addEventListener("click", () => {
      tabButtons.forEach((btn) => btn.classList.remove("active"))
      button.classList.add("active")
      // TODO: Implement tab content switching
    })
  })

  // Navigation items
  navItems.forEach((item) => {
    item.addEventListener("click", function (event) {
      event.preventDefault()
      const componentName = this.getAttribute("data-component")
      
      // Check if this is the admin dashboard and handle access control
      if (componentName === "admindashboard") {
        if (currentUserRole !== "Admin") {
          // Show access denied notification for non-admin users
          notificationSystem.addNotification("Access Denied: Only administrators can access this section.", "error")
          return
        }
      }
      
      if (componentName && this.id !== "collapseSidebar") {
        switchContent(componentName)
        navItems.forEach((i) => i.classList.remove("active"))
        this.classList.add("active")

        // Add and remove 'flash' class for animation
        this.classList.add("flash")
        setTimeout(() => {
          this.classList.remove("flash")
        }, 300)
      }
    })
  })

  // Action buttons
  actionButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      const action = e.currentTarget.textContent.trim()
      handleAction(action)
    })
  })
  
  // Initialize the notification system
  async function initializeNotificationSystem() {
    try {
      await notificationSystem.init()
      console.log("Notification system initialized")
    } catch (error) {
      console.error("Error initializing notification system:", error)
    }
  }
  
  // Initialize the search system
  async function initializeSearchSystem() {
    try {
      await searchSystem.init()
      console.log("Search system initialized")
      
      // Add event listeners for search input
      if (searchInput) {
        searchInput.addEventListener('input', handleSearchInput)
        searchInput.addEventListener('focus', () => {
          if (searchInput.value.trim()) {
            performSearch()
          }
        })
        searchInput.addEventListener('keydown', (e) => {
          if (e.key === 'Enter') {
            performSearch()
          }
        })
      }
      
      if (searchButton) {
        searchButton.addEventListener('click', () => {
          if (searchInput.value.trim()) {
            performSearch()
          }
        })
      }
    } catch (error) {
      console.error("Error initializing search system:", error)
    }
  }
  
  // Initialize the connection system
  async function initializeConnectionSystem() {
    try {
      await connectionUI.init()
      console.log("Connection system initialized")
    } catch (error) {
      console.error("Error initializing connection system:", error)
      notificationSystem.addNotification("Failed to initialize connection system. Some features may not work properly.", "error")
    }
  }
  
  // Handle search input
  function handleSearchInput(e) {
    const term = e.target.value.trim()
    
    // Update search term in search system
    searchSystem.setSearchTerm(term)
    
    // If empty, close results
    if (!term) {
      searchSystem.closeSearchResults(document.body)
      return
    }
    
    // Debounce search to avoid too many searches while typing
    if (searchSystem.searchTimeout) {
      clearTimeout(searchSystem.searchTimeout)
    }
    
    searchSystem.searchTimeout = setTimeout(() => {
      performSearch()
    }, 300)
  }
  
  // Perform search and show results
  async function performSearch() {
    try {
      if (!searchInput.value.trim()) return
      
      // Show loading indicator
      searchButton.innerHTML = `
        <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      `
      
      // Perform search
      await searchSystem.performSearch()
      
      // Reset search button
      searchButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
      `
      
      // Display results
      searchSystem.displaySearchResults(document.body)
    } catch (error) {
      console.error("Error performing search:", error)
      
      // Reset search button on error
      searchButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
      `
      
      // Show error notification
      notificationSystem.addNotification("Search failed: " + error.message, "error")
    }
  }
  
  // Handle notifications button click
  function handleNotifications() {
    notificationSystem.showNotificationPopup(document.body)
  }
  
  // Section 3: User Interface Functions
  function loadUserData() {
    // Check if chrome is defined
    let chromeStorageAvailable = false
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chromeStorageAvailable = true
    }

    if (chromeStorageAvailable) {
      chrome.storage.local.get(["user"], (result) => {
        if (result.user) {
          userName.textContent = result.user["User Name"]
          userAvatar.src = `images/avatars/avatar_${result.user.Avatar || "default"}.jpg`
          
          // Store user role
          currentUserRole = result.user.Role
          
          // Hide admin dashboard for non-admin users
          if (currentUserRole !== "Admin") {
            const adminDashboardItem = document.querySelector('.nav-item[data-component="admindashboard"]')
            if (adminDashboardItem) {
              adminDashboardItem.style.display = "none"
            }
          }
          
          loadDashboardData(result.user)
        } else {
          window.location.href = "login.html"
        }
      })
    } else {
      console.warn("Chrome storage API not available. Running in a non-extension environment?")
      // Provide fallback behavior for non-extension environments, e.g., using localStorage
      let user = localStorage.getItem("user")
      if (user) {
        user = JSON.parse(user)
        userName.textContent = user["User Name"]
        userAvatar.src = `images/avatars/avatar_${user.Avatar || "default"}.jpg`
        
        // Store user role
        currentUserRole = user.Role
        
        // Hide admin dashboard for non-admin users
        if (currentUserRole !== "Admin") {
          const adminDashboardItem = document.querySelector('.nav-item[data-component="admindashboard"]')
          if (adminDashboardItem) {
            adminDashboardItem.style.display = "none"
          }
        }
        
        loadDashboardData(user)
      } else {
        // For demo purposes, create a temporary user instead of redirecting
        const demoUser = {
          "User Name": "Demo User",
          "Avatar": "default",
          "Role": "User" // Default to regular user
        }
        userName.textContent = demoUser["User Name"]
        userAvatar.src = `images/avatars/avatar_${demoUser.Avatar || "default"}.jpg`
        localStorage.setItem("user", JSON.stringify(demoUser))
        
        // Store user role
        currentUserRole = demoUser.Role
        
        // Hide admin dashboard for non-admin users
        if (currentUserRole !== "Admin") {
          const adminDashboardItem = document.querySelector('.nav-item[data-component="admindashboard"]')
          if (adminDashboardItem) {
            adminDashboardItem.style.display = "none"
          }
        }
        
        loadDashboardData(demoUser)
      }
    }
  }

  function handleLogout() {
    let chromeStorageAvailable = false
    if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
      chromeStorageAvailable = true
    }

    if (chromeStorageAvailable) {
      chrome.storage.local.remove(["loggedIn", "user"], () => {
        chrome.action.setPopup({ popup: "login.html" }, () => {
          window.location.href = "login.html"
        })
      })
    } else {
      // Fallback for non-extension environments
      localStorage.removeItem("loggedIn")
      localStorage.removeItem("user")
      window.location.href = "login.html"
    }
  }

  function handleAction(action) {
    switch (action) {
      case "Fetch Data":
        // TODO: Implement fetch data functionality
        console.log("Fetching data...")
        break
      case "View Data":
        // TODO: Implement view data functionality
        console.log("Viewing data...")
        break
      case "Export Data":
        // TODO: Implement export data functionality
        console.log("Exporting data...")
        break
      case "Clear History":
        // TODO: Implement clear history functionality
        console.log("Clearing history...")
        break
    }
  }

  function loadDashboardData(user) {
    // Simulated recent activity data
    const activities = [
      "Completed task: Update client database",
      "Scheduled meeting with Team A",
      "Generated monthly report",
    ]

    // Simulated notifications
    const notifications = [
      "New message from John Doe",
      "Task deadline approaching: Project X",
      "System update scheduled for tomorrow",
    ]

    // Populate recent activity
    const recentActivity = document.getElementById("recentActivity")
    if (recentActivity) {
      recentActivity.innerHTML = "" // Clear existing content
      activities.forEach((activity) => {
        const li = document.createElement("li")
        li.textContent = activity
        recentActivity.appendChild(li)
      })
    }

    // Populate notifications
    const notificationsList = document.getElementById("notificationsList")
    if (notificationsList) {
      notificationsList.innerHTML = "" // Clear existing content
      notifications.forEach((notification) => {
        const li = document.createElement("li")
        li.textContent = notification
        notificationsList.appendChild(li)
      })
    }
  }

  // Section 4: Dark Mode Functionality
  const darkModeButton = document.getElementById("darkModeButton")
  darkModeButton.addEventListener("click", toggleDarkMode)

  // Load dark mode preference
  loadDarkModePreference()

  // Section 5: Dark Mode Persistence
  let chromeStorageAvailable = false
  if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
    chromeStorageAvailable = true
  }

  //This section is already handled by loadDarkModePreference in settings.js

  // Sidebar Toggle Functionality
  const sidebar = document.getElementById("sidebar")
  const collapseSidebarButton = document.getElementById("collapseSidebar")
  const dashboardContainer = document.querySelector(".dashboard-container")

  collapseSidebarButton.addEventListener("click", (e) => {
    e.stopPropagation() // Prevent the click from triggering the parent nav-item click event
    sidebar.classList.toggle("collapsed")
    dashboardContainer.classList.toggle("sidebar-collapsed")

    // Add animation class
    collapseSidebarButton.classList.add("animating")
    setTimeout(() => {
      collapseSidebarButton.classList.remove("animating")
    }, 300) // Match this with the transition duration

    // Save sidebar state
    const isCollapsed = sidebar.classList.contains("collapsed")
    if (chromeStorageAvailable) {
      chrome.storage.local.set({ sidebarCollapsed: isCollapsed })
    } else {
      localStorage.setItem("sidebarCollapsed", isCollapsed)
    }
  })

  // Load sidebar state
  function loadSidebarState() {
    if (chromeStorageAvailable) {
      chrome.storage.local.get(["sidebarCollapsed"], (result) => {
        if (result.sidebarCollapsed) {
          sidebar.classList.add("collapsed")
          dashboardContainer.classList.add("sidebar-collapsed")
        }
      })
    } else {
      const isCollapsed = localStorage.getItem("sidebarCollapsed") === "true"
      if (isCollapsed) {
        sidebar.classList.add("collapsed")
        dashboardContainer.classList.add("sidebar-collapsed")
      }
    }
  }

  function showLoading() {
    dynamicContent.innerHTML =
      '<div class="flex justify-center items-center h-full"><div class="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div></div>'
  }

  function showError(message) {
    dynamicContent.innerHTML = `<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline">${message}</span>
    </div>`
  }

  function switchContent(componentName) {
    showLoading()

    setTimeout(() => {
      try {
        if (components[componentName]) {
          if (currentComponent) {
            // Clean up previous component if needed
          }
          currentComponent = new components[componentName](dynamicContent)
          
          // Check if the component has an init method and call it
          if (typeof currentComponent.init === 'function') {
            currentComponent.init()
          } else if (typeof currentComponent.render === 'function') {
            // Fallback to render if init doesn't exist
            currentComponent.render()
          } else {
            throw new Error(`Component ${componentName} has no init or render method`)
          }
        } else {
          throw new Error(`Component ${componentName} not found`)
        }
      } catch (error) {
        console.error("Error rendering component:", error)
        showError(`Failed to load ${componentName} content. ${error.message}`)
      }
    }, 300) // Reduced delay for better UX
  }
  
  // Handle navigation events from search results
  document.addEventListener('navigateToPart', (e) => {
    const partId = e.detail.partId
    if (partId) {
      // Switch to part master component
      switchContent('partmaster')
      
      // Wait for component to load, then show part details
      setTimeout(() => {
        if (currentComponent && typeof currentComponent.showPartDetails === 'function') {
          currentComponent.showPartDetails(partId)
        }
      }, 500)
    }
  })
  
  document.addEventListener('navigateToHistory', (e) => {
    const historyIndex = e.detail.historyIndex
    if (historyIndex !== undefined) {
      // Switch to analytics component
      switchContent('analytics')
      
      // Wait for component to load, then show history item
      setTimeout(() => {
        if (currentComponent && typeof currentComponent.showHistoryItem === 'function') {
          currentComponent.showHistoryItem(historyIndex)
        }
      }, 500)
    }
  })

  // Set default view
  switchContent("dashboard")

  loadSidebarState()
})