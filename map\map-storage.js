// Storage utility for map component

export class MapStorage {
  constructor() {
    this.storageKey = 'enventbridge_project_locations';
    this.sampleData = [
      {
        id: 1,
        projectName: "Downtown Energy Center",
        customerName: "City Utilities Inc.",
        latitude: 40.7128,
        longitude: -74.0060,
        gasType: "Natural Gas",
        installDate: "2023-05-15",
        photoUrl: "https://images.unsplash.com/photo-1544982590-dd661f77d66c?q=80&w=500",
        notes: "Major installation serving downtown district."
      },
      {
        id: 2,
        projectName: "West Hills Residence",
        customerName: "West Hills HOA",
        latitude: 34.0522,
        longitude: -118.2437,
        gasType: "LPG",
        installDate: "2023-07-22",
        photoUrl: "https://images.unsplash.com/photo-1568605114967-8130f3a36994?q=80&w=500",
        notes: "Residential community system with 24 units."
      },
      {
        id: 3,
        projectName: "North Industrial Complex",
        customerName: "Midwest Manufacturing Co.",
        latitude: 41.8781,
        longitude: -87.6298,
        gasType: "CNG",
        installDate: "2023-02-10",
        photoUrl: "https://images.unsplash.com/photo-1586528116493-a029325540fa?q=80&w=500",
        notes: "Industrial application with high-volume requirements."
      }
    ];
  }

  // Load project data from storage or use sample data if not available
  loadProjects() {
    try {
      console.log("Loading project data from storage");
      
      // Check if chrome extension storage is available
      if (typeof chrome !== "undefined" && chrome.storage) {
        console.log("Using Chrome storage API");
        return new Promise((resolve) => {
          chrome.storage.local.get([this.storageKey], (result) => {
            if (result[this.storageKey]) {
              try {
                const data = JSON.parse(result[this.storageKey]);
                console.log(`Loaded ${data.length} projects from Chrome storage`);
                resolve(data);
              } catch (parseError) {
                console.error("Error parsing project data from Chrome storage:", parseError);
                // Initialize with sample data if parsing fails
                this.saveProjects(this.sampleData);
                resolve(this.sampleData);
              }
            } else {
              console.log("No data in Chrome storage, using sample data");
              // Initialize with sample data
              this.saveProjects(this.sampleData);
              resolve(this.sampleData);
            }
          });
        });
      } else {
        console.log("Using localStorage API");
        // Use localStorage for non-extension environments
        const data = localStorage.getItem(this.storageKey);
        if (data) {
          try {
            const parsedData = JSON.parse(data);
            console.log(`Loaded ${parsedData.length} projects from localStorage`);
            return Promise.resolve(parsedData);
          } catch (parseError) {
            console.error("Error parsing project data from localStorage:", parseError);
            // Initialize with sample data if parsing fails
            this.saveProjects(this.sampleData);
            return Promise.resolve(this.sampleData);
          }
        } else {
          console.log("No data in localStorage, using sample data");
          // Initialize with sample data
          this.saveProjects(this.sampleData);
          return Promise.resolve(this.sampleData);
        }
      }
    } catch (error) {
      console.error("Error loading project data:", error);
      return Promise.resolve(this.sampleData);
    }
  }

  // Save project data to storage
  saveProjects(projects) {
    try {
      console.log(`Saving ${projects.length} projects to storage`);
      
      // Check if chrome extension storage is available
      if (typeof chrome !== "undefined" && chrome.storage) {
        console.log("Using Chrome storage API for saving");
        return new Promise((resolve) => {
          chrome.storage.local.set({ [this.storageKey]: JSON.stringify(projects) }, () => {
            if (chrome.runtime.lastError) {
              console.error("Chrome storage error:", chrome.runtime.lastError);
            } else {
              console.log("Projects saved to Chrome storage successfully");
            }
            resolve(true);
          });
        });
      } else {
        console.log("Using localStorage API for saving");
        // Use localStorage for non-extension environments
        localStorage.setItem(this.storageKey, JSON.stringify(projects));
        console.log("Projects saved to localStorage successfully");
        
        // Also save with the enventbridge prefix for fullscreen view
        localStorage.setItem('enventbridge_projectData', JSON.stringify(projects));
        console.log("Projects also saved to enventbridge_projectData for fullscreen view");
        
        return Promise.resolve(true);
      }
    } catch (error) {
      console.error("Error saving project data:", error);
      return Promise.resolve(false);
    }
  }

  // Add a new project and save to storage
  addProject(project) {
    return this.loadProjects().then(projects => {
      // Generate new ID
      const newId = projects.length > 0 
        ? Math.max(...projects.map(p => p.id)) + 1 
        : 1;
      
      // Add new project with ID
      const newProject = { ...project, id: newId };
      const updatedProjects = [...projects, newProject];
      
      // Save and return updated list
      return this.saveProjects(updatedProjects)
        .then(() => updatedProjects);
    });
  }

  // Update an existing project
  updateProject(updatedProject) {
    return this.loadProjects().then(projects => {
      const index = projects.findIndex(p => p.id === updatedProject.id);
      if (index === -1) {
        throw new Error(`Project with ID ${updatedProject.id} not found`);
      }
      
      // Update project at index
      projects[index] = updatedProject;
      
      // Save and return updated list
      return this.saveProjects(projects)
        .then(() => projects);
    });
  }

  // Delete a project by ID
  deleteProject(projectId) {
    return this.loadProjects().then(projects => {
      const updatedProjects = projects.filter(p => p.id !== projectId);
      
      // Save and return updated list
      return this.saveProjects(updatedProjects)
        .then(() => updatedProjects);
    });
  }
} 