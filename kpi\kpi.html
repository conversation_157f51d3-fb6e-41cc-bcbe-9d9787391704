<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Envent Bridge KPI Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  
  <!-- Import external libraries -->
  <script src="../kpi/library/motion.js"></script>
  <script src="../kpi/library/echarts.js"></script>
  <script src="../kpi/library/apexcharts.min.js"></script>
  <script src="../kpi/library/gridjs.umd.js"></script>
  <script src="../kpi/library/xlsx.full.min.js"></script>
  <script src="../kpi/library/jszip.min.js"></script>
  <script src="../kpi/library/intro.min.js"></script>
  
  <style>
      /* Base styles */
      body, html {
          width: 100%;
          height: 100%;
          margin: 0;
          padding: 0;
          overflow: hidden;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      }

      /* Dark mode styles */
      .dark {
          background-color: #1a1a1a;
          color: #ffffff;
      }

      .dark .sidebar {
          background-color: #2d2d2d;
          border-color: #3d3d2d;
      }

      .dark .header {
          background-color: #2d2d2d;
          border-color: #3d3d2d;
      }

      .dark .content-area {
          background-color: #1a1a1a;
      }

      .dark .tab-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      .dark .tab-button.active {
          background-color: #3d3d3d;
      }

      .dark .action-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      /* Layout components */
      .dashboard-container {
          display: grid;
          grid-template-columns: auto 1fr;
          grid-template-rows: 60px 1fr;
          height: 100vh;
          width: 100vw;
          background-color: #f5f5f5;
      }

      /* Header styles - START */
      .header {
          grid-column: 1 / -1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 1rem;
          background-color: #ffffff;
          border-bottom: 1px solid #e5e5e5;
          z-index: 10;
      }

      .logo {
          flex: 0 0 auto;
      }

      .search-bar {
          position: relative;
          width: 200px;
          height: 32px;
          margin-right: 0.5rem;
      }

      .search-input {
          width: 100%;
          height: 100%;
          padding: 0 32px 0 12px;
          border: 1px solid #e5e5e5;
          border-radius: 4px;
          font-size: 0.875rem;
          transition: all 0.3s ease;
      }

      .search-input:focus {
          outline: none;
          border-color: #0066ff;
          box-shadow: 0 0 0 2px rgba(0, 102, 255, 0.2);
      }

      .search-button {
          position: absolute;
          right: 8px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
      }

      .search-button:hover {
          color: #0066ff;
      }

      .header-actions {
          display: flex;
          align-items: center;
          gap: 0.5rem;
      }

      .user-profile {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          background-color: #f5f5f5;
      }

      .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
      }

      .icon-button {
          padding: 0.5rem;
          border: none;
          border-radius: 4px;
          background: none;
          cursor: pointer;
      }
      /* Header styles - END */

      /* Sidebar styles - START */
      .sidebar {
          grid-row: 2 / -1;
          background-color: #ffffff;
          border-right: 1px solid #e5e5e5;
          padding: 1rem;
          transition: width 0.3s ease;
          width: 200px;
          overflow-x: hidden;
          display: flex;
          flex-direction: column;
          z-index: 10;
      }

      .sidebar.collapsed {
          width: 60px;
          overflow: visible;
          padding: 1rem 0.5rem;
      }

      .sidebar.collapsed .nav-item {
          justify-content: center;
      }

      .sidebar.collapsed .nav-item span {
          display: none;
      }

      .sidebar.collapsed .nav-item svg {
          margin: 0;
      }

      .nav-item {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 0.75rem;
          padding: 0.75rem;
          margin-bottom: 0.5rem;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s, color 0.2s;
          font-weight: 600;
          font-size: 0.813rem;
          color: #737791;
          white-space: nowrap;
      }

      .nav-item svg {
          width: 20px;
          height: 20px;
          stroke: currentColor;
          stroke-width: 1.5;
          stroke-linecap: round;
          stroke-linejoin: round;
          fill: none;
          transition: transform 0.3s ease;
      }

      .sidebar.collapsed .nav-item#collapseSidebar svg {
          transform: rotate(180deg);
      }

      .nav-item.flash {
          animation: flash 0.3s;
      }

      @keyframes flash {
        0% { background-color: #0066ff; color: white; }
        100% { background-color: #E8EFFF; color: #0066ff; }
      }

      /* Sidebar styles - END */

      /* Main content styles - START */
      .content-area {
          grid-row: 2 / -1;
          position: relative;
          overflow-y: auto;
          transition: all 0.3s ease;
          height: 100%;
          width: 100%;
          padding: 0;
          margin: 0;
          box-sizing: border-box;
      }

      .content-area.ai-style-change-2 {
          position: relative;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: block;
      }

      /* Removed margin-left styles for content-area */

      .tabs {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 1rem;
      }

      .tab-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          background-color: #f5f5f5;
          cursor: pointer;
          transition: background-color 0.2s;
      }

      .tab-button.active {
          background-color: #e5e5e5;
      }

      .action-buttons {
          display: flex;
          gap: 0.5rem;
          margin-bottom: 1rem;
      }

      .action-button {
          padding: 0.5rem 1rem;
          border: none;
          border-radius: 4px;
          background-color: #f5f5f5;
          cursor: pointer;
          transition: background-color 0.2s;
          font-size: 0.875rem;
          font-weight: 500;
          color: #333;
      }

      .action-button:hover {
          background-color: #e0e0e0;
      }

      /* Main content styles - END */

      /* Utility classes */
      .icon-button {
          width: 27px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none;
          cursor: pointer;
          transition: background-color 0.2s, color 0.2s;
      }

      .notification-button {
          background-color: #0066ff;
          color: white;
      }
      
      .connection-button {
          background-color: #0078d7;
          color: white;
      }
      
      .connection-button.connected {
          background-color: #10b981;
          position: relative;
      }
      
      .connection-button .connection-count {
          position: absolute;
          top: -5px;
          right: -5px;
          background-color: #ef4444;
          color: white;
          border-radius: 50%;
          width: 18px;
          height: 18px;
          font-size: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      .dark-mode-button {
          background-color: #333;
          color: white;
      }

      .logout-button {
          background-color: #ff3333;
          color: white;
      }

      .icon-button:hover {
          opacity: 0.8;
      }

      /* Dark mode styles */
      body.dark-mode {
          background-color: #1a1a1a;
          color: #ffffff;
      }

      body.dark-mode .dashboard-container {
          background-color: #1a1a1a;
      }

      body.dark-mode .header {
          background-color: #2d2d2d;
          border-color: #3d3d3d;
      }

      body.dark-mode .sidebar {
          background-color: #2d2d2d;
          border-color: #3d3d3d;
      }

      body.dark-mode .content-area {
          background-color: #1a1a1a;
      }

      body.dark-mode .tab-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .tab-button.active {
          background-color: #3d3d3d;
      }

      body.dark-mode .action-button {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .preview-table {
          background-color: #2d2d2d;
          color: #ffffff;
      }

      body.dark-mode .icon-button {
          color: #ffffff;
      }

      body.dark-mode .search-input {
          background-color: #2d2d2d;
          color: #ffffff;
          border-color: #3d3d3d;
      }

      body.dark-mode .search-button {
          color: #a0aec0;
      }

      body.dark-mode .search-button:hover {
          color: #0066ff;
      }

      body.dark-mode .user-profile {
          background-color: #3d3d3d;
      }

      .toggle-sidebar {
          position: absolute;
          top: 1rem;
          right: -12px;
          background-color: #ffffff;
          border: 1px solid #e5e5e5;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          transition: transform 0.3s ease;
      }

      .sidebar.collapsed .toggle-sidebar {
          transform: rotate(180deg);
      }

      .icon-button i {
          font-size: 13px;
      }

      .nav-item svg, .search-button svg {
          width: 16px;
          height: 16px;
          stroke: currentColor;
          stroke-width: 1.5;
          stroke-linecap: round;
          stroke-linejoin: round;
          fill: none;
          transition: transform 0.3s ease;
      }

      .nav-item-spacer {
        flex-grow: 1;
      }

      .sidebar.collapsed #collapseSidebar svg {
        transform: rotate(180deg);
      }

      /* Styles for active nav items */
      .nav-item.active {
          background-color: #E8EFFF;
          color: #0066ff;
      }

      .nav-item.active svg {
          stroke: #0066ff;
      }

      #collapseSidebar svg {
        transition: transform 0.3s ease;
      }

      #dynamicContent {
          position: absolute;
          top: 18px;
          left: 18px;
          right: 18px;
          bottom: 0;
          display: block;
          margin: 0;
          padding: 0;
          box-sizing: border-box;
          overflow-y: auto;
      }

      /* KPI specific styles */
      .kpi-card {
          background-color: #ffffff;
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          padding: 1rem;
          margin-bottom: 1rem;
          transition: transform 0.2s, box-shadow 0.2s;
          position: relative;
      }

      .kpi-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      .dark .kpi-card {
          background-color: #2d2d2d;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .kpi-value {
          font-size: 1.5rem;
          font-weight: bold;
          margin: 0.5rem 0;
      }

      .kpi-chart {
          width: 100%;
          height: 200px;
      }

      .kpi-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
          gap: 1rem;
          margin-bottom: 1rem;
          min-height: 400px;
      }

      /* Add Card specific styles */
      .add-card {
          border: 2px dashed #e2e8f0;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          min-height: 130px;
          transition: all 0.3s ease;
      }

      .add-card:hover {
          border-color: #0066ff;
          background-color: #f0f7ff;
      }

      .dark .add-card {
          border-color: #4b5563;
      }

      .dark .add-card:hover {
          border-color: #60a5fa;
          background-color: #1e3a5f;
      }

      /* Add Card image animation */
      .add-card img {
          transition: transform 0.3s ease;
      }

      .add-card:hover img {
          transform: scale(1.05);
      }

      /* KPI component loading indicator */
      .component-loading {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
          flex-direction: column;
      }

      .loading-spinner {
          border: 4px solid rgba(0, 0, 0, 0.1);
          border-left-color: #0066ff;
          border-radius: 50%;
          width: 40px;
          height: 40px;
          animation: spin 1s linear infinite;
          margin-bottom: 1rem;
      }

      @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
      }

      .dark .loading-spinner {
          border-color: rgba(255, 255, 255, 0.1);
          border-left-color: #0066ff;
      }
      
      /* Draggable card styles */
      .drag-handle {
          position: absolute;
          top: 0.5rem;
          right: 0.5rem;
          width: 24px;
          height: 24px;
          background-color: rgba(0, 0, 0, 0.05);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: grab;
          opacity: 0;
          transition: opacity 0.2s, background-color 0.2s;
          z-index: 10;
      }
      
      .kpi-card:hover .drag-handle {
          opacity: 0.8;
      }
      
      .edit-mode .kpi-card .drag-handle {
          opacity: 1;
          background-color: rgba(0, 102, 255, 0.1);
      }
      
      .dark .drag-handle {
          background-color: rgba(255, 255, 255, 0.1);
      }
      
      .dark .edit-mode .kpi-card .drag-handle {
          background-color: rgba(0, 102, 255, 0.2);
      }
      
      .drag-handle:hover {
          background-color: rgba(0, 102, 255, 0.2);
      }
      
      .resize-handle {
          position: absolute;
          bottom: 0.25rem;
          right: 0.25rem;
          width: 12px;
          height: 12px;
          border-right: 2px solid #ccc;
          border-bottom: 2px solid #ccc;
          cursor: nwse-resize;
          opacity: 0;
          transition: opacity 0.2s, border-color 0.2s;
          z-index: 10;
      }
      
      .kpi-card:hover .resize-handle {
          opacity: 0.8;
      }
      
      .edit-mode .kpi-card .resize-handle {
          opacity: 1;
          border-right-color: rgba(0, 102, 255, 0.5);
          border-bottom-color: rgba(0, 102, 255, 0.5);
      }
      
      .dragging {
          z-index: 100 !important;
          cursor: grabbing !important;
          opacity: 0.8;
          box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2) !important;
          transition: none !important;
          pointer-events: none;
      }
      
      .resizing {
          opacity: 0.85;
          box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
          outline: 2px dashed rgba(0, 102, 255, 0.5) !important;
          transition: none !important;
      }
      
      /* Card edit mode */
      .edit-mode {
          background-color: rgba(0, 102, 255, 0.05);
          border: 1px dashed rgba(0, 102, 255, 0.3);
          padding: 0.5rem;
          border-radius: 8px;
      }
      
      .edit-mode .kpi-card {
          outline: 2px solid transparent;
          cursor: move;
          transition: transform 0.1s, box-shadow 0.2s, outline 0.2s;
      }
      
      .edit-mode .kpi-card:hover {
          outline: 2px solid rgba(0, 102, 255, 0.5);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
  </style>
</head>
<body>
  <div class="dashboard-container">
      <!-- Section 1: Header -->
      <header class="header">
          <div class="logo">
              <span style="color: #0066ff; font-size: 1.5rem; font-weight: bold;">Envent Bridge KPI</span>
          </div>
          
          <div class="header-actions">
              <div class="search-bar">
                  <input type="text" class="search-input" placeholder="Search KPI metrics...">
                  <button class="search-button">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-search"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
                  </button>
              </div>
              
              <button class="icon-button notification-button" id="notificationButton" title="Notifications">
                  <i class="fas fa-bell"></i>
              </button>
              
              <button class="icon-button connection-button" id="connectionButton" title="External Connections">
                  <i class="fas fa-plug"></i>
              </button>
              
              <button class="icon-button dark-mode-button" id="darkModeButton" title="Toggle Dark Mode">
                  <i class="fas fa-moon"></i>
              </button>
              
              <button class="icon-button logout-button" id="logoutButton" title="Logout">
                  <i class="fas fa-sign-out-alt"></i>
              </button>
              
              <div class="user-profile">
                  <img id="userAvatar" class="user-avatar" src="../images/avatars/default.jpg" alt="User Avatar">
                  <span id="userName">User Name</span>
              </div>
          </div>
      </header>
      <!-- Header Section - END -->

      <!-- Section 2: Sidebar Navigation -->
      <nav class="sidebar" id="sidebar">
          <div class="nav-item active" data-component="home">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                  <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              <span>Home</span>
          </div>
          <div class="nav-item" data-component="sales">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
              </svg>
              <span>Sales</span>
          </div>
          <div class="nav-item" data-component="finance">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
              </svg>
              <span>Finance</span>
          </div>
          <div class="nav-item" data-component="purchasing">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
              </svg>
              <span>Purchasing</span>
          </div>
          <div class="nav-item" data-component="inventory">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Inventory</span>
          </div>
          <div class="nav-item" data-component="logistics">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <rect x="1" y="3" width="15" height="13"></rect>
                  <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
                  <circle cx="5.5" cy="18.5" r="2.5"></circle>
                  <circle cx="18.5" cy="18.5" r="2.5"></circle>
              </svg>
              <span>Logistics</span>
          </div>
          <div class="nav-item" data-component="projects">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span>Projects</span>
          </div>
          <div class="nav-item" data-component="mrp">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M2 21h20v-2H2v2zM4 10v9h5v-9H4zm7 0v9h5v-9h-5zm7 0v9h5v-9h-5zM10 4v3h10V4H10zM6 7V4L2 8l4 4V9h3V7H6z"></path>
              </svg>
              <span>MRP</span>
          </div>
          <div class="nav-item-spacer"></div>
          <div class="nav-item" id="toggleEditMode">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
              <span>Edit Dashboard</span>
          </div>
          <div class="nav-item" id="collapseSidebar">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M15 18l-6-6 6-6"/>
              </svg>
              <span>Collapse Menu</span>
          </div>
      </nav>
      <!-- Sidebar Navigation - END -->

      <!-- Section 3: Main Content Area -->
      <main class="content-area ai-style-change-2">
          <div id="dynamicContent">
              <!-- Content will be dynamically populated based on active navigation item -->
          </div>
      </main>
      <!-- Main Content Area - END -->
  </div>

  <!-- Section 4: Scripts -->
  <script type="module" src="../core/settings.js"></script>
  <script type="module" src="../core/notifications.js"></script>
  <script type="module" src="../core/connection-ui.js"></script>
  <script type="module" src="../core/connection.js"></script>
  <script type="module" src="kpi.js"></script>
</body>
</html> 