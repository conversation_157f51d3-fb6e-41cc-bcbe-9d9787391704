// search.js - Unified search functionality across extension

export class SearchSystem {
  constructor() {
    this.searchTerm = '';
    this.searchResults = {
      parts: [],
      history: []
    };
    this.isSearching = false;
    this.lastSearch = null;
  }

  // Initialize search system
  async init() {
    try {
      return true;
    } catch (error) {
      console.error("Error initializing search system:", error);
      return false;
    }
  }

  // Set search term
  setSearchTerm(term) {
    this.searchTerm = term;
  }

  // Get search results
  getSearchResults() {
    return this.searchResults;
  }

  // Perform search across all available data sources
  async performSearch(term = null) {
    if (term !== null) {
      this.searchTerm = term;
    }

    if (!this.searchTerm.trim()) {
      this.searchResults = { parts: [], history: [] };
      return this.searchResults;
    }

    this.isSearching = true;
    this.lastSearch = new Date();

    try {
      // Search in parallel for better performance
      const [partsResults, historyResults] = await Promise.all([
        this.searchParts(),
        this.searchHistory()
      ]);

      this.searchResults = {
        parts: partsResults,
        history: historyResults
      };

      this.isSearching = false;
      return this.searchResults;
    } catch (error) {
      console.error("Error performing search:", error);
      this.isSearching = false;
      throw error;
    }
  }

  // Search parts database
  async searchParts() {
    try {
      const searchTerm = this.searchTerm.toLowerCase();
      
      // Load parts data from Chrome storage
      let partsData = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await new Promise(resolve => {
          chrome.storage.local.get('masterparts', data => {
            resolve(data?.masterparts || []);
          });
        });
        
        partsData = result;
      } else {
        // Fallback to localStorage
        try {
          const storedData = localStorage.getItem('masterparts');
          if (storedData) {
            partsData = JSON.parse(storedData);
          }
        } catch (error) {
          console.error("Error parsing localStorage data:", error);
        }
      }
      
      if (!Array.isArray(partsData) || partsData.length === 0) {
        return [];
      }
      
      // Perform search
      const results = partsData.filter(part => {
        // Search across all part fields
        return Object.values(part).some(value => 
          value && String(value).toLowerCase().includes(searchTerm)
        );
      });
      
      // Limit results for performance
      return results.slice(0, 20);
    } catch (error) {
      console.error("Error searching parts:", error);
      return [];
    }
  }

  // Search history data
  async searchHistory() {
    try {
      const searchTerm = this.searchTerm.toLowerCase();
      
      // Load history data from Chrome storage
      let historyData = [];
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await new Promise(resolve => {
          chrome.storage.local.get('analyticsHistoryData', data => {
            resolve(data?.analyticsHistoryData || []);
          });
        });
        
        historyData = result;
      } else {
        // Fallback to localStorage
        try {
          const storedData = localStorage.getItem('analyticsHistoryData');
          if (storedData) {
            historyData = JSON.parse(storedData);
          }
        } catch (error) {
          console.error("Error parsing localStorage data:", error);
        }
      }
      
      if (!Array.isArray(historyData) || historyData.length === 0) {
        return [];
      }
      
      // Define important fields to search in
      const importantFields = [
        "Reference Number", "Customer Name", "User Name",
        "Carrier", "Inventory ID", "Part Description",
        "Order Number", "Tracking Number"
      ];
      
      // Perform search
      const results = historyData.filter(item => {
        // Search only in important fields first
        const matchesImportantField = importantFields.some(field => 
          item[field] && String(item[field]).toLowerCase().includes(searchTerm)
        );
        
        if (matchesImportantField) return true;
        
        // If not found in important fields, search all fields
        return Object.values(item).some(value => 
          value && String(value).toLowerCase().includes(searchTerm)
        );
      });
      
      // Limit results for performance
      return results.slice(0, 20);
    } catch (error) {
      console.error("Error searching history:", error);
      return [];
    }
  }

  // Display search results in UI
  displaySearchResults(container) {
    if (!container) return;
    
    // Close any existing popup
    this.closeSearchResults(container);
    
    // Prepare results
    const hasResults = this.searchResults.parts.length > 0 || this.searchResults.history.length > 0;
    const totalResults = this.searchResults.parts.length + this.searchResults.history.length;
    
    // Create search results popup
    const resultsHTML = `
      <div id="searchResultsPopup" class="search-results-popup">
        <div class="search-results-header">
          <h3>Search Results (${totalResults})</h3>
          <button id="closeSearchResultsBtn" class="close-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="search-results-tabs">
          <button class="tab-btn active" data-tab="all">All (${totalResults})</button>
          <button class="tab-btn" data-tab="parts">Parts (${this.searchResults.parts.length})</button>
          <button class="tab-btn" data-tab="history">History (${this.searchResults.history.length})</button>
        </div>
        <div class="search-results-content">
          ${hasResults ? this.renderSearchResults() : 
            '<div class="empty-results">No results found for "' + this.searchTerm + '"</div>'}
        </div>
      </div>
      
      <style>
        .search-results-popup {
          position: absolute;
          top: 60px;
          right: 20px;
          width: 500px;
          max-width: 80vw;
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.15);
          z-index: 1000;
          overflow: hidden;
        }
        
        .dark-mode .search-results-popup {
          background: #2d2d2d;
          color: white;
          border: 1px solid #3d3d3d;
        }
        
        .search-results-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #e5e5e5;
        }
        
        .dark-mode .search-results-header {
          border-color: #3d3d3d;
        }
        
        .search-results-header h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
        }
        
        .close-btn {
          background: none;
          border: none;
          cursor: pointer;
          color: #6b7280;
        }
        
        .dark-mode .close-btn {
          color: #d1d5db;
        }
        
        .close-btn:hover {
          color: #374151;
        }
        
        .dark-mode .close-btn:hover {
          color: white;
        }
        
        .search-results-tabs {
          display: flex;
          border-bottom: 1px solid #e5e5e5;
        }
        
        .dark-mode .search-results-tabs {
          border-color: #3d3d3d;
        }
        
        .tab-btn {
          padding: 10px 16px;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #6b7280;
          border-bottom: 2px solid transparent;
        }
        
        .dark-mode .tab-btn {
          color: #d1d5db;
        }
        
        .tab-btn.active {
          color: #0066ff;
          border-bottom-color: #0066ff;
          font-weight: 500;
        }
        
        .dark-mode .tab-btn.active {
          color: #90caf9;
          border-bottom-color: #90caf9;
        }
        
        .tab-btn:hover:not(.active) {
          background: #f9fafb;
          color: #374151;
        }
        
        .dark-mode .tab-btn:hover:not(.active) {
          background: #374151;
          color: white;
        }
        
        .search-results-content {
          max-height: 400px;
          overflow-y: auto;
          padding: 0;
        }
        
        .result-section {
          padding: 8px 0;
        }
        
        .result-section-heading {
          padding: 4px 16px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          color: #6b7280;
          background: #f9fafb;
        }
        
        .dark-mode .result-section-heading {
          color: #d1d5db;
          background: #374151;
        }
        
        .result-item {
          padding: 8px 16px;
          border-bottom: 1px solid #f3f4f6;
          cursor: pointer;
        }
        
        .dark-mode .result-item {
          border-color: #3d3d3d;
        }
        
        .result-item:hover {
          background: #f9fafb;
        }
        
        .dark-mode .result-item:hover {
          background: #374151;
        }
        
        .result-item-title {
          font-weight: 500;
          margin-bottom: 2px;
          color: #111827;
        }
        
        .dark-mode .result-item-title {
          color: #f9fafb;
        }
        
        .result-item-subtitle {
          font-size: 12px;
          color: #6b7280;
        }
        
        .dark-mode .result-item-subtitle {
          color: #9ca3af;
        }
        
        .highlighted {
          background: #FFFF00;
          color: #111827;
          padding: 0 2px;
          border-radius: 2px;
        }
        
        .dark-mode .highlighted {
          background: #3b5bdb;
          color: white;
        }
        
        .empty-results {
          padding: 32px 16px;
          text-align: center;
          color: #6b7280;
        }
        
        .dark-mode .empty-results {
          color: #9ca3af;
        }
        
        .part-section {
          display: block;
        }
        
        .history-section {
          display: block;
        }
        
        .hidden-section {
          display: none;
        }
      </style>
    `;
    
    // Append results to container
    container.insertAdjacentHTML('beforeend', resultsHTML);
    
    // Add event listeners
    const closeBtn = container.querySelector('#closeSearchResultsBtn');
    const tabButtons = container.querySelectorAll('.tab-btn');
    
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.closeSearchResults(container);
      });
    }
    
    tabButtons.forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        tabButtons.forEach(t => t.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        
        // Show/hide results sections based on selected tab
        const tabName = tab.getAttribute('data-tab');
        const partSection = container.querySelector('.part-section');
        const historySection = container.querySelector('.history-section');
        
        if (tabName === 'all') {
          partSection?.classList.remove('hidden-section');
          historySection?.classList.remove('hidden-section');
        } else if (tabName === 'parts') {
          partSection?.classList.remove('hidden-section');
          historySection?.classList.add('hidden-section');
        } else if (tabName === 'history') {
          partSection?.classList.add('hidden-section');
          historySection?.classList.remove('hidden-section');
        }
      });
    });
    
    // Add click event listeners for result items
    const partItems = container.querySelectorAll('.part-result-item');
    const historyItems = container.querySelectorAll('.history-result-item');
    
    partItems.forEach(item => {
      item.addEventListener('click', () => {
        const partId = item.getAttribute('data-part-id');
        this.navigateToResult('parts', partId);
      });
    });
    
    historyItems.forEach(item => {
      item.addEventListener('click', () => {
        const historyId = item.getAttribute('data-history-id');
        this.navigateToResult('history', historyId);
      });
    });
    
    // Close when clicking outside
    document.addEventListener('click', this.handleOutsideClick.bind(this, container));
  }
  
  // Close search results popup
  closeSearchResults(container) {
    const existingPopup = container.querySelector('#searchResultsPopup');
    if (existingPopup) {
      existingPopup.remove();
      document.removeEventListener('click', this.handleOutsideClick);
    }
  }
  
  // Handle clicks outside the popup
  handleOutsideClick(container, event) {
    const popup = container.querySelector('#searchResultsPopup');
    const searchInput = document.querySelector('.search-input');
    const searchButton = document.querySelector('.search-button');
    
    if (popup && !popup.contains(event.target) && 
        !searchInput?.contains(event.target) && 
        !searchButton?.contains(event.target)) {
      this.closeSearchResults(container);
    }
  }
  
  // Render search results HTML
  renderSearchResults() {
    let html = '';
    
    // Part results section
    if (this.searchResults.parts.length > 0) {
      html += `
        <div class="result-section part-section">
          <div class="result-section-heading">Parts</div>
          <div class="result-section-items">
      `;
      
      this.searchResults.parts.forEach(part => {
        const partNumber = this.highlightMatch(part['Part Number'] || '');
        const description = this.highlightMatch(part['Description'] || '');
        const manufacturer = part['Manufacturer'] || '';
        
        html += `
          <div class="result-item part-result-item" data-part-id="${part['Part Number']}">
            <div class="result-item-title">${partNumber}</div>
            <div class="result-item-subtitle">${description}</div>
            <div class="result-item-subtitle">${manufacturer}</div>
          </div>
        `;
      });
      
      html += `
          </div>
        </div>
      `;
    }
    
    // History results section
    if (this.searchResults.history.length > 0) {
      html += `
        <div class="result-section history-section">
          <div class="result-section-heading">Shipping History</div>
          <div class="result-section-items">
      `;
      
      this.searchResults.history.forEach((item, index) => {
        const refNumber = this.highlightMatch(item['Reference Number'] || '');
        const customer = this.highlightMatch(item['Customer Name'] || '');
        const carrier = item['Carrier'] || '';
        const trackingNumber = item['Tracking Number'] || '';
        
        html += `
          <div class="result-item history-result-item" data-history-id="${index}">
            <div class="result-item-title">${refNumber}</div>
            <div class="result-item-subtitle">${customer}</div>
            <div class="result-item-subtitle">${carrier} ${trackingNumber ? '- ' + trackingNumber : ''}</div>
          </div>
        `;
      });
      
      html += `
          </div>
        </div>
      `;
    }
    
    return html;
  }
  
  // Highlight matching text in search results
  highlightMatch(text) {
    if (!text || !this.searchTerm) return text;
    
    const searchTerm = this.searchTerm.toLowerCase();
    const textLower = text.toLowerCase();
    
    if (!textLower.includes(searchTerm)) return text;
    
    // Find the starting index of the match
    const startIndex = textLower.indexOf(searchTerm);
    const endIndex = startIndex + searchTerm.length;
    
    // Create highlighted HTML
    return text.substring(0, startIndex) + 
           '<span class="highlighted">' + 
           text.substring(startIndex, endIndex) + 
           '</span>' + 
           text.substring(endIndex);
  }
  
  // Navigate to a search result
  navigateToResult(type, id) {
    if (type === 'parts') {
      // Navigate to part detail
      document.dispatchEvent(new CustomEvent('navigateToPart', {
        detail: { partId: id }
      }));
    } else if (type === 'history') {
      // Navigate to history item
      document.dispatchEvent(new CustomEvent('navigateToHistory', {
        detail: { historyIndex: id }
      }));
    }
  }
}