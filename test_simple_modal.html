<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Simple Modal Close Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <button id="openModalBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Open Simple Modal
            </button>
        </div>
    </div>

    <script>
        document.getElementById('openModalBtn').addEventListener('click', () => {
            showSimpleModal();
        });

        function showSimpleModal() {
            const modalHtml = `
                <div id="orderDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-10 mx-auto p-6 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
                        <div class="flex items-center justify-between pb-3 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">
                                Sales Order Details - TEST001
                            </h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600 p-2 rounded-md hover:bg-gray-100" onclick="this.closest('#orderDetailsModal').remove();">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <div class="mt-4">
                            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                                <h4 class="text-sm font-medium text-gray-500 mb-2">Test Modal Content</h4>
                                <p class="text-sm text-gray-600">This is a test modal to verify the close button functionality.</p>
                                <p class="text-sm text-gray-600 mt-2">You should be able to close this modal by:</p>
                                <ul class="text-sm text-gray-600 mt-2 ml-4 list-disc">
                                    <li>Clicking the X button in the top-right corner</li>
                                    <li>Clicking the Close button at the bottom</li>
                                    <li>Clicking outside the modal (on the gray background)</li>
                                    <li>Pressing the Escape key</li>
                                </ul>
                            </div>

                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Production</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                <div class="flex items-center space-x-2">
                                                    <span>331SDS</span>
                                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                                        <i class="fas fa-cogs mr-1"></i>
                                                        BOM ID
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                H2S Analyzer, Dual Sensing, Class 1, Div. 2, Second Gen
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                1.00 EACH
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                    <i class="fas fa-industry mr-1"></i>
                                                    SYS000652
                                                </span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                <div class="flex items-center space-x-2">
                                                    <span>1100399</span>
                                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                        <i class="fas fa-box mr-1"></i>
                                                        Inventory
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                Probe, direct drive, A+ Genie 760, SS, 1" NPT process connection
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                1.00 EACH
                                            </td>
                                            <td class="px-4 py-3 text-sm text-gray-900">
                                                <span class="text-xs text-gray-500">No BOM</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="flex justify-end pt-4 border-t border-gray-200">
                            <button type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="this.closest('#orderDetailsModal').remove();">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Simple escape key handler
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    const modal = document.getElementById('orderDetailsModal');
                    if (modal) {
                        modal.remove();
                        document.removeEventListener('keydown', handleEscape);
                        console.log('Modal closed via Escape key');
                    }
                }
            };

            // Add escape key listener
            document.addEventListener('keydown', handleEscape);

            // Add backdrop click listener
            setTimeout(() => {
                const modal = document.getElementById('orderDetailsModal');
                if (modal) {
                    modal.addEventListener('click', (e) => {
                        if (e.target === modal) {
                            modal.remove();
                            document.removeEventListener('keydown', handleEscape);
                            console.log('Modal closed via backdrop click');
                        }
                    });
                    console.log('Modal event listeners set up');
                }
            }, 100);
        }
    </script>
</body>
</html>
