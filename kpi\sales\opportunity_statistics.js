// Statistics component for Sales KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class OpportunityStatistics {
  constructor(container, opportunityComponent) {
    this.container = container;
    this.opportunityComponent = opportunityComponent;
    this.statistics = null;
    this.isLoading = true;
    this.notificationSystem = new NotificationSystem();
    this.timeframe = 'year'; // default timeframe: year, quarter, month
    this.renderHeader = false; // Default to false - header handled by parent component
    this.dateRange = null; // Date range for filtering
    this.filterStatus = 'all'; // Status filter
    
    // Pagination for Products Quoted table
    this.currentProductPage = 1;
    this.itemsPerProductPage = 10;
    this.totalProductPages = 1;
  }

  async init() {
    console.log("Initializing Opportunity Statistics component");
    this.isLoading = true;
    this.render();
    
    try {
      // Calculate statistics from opportunity data
      await this.calculateStatistics();
      
      // Calculate product pagination
      this.calculateProductPagination();
      
      this.isLoading = false;
      this.render();
      
      // Only set up event listeners if we're rendering our own header
      if (this.renderHeader) {
        this.setupEventListeners();
      }
      
      // Set up pagination listeners for Products table
      this.setupProductPaginationListeners();
    } catch (error) {
      console.error("Error initializing opportunity statistics:", error);
      this.isLoading = false;
      this.showError("Failed to initialize statistics: " + error.message);
      this.render();
    }
  }

  async calculateStatistics() {
    // Get opportunities from the main component
    const opportunities = this.opportunityComponent.opportunities;

    if (!opportunities || opportunities.length === 0) {
      throw new Error("No opportunity data available");
    }

    // Get filtered opportunities based on filters
    let filteredOpportunities = [...opportunities];

    // Apply date range filter if specified
    if (this.dateRange && this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999);

      filteredOpportunities = filteredOpportunities.filter(opp => {
        let dateToCheck = null;
        // Always prioritize CreatedDate for consistency with main component
        if (opp.ODataInfo?.CreatedDate) {
          dateToCheck = new Date(opp.ODataInfo.CreatedDate);
        } else if (opp.LastModified instanceof Date) {
          // Fall back to LastModified if CreatedDate not available
          dateToCheck = opp.LastModified;
        }

        if (dateToCheck && !isNaN(dateToCheck.getTime())) {
          return dateToCheck >= startDate && dateToCheck <= endDate;
        }
        return false;
      });
    }

    // Apply status filter if specified and not 'all'
    if (this.filterStatus && this.filterStatus !== 'all') {
      filteredOpportunities = filteredOpportunities.filter(opp => 
        opp.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }

    // Initialize statistics structure
    this.statistics = {
      summary: {
        totalOpportunities: filteredOpportunities.length,
        totalValue: 0,
        avgCycleTime: 0,
        winRate: 0,
        openOpportunities: 0,
        closedOpportunities: 0,
        wonOpportunities: 0,
        lostOpportunities: 0
      },
      byStage: {},
      byStatus: {},
      byOwner: {},
      byMonth: {},
      byValue: {
        small: { count: 0, value: 0 },    // < $10,000
        medium: { count: 0, value: 0 },   // $10,000 - $50,000
        large: { count: 0, value: 0 },    // $50,000 - $100,000
        enterprise: { count: 0, value: 0 } // > $100,000
      },
      byCycle: {
        short: { count: 0, avgDays: 0 },   // < 30 days
        medium: { count: 0, avgDays: 0 },  // 30-90 days
        long: { count: 0, avgDays: 0 }     // > 90 days
      },
      byProduct: {} // New: Store product statistics
    };

    // Process filtered opportunities
    let totalCycleTime = 0;
    let cycleTimeCount = 0;

    filteredOpportunities.forEach(opp => {
      // Add to total value
      this.statistics.summary.totalValue += parseFloat(opp.Amount) || 0;

      // Count by status
      if (opp.Status) {
        if (!this.statistics.byStatus[opp.Status]) {
          this.statistics.byStatus[opp.Status] = { count: 0, value: 0 };
        }
        this.statistics.byStatus[opp.Status].count++;
        this.statistics.byStatus[opp.Status].value += parseFloat(opp.Amount) || 0;

        // Update summary counters
        if (opp.Status.toLowerCase() === 'won') {
          this.statistics.summary.wonOpportunities++;
          this.statistics.summary.closedOpportunities++;
        } else if (opp.Status.toLowerCase() === 'lost') {
          this.statistics.summary.lostOpportunities++;
          this.statistics.summary.closedOpportunities++;
        } else {
          this.statistics.summary.openOpportunities++;
        }
      }

      // Count by stage
      if (opp.Stage) {
        if (!this.statistics.byStage[opp.Stage]) {
          this.statistics.byStage[opp.Stage] = { count: 0, value: 0 };
        }
        this.statistics.byStage[opp.Stage].count++;
        this.statistics.byStage[opp.Stage].value += parseFloat(opp.Amount) || 0;
      }

      // Count by owner
      if (opp.OwnerEmployeeName) {
        if (!this.statistics.byOwner[opp.OwnerEmployeeName]) {
          this.statistics.byOwner[opp.OwnerEmployeeName] = { count: 0, value: 0, won: 0, lost: 0 };
        }
        this.statistics.byOwner[opp.OwnerEmployeeName].count++;
        this.statistics.byOwner[opp.OwnerEmployeeName].value += parseFloat(opp.Amount) || 0;
        
        if (opp.Status && opp.Status.toLowerCase() === 'won') {
          this.statistics.byOwner[opp.OwnerEmployeeName].won++;
        } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
          this.statistics.byOwner[opp.OwnerEmployeeName].lost++;
        }
      }

      // Count by value range
      const amount = parseFloat(opp.Amount) || 0;
      if (amount < 10000) {
        this.statistics.byValue.small.count++;
        this.statistics.byValue.small.value += amount;
      } else if (amount < 50000) {
        this.statistics.byValue.medium.count++;
        this.statistics.byValue.medium.value += amount;
      } else if (amount < 100000) {
        this.statistics.byValue.large.count++;
        this.statistics.byValue.large.value += amount;
      } else {
        this.statistics.byValue.enterprise.count++;
        this.statistics.byValue.enterprise.value += amount;
      }

      // Calculate cycle time if available
      const cycleDays = this.getCycleDays(opp);
      if (cycleDays !== 'N/A') {
        const days = parseInt(cycleDays);
        totalCycleTime += days;
        cycleTimeCount++;

        // Group by cycle length
        if (days < 30) {
          this.statistics.byCycle.short.count++;
          this.statistics.byCycle.short.avgDays = 
            (this.statistics.byCycle.short.avgDays * (this.statistics.byCycle.short.count - 1) + days) / 
            this.statistics.byCycle.short.count;
        } else if (days < 90) {
          this.statistics.byCycle.medium.count++;
          this.statistics.byCycle.medium.avgDays = 
            (this.statistics.byCycle.medium.avgDays * (this.statistics.byCycle.medium.count - 1) + days) / 
            this.statistics.byCycle.medium.count;
        } else {
          this.statistics.byCycle.long.count++;
          this.statistics.byCycle.long.avgDays = 
            (this.statistics.byCycle.long.avgDays * (this.statistics.byCycle.long.count - 1) + days) / 
            this.statistics.byCycle.long.count;
        }
      }

      // Group by month (created date)
      if (opp.ODataInfo && opp.ODataInfo.CreatedDate) {
        const createdDate = new Date(opp.ODataInfo.CreatedDate);
        const monthKey = `${createdDate.getFullYear()}-${String(createdDate.getMonth() + 1).padStart(2, '0')}`;
        
        if (!this.statistics.byMonth[monthKey]) {
          this.statistics.byMonth[monthKey] = { 
            count: 0, 
            value: 0, 
            won: 0,
            lost: 0,
            display: `${createdDate.toLocaleString('default', { month: 'short' })} ${createdDate.getFullYear()}`
          };
        }
        
        this.statistics.byMonth[monthKey].count++;
        this.statistics.byMonth[monthKey].value += parseFloat(opp.Amount) || 0;
        
        if (opp.Status && opp.Status.toLowerCase() === 'won') {
          this.statistics.byMonth[monthKey].won++;
        } else if (opp.Status && opp.Status.toLowerCase() === 'lost') {
          this.statistics.byMonth[monthKey].lost++;
        }
      }
    });

    // Calculate averages and rates
    if (cycleTimeCount > 0) {
      this.statistics.summary.avgCycleTime = Math.round(totalCycleTime / cycleTimeCount);
    }

    const closedOpportunities = this.statistics.summary.closedOpportunities;
    if (closedOpportunities > 0) {
      this.statistics.summary.winRate = Math.round((this.statistics.summary.wonOpportunities / closedOpportunities) * 100);
    }

    // Sort months chronologically
    this.statistics.byMonth = Object.fromEntries(
      Object.entries(this.statistics.byMonth)
        .sort(([a], [b]) => a.localeCompare(b))
    );

    // After processing all opportunities, calculate product statistics
    this.calculateProductStatistics(filteredOpportunities);

    console.log("Statistics calculated:", this.statistics);
    return this.statistics;
  }

  getDateRanges() {
    const now = new Date();
    const ranges = {
      year: {
        start: new Date(now.getFullYear(), 0, 1),
        end: new Date(now.getFullYear(), 11, 31)
      },
      quarter: {
        start: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1),
        end: new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0)
      },
      month: {
        start: new Date(now.getFullYear(), now.getMonth(), 1),
        end: new Date(now.getFullYear(), now.getMonth() + 1, 0)
      }
    };
    return ranges;
  }

  getCycleDays(opp) {
    return this.opportunityComponent.calculateSalesCycle(opp);
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderStatistics();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Calculating statistics...</p>
      </div>
    `;
  }

  renderStatistics() {
    if (!this.statistics) {
      this.container.innerHTML = `
        <p class="text-center text-gray-500 dark:text-gray-400">No statistics available</p>
      `;
      return;
    }

    const { summary, byStage, byStatus, byOwner, byMonth, byValue, byCycle } = this.statistics;

    // Create header content - only included if renderHeader is true
    let headerContent = '';
    if (this.renderHeader) {
      headerContent = `
        <!-- Timeframe selector -->
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">Statistics</h2>
          <div class="inline-flex rounded-md shadow-sm" role="group">
            <button type="button" id="timeframe-year" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'year' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-l-lg">
              Year
            </button>
            <button type="button" id="timeframe-quarter" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'quarter' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} border-l border-gray-200 dark:border-gray-600">
              Quarter
            </button>
            <button type="button" id="timeframe-month" class="px-4 py-2 text-sm font-medium ${this.timeframe === 'month' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'} rounded-r-lg border-l border-gray-200 dark:border-gray-600">
              Month
            </button>
          </div>
        </div>
      `;
    }

    this.container.innerHTML = `
      ${headerContent}

      <!-- First Row of Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <!-- Total Value Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Total Value</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${this.formatCurrency(summary.totalValue)}</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">${summary.totalOpportunities} opportunities</span>
              </div>
            </div>
            <div class="p-2 bg-green-50 dark:bg-green-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Opps Won Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Opps Won</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.wonOpportunities}</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Value: ${this.formatCurrency(this.getSumByStatus('Won', byStatus))}</span>
              </div>
            </div>
            <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Opps Lost Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Opps Lost</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.lostOpportunities}</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Value: ${this.formatCurrency(this.getSumByStatus('Lost', byStatus))}</span>
              </div>
            </div>
            <div class="p-2 bg-red-100 dark:bg-red-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Closing Ratio Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Closing Ratio</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.winRate}%</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Closed: ${summary.closedOpportunities} opps</span>
              </div>
            </div>
            <div class="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Second Row of Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <!-- Total Opportunities Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Total Opportunities</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.totalOpportunities}</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">All opportunities</span>
              </div>
            </div>
            <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Win Rate Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Win Rate</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.winRate}%</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Won: ${summary.wonOpportunities} / Closed: ${summary.closedOpportunities}</span>
              </div>
            </div>
            <div class="p-2 bg-green-50 dark:bg-green-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Avg. Cycle Time Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Avg. Cycle Time</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.avgCycleTime} days</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Short: ${byCycle.short.count} / Medium: ${byCycle.medium.count} / Long: ${byCycle.long.count}</span>
              </div>
            </div>
            <div class="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Open Opportunities Card -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
          <div class="flex justify-between">
            <div>
              <p class="text-xs text-gray-500 dark:text-gray-400">Open Opportunities</p>
              <h3 class="text-xl font-bold text-gray-800 dark:text-white mt-1">${summary.openOpportunities}</h3>
              <div class="text-xs flex items-center mt-1">
                <span class="text-gray-500">Potential: ${this.formatCurrency(summary.totalValue - this.getSumByStatus('Lost', byStatus))}</span>
              </div>
            </div>
            <div class="p-2 bg-red-100 dark:bg-red-800 rounded-xl flex-shrink-0 w-10 h-10 flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600 dark:text-red-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Restructured Charts Section -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Left Column - Stage Distribution only -->
        <div>
          <!-- Stage Distribution -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3 h-full">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Opportunities by Stage</h3>
            <div class="space-y-3">
              ${this.renderProgressBars(byStage)}
            </div>
          </div>
        </div>
        
        <!-- Right Column - Stacked Charts (Status, Size Distribution, Monthly Trend) -->
        <div class="space-y-6">
          <!-- Status Distribution at top -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Opportunities by Status</h3>
            <div class="space-y-3">
              ${this.renderProgressBars(byStatus)}
            </div>
          </div>
          
          <!-- Size Distribution in middle -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Opportunity Size Distribution</h3>
            <div class="space-y-4">
              ${this.renderValueDistribution(byValue)}
            </div>
          </div>
          
          <!-- Monthly Trend at bottom -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Monthly Trend</h3>
            <div class="h-60">
              ${this.renderMonthlyChart(byMonth)}
            </div>
          </div>
        </div>
      </div>

      <!-- Products Quoted Table Section -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3 mb-6" id="products-quoted-section">
        <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Products Quoted</h3>
        <div class="overflow-x-auto">
          ${this.renderProductsTable()}
        </div>
      </div>

      <!-- Sales Rep Performance -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-100 dark:border-gray-700 p-3 mb-6">
        <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Sales Rep Performance</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Count</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Won</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Lost</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Win Rate</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Total Value</th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Avg. Value</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderOwnerRows(byOwner)}
            </tbody>
          </table>
        </div>
      </div>
    `;
    
    // Setup event listeners for the product pagination
    this.setupProductPaginationListeners();
  }

  renderProgressBars(data) {
    // Get total for percentage calculation
    const total = Object.values(data).reduce((sum, item) => sum + item.count, 0);
    if (total === 0) return '<p>No data available</p>';

    // Sort items by count descending
    const sortedItems = Object.entries(data).sort((a, b) => b[1].count - a[1].count);

    // Generate HTML for progress bars
    return sortedItems.map(([key, item]) => {
      const percentage = Math.round((item.count / total) * 100);
      return `
        <div>
          <div class="flex justify-between mb-1">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">${key}</span>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">${item.count} (${percentage}%)</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
            <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${percentage}%"></div>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            ${this.formatCurrency(item.value)}
          </div>
        </div>
      `;
    }).join('');
  }

  renderMonthlyChart(byMonth) {
    // Get array of months (keys) and prepare data points
    const months = Object.keys(byMonth);
    if (months.length === 0) return '<p>No monthly data available</p>';

    // Limit to the last 12 months
    const recentMonths = months.slice(-12);
    const chartData = recentMonths.map(month => {
      const data = byMonth[month];
      return {
        month: data.display,
        count: data.count,
        value: data.value,
        won: data.won || 0,
        lost: data.lost || 0
      };
    });

    // Calculate maximum values for scaling
    const maxCount = Math.max(...chartData.map(d => d.count), 1);
    const maxBarHeight = 150; // Max bar height in pixels

    // Generate improved chart with clearer won/lost representation
    return `
      <div class="flex h-full items-end justify-between px-4 py-2">
        ${chartData.map(data => {
          // Calculate heights proportionally
          const totalHeight = Math.round((data.count / maxCount) * maxBarHeight);
          // Ensure won and lost heights are calculated correctly
          const wonPercent = data.count > 0 ? data.won / data.count : 0;
          const lostPercent = data.count > 0 ? data.lost / data.count : 0;
          const wonHeight = Math.round(totalHeight * wonPercent);
          const lostHeight = Math.round(totalHeight * lostPercent);
          const otherHeight = totalHeight - wonHeight - lostHeight;
          
          return `
            <div class="flex flex-col items-center relative group">
              <div class="mx-1 flex flex-col items-center" style="height: ${totalHeight}px">
                ${otherHeight > 0 ? `<div class="w-6 bg-gray-300 dark:bg-gray-500 rounded-t-sm" style="height: ${otherHeight}px"></div>` : ''}
                ${wonHeight > 0 ? `<div class="w-6 bg-green-500 ${otherHeight === 0 ? 'rounded-t-sm' : ''}" style="height: ${wonHeight}px"></div>` : ''}
                ${lostHeight > 0 ? `<div class="w-6 bg-red-500 ${(otherHeight === 0 && wonHeight === 0) ? 'rounded-t-sm' : ''}" style="height: ${lostHeight}px"></div>` : ''}
              </div>
              <div class="w-16 text-center mt-2">
                <div class="text-xs font-medium text-gray-700 dark:text-gray-300 truncate" title="${data.month}">
                  ${data.month.split(' ')[0]}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  ${data.count}
                </div>
              </div>
              <div class="absolute bottom-full mb-1 bg-black bg-opacity-80 text-white text-xs rounded px-2 py-1 hidden group-hover:block whitespace-nowrap z-10">
                <div>Total: ${data.count}</div>
                <div>Won: ${data.won}</div>
                <div>Lost: ${data.lost}</div>
              </div>
            </div>
          `;
        }).join('')}
      </div>
      <div class="flex justify-center mt-4 space-x-6">
        <div class="flex items-center">
          <div class="w-3 h-3 bg-green-500 rounded-sm mr-1"></div>
          <span class="text-xs text-gray-700 dark:text-gray-300">Won: ${chartData.reduce((sum, d) => sum + d.won, 0)}</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-red-500 rounded-sm mr-1"></div>
          <span class="text-xs text-gray-700 dark:text-gray-300">Lost: ${chartData.reduce((sum, d) => sum + d.lost, 0)}</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 bg-gray-300 dark:bg-gray-500 rounded-sm mr-1"></div>
          <span class="text-xs text-gray-700 dark:text-gray-300">Other</span>
        </div>
      </div>
    `;
  }

  renderValueDistribution(byValue) {
    const categories = [
      { key: 'small', label: 'Small (<$10K)', color: 'bg-blue-400' },
      { key: 'medium', label: 'Medium ($10K-$50K)', color: 'bg-green-400' },
      { key: 'large', label: 'Large ($50K-$100K)', color: 'bg-yellow-400' },
      { key: 'enterprise', label: 'Enterprise (>$100K)', color: 'bg-red-400' }
    ];
    
    const total = categories.reduce((sum, cat) => sum + byValue[cat.key].count, 0);
    if (total === 0) return '<p>No data available</p>';
    
    return categories.map(cat => {
      const data = byValue[cat.key];
      const percentage = Math.round((data.count / total) * 100) || 0;
      
      return `
        <div>
          <div class="flex justify-between mb-1">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">${cat.label}</span>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">${data.count} (${percentage}%)</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
            <div class="${cat.color} h-2.5 rounded-full" style="width: ${percentage}%"></div>
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            ${this.formatCurrency(data.value)}
          </div>
        </div>
      `;
    }).join('');
  }

  renderOwnerRows(byOwner) {
    if (Object.keys(byOwner).length === 0) {
      return `
        <tr>
          <td colspan="7" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No owner data available
          </td>
        </tr>
      `;
    }

    // Sort owners by total value descending
    const sortedOwners = Object.entries(byOwner).sort((a, b) => b[1].value - a[1].value);

    let totalCount = 0;
    let totalWon = 0;
    let totalLost = 0;
    let totalValue = 0;

    // Generate rows for each sales rep
    const ownerRows = sortedOwners.map(([name, data]) => {
      const closedOpps = data.won + data.lost;
      const winRate = closedOpps > 0 ? Math.round((data.won / closedOpps) * 100) : 0;
      const avgValue = data.count > 0 ? data.value / data.count : 0;

      // Add to totals
      totalCount += data.count;
      totalWon += data.won;
      totalLost += data.lost;
      totalValue += data.value;

      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
            ${name}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${data.count}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${data.won}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${data.lost}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${winRate}%
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${this.formatCurrency(data.value)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
            ${this.formatCurrency(avgValue)}
          </td>
        </tr>
      `;
    }).join('');

    // Calculate totals
    const totalClosedOpps = totalWon + totalLost;
    const overallWinRate = totalClosedOpps > 0 ? Math.round((totalWon / totalClosedOpps) * 100) : 0;
    const overallAvgValue = totalCount > 0 ? totalValue / totalCount : 0;

    // Add a totals row
    const totalsRow = `
      <tr class="bg-gray-100 dark:bg-gray-700 font-medium">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          TOTAL
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${totalCount}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${totalWon}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${totalLost}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${overallWinRate}%
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(totalValue)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(overallAvgValue)}
        </td>
      </tr>
    `;

    return ownerRows + totalsRow;
  }

  getSumByStatus(status, byStatus) {
    return Object.entries(byStatus)
      .filter(([key]) => key.toLowerCase() === status.toLowerCase())
      .reduce((sum, [_, data]) => sum + data.value, 0);
  }

  setupEventListeners() {
    // Only set up timeframe buttons if we're rendering our own header
    if (!this.renderHeader) return;
    
    // Add event listeners for timeframe buttons
    const timeframeButtons = [
      { id: 'timeframe-year', value: 'year' },
      { id: 'timeframe-quarter', value: 'quarter' },
      { id: 'timeframe-month', value: 'month' }
    ];
    
    timeframeButtons.forEach(button => {
      const element = document.getElementById(button.id);
      if (element) {
        element.addEventListener('click', () => {
          this.timeframe = button.value;
          this.calculateStatistics().then(() => {
            this.render();
            this.setupEventListeners();
          });
        });
      }
    });
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'statistics-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('statistics-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    }
  }

  // New method to calculate product statistics
  calculateProductStatistics(opportunities) {
    const productStats = {};
    let totalQuoted = 0;
    let totalOrdered = 0;
    
    // Process each opportunity
    opportunities.forEach(opp => {
      // Skip if no products
      if (!opp.Products || !Array.isArray(opp.Products)) return;
      
      // Process each product in the opportunity
      opp.Products.forEach(product => {
        // Using the correct field names from the actual data structure
        // In opportunity.js, products use inventoryId not Model, and quantity instead of Quantity
        const inventoryId = product.inventoryId;
        const quantity = parseInt(product.quantity) || 0;
        
        if (!inventoryId) return;
        
        // Initialize product stats if not exists
        if (!productStats[inventoryId]) {
          productStats[inventoryId] = {
            quoted: 0,
            ordered: 0,
            description: product.description || '' // Store description for display
          };
        }
        
        // Add to quoted quantity
        productStats[inventoryId].quoted += quantity;
        totalQuoted += quantity;
        
        // If opportunity is won, add to ordered quantity
        if (opp.Status && opp.Status.toLowerCase() === 'won') {
          productStats[inventoryId].ordered += quantity;
          totalOrdered += quantity;
        }
      });
    });
    
    // Convert to array and calculate closing ratios
    const productsArray = Object.entries(productStats).map(([inventoryId, stats]) => {
      const closingRatio = stats.quoted > 0 ? Math.round((stats.ordered / stats.quoted) * 100) : 0;
      return {
        model: inventoryId,
        description: stats.description,
        quoted: stats.quoted,
        ordered: stats.ordered,
        closingRatio
      };
    });
    
    // Sort by most quoted
    productsArray.sort((a, b) => b.quoted - a.quoted);
    
    // Add a total row
    const totalClosingRatio = totalQuoted > 0 ? Math.round((totalOrdered / totalQuoted) * 100) : 0;
    productsArray.push({
      model: 'TOTAL',
      description: 'All Products',
      quoted: totalQuoted,
      ordered: totalOrdered,
      closingRatio: totalClosingRatio
    });
    
    // Store in statistics
    this.statistics.byProduct = productsArray;
  }

  // Add method to calculate product pagination
  calculateProductPagination() {
    if (!this.statistics || !this.statistics.byProduct) {
      this.totalProductPages = 1;
      return;
    }
    
    // Exclude the TOTAL row from pagination calculation
    const productCount = this.statistics.byProduct.length - 1;
    this.totalProductPages = Math.max(1, Math.ceil(productCount / this.itemsPerProductPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentProductPage > this.totalProductPages) {
      this.currentProductPage = this.totalProductPages;
    }
  }
  
  // Add method to setup product pagination listeners
  setupProductPaginationListeners() {
    // Clean up existing listeners first by replacing the buttons with their clones
    const paginationButtons = [
      'product-first-page',
      'product-prev-page',
      'product-next-page',
      'product-last-page'
    ];
    
    paginationButtons.forEach(id => {
      const button = document.getElementById(id);
      if (button) {
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);
      }
    });
    
    // First Page button
    const firstPageBtn = document.getElementById('product-first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentProductPage > 1) {
          this.currentProductPage = 1;
          this.updateProductTable();
        }
      });
    }
    
    // Previous Page button
    const prevPageBtn = document.getElementById('product-prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentProductPage > 1) {
          this.currentProductPage--;
          this.updateProductTable();
        }
      });
    }
    
    // Next Page button
    const nextPageBtn = document.getElementById('product-next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentProductPage < this.totalProductPages) {
          this.currentProductPage++;
          this.updateProductTable();
        }
      });
    }
    
    // Last Page button
    const lastPageBtn = document.getElementById('product-last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentProductPage < this.totalProductPages) {
          this.currentProductPage = this.totalProductPages;
          this.updateProductTable();
        }
      });
    }
  }
  
  // Add a new method to update the product table in the DOM
  updateProductTable() {
    const productTableContainer = document.querySelector('.products-table-container');
    if (productTableContainer) {
      // Get the parent element that contains the table
      const parentElement = productTableContainer.parentElement;
      if (parentElement) {
        // Replace the table with a new one
        parentElement.innerHTML = this.renderProductsTable();
        // Reattach event listeners
        this.setupProductPaginationListeners();
      }
    } else {
      console.error("Could not find products table container to update");
    }
  }

  // New method to render the products table
  renderProductsTable() {
    // Return early if there's no product data
    if (!this.statistics || !this.statistics.byProduct || 
        this.statistics.byProduct.length === 0 || 
        (this.statistics.byProduct.length === 1 && 
         this.statistics.byProduct[0].model === 'TOTAL' && 
         this.statistics.byProduct[0].quoted === 0)) {
      
      return `
        <div class="products-table-container">
          <p class="text-center text-gray-500 dark:text-gray-400 py-4">No product data available</p>
        </div>
      `;
    }
    
    const allProducts = this.statistics.byProduct;
    
    // Separate the TOTAL row
    const totalRow = allProducts.find(p => p.model === 'TOTAL');
    const productsWithoutTotal = allProducts.filter(p => p.model !== 'TOTAL');
    
    // Calculate pagination
    const start = (this.currentProductPage - 1) * this.itemsPerProductPage;
    const end = Math.min(start + this.itemsPerProductPage, productsWithoutTotal.length);
    
    // Get products for current page
    let displayedProducts = productsWithoutTotal.slice(start, end);
    
    // Add TOTAL row to the last page only
    if (this.currentProductPage === this.totalProductPages && totalRow) {
      displayedProducts.push(totalRow);
    }
    
    // Generate table HTML
    const tableHtml = `
      <div class="products-table-container">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Product Model</th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty Quoted</th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty Ordered</th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Closing Ratio</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${displayedProducts.map((product, index) => {
              // Determine if this is the total row
              const isTotal = product.model === 'TOTAL';
              
              // Determine row background color (alternating or highlighted for total)
              const rowClass = isTotal 
                ? 'bg-gray-100 dark:bg-gray-700 font-medium' 
                : index % 2 === 0 
                  ? 'bg-white dark:bg-gray-900'
                  : 'bg-gray-50 dark:bg-gray-800';
              
              // Determine closing ratio color
              const ratioColor = isTotal
                ? ''
                : product.closingRatio >= 50
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400';
                  
              return `
                <tr class="${rowClass} hover:bg-gray-100 dark:hover:bg-gray-800">
                  <td class="px-3 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                    ${this.escapeHtml(product.model)}
                  </td>
                  <td class="px-3 py-3 text-sm text-gray-700 dark:text-gray-300 max-w-xs truncate">
                    ${this.escapeHtml(product.description || '')}
                  </td>
                  <td class="px-3 py-3 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
                    ${product.quoted.toLocaleString()}
                  </td>
                  <td class="px-3 py-3 whitespace-nowrap text-sm text-right text-gray-700 dark:text-gray-300">
                    ${product.ordered.toLocaleString()}
                  </td>
                  <td class="px-3 py-3 whitespace-nowrap text-sm text-right font-medium ${ratioColor}">
                    ${product.closingRatio}%
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
        
        <!-- Pagination -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div id="product-showing-text" class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${start + 1} to ${end} of ${productsWithoutTotal.length} products
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="product-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="product-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span id="product-page-display" class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentProductPage} of ${this.totalProductPages}
            </span>
            
            <button id="product-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage === this.totalProductPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage === this.totalProductPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="product-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentProductPage === this.totalProductPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentProductPage === this.totalProductPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;
    
    return tableHtml;
  }

  // Add escapeHtml method for safe text output
  escapeHtml(text) {
    if (!text) return '';
    
    return String(text)
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
} 