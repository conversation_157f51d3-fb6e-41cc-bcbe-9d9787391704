// Purchasing component for KPI Dashboard
export class PurchasingComponent {
  constructor(container) {
    this.container = container;
    this.activeTab = 'overview';
    this.tabs = [
      { id: 'overview', label: 'Overview', icon: 'chart-line' },
      { id: 'purchase_analytics', label: 'Purchase Analytics', icon: 'chart-pie' },
      { id: 'purchase_order', label: 'Purchase Orders', icon: 'file-invoice' },
      { id: 'vendor_metrics', label: 'Vendor Metrics', icon: 'building' },
      { id: 'inventory_impact', label: 'Inventory Impact', icon: 'boxes' },
      { id: 'price_tracker', label: 'Price Tracker', icon: 'chart-line-up' }
    ];
    this.visibleTabsStart = 0;
  }

  init() {
    this.render();
    this.setupEventListeners();
    this.switchTab('overview');
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4">
        <h1 class="text-2xl font-bold mb-6">Purchasing KPI Dashboard</h1>
        
        <!-- Tab Navigation with Pagination -->
        <div class="flex items-center mb-6 border-b border-gray-200 dark:border-gray-700">
          <!-- Left Scroll Button -->
          <button id="tabScrollLeft" class="px-2 py-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-l-md">
            <i class="fas fa-chevron-left"></i>
          </button>
          
          <!-- Tab Buttons Container -->
          <div class="flex overflow-hidden" style="max-width: calc(100% - 80px);">
            <div id="tabsContainer" class="flex transition-transform duration-300 ease-in-out">
              ${this.tabs.map(tab => `
                <button id="tab-${tab.id}" data-tab="${tab.id}" class="tab-button px-4 py-2 mr-1 flex items-center">
                  <i class="fas fa-${tab.icon} mr-2"></i>
                  <span>${tab.label}</span>
                </button>
              `).join('')}
            </div>
          </div>
          
          <!-- Right Scroll Button -->
          <button id="tabScrollRight" class="px-2 py-2 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-r-md">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        
        <!-- Tab Content Container -->
        <div id="purchasingTabContent" class="tab-content">
          <!-- Content will be loaded dynamically -->
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Tab button click handlers
    this.tabs.forEach(tab => {
      const tabButton = document.getElementById(`tab-${tab.id}`);
      if (tabButton) {
        tabButton.addEventListener('click', () => this.switchTab(tab.id));
      }
    });

    // Tab scroll buttons
    const scrollLeftBtn = document.getElementById('tabScrollLeft');
    const scrollRightBtn = document.getElementById('tabScrollRight');
    
    if (scrollLeftBtn) {
      scrollLeftBtn.addEventListener('click', () => this.scrollTabs('left'));
    }
    
    if (scrollRightBtn) {
      scrollRightBtn.addEventListener('click', () => this.scrollTabs('right'));
    }
    
    // Update tab visibility on window resize
    window.addEventListener('resize', () => this.updateTabVisibility());
  }

  scrollTabs(direction) {
    const maxVisibleTabs = 4;
    const maxStartIndex = Math.max(0, this.tabs.length - maxVisibleTabs);
    
    if (direction === 'left') {
      this.visibleTabsStart = Math.max(0, this.visibleTabsStart - 1);
    } else if (direction === 'right') {
      this.visibleTabsStart = Math.min(maxStartIndex, this.visibleTabsStart + 1);
    }
    
    this.updateTabVisibility();
  }

  updateTabVisibility() {
    const tabsContainer = document.getElementById('tabsContainer');
    if (!tabsContainer) return;
    
    const tabWidth = 150; // Approximate width of each tab
    const translateX = -this.visibleTabsStart * tabWidth;
    tabsContainer.style.transform = `translateX(${translateX}px)`;
    
    // Update scroll button states
    const scrollLeftBtn = document.getElementById('tabScrollLeft');
    const scrollRightBtn = document.getElementById('tabScrollRight');
    
    if (scrollLeftBtn) {
      scrollLeftBtn.disabled = this.visibleTabsStart === 0;
      scrollLeftBtn.classList.toggle('opacity-50', this.visibleTabsStart === 0);
    }
    
    if (scrollRightBtn) {
      const maxVisibleTabs = 4;
      const maxStartIndex = Math.max(0, this.tabs.length - maxVisibleTabs);
      scrollRightBtn.disabled = this.visibleTabsStart >= maxStartIndex;
      scrollRightBtn.classList.toggle('opacity-50', this.visibleTabsStart >= maxStartIndex);
    }
  }

  async switchTab(tabId) {
    // Update active tab
    this.activeTab = tabId;
    
    // Update tab button styles
    this.tabs.forEach(tab => {
      const tabButton = document.getElementById(`tab-${tab.id}`);
      if (tabButton) {
        tabButton.classList.toggle('active', tab.id === tabId);
        if (tab.id === tabId) {
          tabButton.classList.add('border-b-2', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
          tabButton.classList.remove('text-gray-500', 'dark:text-gray-400');
        } else {
          tabButton.classList.remove('border-b-2', 'border-blue-500', 'text-blue-600', 'dark:text-blue-400');
          tabButton.classList.add('text-gray-500', 'dark:text-gray-400');
        }
      }
    });
    
    // Get content container
    const contentContainer = document.getElementById('purchasingTabContent');
    if (!contentContainer) return;
    
    // Show loading state
    contentContainer.innerHTML = `
      <div class="flex justify-center items-center p-10">
        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
      </div>
    `;
    
    try {
      // Load tab content based on tab ID
      switch (tabId) {
        case 'overview':
          this.renderOverviewTab(contentContainer);
          break;
        case 'purchase_analytics':
          // Load ApexCharts library first
          if (!window.ApexCharts) {
            await new Promise((resolve, reject) => {
              const script = document.createElement('script');
              script.src = '../library/apexcharts.min.js';
              script.onload = resolve;
              script.onerror = () => reject(new Error('Failed to load ApexCharts library'));
              document.head.appendChild(script);
            });
          }
          
          // Import and initialize the PurchaseAnalytics component
          const PurchaseAnalyticsModule = await import('./purchase_analytics.js');
          const purchaseAnalyticsComp = new PurchaseAnalyticsModule.PurchaseAnalyticsComponent(contentContainer);
          await purchaseAnalyticsComp.init();
          break;
        case 'purchase_order':
          // Import and initialize the PurchaseOrder component
          const PurchaseOrderModule = await import('./purchase_order.js');
          const purchaseOrderComp = new PurchaseOrderModule.PurchaseOrderComponent(contentContainer);
          await purchaseOrderComp.init();
          break;
        case 'vendor_metrics':
          // Import and initialize the VendorMetrics component
          const VendorMetricsModule = await import('./vendor-metrics.js');
          const vendorMetricsComp = new VendorMetricsModule.VendorMetricsComponent(contentContainer);
          await vendorMetricsComp.init();
          break;
        case 'inventory_impact':
          // Import and initialize the InventoryImpact component
          const InventoryImpactModule = await import('./inventory_impact.js');
          const inventoryImpactComp = new InventoryImpactModule.InventoryImpactComponent(contentContainer);
          await inventoryImpactComp.init();
          break;
        case 'price_tracker':
          // Import and initialize the PriceTracker component
          const PriceTrackerModule = await import('./price_tracker.js');
          const priceTrackerComp = new PriceTrackerModule.PriceTrackerComponent(contentContainer);
          await priceTrackerComp.init();
          break;
        default:
          // For other tabs, show a placeholder
          contentContainer.innerHTML = `
            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 dark:bg-blue-900 dark:border-blue-600">
              <div class="flex items-center">
                <div class="flex-shrink-0 text-blue-500 dark:text-blue-400">
                  <i class="fas fa-info-circle"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-blue-800 dark:text-blue-200">
                    The ${this.tabs.find(t => t.id === tabId)?.label || tabId} tab is under development.
                  </p>
                </div>
              </div>
            </div>
          `;
      }
    } catch (error) {
      console.error(`Error loading tab ${tabId}:`, error);
      contentContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">
                Error loading tab content: ${error.message}
              </p>
            </div>
          </div>
        </div>
      `;
    }
  }

  renderOverviewTab(container) {
    container.innerHTML = `
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- Purchase Summary Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Purchase Summary</h3>
          <div class="grid grid-cols-2 gap-4">
            <div class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Active POs</p>
              <p class="text-xl font-bold text-blue-600 dark:text-blue-400">--</p>
            </div>
            <div class="bg-green-50 dark:bg-green-900 p-3 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Total Value</p>
              <p class="text-xl font-bold text-green-600 dark:text-green-400">$--</p>
            </div>
            <div class="bg-yellow-50 dark:bg-yellow-900 p-3 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Pending Receipts</p>
              <p class="text-xl font-bold text-yellow-600 dark:text-yellow-400">--</p>
            </div>
            <div class="bg-purple-50 dark:bg-purple-900 p-3 rounded-lg">
              <p class="text-sm text-gray-500 dark:text-gray-400">Avg Lead Time</p>
              <p class="text-xl font-bold text-purple-600 dark:text-purple-400">-- days</p>
            </div>
          </div>
        </div>
        
        <!-- Vendor Performance Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow lg:col-span-2">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Top Vendors</h3>
          <div class="h-64 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
            <p class="text-gray-500 dark:text-gray-400">Vendor performance chart coming soon</p>
          </div>
        </div>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Purchase Orders -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Recent Purchase Orders</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">PO #</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendor</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                  <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">Coming soon</td>
                  <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">-</td>
                  <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">-</td>
                  <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">-</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Purchase Trends Card -->
        <div class="kpi-card p-4 bg-white dark:bg-gray-800 rounded-lg shadow">
          <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-4">Purchase Trends</h3>
          <div class="h-64 flex items-center justify-center bg-gray-100 rounded-lg dark:bg-gray-700">
            <p class="text-gray-500 dark:text-gray-400">Trend chart coming soon</p>
          </div>
        </div>
      </div>
    `;
  }
} 