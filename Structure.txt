project-root/
│
├── index.js                  (Main initializer, core entry point)
├── background.js             (Background tasks and caching)
│
├── dashboard/
│   ├── dashboard.js          
│   ├── dashboard-actions.js  
│   ├── dashboard-process.js  
│   ├── dashboard-export.js   
│   └── dashboard-scheduling.js 
│
├── analytics/
│   ├── analytics.js          
│   ├── analytics-history.js  
│   └── charts.js             
│
├── assistant/
│   ├── assistant.js          (AI-driven assistant/chatbot)
│   ├── monday-api.js         (Integration with Monday.com)
│   └── search-utils.js       (Utilities for search result formatting)
│
├── admin/
│   ├── admin-dashboard.js    
│   ├── admin-notifications.js
│   └── admin-tools.js        
│
├── core/
│   ├── login.js              (User authentication and login handling)
│   ├── notifications.js      (Main notification system)
│   ├── settings.js           (Global user settings management)
│   └── search.js             (Assistant's search functionality)
│
├── json/
│   └── masterparts.json      
│
├── masterparts/              
│   └── master-parts.js       (Core parts database management used by assistant)
│
├── images/                   
│
├── index.html                
├── login.html                
│
└── manifest.json             
