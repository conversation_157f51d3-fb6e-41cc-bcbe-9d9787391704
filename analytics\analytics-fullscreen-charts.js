// analytics-fullscreen-charts.js
// Handles chart management features for the analytics dashboard
// Features: resize, remove/hide, fullscreen mode, and chart library

export class ChartManager {
  constructor() {
    this.charts = new Map(); // Store chart instances
    this.hiddenCharts = []; // Track hidden charts
    this.chartSizes = new Map(); // Track chart sizes
    this.inFullscreenMode = false;
    this.fullscreenChart = null;
    this.originalParent = null;
    this.originalSize = null;
    this.chartPositions = new Map(); // For drag and drop positioning
    this.lastZIndex = 100; // For layering during drag
    this.printManager = null; // Will be set from main initialization
  }

  /**
   * Initialize chart management features
   */
  init() {
    // Add chart management UI to all chart containers
    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => this.addChartControls(container));
    
    // Initialize chart library panel
    this.initChartLibrary();
    
    // Add event listener for ESC key to exit fullscreen
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.inFullscreenMode) {
        this.exitFullscreen();
      }
    });
  }

  /**
   * Add control buttons to chart container
   */
  addChartControls(container) {
    // Create controls container
    const controlsDiv = document.createElement('div');
    controlsDiv.className = 'chart-controls absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 no-print';
    
    // Add resize handle
    const resizeHandle = document.createElement('div');
    resizeHandle.className = 'resize-handle absolute bottom-0 right-0 w-4 h-4 cursor-nwse-resize opacity-0 hover:opacity-100 transition-opacity duration-200';
    resizeHandle.innerHTML = `
      <svg viewBox="0 0 24 24" fill="none" class="w-4 h-4 text-gray-500">
        <path d="M22 22L16 16M22 16L16 22" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
    `;
    
    // Add fullscreen button
    const fullscreenBtn = document.createElement('button');
    fullscreenBtn.className = 'p-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors duration-150';
    fullscreenBtn.innerHTML = `
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5v-4m0 4h-4m4 0l-5-5"></path>
      </svg>
    `;
    fullscreenBtn.title = "Fullscreen";
    
    // Add hide button
    const hideBtn = document.createElement('button');
    hideBtn.className = 'p-1 bg-gray-100 hover:bg-gray-200 rounded text-gray-600 hover:text-gray-800 transition-colors duration-150';
    hideBtn.innerHTML = `
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    `;
    hideBtn.title = "Hide chart";
    
    // Add event listeners
    fullscreenBtn.addEventListener('click', () => this.enterFullscreen(container));
    hideBtn.addEventListener('click', () => this.hideChart(container));
    
    // Add controls to container
    controlsDiv.appendChild(fullscreenBtn);
    controlsDiv.appendChild(hideBtn);
    
    // Make chart container position relative for absolute positioning of controls
    container.style.position = 'relative';
    container.classList.add('group');
    
    // Add controls to container
    container.appendChild(controlsDiv);
    container.appendChild(resizeHandle);
    
    // Setup resize functionality
    this.setupResize(container, resizeHandle);
    
    // Setup drag and drop
    this.setupDragAndDrop(container);
    
    // Store initial size
    this.chartSizes.set(container.id, {
      width: container.offsetWidth,
      height: container.offsetHeight
    });
  }

  /**
   * Set up chart resize functionality
   */
  setupResize(container, handle) {
    let isResizing = false;
    let startX, startY, startWidth, startHeight;
    
    handle.addEventListener('mousedown', (e) => {
      isResizing = true;
      startX = e.clientX;
      startY = e.clientY;
      startWidth = container.offsetWidth;
      startHeight = container.offsetHeight;
      
      document.body.style.cursor = 'nwse-resize';
      e.preventDefault();
      
      // Add overlay while resizing
      const overlay = document.createElement('div');
      overlay.className = 'resize-overlay fixed inset-0 z-50';
      overlay.style.cursor = 'nwse-resize';
      document.body.appendChild(overlay);
      
      const onMouseMove = (e) => {
        if (!isResizing) return;
        
        const width = startWidth + e.clientX - startX;
        const height = startHeight + e.clientY - startY;
        
        container.style.width = `${Math.max(300, width)}px`;
        container.style.height = `${Math.max(200, height)}px`;
        
        // If there's a chart instance, update it
        const chartId = container.querySelector('[id$="Chart"]')?.id;
        if (chartId && this.charts.has(chartId)) {
          const chart = this.charts.get(chartId);
          if (chart && chart.updateOptions) {
            chart.updateOptions({
              chart: {
                width: container.offsetWidth,
                height: container.offsetHeight
              }
            });
          }
        }
      };
      
      const onMouseUp = () => {
        isResizing = false;
        document.body.style.cursor = 'default';
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        
        // Remove overlay
        const overlay = document.querySelector('.resize-overlay');
        if (overlay) {
          overlay.remove();
        }
        
        // Save new size
        this.chartSizes.set(container.id, {
          width: container.offsetWidth,
          height: container.offsetHeight
        });
      };
      
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    });
  }

  /**
   * Set up drag and drop functionality for chart containers
   */
  setupDragAndDrop(container) {
    const header = container.querySelector('h3') || container.querySelector('.flex');
    if (!header) return;
    
    let isDragging = false;
    let offsetX, offsetY;
    
    // Make header look draggable
    header.style.cursor = 'move';
    
    header.addEventListener('mousedown', (e) => {
      // Don't start drag if clicked on a control
      if (e.target.closest('.chart-controls')) return;
      
      isDragging = true;
      
      // Store original position if not already saved
      if (!this.chartPositions.has(container.id)) {
        this.chartPositions.set(container.id, {
          left: container.offsetLeft,
          top: container.offsetTop,
          parent: container.parentElement
        });
      }
      
      offsetX = e.clientX - container.getBoundingClientRect().left;
      offsetY = e.clientY - container.getBoundingClientRect().top;
      
      // Change styling during drag
      container.style.zIndex = this.lastZIndex++;
      container.style.position = 'absolute';
      container.style.width = `${container.offsetWidth}px`;
      container.style.height = `${container.offsetHeight}px`;
      
      // Add overlay to prevent text selection while dragging
      const overlay = document.createElement('div');
      overlay.className = 'drag-overlay fixed inset-0 z-40';
      overlay.style.cursor = 'move';
      document.body.appendChild(overlay);
      
      const onMouseMove = (e) => {
        if (!isDragging) return;
        
        const x = e.clientX - offsetX;
        const y = e.clientY - offsetY;
        
        // Stay within the dashboard container
        const dashboardContainer = document.querySelector('.dashboard-container');
        const maxX = dashboardContainer.offsetWidth - container.offsetWidth;
        const maxY = dashboardContainer.scrollHeight - container.offsetHeight;
        
        container.style.left = `${Math.max(0, Math.min(maxX, x))}px`;
        container.style.top = `${Math.max(0, Math.min(maxY, y))}px`;
      };
      
      const onMouseUp = () => {
        isDragging = false;
        
        // Remove overlay
        const overlay = document.querySelector('.drag-overlay');
        if (overlay) {
          overlay.remove();
        }
        
        // End the drag operation
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };
      
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      
      e.preventDefault();
    });
  }

  /**
   * Enter fullscreen mode for a chart
   */
  enterFullscreen(container) {
    if (this.inFullscreenMode) return;
    
    // Create fullscreen overlay
    const overlay = document.createElement('div');
    overlay.className = 'fixed inset-0 bg-gray-900 bg-opacity-75 z-50 flex items-center justify-center';
    overlay.id = 'chartFullscreenOverlay';
    
    // Create close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'absolute top-4 right-4 bg-white rounded-full p-2 text-gray-800 hover:bg-gray-200 transition-colors duration-150';
    closeBtn.innerHTML = `
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    `;
    closeBtn.addEventListener('click', () => this.exitFullscreen());
    
    // Create content container
    const contentContainer = document.createElement('div');
    contentContainer.className = 'bg-white rounded-lg shadow-xl p-6 w-11/12 h-5/6 overflow-auto';
    
    // Save original state
    this.inFullscreenMode = true;
    this.fullscreenChart = container;
    this.originalParent = container.parentElement;
    this.originalSize = {
      width: container.style.width,
      height: container.style.height
    };
    
    // Move the chart to the fullscreen container
    contentContainer.appendChild(container);
    overlay.appendChild(closeBtn);
    overlay.appendChild(contentContainer);
    document.body.appendChild(overlay);
    
    // Adjust size to fill fullscreen container
    container.style.width = '100%';
    container.style.height = '90%';
    
    // If there's a chart instance, update it
    const chartId = container.querySelector('[id$="Chart"]')?.id;
    if (chartId && this.charts.has(chartId)) {
      const chart = this.charts.get(chartId);
      if (chart && chart.updateOptions) {
        chart.updateOptions({
          chart: {
            width: contentContainer.offsetWidth - 48, // Adjust for padding
            height: contentContainer.offsetHeight - 48
          }
        });
      }
    }
  }

  /**
   * Exit fullscreen mode
   */
  exitFullscreen() {
    if (!this.inFullscreenMode) return;
    
    const overlay = document.getElementById('chartFullscreenOverlay');
    if (!overlay) return;
    
    // Restore original container
    if (this.originalParent && this.fullscreenChart) {
      this.originalParent.appendChild(this.fullscreenChart);
      
      // Restore original size
      if (this.originalSize) {
        this.fullscreenChart.style.width = this.originalSize.width;
        this.fullscreenChart.style.height = this.originalSize.height;
      }
      
      // If there's a chart instance, update it
      const chartId = this.fullscreenChart.querySelector('[id$="Chart"]')?.id;
      if (chartId && this.charts.has(chartId)) {
        const chart = this.charts.get(chartId);
        if (chart && chart.updateOptions) {
          chart.updateOptions({
            chart: {
              width: this.fullscreenChart.offsetWidth,
              height: this.fullscreenChart.offsetHeight
            }
          });
        }
      }
    }
    
    // Remove overlay
    overlay.remove();
    
    // Reset fullscreen state
    this.inFullscreenMode = false;
    this.fullscreenChart = null;
    this.originalParent = null;
    this.originalSize = null;
  }

  /**
   * Hide a chart
   * @param {HTMLElement} container - The chart container element
   */
  hideChart(container) {
    if (!container) return;
    
    console.log('Hiding chart container');
    
    // Store the chart in hidden charts array
    const chartElement = container.querySelector('[id$="Chart"]');
    const chartTitle = container.querySelector('h3')?.textContent || 'Unnamed Chart';
    
    if (chartElement) {
      const chartId = chartElement.id;
      
      // Save chart info for restoration
      this.hiddenCharts.push({
        id: chartId,
        title: chartTitle,
        container: container,
        position: {
          left: container.style.left,
          top: container.style.top,
          width: container.style.width,
          height: container.style.height
        }
      });
      
      // Hide the container
      container.classList.add('hidden');
      
      // Update the hidden charts list in settings panel
      this.updateHiddenChartsList();
      
      // Show notification
      this.showNotification(`Chart "${chartTitle}" has been hidden. You can restore it from Settings > Chart Library.`);
    } else {
      console.warn('Chart element not found in container');
    }
  }

  /**
   * Restore a hidden chart
   * @param {number} index - Index of the chart in hiddenCharts array
   */
  restoreChart(index) {
    if (index < 0 || index >= this.hiddenCharts.length) {
      console.warn(`Invalid chart index: ${index}`);
      return;
    }
    
    const chartInfo = this.hiddenCharts[index];
    const container = chartInfo.container;
    
    if (container) {
      console.log(`Restoring chart: ${chartInfo.title}`);
      
      // Show the container
      container.classList.remove('hidden');
      
      // Restore position if it was set
      if (chartInfo.position) {
        if (chartInfo.position.left) container.style.left = chartInfo.position.left;
        if (chartInfo.position.top) container.style.top = chartInfo.position.top;
        if (chartInfo.position.width) container.style.width = chartInfo.position.width;
        if (chartInfo.position.height) container.style.height = chartInfo.position.height;
      }
      
      // Remove from hidden charts
      this.hiddenCharts.splice(index, 1);
      
      // Update chart instance size
      setTimeout(() => {
        const chartId = chartInfo.id;
        const chart = this.getChart(chartId);
        if (chart) {
          console.log(`Updating chart size: ${chartId}`);
          chart.updateOptions({
            chart: {
              width: container.clientWidth,
              height: container.clientHeight
            }
          });
        }
      }, 100);
    } else {
      console.warn('Chart container not found, removing from hidden charts');
      this.hiddenCharts.splice(index, 1);
    }
  }

  /**
   * Initialize the chart library and set up required components
   */
  initChartLibrary() {
    console.log('Initializing Apex charts');
    
    try {
      // Initialize any global chart options here
      this.setupDefaultChartOptions();
      
      // Create settings button if it doesn't exist
      let settingsBtn = document.getElementById('dashboardSettingsBtn');

      // Make the button container visible
      let headerControls = document.getElementById('dashboardControlButtons');
      if (headerControls) {
        headerControls.classList.remove('hidden');
        console.log('Made dashboard control buttons visible');
      } else {
        console.warn('Header controls container not found, creating it');
        
        // Create the header controls container if it doesn't exist
        const header = document.querySelector('header');
        if (header) {
          headerControls = document.createElement('div');
          headerControls.id = 'dashboardControlButtons';
          headerControls.className = 'flex gap-2 items-center';
          header.appendChild(headerControls);
          console.log('Created new dashboard control buttons container');
        } else {
          console.error('Header element not found, cannot create control buttons');
        }
      }

      // If the settings button wasn't in the original HTML, create it
      if (!settingsBtn && headerControls) {
        console.log('Creating settings button');
        settingsBtn = document.createElement('button');
        settingsBtn.className = 'px-3 py-1.5 bg-indigo-500 text-white text-sm rounded-md hover:bg-indigo-600 transition-colors duration-150 flex items-center';
        settingsBtn.id = 'dashboardSettingsBtn';
        settingsBtn.innerHTML = `
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Settings
        `;
        headerControls.appendChild(settingsBtn);
      }

      // Ensure the settings button has a click handler
      if (settingsBtn) {
        console.log('Adding click handler to settings button');
        // Remove any existing event listeners (prevent duplicates)
        const newSettingsBtn = settingsBtn.cloneNode(true);
        if (settingsBtn.parentNode) {
          settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);
        }
        settingsBtn = newSettingsBtn;
        
        // Add click handler to show/hide settings panel
        settingsBtn.addEventListener('click', () => {
          this.toggleSettingsPanel();
        });
      } else {
        console.error('Settings button not found and could not be created');
      }

      // Create settings panel if needed
      this.createSettingsPanel();
      
      // If print report button exists, attach print manager
      const printBtn = document.getElementById('printReport');
      if (printBtn && this.printManager && typeof this.printManager.showPrintDialog === 'function') {
        console.log('Attaching print manager to print button');
        // Clone to remove existing event listeners
        const newPrintBtn = printBtn.cloneNode(true);
        if (printBtn.parentNode) {
          printBtn.parentNode.replaceChild(newPrintBtn, printBtn);
        }
        
        // Add click handler
        newPrintBtn.addEventListener('click', () => {
          this.printManager.showPrintDialog();
        });
      } else if (!this.printManager) {
        console.warn('Print manager not initialized (this.printManager is null)');
      } else if (typeof this.printManager.showPrintDialog !== 'function') {
        console.warn('Print manager missing showPrintDialog method');
      }
    } catch (error) {
      console.error('Error initializing chart library:', error);
    }
  }
  
  /**
   * Remove the original refresh and export buttons from the main UI
   */
  removeMainActionButtons() {
    // Get the buttons
    const refreshBtn = document.getElementById('refreshData');
    const exportBtn = document.getElementById('exportCSV');
    const printBtn = document.getElementById('printReport');
    const settingsBtn = document.getElementById('dashboardSettingsBtn');
    
    // First make all buttons visible (remove hidden class from container)
    const buttonsContainer = document.getElementById('dashboardControlButtons');
    if (buttonsContainer) {
      console.log('Making button container visible');
      buttonsContainer.classList.remove('hidden');
    } else {
      console.warn('Button container not found during removeMainActionButtons');
    }
    
    // Hide specific buttons
    if (refreshBtn) {
      console.log('Hiding refresh button');
      refreshBtn.classList.add('hidden');
    }
    
    if (exportBtn) {
      console.log('Hiding export button');
      exportBtn.classList.add('hidden');
    }
    
    // Make sure Print Report and Settings buttons are visible
    if (printBtn) {
      console.log('Making print button visible');
      printBtn.classList.remove('hidden');
    } else {
      console.warn('Print button not found');
    }
    
    if (settingsBtn) {
      console.log('Making settings button visible');
      settingsBtn.classList.remove('hidden');
    } else {
      console.warn('Settings button not found during removeMainActionButtons');
    }
  }

  /**
   * Toggle settings panel visibility
   */
  toggleSettingsPanel() {
    console.log('Toggling settings panel');
    const settingsPanel = document.getElementById('dashboardSettingsPanel');
    if (settingsPanel) {
      settingsPanel.classList.toggle('translate-x-full');
      console.log('Settings panel toggled', settingsPanel.classList.contains('translate-x-full') ? 'hidden' : 'visible');
    } else {
      console.error('Settings panel not found in toggleSettingsPanel');
    }
  }

  /**
   * Save current layout (for use in settings panel)
   */
  saveCurrentLayout() {
    // Get visible charts
    const visibleCharts = Array.from(document.querySelectorAll('.chart-container')).filter(
      container => container.style.display !== 'none'
    );
    
    // Create layout object
    const layout = {
      timestamp: Date.now(),
      name: `Layout ${new Date().toLocaleDateString()}`,
      charts: visibleCharts.map(container => {
        const chartElement = container.querySelector('[id$="Chart"]');
        return {
          id: chartElement?.id || '',
          title: container.querySelector('h3')?.textContent || 'Unnamed Chart',
          position: {
            left: container.style.left || '',
            top: container.style.top || '',
            width: container.style.width || '',
            height: container.style.height || ''
          }
        };
      })
    };
    
    // Save to localStorage
    try {
      // Get existing layouts
      const existingLayouts = JSON.parse(localStorage.getItem('dashboardLayouts') || '[]');
      existingLayouts.push(layout);
      
      // Save updated layouts
      localStorage.setItem('dashboardLayouts', JSON.stringify(existingLayouts));
      
      alert('Dashboard layout saved successfully');
    } catch (error) {
      console.error('Error saving layout:', error);
      alert('Failed to save dashboard layout');
    }
  }

  /**
   * Update the chart library with hidden charts
   */
  updateChartLibrary() {
    const listContainer = document.getElementById('hiddenChartsList');
    if (!listContainer) return;
    
    if (this.hiddenCharts.length === 0) {
      listContainer.innerHTML = '<p class="text-sm text-gray-500">No hidden charts</p>';
      return;
    }
    
    let html = '';
    this.hiddenCharts.forEach((chart, index) => {
      html += `
        <div class="bg-gray-50 rounded-lg p-3 mb-3">
          <div class="font-medium text-gray-800">${chart.title}</div>
          <button class="restore-chart-btn mt-2 px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors" data-index="${index}">
            Restore Chart
          </button>
        </div>
      `;
    });
    
    listContainer.innerHTML = html;
    
    // Add event listeners to restore buttons
    const restoreButtons = listContainer.querySelectorAll('.restore-chart-btn');
    restoreButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const index = parseInt(btn.dataset.index);
        this.restoreChart(index);
      });
    });
  }

  /**
   * Register a chart instance to allow resizing and other operations
   */
  registerChart(chartId, chartInstance) {
    this.charts.set(chartId, chartInstance);
  }

  /**
   * Get a registered chart instance
   */
  getChart(chartId) {
    return this.charts.get(chartId);
  }

  /**
   * Resize all charts (useful when layout changes)
   */
  resizeAllCharts() {
    this.charts.forEach((chart, id) => {
      const container = document.getElementById(id).closest('.chart-container');
      if (container && chart && chart.updateOptions) {
        chart.updateOptions({
          chart: {
            width: container.offsetWidth,
            height: container.offsetHeight
          }
        });
      }
    });
  }

  /**
   * Create or update the settings panel
   */
  createSettingsPanel() {
    console.log('Creating settings panel with chart library');
    
    // Get settings panel or create it if it doesn't exist
    let settingsPanel = document.getElementById('dashboardSettingsPanel');
    if (!settingsPanel) {
      console.warn('Settings panel not found in DOM, cannot create settings panel');
      return;
    }
    
    // Ensure close button exists and works
    const closeSettingsBtn = document.getElementById('closeSettingsBtn');
    if (closeSettingsBtn) {
      // Remove existing event listeners to prevent duplicates
      const newCloseBtn = closeSettingsBtn.cloneNode(true);
      if (closeSettingsBtn.parentNode) {
        closeSettingsBtn.parentNode.replaceChild(newCloseBtn, closeSettingsBtn);
      }
      
      // Add event listener to close button
      newCloseBtn.addEventListener('click', () => {
        console.log('Close settings button clicked');
        this.toggleSettingsPanel();
      });
    }

    // --- HANDLE PRINT SETTINGS SECTION ---
    // Find the print settings container but don't recreate it
    const printSettingsContainer = document.getElementById('printSettingsContainer');
    if (printSettingsContainer) {
      // Check if chart selection exists, create only if it doesn't
      let chartPrintContainer = document.getElementById('chartPrintContainer');
      if (!chartPrintContainer) {
        // Create only the chart selection part, not the entire print settings
        const chartPrintSelection = document.createElement('div');
        chartPrintSelection.id = 'chartPrintContainer';
        chartPrintSelection.className = 'space-y-3 mt-4';
        chartPrintSelection.innerHTML = `
          <label class="text-sm text-gray-700 block mb-1">Select charts for printing:</label>
          <div id="chartSelectorList" class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2 mb-2">
            <div class="flex justify-between pb-2 mb-2 border-b border-gray-200">
              <button id="selectAllChartsPrintBtn" class="px-2 py-1 text-xs bg-blue-500 text-white rounded">Select All</button>
              <button id="deselectAllChartsPrintBtn" class="px-2 py-1 text-xs bg-gray-500 text-white rounded">Deselect All</button>
            </div>
            <div id="chartSelectList">
              <!-- Chart list will be inserted here -->
            </div>
          </div>
          <div class="mt-2 text-xs text-gray-600" id="chartPrintCount">No charts selected</div>
        `;
        
        // Add the chart selection to the print settings container
        // Find the print button if it exists
        const printButton = document.getElementById('printFromSettings');
        if (printButton) {
          printButton.parentNode.insertBefore(chartPrintSelection, printButton);
        } else {
          printSettingsContainer.appendChild(chartPrintSelection);
        }
        
        // Set up chart selection event listeners
        document.getElementById('selectAllChartsPrintBtn')?.addEventListener('click', () => {
          if (this.printManager) this.printManager.selectAllCharts(true);
        });
        
        document.getElementById('deselectAllChartsPrintBtn')?.addEventListener('click', () => {
          if (this.printManager) this.printManager.selectAllCharts(false);
        });
      }
      
      // Always update the chart selector list
      this.updateChartSelectorList();
    }
    
    // --- HANDLE CHART LIBRARY SECTION ---
    // Check if chart library section exists, recreate if needed
    let chartLibrarySection = document.getElementById('chartLibrarySection');
    if (!chartLibrarySection) {
      // Create chart library section
      chartLibrarySection = document.createElement('div');
      chartLibrarySection.id = 'chartLibrarySection';
      chartLibrarySection.className = 'p-4 border-b border-gray-200';
      chartLibrarySection.innerHTML = `
        <h4 class="font-medium text-gray-800 mb-3">Chart Library</h4>
        <div id="hiddenChartsList" class="mb-3">
          <p class="text-sm text-gray-600 mb-2">Hidden charts:</p>
          <div id="hiddenChartsContainer" class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
            <p class="text-sm text-gray-500 italic">No hidden charts</p>
          </div>
          <button id="restoreAllChartsBtn" class="mt-2 px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Restore All Charts
          </button>
        </div>
      `;
      
      // Find the display settings section to insert before
      const displaySettings = settingsPanel.querySelector('.p-4.border-b:last-child');
      
      // Insert the chart library section at the end
      if (displaySettings && displaySettings.parentNode) {
        displaySettings.parentNode.insertBefore(chartLibrarySection, displaySettings.nextSibling);
      } else {
        settingsPanel.appendChild(chartLibrarySection);
      }
      
      // Add restore all charts event listener
      document.getElementById('restoreAllChartsBtn')?.addEventListener('click', () => {
        this.restoreAllCharts();
      });
    }
    
    // Always update hidden charts list
    this.updateHiddenChartsList();
    
    // --- HANDLE PRINT BUTTON FUNCTIONALITY ---
    // Make sure print dialog button correctly connects to PrintManager (only once)
    const printFromSettingsBtn = document.getElementById('printFromSettings');
    if (printFromSettingsBtn && this.printManager && !printFromSettingsBtn.hasAttribute('data-print-handler-attached')) {
      // Remove any existing event listeners
      const newPrintBtn = printFromSettingsBtn.cloneNode(true);
      if (printFromSettingsBtn.parentNode) {
        printFromSettingsBtn.parentNode.replaceChild(newPrintBtn, printFromSettingsBtn);
      }
      
      // Add event listener
      newPrintBtn.addEventListener('click', () => {
        if (typeof this.printManager.executePrint === 'function') {
          this.printManager.executePrint();
        } else {
          console.error('PrintManager.executePrint is not a function');
          window.print(); // Fallback
        }
      });
      
      // Mark that we've attached an event handler
      newPrintBtn.setAttribute('data-print-handler-attached', 'true');
    }
    
    // Initialize print settings if PrintManager is available (but only once)
    if (this.printManager && typeof this.printManager.injectPrintSettings === 'function' && 
        !printSettingsContainer.hasAttribute('data-print-settings-injected')) {
      console.log('Initializing print settings');
      try {
        this.printManager.injectPrintSettings(printSettingsContainer);
        printSettingsContainer.setAttribute('data-print-settings-injected', 'true');
      } catch (error) {
        console.error('Error initializing print settings:', error);
      }
    }
  }
  
  /**
   * Update chart selector list in settings panel
   */
  updateChartSelectorList() {
    const selectorList = document.getElementById('chartSelectList');
    if (!selectorList) return;
    
    // Clear existing content
    selectorList.innerHTML = '';
    
    // Get all chart containers
    const chartContainers = document.querySelectorAll('.chart-container');
    
    chartContainers.forEach(container => {
      const chartEl = container.querySelector('[id$="Chart"]');
      const chartId = chartEl?.id;
      if (!chartId) return;
      
      const chartTitle = container.querySelector('h3')?.textContent || 'Unnamed Chart';
      
      // Create checkbox item
      const checkboxItem = document.createElement('div');
      checkboxItem.className = 'flex items-center py-1';
      checkboxItem.innerHTML = `
        <input type="checkbox" id="print_${chartId}" class="print-select-checkbox mr-2" checked>
        <label for="print_${chartId}" class="text-sm text-gray-700 truncate">${chartTitle}</label>
      `;
      
      // Add to list
      selectorList.appendChild(checkboxItem);
      
      // Add event listener
      const checkbox = checkboxItem.querySelector('input');
      checkbox.addEventListener('change', (e) => {
        if (e.target.checked) {
          // Add to print queue
          if (this.printManager) this.printManager.printQueue.add(chartId);
        } else {
          // Remove from print queue
          if (this.printManager) this.printManager.printQueue.delete(chartId);
        }
        
        // Update count
        if (this.printManager) this.printManager.updateSelectedCount();
      });
      
      // Initialize in print queue
      if (this.printManager) this.printManager.printQueue.add(chartId);
    });
    
    // Update count display
    if (this.printManager) this.printManager.updateSelectedCount();
  }
  
  /**
   * Update hidden charts list
   */
  updateHiddenChartsList() {
    const hiddenChartsContainer = document.getElementById('hiddenChartsContainer');
    if (!hiddenChartsContainer) return;
    
    // Clear existing items
    hiddenChartsContainer.innerHTML = '';
    
    // Check if there are any hidden charts
    if (this.hiddenCharts.length === 0) {
      hiddenChartsContainer.innerHTML = '<p class="text-sm text-gray-500 italic">No hidden charts</p>';
      return;
    }
    
    // Create a list of hidden charts with restore buttons
    const list = document.createElement('ul');
    list.className = 'space-y-1';
    
    this.hiddenCharts.forEach((chartInfo, index) => {
      const item = document.createElement('li');
      item.className = 'flex justify-between items-center text-sm';
      item.innerHTML = `
        <span class="text-gray-700">${chartInfo.title || 'Chart ' + index}</span>
        <button data-index="${index}" class="restore-chart-btn px-2 py-0.5 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-xs">
          Restore
        </button>
      `;
      
      // Add click handler
      const restoreBtn = item.querySelector('.restore-chart-btn');
      restoreBtn.addEventListener('click', () => {
        this.restoreChart(index);
        this.updateHiddenChartsList();
      });
      
      list.appendChild(item);
    });
    
    hiddenChartsContainer.appendChild(list);
  }
  
  /**
   * Restore all hidden charts
   */
  restoreAllCharts() {
    console.log('Restoring all hidden charts');
    
    // Store the length since we'll be modifying the array while iterating
    const count = this.hiddenCharts.length;
    for (let i = count - 1; i >= 0; i--) {
      this.restoreChart(i);
    }
    
    // Update the hidden charts list
    this.updateHiddenChartsList();
    
    // Show notification
    this.showNotification(`Restored ${count} hidden chart${count !== 1 ? 's' : ''}`);
  }
  
  /**
   * Show a notification message
   */
  showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notification = document.getElementById('chartManagerNotification');
    if (!notification) {
      notification = document.createElement('div');
      notification.id = 'chartManagerNotification';
      notification.className = 'fixed top-4 right-4 max-w-md z-50 transform transition-all duration-500 translate-y-[-100%] opacity-0';
      document.body.appendChild(notification);
    }
    
    // Set appearance based on type
    let bgColor, textColor, icon;
    switch(type) {
      case 'error':
        bgColor = 'bg-red-50 border-red-200';
        textColor = 'text-red-800';
        icon = `<svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`;
        break;
      case 'success':
        bgColor = 'bg-green-50 border-green-200';
        textColor = 'text-green-800';
        icon = `<svg class="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>`;
        break;
      default: // info
        bgColor = 'bg-blue-50 border-blue-200';
        textColor = 'text-blue-800';
        icon = `<svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`;
    }
    
    // Set content
    notification.className = `fixed top-4 right-4 max-w-md z-50 p-4 rounded-lg border ${bgColor} ${textColor} shadow-sm transition-all duration-500`;
    notification.innerHTML = `
      <div class="flex">
        <div class="flex-shrink-0">
          ${icon}
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">${message}</p>
        </div>
      </div>
    `;
    
    // Show notification
    setTimeout(() => {
      notification.style.transform = 'translateY(0)';
      notification.style.opacity = '1';
    }, 10);
    
    // Hide after delay
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateY(-100%)';
    }, 3000);
  }

  /**
   * Setup default chart options for all charts
   */
  setupDefaultChartOptions() {
    // This can be used to set any global ApexCharts options
    if (typeof window.ApexCharts !== 'undefined') {
      console.log('Setting up ApexCharts default options');
      
      // Make sure defaultOptions exists
      if (!ApexCharts.defaultOptions) {
        ApexCharts.defaultOptions = {};
      }
      
      // Make sure chart property exists
      if (!ApexCharts.defaultOptions.chart) {
        ApexCharts.defaultOptions.chart = {};
      }
      
      // Now it's safe to set properties
      ApexCharts.defaultOptions = {
        ...ApexCharts.defaultOptions,
        chart: {
          ...ApexCharts.defaultOptions.chart,
          fontFamily: 'Inter, sans-serif',
          background: 'transparent',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true,
            easing: 'easeinout',
            speed: 800,
            animateGradually: {
              enabled: true,
              delay: 150
            },
            dynamicAnimation: {
              enabled: true,
              speed: 350
            }
          }
        },
        colors: ['#4f46e5', '#06b6d4', '#8b5cf6', '#10b981', '#f59e0b', '#ef4444'],
        tooltip: {
          enabled: true,
          theme: 'light',
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, sans-serif'
          }
        },
        grid: {
          borderColor: '#e2e8f0',
          strokeDashArray: 2
        },
        dataLabels: {
          enabled: false
        }
      };
      
      console.log('Successfully set up default ApexCharts options');
    } else {
      console.warn('ApexCharts not available - skipping default options setup');
    }
  }
} 