// Debug info about localStorage before loading main script
console.log('==================== DEBUG INFO ====================');
console.log('localStorage keys:', Object.keys(localStorage));

// Check for project data
const projectDataRaw = localStorage.getItem('enventbridge_projectData');
console.log('Project data exists:', !!projectDataRaw);
if (projectDataRaw) {
  try {
    const parsedData = JSON.parse(projectDataRaw);
    console.log('Projects count:', parsedData.length);
  } catch(e) {
    console.error('Failed to parse project data:', e);
  }
}

// Check for token
const hasToken = !!localStorage.getItem('enventbridge_mapboxToken');
console.log('Mapbox token exists:', hasToken);

// Check for dark mode
const darkMode = localStorage.getItem('enventbridge_darkMode');
console.log('Dark mode setting:', darkMode);
console.log('==================== END DEBUG ===================='); 