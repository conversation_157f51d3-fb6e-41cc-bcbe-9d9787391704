/**
 * chart-loader.js - <PERSON>les loading ApexCharts library reliably
 */

// Global variable to track if the library is loaded
let isApexChartsLoaded = false;

// Global flag or variable to track if loaded
let apexChartsLoadPromise = null;

/**
 * Load ApexCharts library and return a promise that resolves when loaded
 * @returns {Promise} Promise that resolves when ApexCharts is loaded
 */
export function loadApexCharts() {
  if (window.ApexCharts) {
    // Already loaded
    console.log("ApexCharts already available");
    return Promise.resolve(true);
  }

  if (apexChartsLoadPromise) {
    // Already in progress
    return apexChartsLoadPromise;
  }

  console.log("Loading apexcharts.min.js from local directory...");
  apexChartsLoadPromise = new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src = "analytics/apexcharts.min.js"; // ensure correct path
    script.async = false;

    script.onload = () => {
      console.log("ApexCharts loaded from local file");
      resolve(true);
    };

    script.onerror = (err) => {
      console.error("Failed to load apexcharts.min.js locally", err);
      reject(err);
    };

    document.head.appendChild(script);
  });

  return apexChartsLoadPromise;
}

/**
 * Check if ApexCharts is loaded
 * @returns {boolean} True if ApexCharts is loaded
 */
export function isApexChartsAvailable() {
  return !!window.ApexCharts;
}

/**
 * Try loading ApexCharts from alternative paths
 * @param {Function} resolve - Promise resolve callback
 * @param {Function} reject - Promise reject callback
 */
function tryAlternativePaths(resolve, reject) {
  // Define alternative paths to try
  const paths = [
    './apexcharts.min.js',            // Same directory as the current page
    '/apexcharts.min.js',             // Root directory
    '/analytics/apexcharts.min.js',   // Analytics folder from root
    '../apexcharts.min.js'            // Parent directory
  ];
  
  let currentPath = 0;
  
  function tryNextPath() {
    if (currentPath >= paths.length) {
      console.error("❌ Failed to load ApexCharts from all known locations");
      return reject(new Error("Failed to load ApexCharts library"));
    }
    
    const script = document.createElement('script');
    script.src = paths[currentPath];
    script.async = false;
    
    script.onload = () => {
      console.log(`✅ ApexCharts loaded successfully from ${paths[currentPath]}`);
      isApexChartsLoaded = true;
      resolve(window.ApexCharts);
    };
    
    script.onerror = () => {
      console.warn(`Failed to load from ${paths[currentPath]}, trying next path...`);
      currentPath++;
      tryNextPath();
    };
    
    document.head.appendChild(script);
  }
  
  // Start trying paths
  tryNextPath();
}