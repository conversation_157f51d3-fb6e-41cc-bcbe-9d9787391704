// Event handlers for the map interface

// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  // Close button handler
  const closeButton = document.getElementById('closeButton');
  if (closeButton) {
    closeButton.addEventListener('click', function() {
      window.close();
    });
  }

  // Project search functionality
  const searchInput = document.getElementById('projectSearch');
  if (searchInput) {
    searchInput.addEventListener('input', function() {
      filterProjects(this.value.toLowerCase());
    });
  }

  // Location button handler
  const locationButton = document.getElementById('locationButton');
  if (locationButton && window.mapInstance) {
    locationButton.addEventListener('click', function() {
      getUserLocation();
    });
  }
});

// Filter projects based on search input
function filterProjects(searchText) {
  const projectItems = document.querySelectorAll('.project-item');
  
  projectItems.forEach(item => {
    const text = item.textContent.toLowerCase();
    if (text.includes(searchText)) {
      item.style.display = 'block';
    } else {
      item.style.display = 'none';
    }
  });
}

// Get user's current location and fly to it
function getUserLocation() {
  if (!window.mapInstance) {
    console.error('Map instance not available');
    return;
  }

  if ('geolocation' in navigator) {
    // Show loading indicator for location
    const loadingContainer = document.getElementById('loadingContainer');
    if (loadingContainer) {
      loadingContainer.innerHTML = '<div class="loading-spinner"></div>';
      loadingContainer.style.display = 'flex';
    }

    navigator.geolocation.getCurrentPosition(
      // Success callback
      function(position) {
        const { latitude, longitude } = position.coords;
        
        // Hide loading indicator
        if (loadingContainer) {
          loadingContainer.style.display = 'none';
        }
        
        console.log(`User location: ${latitude}, ${longitude}`);
        
        // Add marker for user location if it doesn't exist yet
        if (!window.userLocationMarker) {
          // Create a DOM element for the marker
          const el = document.createElement('div');
          el.className = 'user-location-marker';
          el.style.backgroundColor = '#007bff';
          el.style.width = '15px';
          el.style.height = '15px';
          el.style.borderRadius = '50%';
          el.style.border = '2px solid white';
          el.style.boxShadow = '0 0 0 2px rgba(0,123,255,0.5)';
          
          // Add the marker to the map
          window.userLocationMarker = new mapboxgl.Marker(el)
            .setLngLat([longitude, latitude])
            .addTo(window.mapInstance);
        } else {
          // Update existing marker position
          window.userLocationMarker.setLngLat([longitude, latitude]);
        }
        
        // Fly to user location
        window.mapInstance.flyTo({
          center: [longitude, latitude],
          zoom: 14,
          essential: true
        });
      },
      // Error callback
      function(error) {
        // Hide loading indicator
        if (loadingContainer) {
          loadingContainer.style.display = 'none';
        }
        
        console.error('Error getting location:', error);
        
        // Show error message
        let errorMsg = 'Unable to get your location. ';
        switch(error.code) {
          case error.PERMISSION_DENIED:
            errorMsg += 'Please enable location services in your browser settings.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMsg += 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMsg += 'Request timed out. Please try again.';
            break;
          default:
            errorMsg += 'An unknown error occurred.';
        }
        
        alert(errorMsg);
      },
      // Options
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  } else {
    alert('Geolocation is not supported by your browser.');
  }
} 