/**
 * Chart Manager - Manages chart library and UI controls
 */
export class ChartManager {
  constructor() {
    this.charts = {};
    this.hiddenCharts = [];
    this.settingsPanelOpen = false;
  }
  
  /**
   * Initialize the Chart Manager
   */
  init() {
    console.log("Initializing Chart Manager");
    
    // Initialize the chart library UI
    this.initChartLibrary();
    
    // Handle resize events for charts
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // Initialize print functionality
    this.initPrintFunctionality();
  }
  
  /**
   * Initialize the chart library panel
   */
  initChartLibrary() {
    // Create settings button if it doesn't exist
    let settingsBtn = document.getElementById('dashboardSettingsBtn');
    
    // Make the button container visible
    const headerControls = document.getElementById('dashboardControlButtons');
    if (headerControls) {
      headerControls.classList.remove('hidden');
    }
    
    // If the settings button wasn't in the original HTML, create it
    if (!settingsBtn && headerControls) {
      settingsBtn = document.createElement('button');
      settingsBtn.className = 'px-3 py-1.5 bg-indigo-500 text-white text-sm rounded-md hover:bg-indigo-600 transition-colors duration-150 flex items-center';
      settingsBtn.id = 'dashboardSettingsBtn';
      settingsBtn.innerHTML = `
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        Settings
      `;
      headerControls.appendChild(settingsBtn);
    }
    
    // Create settings panel (initially hidden)
    const settingsPanel = document.createElement('div');
    settingsPanel.id = 'dashboardSettingsPanel';
    settingsPanel.className = 'fixed right-0 top-0 h-full w-96 bg-white shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out z-40 overflow-y-auto';
    settingsPanel.innerHTML = `
      <div class="p-4 bg-gradient-to-r from-indigo-500 to-blue-600 text-white">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold">Settings</h3>
          <button id="closeSettingsBtn" class="text-white hover:text-gray-200">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Dashboard Actions -->
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Dashboard Actions</h4>
        <div class="flex space-x-2">
          <button id="settingsRefreshBtn" class="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh Data
          </button>
          <button id="settingsExportBtn" class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Export Data
          </button>
          <button id="settingsPrintBtn" class="px-3 py-1.5 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print Report
          </button>
        </div>
      </div>
      
      <!-- Chart Library Section -->
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Chart Library</h4>
        <p class="text-sm text-gray-600 mb-3">Restore charts that you've hidden.</p>
        <div id="hiddenChartsList" class="max-h-48 overflow-y-auto">
          <p class="text-sm text-gray-500">No hidden charts</p>
        </div>
      </div>
      
      <!-- Layout Management -->
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Layout Management</h4>
        <button id="settingsSaveLayoutBtn" class="w-full px-3 py-1.5 bg-indigo-100 text-indigo-700 text-sm rounded-md hover:bg-indigo-200 transition-colors duration-150 mb-2 flex items-center justify-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
          </svg>
          Save Current Layout
        </button>
        <button id="settingsRestoreDefaultBtn" class="w-full px-3 py-1.5 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-100 transition-colors duration-150 flex items-center justify-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          Restore Default Layout
        </button>
      </div>
      
      <!-- Print Settings Placeholder - will be filled by PrintManager -->
      <div id="printSettingsContainer"></div>
    `;
    
    document.body.appendChild(settingsPanel);
    
    // Add event listeners
    if (settingsBtn) {
      settingsBtn.addEventListener('click', () => this.toggleSettingsPanel());
    }
    document.getElementById('closeSettingsBtn').addEventListener('click', () => this.toggleSettingsPanel());
    
    // Attach action buttons to existing functionality
    const refreshBtn = document.getElementById('settingsRefreshBtn');
    const exportBtn = document.getElementById('settingsExportBtn');
    const printBtn = document.getElementById('settingsPrintBtn');
    const saveLayoutBtn = document.getElementById('settingsSaveLayoutBtn');
    const restoreDefaultBtn = document.getElementById('settingsRestoreDefaultBtn');
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        // Find and click the main refresh button
        const mainRefreshBtn = document.getElementById('refreshData');
        if (mainRefreshBtn) {
          mainRefreshBtn.click();
        } else if (window.dashboardInstance && typeof window.dashboardInstance.refreshData === 'function') {
          window.dashboardInstance.refreshData();
        }
      });
    }
    
    if (exportBtn) {
      exportBtn.addEventListener('click', () => {
        // Find and click the main export button
        const mainExportBtn = document.getElementById('exportCSV');
        if (mainExportBtn) {
          mainExportBtn.click();
        }
      });
    }
    
    if (printBtn) {
      printBtn.addEventListener('click', () => {
        // Find and click the main print button
        const mainPrintBtn = document.getElementById('printReport');
        if (mainPrintBtn) {
          mainPrintBtn.click();
        }
      });
    }
    
    if (saveLayoutBtn) {
      saveLayoutBtn.addEventListener('click', () => {
        // Save current layout
        if (window.dashboardInstance && typeof window.dashboardInstance.saveCurrentLayout === 'function') {
          window.dashboardInstance.saveCurrentLayout();
        } else {
          this.saveCurrentLayout();
        }
      });
    }
    
    if (restoreDefaultBtn) {
      restoreDefaultBtn.addEventListener('click', () => {
        if (confirm('Are you sure you want to restore the default layout? Any unsaved changes will be lost.')) {
          location.reload();
        }
      });
    }
    
    // Initialize print settings if PrintManager is available
    this.initPrintSettings();
    
    // Always display the Print Report button and Settings button, but hide other buttons
    this.removeMainActionButtons();
  }
  
  /**
   * Remove the original refresh and export buttons from the main UI
   */
  removeMainActionButtons() {
    // Get the buttons
    const refreshBtn = document.getElementById('refreshData');
    const exportBtn = document.getElementById('exportCSV');
    const printBtn = document.getElementById('printReport');
    const settingsBtn = document.getElementById('dashboardSettingsBtn');
    
    // First make all buttons visible (remove hidden class from container)
    const buttonsContainer = document.getElementById('dashboardControlButtons');
    if (buttonsContainer) {
      buttonsContainer.classList.remove('hidden');
    }
    
    // Hide specific buttons
    if (refreshBtn) refreshBtn.classList.add('hidden');
    if (exportBtn) exportBtn.classList.add('hidden');
    
    // Make sure Print Report and Settings buttons are visible
    if (printBtn) printBtn.classList.remove('hidden');
    if (settingsBtn) settingsBtn.classList.remove('hidden');
  }
  
  /**
   * Toggle the settings panel
   */
  toggleSettingsPanel() {
    const panel = document.getElementById('dashboardSettingsPanel');
    if (!panel) return;
    
    this.settingsPanelOpen = !this.settingsPanelOpen;
    
    if (this.settingsPanelOpen) {
      panel.classList.remove('translate-x-full');
      // Add overlay
      const overlay = document.createElement('div');
      overlay.id = 'settingsPanelOverlay';
      overlay.className = 'fixed inset-0 bg-black bg-opacity-25 z-30';
      overlay.addEventListener('click', () => this.toggleSettingsPanel());
      document.body.appendChild(overlay);
    } else {
      panel.classList.add('translate-x-full');
      // Remove overlay
      const overlay = document.getElementById('settingsPanelOverlay');
      if (overlay) overlay.remove();
    }
  }
  
  /**
   * Initialize print settings
   */
  initPrintSettings() {
    const container = document.getElementById('printSettingsContainer');
    if (!container) return;
    
    container.innerHTML = `
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Print Settings</h4>
        <div class="space-y-3">
          <div>
            <label for="printTitle" class="block text-sm font-medium text-gray-700 mb-1">Report Title</label>
            <input type="text" id="printTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="Shipping Analytics Report">
          </div>
          <div>
            <label for="printSubtitle" class="block text-sm font-medium text-gray-700 mb-1">Subtitle</label>
            <input type="text" id="printSubtitle" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" value="Monthly Summary">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Include Sections</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" checked>
                <span class="ml-2 text-sm text-gray-700">Overview</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" checked>
                <span class="ml-2 text-sm text-gray-700">Cost Analysis</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" checked>
                <span class="ml-2 text-sm text-gray-700">Carrier Performance</span>
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500" checked>
                <span class="ml-2 text-sm text-gray-700">Shipping Trends</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    `;
  }
  
  /**
   * Initialize print functionality
   */
  initPrintFunctionality() {
    // Print button event listener
    const printBtn = document.getElementById('printReport');
    if (printBtn) {
      printBtn.addEventListener('click', () => this.printDashboard());
    }
  }
  
  /**
   * Print the dashboard
   */
  printDashboard() {
    // Open print settings or print directly
    const printTitle = document.getElementById('printTitle')?.value || 'Shipping Analytics Report';
    const printSubtitle = document.getElementById('printSubtitle')?.value || 'Monthly Summary';
    
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>${printTitle}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
            }
            .title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 5px;
            }
            .subtitle {
              font-size: 16px;
              color: #666;
              margin-bottom: 20px;
            }
            .date {
              font-size: 14px;
              color: #888;
              margin-bottom: 30px;
            }
            .section {
              margin-bottom: 30px;
              page-break-inside: avoid;
            }
            .section-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 15px;
              padding-bottom: 5px;
              border-bottom: 1px solid #ddd;
            }
            .chart-container {
              margin-bottom: 20px;
              text-align: center;
            }
            .chart-title {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            @media print {
              body {
                padding: 0;
              }
              .section {
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="title">${printTitle}</div>
            <div class="subtitle">${printSubtitle}</div>
            <div class="date">Generated on ${new Date().toLocaleDateString()}</div>
          </div>
          
          <div id="printContent"></div>
        </body>
      </html>
    `);
    
    // Copy dashboard content
    const mainContent = document.getElementById('dashboardContent');
    if (mainContent) {
      // Clone the content
      const contentClone = mainContent.cloneNode(true);
      
      // Remove any non-printing elements
      const nonPrintElements = contentClone.querySelectorAll('.non-print');
      nonPrintElements.forEach(el => el.remove());
      
      // Insert into print window
      printWindow.document.getElementById('printContent').appendChild(contentClone);
      
      // Trigger print
      setTimeout(() => {
        printWindow.print();
        // Close after printing
        printWindow.onafterprint = function() {
          printWindow.close();
        };
      }, 500);
    }
  }
  
  /**
   * Handle window resize event for charts
   */
  handleResize() {
    // Resize all charts
    Object.values(this.charts).forEach(chart => {
      if (chart && typeof chart.render === 'function') {
        chart.render();
      }
    });
  }
  
  /**
   * Save current layout
   */
  saveCurrentLayout() {
    // Implement layout saving
    alert('Layout saved successfully!');
  }
} 