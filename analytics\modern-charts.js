/**
 * modern-charts.js - Enhanced modern charts for the shipping analytics dashboard
 * Using ApexCharts for high-quality, interactive and animated charts
 */

/**
 * Check if ApexCharts is available
 * @returns {boolean} True if ApexCharts is defined
 */
function isApexChartsAvailable() {
  if (typeof ApexCharts === 'undefined') {
    console.error('ApexCharts is not defined. Charts cannot be rendered.');
    return false;
  }
  return true;
}

/**
 * Fallback chart display when ApexCharts is not available
 * @param {HTMLElement} container - The container element
 * @param {string} message - The message to display
 */
function renderFallbackMessage(container, message = 'Chart could not be rendered. Library not available.') {
  if (!container) return;
  
  container.innerHTML = `
    <div class="p-4 text-center">
      <div class="text-red-500 mb-2">⚠️ Chart Error</div>
      <div class="text-gray-700">${message}</div>
    </div>
  `;
}

/**
 * Create a modern line chart with smooth animations
 * @param {Object} options Configuration options
 * @returns {ApexCharts|null} ApexCharts instance or null if error
 */
export function createModernLineChart(options) {
  // Check if ApexCharts is available
  if (!isApexChartsAvailable()) {
    if (options && options.container) {
      renderFallbackMessage(options.container);
    }
    return null;
  }

  const defaultOptions = {
    container: null,
    series: [],
    height: 350,
    title: '',
    xaxisType: 'category',
    xaxisTitle: '',
    yaxisTitle: '',
    colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
    gradientToOpacity: 0.2,
    strokeWidth: 3,
    markerSize: 4,
    animate: true,
    showDataLabels: false,
    tooltip: {},
    fillGradient: true,
    curve: 'smooth'
  };

  // Merge default options with provided options
  const config = { ...defaultOptions, ...options };
  
  // Ensure container exists
  if (!config.container) {
    console.error('Container element is required for chart rendering');
    return null;
  }
  
  try {
    // Configure chart options
    const chartOptions = {
      series: config.series,
      chart: {
        height: config.height,
        type: 'area',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: config.animate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      title: {
        text: config.title,
        align: 'left',
        style: {
          fontSize: '16px',
          fontWeight: 500,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#111827'
        }
      },
      colors: config.colors,
      dataLabels: {
        enabled: config.showDataLabels
      },
      stroke: {
        curve: config.curve,
        width: config.strokeWidth
      },
      fill: {
        type: config.fillGradient ? 'gradient' : 'solid',
        gradient: {
          shadeIntensity: 1,
          opacityFrom: 0.7,
          opacityTo: config.gradientToOpacity,
          stops: [0, 90, 100]
        }
      },
      markers: {
        size: config.markerSize,
        strokeWidth: 0,
        hover: {
          size: config.markerSize + 2
        }
      },
      xaxis: {
        type: config.xaxisType,
        categories: config.categories || [],
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        title: {
          text: config.xaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      yaxis: {
        title: {
          text: config.yaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        labels: {
          formatter: config.yFormatter,
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: 0.5
        }
      },
      tooltip: {
        ...config.tooltip
      }
    };

    // Clear container
    config.container.innerHTML = '';
    
    // Create chart
    const chart = new ApexCharts(config.container, chartOptions);
    chart.render();
    
    return chart;
  } catch (error) {
    console.error('Error creating line chart:', error);
    renderFallbackMessage(config.container, `Chart error: ${error.message}`);
    return null;
  }
}

/**
 * Create a modern bar chart with animations
 * @param {Object} options Configuration options
 * @returns {ApexCharts|null} ApexCharts instance or null if error
 */
export function createModernBarChart(options) {
  // Check if ApexCharts is available
  if (!isApexChartsAvailable()) {
    if (options && options.container) {
      renderFallbackMessage(options.container);
    }
    return null;
  }

  const defaultOptions = {
    container: null,
    series: [],
    height: 350,
    title: '',
    xaxisTitle: '',
    yaxisTitle: '',
    colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
    animate: true,
    horizontal: false,
    stacked: false,
    distributed: false,
    showDataLabels: false,
    tooltip: {},
    columnWidth: '70%',
    borderRadius: 4,
    categories: []
  };

  // Merge options
  const config = { ...defaultOptions, ...options };
  
  if (!config.container) {
    console.error('Container element is required for chart rendering');
    return null;
  }
  
  try {
    // Configure chart options
    const chartOptions = {
      series: config.series,
      chart: {
        type: config.horizontal ? 'bar' : 'column',
        height: config.height,
        stacked: config.stacked,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: config.animate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      title: {
        text: config.title,
        align: 'left',
        style: {
          fontSize: '16px',
          fontWeight: 500,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#111827'
        }
      },
      plotOptions: {
        bar: {
          horizontal: config.horizontal,
          columnWidth: config.columnWidth,
          barHeight: config.horizontal ? '70%' : undefined,
          borderRadius: config.borderRadius,
          distributed: config.distributed,
          dataLabels: {
            position: config.horizontal ? 'top' : 'center'
          }
        }
      },
      dataLabels: {
        enabled: config.showDataLabels,
        formatter: config.dataLabelsFormatter,
        style: {
          fontSize: '12px',
          colors: [config.horizontal ? '#fff' : '#000']
        }
      },
      colors: config.colors,
      stroke: {
        width: 1,
        colors: ['#fff']
      },
      xaxis: {
        categories: config.categories,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        title: {
          text: config.xaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      yaxis: {
        title: {
          text: config.yaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        labels: {
          formatter: config.yFormatter,
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: "vertical",
          shadeIntensity: 0.2,
          gradientToColors: undefined,
          inverseColors: true,
          opacityFrom: 0.85,
          opacityTo: 1,
          stops: [0, 100]
        }
      },
      grid: {
        borderColor: '#e5e7eb',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: 0.5
        }
      },
      tooltip: {
        ...config.tooltip
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        offsetY: 5
      }
    };

    // Clear container
    config.container.innerHTML = '';
    
    // Create chart
    const chart = new ApexCharts(config.container, chartOptions);
    chart.render();
    
    return chart;
  } catch (error) {
    console.error('Error creating bar chart:', error);
    renderFallbackMessage(config.container, `Chart error: ${error.message}`);
    return null;
  }
}

/**
 * Create a modern pie or donut chart
 * @param {Object} options Configuration options
 * @returns {ApexCharts|null} ApexCharts instance or null if error
 */
export function createModernPieChart(options) {
  // Check if ApexCharts is available
  if (!isApexChartsAvailable()) {
    if (options && options.container) {
      renderFallbackMessage(options.container);
    }
    return null;
  }

  const defaultOptions = {
    container: null,
    series: [],
    labels: [],
    height: 350,
    title: '',
    colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', 
             '#ec4899', '#6366f1', '#14b8a6', '#f97316', '#8b5cf6'],
    animate: true,
    donut: false,
    showDataLabels: true,
    showLegend: true,
    tooltip: {}
  };

  // Merge options
  const config = { ...defaultOptions, ...options };
  
  if (!config.container) {
    console.error('Container element is required for chart rendering');
    return null;
  }
  
  try {
    // Configure chart options
    const chartOptions = {
      series: config.series,
      labels: config.labels,
      chart: {
        type: config.donut ? 'donut' : 'pie',
        height: config.height,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        animations: {
          enabled: config.animate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      title: {
        text: config.title,
        align: 'center',
        style: {
          fontSize: '16px',
          fontWeight: 500,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#111827'
        }
      },
      colors: config.colors,
      dataLabels: {
        enabled: config.showDataLabels,
        formatter: config.dataLabelsFormatter,
        style: {
          fontSize: '12px',
          fontFamily: 'Inter, Segoe UI, sans-serif',
          fontWeight: 'normal',
          colors: ['#fff']
        },
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: true,
          top: 1,
          left: 1,
          blur: 2,
          opacity: 0.45
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: config.donut ? '65%' : '0%',
            labels: {
              show: config.donut,
              name: {
                show: true,
                fontSize: '16px',
                fontFamily: 'Inter, Segoe UI, sans-serif',
                fontWeight: 600
              },
              value: {
                show: true,
                fontSize: '22px',
                fontFamily: 'Inter, Segoe UI, sans-serif',
                fontWeight: 400,
                formatter: config.valueFormatter
              },
              total: {
                show: config.donut,
                label: 'Total',
                fontSize: '16px',
                fontFamily: 'Inter, Segoe UI, sans-serif'
              }
            }
          }
        }
      },
      legend: {
        show: config.showLegend,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        offsetY: 10
      },
      stroke: {
        width: 2,
        colors: undefined
      },
      tooltip: {
        ...config.tooltip
      }
    };

    // Clear container
    config.container.innerHTML = '';
    
    // Create chart
    const chart = new ApexCharts(config.container, chartOptions);
    chart.render();
    
    return chart;
  } catch (error) {
    console.error('Error creating pie chart:', error);
    renderFallbackMessage(config.container, `Chart error: ${error.message}`);
    return null;
  }
}

/**
 * Create a modern map visualization for geographic data
 * @param {Object} options Configuration options
 */
export function createMapVisualization(options) {
  // Check if maplibregl is available
  if (typeof maplibregl === 'undefined') {
    if (options && options.container) {
      renderFallbackMessage(options.container, 'Map cannot be rendered. Mapping library not available.');
    }
    return null;
  }

  const defaultOptions = {
    container: null,
    data: [],
    height: 400,
    title: ''
  };

  // Merge options
  const config = { ...defaultOptions, ...options };
  
  if (!config.container || !config.container.id) {
    console.error('Container element with ID is required for map rendering');
    return null;
  }
  
  // Create title if specified
  if (config.title) {
    const titleElement = document.createElement('h3');
    titleElement.className = 'text-base font-semibold text-gray-700 mb-2 text-center';
    titleElement.textContent = config.title;
    config.container.appendChild(titleElement);
  }
  
  // Create map container
  const mapElement = document.createElement('div');
  mapElement.style.height = `${config.height}px`;
  mapElement.className = 'w-full rounded-lg overflow-hidden';
  config.container.appendChild(mapElement);
  
  // Initialize the map
  const map = new maplibregl.Map({
    container: mapElement,
    style: 'https://api.maptiler.com/maps/basic-v2/style.json?key=get_your_own_key',
    center: [0, 20],
    zoom: 1
  });
  
  // Add navigation controls
  map.addControl(new maplibregl.NavigationControl(), 'top-right');
  
  // Add data to map when it loads
  map.on('load', () => {
    // Process data to GeoJSON format
    const points = {
      type: 'FeatureCollection',
      features: config.data.map(item => ({
        type: 'Feature',
        properties: {
          count: item.count,
          name: item.country,
          description: `${item.count} shipments`
        },
        geometry: {
          type: 'Point',
          coordinates: item.coordinates || [0, 0]
        }
      }))
    };
    
    // Add data source
    map.addSource('shipments', {
      type: 'geojson',
      data: points
    });
    
    // Add circles layer
    map.addLayer({
      id: 'shipment-points',
      type: 'circle',
      source: 'shipments',
      paint: {
        'circle-radius': [
          'interpolate', ['linear'], ['get', 'count'],
          1, 5,
          10, 10,
          100, 20,
          1000, 30
        ],
        'circle-color': '#8b5cf6',
        'circle-opacity': 0.7,
        'circle-stroke-width': 1,
        'circle-stroke-color': '#fff'
      }
    });
    
    // Add click event for popup
    map.on('click', 'shipment-points', (e) => {
      const props = e.features[0].properties;
      
      new maplibregl.Popup()
        .setLngLat(e.lngLat)
        .setHTML(`<h3 class="font-medium">${props.name}</h3><p>${props.description}</p>`)
        .addTo(map);
    });
    
    // Change cursor when hovering over points
    map.on('mouseenter', 'shipment-points', () => {
      map.getCanvas().style.cursor = 'pointer';
    });
    
    map.on('mouseleave', 'shipment-points', () => {
      map.getCanvas().style.cursor = '';
    });
  });
  
  return map;
}

/**
 * Create a modern mixed chart (line + bar)
 * @param {Object} options Configuration options
 * @returns {ApexCharts|null} ApexCharts instance or null if error
 */
export function createMixedChart(options) {
  // Check if ApexCharts is available
  if (!isApexChartsAvailable()) {
    if (options && options.container) {
      renderFallbackMessage(options.container);
    }
    return null;
  }

  const defaultOptions = {
    container: null,
    series: [],
    height: 350,
    title: '',
    xaxisTitle: '',
    yaxisTitle: '',
    colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
    animate: true,
    stacked: false,
    showDataLabels: false,
    tooltip: {},
    categories: []
  };

  // Merge options
  const config = { ...defaultOptions, ...options };
  
  if (!config.container) {
    console.error('Container element is required for chart rendering');
    return null;
  }
  
  try {
    // Configure chart options
    const chartOptions = {
      series: config.series,
      chart: {
        height: config.height,
        type: 'line',
        stacked: config.stacked,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        toolbar: {
          show: false
        },
        animations: {
          enabled: config.animate,
          easing: 'easeinout',
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350
          }
        }
      },
      title: {
        text: config.title,
        align: 'left',
        style: {
          fontSize: '16px',
          fontWeight: 500,
          fontFamily: 'Inter, Segoe UI, sans-serif',
          color: '#111827'
        }
      },
      colors: config.colors,
      stroke: {
        width: config.strokeWidth || [0, 3],
        curve: 'smooth'
      },
      plotOptions: {
        bar: {
          columnWidth: '60%',
          borderRadius: 4
        }
      },
      fill: {
        opacity: [0.85, 1],
        type: ['gradient', 'solid'],
        gradient: {
          shade: 'light',
          type: "vertical",
          shadeIntensity: 0.25,
          gradientToColors: undefined,
          inverseColors: true,
          opacityFrom: 1,
          opacityTo: 0.7,
          stops: [0, 100]
        }
      },
      markers: {
        size: 4,
        strokeWidth: 0,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: config.categories,
        labels: {
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        title: {
          text: config.xaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      yaxis: config.yaxis || {
        title: {
          text: config.yaxisTitle,
          style: {
            fontSize: '13px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        },
        labels: {
          formatter: config.yFormatter,
          style: {
            fontSize: '12px',
            fontFamily: 'Inter, Segoe UI, sans-serif'
          }
        }
      },
      tooltip: {
        shared: true,
        intersect: false,
        ...config.tooltip
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        offsetY: 5
      },
      grid: {
        borderColor: '#e5e7eb',
        row: {
          colors: ['transparent', 'transparent'],
          opacity: 0.5
        }
      }
    };

    // Clear container
    config.container.innerHTML = '';
    
    // Create chart
    const chart = new ApexCharts(config.container, chartOptions);
    chart.render();
    
    return chart;
  } catch (error) {
    console.error('Error creating mixed chart:', error);
    renderFallbackMessage(config.container, `Chart error: ${error.message}`);
    return null;
  }
} 