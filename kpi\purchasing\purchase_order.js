// Purchase Order component for KPI Dashboard
import { connectionManager } from '../../core/connection.js';
import { NotificationSystem } from '../../core/notifications.js';

export class PurchaseOrderComponent {
  constructor(container) {
    this.container = container;
    this.poData = [];
    this.filteredPOs = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'date';
    this.sortDirection = 'desc';
    this.filterStatus = 'all';
    this.isLoading = true;
    this.dbName = 'purchaseOrdersDb';
    this.storeName = 'purchaseOrders';
    this.settingsStoreName = 'appSettings'; // New store for app settings
    this.dateRange = {
      start: null,
      end: null
    };
    this.notificationSystem = new NotificationSystem();
    
    // Currency conversion settings
    this.currencyConversion = {
      enabled: true,
      fromCurrency: 'USD',
      toCurrency: 'CAD',
      rate: 1.38 // Default rate USD to CAD
    };
  }

  async init() {
    console.log("Initializing Purchase Order component");
    
    // Only use a single loading indicator
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
    
      try {
        // Load settings including currency conversion
        await this.loadSettings();
      } catch (settingsError) {
        console.error("Error loading settings, continuing with defaults:", settingsError);
        // Continue with default settings if there's an error
      }
      
      // Load data
      await this.loadData();
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing purchase orders:", error);
      this.isLoading = false; // Make sure to clear loading state on error
      this.showError("Failed to initialize: " + error.message);
      this.render(); // Make sure UI is updated even on error
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open with current version first (don't upgrade yet)
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        const currentVersion = db.version;
        db.close();
        
        // Now open with the correct version based on what we found
        const version = this.settingsStoreName && !db.objectStoreNames.contains(this.settingsStoreName) ? 
          Math.max(currentVersion + 1, 2) : currentVersion;
        
        const request = indexedDB.open(this.dbName, version);

        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open purchase orders database"));
        };

        request.onsuccess = (event) => {
          console.log("Successfully opened purchase orders database");
          resolve();
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create object store for purchase orders if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices for common search and sort operations
            store.createIndex("orderNbr", "orderNbr", { unique: false });
            store.createIndex("vendorId", "vendorId", { unique: false });
            store.createIndex("date", "date", { unique: false });
            store.createIndex("status", "status", { unique: false });
            store.createIndex("convertedTotal", "convertedTotal", { unique: false }); // New index for converted total
          }
          
          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          }
          
          console.log("Purchase orders database schema upgraded to version", db.version);
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database version:", event.target.error);
        reject(new Error("Could not check database version"));
      };
    });
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true; // Set loading state but don't render yet to avoid duplicate indicators
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest purchase orders from Acumatica");
        try {
          const result = await this.fetchAcumaticaPOs(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.poData = this.parseAcumaticaPOs(result.data);
            await this.storePOsInIndexedDB(this.poData);
            console.log(`Refreshed and stored ${this.poData.length} purchase orders in IndexedDB`);
            
            // Continue with the rest of the data loading logic
            await this.lookupVendorNames();
            this.filteredPOs = [...this.poData];
            this.calculateTotalPages();
            this.isLoading = false;
            return; // Exit early since we've successfully refreshed
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to normal loading behavior as fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing POs from Acumatica:", fetchError);
          // Fall through to normal loading behavior as fallback
        }
      }
      
      // If not forcing refresh or Acumatica refresh failed, follow normal loading flow
      // If not connected to Acumatica, or if offline and we have data in IndexedDB
      if (!connectionStatus.acumatica.isConnected || (!forceRefresh && !navigator.onLine)) {
        // Try to get data from IndexedDB
        this.poData = await this.getPOsFromIndexedDB();
        
        // If no data in IndexedDB, generate sample data
        if (this.poData.length === 0) {
          console.log("No purchase orders in IndexedDB, generating sample data");
          this.poData = this.generateSampleData();
        }
      } else {
        // Connected to Acumatica, fetch real data
        console.log("Fetching purchase orders from Acumatica");
        try {
          const result = await this.fetchAcumaticaPOs(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.poData = this.parseAcumaticaPOs(result.data);
            await this.storePOsInIndexedDB(this.poData);
            console.log(`Stored ${this.poData.length} purchase orders in IndexedDB`);
          } else {
            // If error fetching from Acumatica, try IndexedDB
            console.warn("Error fetching from Acumatica, trying IndexedDB:", result.error);
            this.poData = await this.getPOsFromIndexedDB();
            
            // If still no data, generate sample data
            if (this.poData.length === 0) {
              this.poData = this.generateSampleData();
            }
          }
        } catch (fetchError) {
          console.error("Error fetching POs from Acumatica:", fetchError);
          // Try to get data from IndexedDB as fallback
          this.poData = await this.getPOsFromIndexedDB();
          
          // If no data in IndexedDB, generate sample data
          if (this.poData.length === 0) {
            this.poData = this.generateSampleData();
          }
        }
      }
      
      // Look up vendor names for all purchase orders from the vendors database
      await this.lookupVendorNames();
      
      // Apply filters
      this.filteredPOs = [...this.poData];
      this.calculateTotalPages();
      
      this.isLoading = false; // Clear loading state but don't render yet
    } catch (error) {
      console.error('Error loading purchase order data:', error);
      this.poData = this.generateSampleData();
      this.filteredPOs = [...this.poData];
      this.calculateTotalPages();
      this.isLoading = false;
    }
  }

  async fetchAcumaticaPOs(instance) {
    try {
      // Set fixed date range for 2024-2026 instead of using relative dates
      const startDate = '2024-01-01';
      const endDate = '2026-12-31';
      
      // Build Acumatica API URL for purchase orders
      const apiUrl = `${instance}/entity/Default/22.200.001/PurchaseOrder?$expand=Details&$filter=Date ge datetimeoffset'${startDate}T00:00:00Z' and Date lt datetimeoffset'${endDate}T23:59:59Z'`;
      
      console.log("Fetching POs with URL:", apiUrl);
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        throw new Error(`Failed to fetch purchase orders: ${response.status} ${response.statusText}`);
      }
      
      // Parse response
      const data = await response.json();
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching purchase orders from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }

  parseAcumaticaPOs(poData) {
    try {
      // Process PO data from Acumatica
      return poData.map(po => {
        // Function to parse date string and normalize to UTC midnight
        const parseAndNormalizeDate = (dateString) => {
          if (!dateString || typeof dateString !== 'string') return null;
          
          try {
            // Extract YYYY-MM-DD part
            const datePart = dateString.substring(0, 10); // e.g., "2024-03-31"
            
            // Validate the format (basic check)
            if (!/^\d{4}-\d{2}-\d{2}$/.test(datePart)) {
                console.warn(`Invalid date format extracted: ${datePart} from ${dateString}`);
                return null;
            }

            // Create a Date object representing midnight UTC for that day
            const date = new Date(`${datePart}T00:00:00.000Z`); 
            
            if (isNaN(date.getTime())) {
              console.warn(`Invalid date created from string: ${datePart}T00:00:00.000Z`);
              return null;
            }
            return date;
          } catch(e) {
            console.error(`Error parsing date: ${dateString}`, e);
            return null;
          }
        };

        // Extract main PO fields
        const poId = po.id;
        const orderNbr = po.OrderNbr?.value || '';
        // Use the new parsing function
        const date = parseAndNormalizeDate(po.Date?.value); // Fallback handled within function
        const promisedDate = parseAndNormalizeDate(po.PromisedOn?.value);
        const vendorId = po.VendorID?.value || '';
        const status = po.Status?.value || 'Unknown';
        const description = po.Description?.value || '';
        const terms = po.Terms?.value || '';
        const currencyId = po.CurrencyID?.value || 'USD';
        
        // Calculate total - ensure it's a number
        const total = parseFloat(po.OrderTotal?.value || 0);
        const lineTotal = parseFloat(po.LineTotal?.value || 0);
        const taxTotal = parseFloat(po.TaxTotal?.value || 0);
        
        // Calculate converted total
        let convertedTotal = total;
        // Only convert if currency is different from target currency and conversion is enabled
        if (currencyId !== this.currencyConversion.toCurrency && this.currencyConversion.enabled) {
          convertedTotal = total * this.currencyConversion.rate;
        }
        
        // Extract line items
        const items = Array.isArray(po.Details) ? po.Details.length : 0;
        // Use the new parsing function for lastModified
        const lastModified = parseAndNormalizeDate(po.LastModifiedDateTime?.value) || date; // Fallback to PO date if missing/invalid

        // Parse into our standard PO format
        return {
          id: poId,
          orderNbr: orderNbr,
          date: date, // Normalized UTC midnight date
          promisedDate: promisedDate, // Normalized UTC midnight date
          vendorId: vendorId,
          vendor: '', // This will be filled in by lookupVendorNames()
          total: total, // Ensure it's a number
          lineTotal: lineTotal,
          taxTotal: taxTotal,
          currency: currencyId,
          convertedTotal: convertedTotal, // Add converted total
          convertedCurrency: this.currencyConversion.toCurrency, // Target currency
          customRate: null, // Add custom rate field (null means use global rate)
          status: this.mapAcumaticaStatus(status),
          description: description,
          terms: terms,
          items: items,
          lastModified: lastModified, // Normalized UTC midnight date
          lineItems: Array.isArray(po.Details) ? po.Details.map(item => ({
            lineNbr: item.LineNbr?.value || 0,
            inventoryId: item.InventoryID?.value || '',
            description: item.LineDescription?.value || item.Description?.value || '',
            quantity: parseFloat(item.OrderQty?.value || 0),
            unitCost: parseFloat(item.UnitCost?.value || 0),
            extendedCost: parseFloat(item.ExtendedCost?.value || 0),
            uom: item.UOM?.value || '',
            received: parseFloat(item.QtyOnReceipts?.value || 0),
            warehouse: item.WarehouseID?.value || '',
            // Use timezone-aware parsing for line item dates
            promisedDate: parseAndNormalizeDate(item.Promised?.value) // Normalized UTC midnight date
          })) : []
        };
      });
    } catch (error) {
      console.error("Error parsing Acumatica POs:", error);
      return [];
    }
  }

  mapAcumaticaStatus(status) {
    // Map Acumatica status to our standard statuses
    switch (status.toLowerCase()) {
      case 'open':
        return 'Open';
      case 'pending approval':
      case 'pending processing':
        return 'Pending';
      case 'completed':
        return 'Received';
      case 'rejected':
      case 'voided':
        return 'Cancelled';
      default:
        return status;
    }
  }

  async getPOsFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open purchase orders database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Purchase orders store not found in database"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const pos = getAllRequest.result;
          // Parse stored date strings (YYYY-MM-DD) back into Date objects (UTC midnight)
          pos.forEach(po => {
            try {
               const parseStoredDate = (dateStr) => {
                 if (!dateStr || typeof dateStr !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                   return null; // Return null if invalid format or missing
                 }
                 // Construct Date object assuming the string is YYYY-MM-DD representing UTC midnight
                 const date = new Date(`${dateStr}T00:00:00.000Z`);
                 return isNaN(date.getTime()) ? null : date;
               };

               po.date = parseStoredDate(po.date);
               po.lastModified = parseStoredDate(po.lastModified);
               po.promisedDate = parseStoredDate(po.promisedDate);
               po.actualDelivery = parseStoredDate(po.actualDelivery);
               
               // Ensure total is a number
               po.total = parseFloat(po.total) || 0;
            } catch (dateError) {
               console.warn(`Error parsing stored dates/total for PO ID ${po.id}:`, dateError, po);
               // Handle invalid data gracefully, maybe mark the PO or set dates to null
               po.date = null; // Set to null on error
            }
          });
          
          // Filter out any POs that failed date parsing (now checking for null)
          const validPOs = pos.filter(po => po.date instanceof Date); // Check if date is a valid Date object
          console.log(`Retrieved ${pos.length} POs, returning ${validPOs.length} with valid dates.`);
          resolve(validPOs);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving purchase orders:", event.target.error);
          reject(new Error("Failed to retrieve purchase orders from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving POs:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storePOsInIndexedDB(pos) {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database for storing:", event.target.error);
        reject(new Error("Could not open purchase orders database for storing"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for writing, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          reject(new Error("Purchase orders store not found for storing"));
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readwrite");
        const store = transaction.objectStore(this.storeName);
        let count = 0;
        let errorCount = 0;
        const totalPOs = pos.length;
        
        // Clear existing data if this is a fresh load
        store.clear().onsuccess = () => {
           console.log(`Cleared existing PO data. Storing ${totalPOs} new POs.`);
           if (totalPOs === 0) {
               resolve(); // Nothing more to do
               db.close();
               return;
           }

          // Add each PO, converting dates to YYYY-MM-DD strings for storage
          pos.forEach(originalPO => {
             // Create a copy to avoid modifying the original object in memory
             const poToStore = { ...originalPO };

             // Convert Date objects (assumed UTC midnight) to YYYY-MM-DD strings
             try {
               const formatDateToUTCString = (dateObj) => {
                   if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
                       return null; // Return null if invalid or not a Date
                   }
                   // Extract UTC components and format as YYYY-MM-DD
                   const year = dateObj.getUTCFullYear();
                   const month = (dateObj.getUTCMonth() + 1).toString().padStart(2, '0');
                   const day = dateObj.getUTCDate().toString().padStart(2, '0');
                   return `${year}-${month}-${day}`;
               };

               poToStore.date = formatDateToUTCString(poToStore.date);
               poToStore.lastModified = formatDateToUTCString(poToStore.lastModified);
               poToStore.promisedDate = formatDateToUTCString(poToStore.promisedDate);
               poToStore.actualDelivery = formatDateToUTCString(poToStore.actualDelivery);

               if (!poToStore.date) {
                   console.warn(`Invalid or missing date for PO ${poToStore.id}, storing date as null.`);
               }

               // Ensure total is a number
               poToStore.total = parseFloat(poToStore.total) || 0;

             } catch (conversionError) {
                 console.error(`Error converting dates to string for PO ${poToStore.id}:`, conversionError);
                 poToStore.date = null; // Ensure date is null on error
                 errorCount++;
                 if (count + errorCount === totalPOs) { 
                   db.close();
                   resolve(); 
                 } // Check completion
                 // Decide if we should skip or store with null dates - storing with null for now
                 // return; // Uncomment this line to skip adding this PO entirely on error
             }
            
            const addRequest = store.add(poToStore);
            
            addRequest.onsuccess = () => {
              count++;
              if (count + errorCount === totalPOs) {
                console.log(`Successfully stored ${count} purchase orders.`);
                db.close();
                resolve(); // Resolve after all operations attempted
              }
            };
            
            addRequest.onerror = (event) => {
              console.error("Error storing purchase order:", poToStore.id, event.target.error);
              errorCount++;
              if (count + errorCount === totalPOs) {
                 console.warn(`Finished storing with ${errorCount} errors.`);
                 db.close();
                 resolve(); // Resolve even if errors occurred
              }
            };
          });
        };
        
        store.clear().onerror = (event) => {
          console.error("Error clearing existing PO data:", event.target.error);
          db.close();
          reject(new Error("Failed to clear existing purchase orders"));
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error storing POs:", event.target.error);
          db.close();
          reject(new Error("Failed to store purchase orders"));
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during write operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async lookupVendorNames() {
    try {
      // Open the vendors database to look up names
      const vendorDb = await this.openVendorDatabase();
      
      if (!vendorDb) {
        console.warn("Could not open vendor database for vendor name lookup");
        // Make sure POs have a vendor name even if lookup fails
        this.poData.forEach(po => {
          po.vendor = po.vendorId || 'Unknown Vendor';
        });
        return;
      }
      
      console.log("Available object stores:", Array.from(vendorDb.objectStoreNames));
      
      // Check if the vendors store exists - should be "vendors" to match vendor-metrics.js
      if (!vendorDb.objectStoreNames.contains("vendors")) {
        console.warn("Vendors object store not found in database");
        // Fall back to showing vendor IDs as names
        this.poData.forEach(po => {
          po.vendor = po.vendorId || 'Unknown Vendor';
        });
        vendorDb.close();
        return;
      }
      
      try {
        // Create a transaction and get the vendors store
        const transaction = vendorDb.transaction(["vendors"], "readonly");
        const vendorStore = transaction.objectStore("vendors");
        
        // Find the right index to use - in vendor-metrics.js it should have a "vendorId" index
        if (vendorStore.indexNames.contains("vendorId")) {
          const vendorIndex = vendorStore.index("vendorId");
          
          // Create a lookup promise for each PO
          const lookupPromises = this.poData.map(po => {
            return new Promise((resolve) => {
              if (!po.vendorId) {
                po.vendor = 'Unknown Vendor';
                resolve();
                return;
              }
              
              const vendorRequest = vendorIndex.get(po.vendorId);
              
              vendorRequest.onsuccess = () => {
                const vendor = vendorRequest.result;
                if (vendor) {
                  po.vendor = vendor.name;
                  console.log(`Found vendor name for ${po.vendorId}: ${vendor.name}`);
                } else {
                  // If vendor not found, keep the ID as the display name
                  po.vendor = po.vendorId;
                  console.log(`No vendor found for ID: ${po.vendorId}`);
                }
                resolve();
              };
              
              vendorRequest.onerror = (event) => {
                // On error, keep the ID as the display name
                console.error("Error looking up vendor:", event.target.error);
                po.vendor = po.vendorId;
                resolve();
              };
            });
          });
          
          // Wait for all lookups to complete
          await Promise.all(lookupPromises);
        } else {
          // No vendorId index found, try to use getAll instead
          console.warn("No vendorId index found in vendors store, using getAll fallback");
          
          const getAllRequest = vendorStore.getAll();
          
          getAllRequest.onsuccess = () => {
            const vendors = getAllRequest.result;
            console.log(`Got ${vendors.length} vendors from database`);
            
            // Create a map of vendorId to vendor name for faster lookup
            const vendorMap = new Map();
            vendors.forEach(vendor => {
              if (vendor.vendorId) {
                vendorMap.set(vendor.vendorId, vendor.name);
              }
            });
            
            // Update PO data with vendor names
            this.poData.forEach(po => {
              po.vendor = vendorMap.get(po.vendorId) || po.vendorId || 'Unknown Vendor';
            });
          };
          
          getAllRequest.onerror = (event) => {
            console.error("Error getting all vendors:", event.target.error);
            this.poData.forEach(po => {
              po.vendor = po.vendorId || 'Unknown Vendor';
            });
          };
          
          // Wait for the transaction to complete
          await new Promise(resolve => {
            transaction.oncomplete = () => resolve();
            transaction.onerror = () => resolve();
          });
        }
      } catch (txError) {
        console.error("Transaction error in vendor lookup:", txError);
        this.poData.forEach(po => {
          po.vendor = po.vendorId || 'Unknown Vendor';
        });
      }
      
      // Close the database
      vendorDb.close();
    } catch (error) {
      console.error("Error looking up vendor names:", error);
      // Make sure we have fallback vendor names
      this.poData.forEach(po => {
        po.vendor = po.vendorId || 'Unknown Vendor';
      });
    }
  }

  async openVendorDatabase() {
    return new Promise((resolve) => {
      // Use exactly the same database name as in vendor-metrics.js
      const vendorDbName = 'vendorMetricsDB'; // Note the uppercase 'DB' - matches vendor-metrics.js
      
      try {
        // Open without specifying version to use existing version
        const request = indexedDB.open(vendorDbName);
        
        request.onerror = (event) => {
          console.warn("Could not open vendor database:", event.target.error);
          resolve(null);
        };
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Successfully opened vendor database, version:", db.version, "object stores:", Array.from(db.objectStoreNames));
          resolve(db);
        };
        
        request.onupgradeneeded = (event) => {
          // This event shouldn't run if the database already exists
          console.log("Vendor database upgrade needed");
          event.target.transaction.abort();
          resolve(null);
        };
      } catch (error) {
        console.error("Error in openVendorDatabase:", error);
        resolve(null);
      }
    });
  }

  showLoading() {
    this.isLoading = true;
    
    // Check if loading indicator already exists
    if (document.getElementById('po-loading-indicator')) {
      return; // Already showing
    }
    
    const loadingElement = document.createElement('div');
    loadingElement.id = 'po-loading-indicator';
    loadingElement.className = 'fixed top-0 left-0 right-0 bottom-0 w-full h-full z-50 overflow-hidden bg-gray-700 bg-opacity-50 flex flex-col items-center justify-center';
    loadingElement.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg flex flex-col items-center">
        <div class="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-2 text-gray-600 dark:text-gray-300">Loading purchase orders...</p>
      </div>
    `;
    document.body.appendChild(loadingElement);
  }

  hideLoading() {
    this.isLoading = false;
    
    // Check for loading indicator
    const loadingElement = document.getElementById('po-loading-indicator');
    if (loadingElement && loadingElement.parentNode) {
      loadingElement.parentNode.removeChild(loadingElement);
    }
  }

  showError(message) {
    this.hideError(); // Clear any existing errors first
    
    const errorElement = document.createElement('div');
    errorElement.id = 'po-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          this.hideError();
        });
      }
    } else {
      console.error("Container not available to show error:", message);
      alert("Error: " + message);
    }
  }

  hideError() {
    const errorElement = document.getElementById('po-error-message');
    if (errorElement) {
      errorElement.remove();
    }
  }

  refreshData() {
    // Don't show a separate loading indicator here - loadData already handles it
    
    // Force refresh from Acumatica
    this.loadData(true)
      .then(() => {
        this.notificationSystem.addNotification("Purchase order data refreshed successfully", "success");
      })
      .catch(error => {
        this.notificationSystem.addNotification("Error refreshing data: " + error.message, "error");
      });
  }

  generateSampleData() {
    const statuses = ['Open', 'Pending', 'Received', 'Cancelled'];
    const vendors = [
      { id: 'VENDOR1', name: 'Acme Supplies' },
      { id: 'VENDOR2', name: 'Best Materials Inc' },
      { id: 'VENDOR3', name: 'Century Components' },
      { id: 'VENDOR4', name: 'Delta Manufacturing' },
      { id: 'VENDOR5', name: 'Echo Industries' }
    ];
    
    const sampleData = [];
    const today = new Date();
    
    for (let i = 1; i <= 48; i++) {
      const date = new Date(today);
      // Generate dates within the last ~1.5 years to match analytics mock data range
      date.setDate(today.getDate() - Math.floor(Math.random() * 365 * 1.5));
      const vendorInfo = vendors[Math.floor(Math.random() * vendors.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const total = Math.round(Math.random() * 9000 + 1000); // Ensure total is number
      
      // Randomly assign currency, with higher chance of USD
      const currencyOptions = ['USD', 'CAD', 'EUR'];
      const currencyWeights = [0.6, 0.3, 0.1]; // 60% USD, 30% CAD, 10% EUR
      const randomVal = Math.random();
      let cumulativeWeight = 0;
      let currency = 'USD';
      
      for (let j = 0; j < currencyOptions.length; j++) {
        cumulativeWeight += currencyWeights[j];
        if (randomVal <= cumulativeWeight) {
          currency = currencyOptions[j];
          break;
        }
      }
      
      // Calculate converted total based on currency
      let convertedTotal = total;
      if (currency !== this.currencyConversion.toCurrency && this.currencyConversion.enabled) {
        convertedTotal = total * this.currencyConversion.rate;
      }

      // Occasionally add a custom rate (10% of POs)
      const hasCustomRate = Math.random() < 0.1;
      const customRate = hasCustomRate ? this.currencyConversion.rate * (0.9 + Math.random() * 0.2) : null; // ±10% of global rate

      let promisedDate = new Date(date);
      promisedDate.setDate(promisedDate.getDate() + (Math.floor(Math.random() * 20) + 5)); // Add mock lead time
      
      let actualDelivery = null;
      if (status === 'Received') {
         actualDelivery = new Date(promisedDate);
         const variance = Math.floor(Math.random() * 15) - 7; // -7 to +7 days variance
         actualDelivery.setDate(actualDelivery.getDate() + variance);
      }

      // If there's a custom rate, apply it instead
      if (hasCustomRate && currency !== this.currencyConversion.toCurrency) {
        convertedTotal = total * customRate;
      }

      sampleData.push({
        id: `PO-${10000 + i}`, // Use orderNbr as ID for consistency if Acumatica ID isn't available
        orderNbr: `PO-${10000 + i}`,
        date: date, // Keep as Date object
        vendorId: vendorInfo.id,
        vendor: vendorInfo.name, // Pre-fill name for mock data
        total: total, // Ensure it's a number
        currency: currency, // Add currency field 
        convertedTotal: convertedTotal, // Store converted total
        convertedCurrency: this.currencyConversion.toCurrency, // Target currency
        customRate: hasCustomRate ? customRate : null, // Add custom rate field
        status: status,
        items: Math.floor(Math.random() * 10) + 1,
        promisedDate: promisedDate, // Keep as Date or null
        actualDelivery: actualDelivery, // Keep as Date or null
        lastModified: new Date(date), // Mock last modified
        lineItems: [] // Keep lineItems array
      });
    }
    
    return sampleData;
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    // Only use the built-in loading UI in the container
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading purchase orders...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Purchase Orders</h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="po-search" 
                placeholder="Search orders..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <select id="status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Statuses</option>
              <option value="open" ${this.filterStatus === 'open' ? 'selected' : ''}>Open</option>
              <option value="pending" ${this.filterStatus === 'pending' ? 'selected' : ''}>Pending</option>
              <option value="received" ${this.filterStatus === 'received' ? 'selected' : ''}>Received</option>
              <option value="cancelled" ${this.filterStatus === 'cancelled' ? 'selected' : ''}>Cancelled</option>
            </select>
            
            <div class="flex gap-2">
              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </button>
              
              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
              
              <!-- New PO Button (now in the action menu) -->
              <button id="new-po-button" class="p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center" title="New Purchase Order">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Purchase Order Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="id">
                  PO # <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="date">
                  Date <span class="sort-indicator">${this.sortField === 'date' ? (this.sortDirection === 'asc' ? '↑' : '↓') : ''}</span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="vendor">
                  Vendor <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="total">
                  Total <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="status">
                  Status <span class="sort-indicator"></span>
                </th>
                <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderTableRows()}
            </tbody>
          </table>
        </div>

        <!-- Updated Pagination matching vendor-metrics.js -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredPOs.length)} to 
            ${Math.min(this.currentPage * this.itemsPerPage, this.filteredPOs.length)} of 
            ${this.filteredPOs.length} results
          </div>
          
          <div class="flex items-center space-x-1">
            <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-double-left"></i>
            </button>
            <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
              <i class="fas fa-angle-left"></i>
            </button>
            
            <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
              ${this.currentPage} of ${this.totalPages}
            </span>
            
            <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-right"></i>
            </button>
            <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
              <i class="fas fa-angle-double-right"></i>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  renderTableRows() {
    if (this.filteredPOs.length === 0) {
      return `
        <tr>
          <td colspan="6" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No purchase orders found matching your criteria
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredPOs.length);
    const displayedPOs = this.filteredPOs.slice(start, end);

    return displayedPOs.map(po => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(po.orderNbr)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(po.date)}
          ${po.promisedDate ? `<div class="text-xs text-gray-400 dark:text-gray-500">Due: ${this.formatDate(po.promisedDate)}</div>` : ''}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
          ${this.escapeHtml(po.vendor)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatAmount(po.convertedTotal, po.convertedCurrency)}
          ${po.currency !== po.convertedCurrency ? 
            `<div class="text-xs text-gray-400 dark:text-gray-500">
              (${this.formatAmount(po.total, po.currency)})
            </div>` : ''}
        </td>
        <td class="px-3 py-4 whitespace-nowrap">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(po.status)}">
            ${this.escapeHtml(po.status)}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${po.id}" class="view-po text-blue-600 hover:text-blue-900 dark:hover:text-blue-400 mr-3">
            <i class="fas fa-eye"></i>
          </button>
          <button data-id="${po.id}" class="edit-po text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-pencil-alt"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  setupEventListeners() {
    // Search input
    const searchInput = this.container.querySelector('#po-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = this.container.querySelector('#clear-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Status filter
    const statusFilter = this.container.querySelector('#status-filter');
    if (statusFilter) {
      statusFilter.addEventListener('change', () => {
        this.filterStatus = statusFilter.value;
        this.currentPage = 1;
        this.applyFilters();
      });
    }

    // Sort headers
    const sortHeaders = this.container.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
      });
    });

    // Updated pagination event handlers to match vendor-metrics.js
    const firstPageBtn = this.container.querySelector('#first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage = 1;
          this.render();
          this.setupEventListeners(); // Re-attach event listeners after render
        }
      });
    }

    const prevPageBtn = this.container.querySelector('#prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
          this.setupEventListeners(); // Re-attach event listeners after render
        }
      });
    }

    const nextPageBtn = this.container.querySelector('#next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
          this.setupEventListeners(); // Re-attach event listeners after render
        }
      });
    }

    const lastPageBtn = this.container.querySelector('#last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage = this.totalPages;
          this.render();
          this.setupEventListeners(); // Re-attach event listeners after render
        }
      });
    }

    // View PO buttons
    const viewButtons = this.container.querySelectorAll('.view-po');
    viewButtons.forEach(button => {
      button.addEventListener('click', () => {
        const poId = button.getAttribute('data-id');
        this.viewPurchaseOrder(poId);
      });
    });

    // Edit PO buttons
    const editButtons = this.container.querySelectorAll('.edit-po');
    editButtons.forEach(button => {
      button.addEventListener('click', () => {
        const poId = button.getAttribute('data-id');
        this.editPurchaseOrder(poId);
      });
    });

    // New PO button
    const newPoButton = this.container.querySelector('#new-po-button');
    if (newPoButton) {
      newPoButton.addEventListener('click', () => {
        this.createNewPurchaseOrder();
      });
    }

    // Date Range button
    const dateRangeButton = this.container.querySelector('#date-range-button');
    if (dateRangeButton) {
      dateRangeButton.addEventListener('click', () => {
        this.showDateRangePicker();
      });
    }

    // Refresh button
    const refreshButton = this.container.querySelector('#refresh-button');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
      });
    }

    // Export button
    const exportButton = this.container.querySelector('#export-button');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        this.exportPurchaseOrderData();
      });
    }

    // Settings button
    const settingsButton = this.container.querySelector('#settings-button');
    if (settingsButton) {
      settingsButton.addEventListener('click', () => {
        this.showSettings();
      });
    }
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.poData;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(po => 
        (po.orderNbr?.toLowerCase().includes(term)) ||
        (po.vendor?.toLowerCase().includes(term)) ||
        (po.description?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(po => 
        po.status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Filter by date range if specified
    if (this.dateRange.start && this.dateRange.end) {
      const startDate = new Date(this.dateRange.start);
      startDate.setHours(0, 0, 0, 0); // Ensure start of day comparison
      const endDate = new Date(this.dateRange.end);
      endDate.setHours(23, 59, 59, 999); // Ensure end of day comparison
      
      filtered = filtered.filter(po => {
         // Ensure po.date is a valid Date object before comparing
         if (!(po.date instanceof Date) || isNaN(po.date.getTime())) {
            console.warn("Skipping PO with invalid date during range filtering:", po);
            return false;
         }
         const poDate = po.date;
         return poDate >= startDate && poDate <= endDate;
      });
    }
    
    // Filter out future-dated POs (based on creation date)
    const today = new Date();
    today.setHours(23, 59, 59, 999); // Consider "today" up to the last millisecond

    filtered = filtered.filter(po => {
       if (!(po.date instanceof Date) || isNaN(po.date.getTime())) {
          // Already logged above if invalid, just skip here
          return false;
       }
       return po.date <= today; // Only include POs with creation date up to today
    });
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle potential null/undefined values and different types
      const valA = fieldA ?? ''; // Default nullish to empty string or 0 for comparison
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'id': // Assuming 'id' refers to 'orderNbr' for sorting
        case 'orderNbr':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'date':
          // Ensure valid dates before comparing
          const dateA = (valA instanceof Date && !isNaN(valA)) ? valA.getTime() : 0;
          const dateB = (valB instanceof Date && !isNaN(valB)) ? valB.getTime() : 0;
          comparison = dateA - dateB;
          break;
        case 'vendor':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'total':
           // Use converted total for sorting when sorting by total
           const totalA = a.convertedTotal !== undefined ? parseFloat(a.convertedTotal) : parseFloat(valA) || 0;
           const totalB = b.convertedTotal !== undefined ? parseFloat(b.convertedTotal) : parseFloat(valB) || 0;
          comparison = totalA - totalB;
          break;
        case 'status':
          comparison = String(valA).localeCompare(String(valB));
          break;
        default: // Default to date sort if field is unknown
          const defaultDateA = (a.date instanceof Date && !isNaN(a.date)) ? a.date.getTime() : 0;
          const defaultDateB = (b.date instanceof Date && !isNaN(b.date)) ? b.date.getTime() : 0;
          comparison = defaultDateA - defaultDateB;
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredPOs = filtered;
    this.calculateTotalPages();
    this.render();
    this.setupEventListeners(); // Re-attach event listeners after render
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredPOs.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  viewPurchaseOrder(poId) {
    const po = this.poData.find(po => po.id === poId);
    if (!po) return;
    
    // Format dates for display
    const formattedDate = this.formatDate(po.date);
    const formattedPromisedDate = po.promisedDate ? this.formatDate(po.promisedDate) : 'Not specified';
    const formattedLastModified = this.formatDate(po.lastModified);
    
    // Get the current rate for this PO (custom or global)
    const currentRate = po.customRate !== null ? po.customRate : this.currencyConversion.rate;
    const useCustomRate = po.customRate !== null;
    
    // Whether conversion is applicable (different currencies)
    const conversionApplicable = po.currency !== this.currencyConversion.toCurrency;
    
    // Build the modal content
    const modalContent = `
      <div class="p-6">
        <div class="flex flex-col md:flex-row justify-between mb-6">
          <h2 class="text-xl font-semibold mb-2">Purchase Order ${this.escapeHtml(po.orderNbr)}</h2>
          <div class="flex items-center">
            <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(po.status)}">
              ${this.escapeHtml(po.status)}
            </span>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <!-- PO Details -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Order Details</h3>
            <div class="space-y-2">
              <p class="text-sm flex justify-between">
                <span class="font-medium">Created Date:</span> 
                <span>${formattedDate}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Promised Date:</span> 
                <span>${formattedPromisedDate}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Description:</span> 
                <span>${this.escapeHtml(po.description)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Terms:</span> 
                <span>${this.escapeHtml(po.terms || 'N/A')}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Last Modified:</span> 
                <span>${formattedLastModified}</span>
              </p>
            </div>
          </div>

          <!-- Vendor & Totals -->
          <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Vendor & Totals</h3>
            <div class="space-y-2">
              <p class="text-sm flex justify-between">
                <span class="font-medium">Vendor:</span> 
                <span>${this.escapeHtml(po.vendor)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Vendor ID:</span> 
                <span>${this.escapeHtml(po.vendorId)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Line Total:</span> 
                <span>${this.formatAmount(po.lineTotal, po.currency)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Tax Total:</span> 
                <span>${this.formatAmount(po.taxTotal, po.currency)}</span>
              </p>
              <p class="text-sm flex justify-between">
                <span class="font-medium">Order Total:</span> 
                <span class="font-semibold">${this.formatAmount(po.total, po.currency)}</span>
              </p>
              ${conversionApplicable ? `
              <p class="text-sm flex justify-between">
                <span class="font-medium">Converted Total (${po.convertedCurrency}):</span> 
                <span class="font-semibold">${this.formatAmount(po.convertedTotal, po.convertedCurrency)}</span>
              </p>
              <!-- Custom Rate Section -->
              <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                <p class="text-sm font-medium mb-2">Currency Conversion</p>
                <div class="flex items-center mb-2">
                  <input type="checkbox" id="po-custom-rate-checkbox" class="mr-2" ${useCustomRate ? 'checked' : ''}>
                  <label for="po-custom-rate-checkbox" class="text-sm">Use custom rate for this PO</label>
                </div>
                <div class="flex items-center mt-1">
                  <div class="flex items-baseline">
                    <span class="text-sm mr-2">1 ${po.currency} =</span>
                    <input 
                      type="number" 
                      step="0.01" 
                      min="0.01" 
                      id="po-custom-rate-input" 
                      class="w-24 px-2 py-1 text-sm bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
                      value="${currentRate}"
                      ${!useCustomRate ? 'disabled' : ''}
                    >
                    <span class="text-sm ml-2">${po.convertedCurrency}</span>
                  </div>
                  <button 
                    id="save-po-rate-button" 
                    class="ml-3 px-2 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    ${!useCustomRate ? 'disabled' : ''}
                  >
                    Save Rate
                  </button>
                </div>
              </div>` : ''}
            </div>
          </div>
        </div>

        <!-- Line Items -->
        <div class="mt-6">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Line Items (${po.lineItems.length})</h3>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Line #</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Cost</th>
                  <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ext. Cost</th>
                  <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Promised</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${po.lineItems.map(item => `
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${item.lineNbr}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(item.inventoryId)}</td>
                    <td class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                      <div class="line-clamp-2">${this.escapeHtml(item.description)}</div>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${item.quantity} ${item.uom}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatAmount(item.unitCost, po.currency)}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatAmount(item.extendedCost, po.currency)}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${item.promisedDate ? this.formatDate(item.promisedDate) : 'N/A'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <!-- Buttons -->
        <div class="flex justify-end gap-2 mt-6">
          <button id="po-details-close" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Close
          </button>
          <button id="po-details-edit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Edit
          </button>
        </div>
      </div>
    `;
    
    // Create modal
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'po-details-modal';
    
    const modalContainer = document.createElement('div');
    modalContainer.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-auto max-w-4xl w-full max-h-[90vh]';
    modalContainer.innerHTML = modalContent;
    
    modalOverlay.appendChild(modalContainer);
    document.body.appendChild(modalOverlay);
    
    // Setup event listeners
    const closeButton = document.getElementById('po-details-close');
    const editButton = document.getElementById('po-details-edit');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (closeButton) {
      closeButton.addEventListener('click', closeModal);
    }
    
    if (editButton) {
      editButton.addEventListener('click', () => {
        closeModal();
        this.editPurchaseOrder(poId);
      });
    }
    
    // Custom rate checkbox and input
    if (conversionApplicable) {
      const customRateCheckbox = document.getElementById('po-custom-rate-checkbox');
      const customRateInput = document.getElementById('po-custom-rate-input');
      const saveRateButton = document.getElementById('save-po-rate-button');
      
      if (customRateCheckbox && customRateInput && saveRateButton) {
        // Toggle custom rate input
        customRateCheckbox.addEventListener('change', (e) => {
          const useCustom = e.target.checked;
          customRateInput.disabled = !useCustom;
          saveRateButton.disabled = !useCustom;
          
          // If turning off custom rate, reset to global rate
          if (!useCustom) {
            customRateInput.value = this.currencyConversion.rate;
          }
        });
        
        // Save custom rate
        saveRateButton.addEventListener('click', async () => {
          const rateValue = parseFloat(customRateInput.value);
          if (isNaN(rateValue) || rateValue <= 0) {
            this.notificationSystem.addNotification("Please enter a valid conversion rate", "error");
            return;
          }
          
          // If checkbox is checked, save the custom rate, otherwise set to null (use global)
          const newRate = customRateCheckbox.checked ? rateValue : null;
          
          // Update the PO with the new rate
          saveRateButton.disabled = true;
          saveRateButton.textContent = 'Saving...';
          
          const success = await this.updatePOCustomRate(poId, newRate);
          
          if (success) {
            this.notificationSystem.addNotification("Custom rate saved successfully", "success");
            closeModal(); // Close the modal after successful save
          } else {
            this.notificationSystem.addNotification("Failed to save custom rate", "error");
            saveRateButton.disabled = false;
            saveRateButton.textContent = 'Save Rate';
          }
        });
      }
    }
    
    // Add keyboard event for Escape key
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        closeModal();
      }
    }, { once: true });
  }

  editPurchaseOrder(poId) {
    const po = this.poData.find(po => po.id === poId);
    if (!po) return;
    
    alert(`Editing Purchase Order: ${poId}`);
    // In a real app, you would implement a proper edit form
  }

  createNewPurchaseOrder() {
    alert('Creating new Purchase Order');
    // In a real app, you would implement a proper creation form
  }

  showDateRangePicker() {
    // Simple implementation - in real app would use a proper date picker component
    alert('Date Range Picker would open here');
    
    // Mock date range selection
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // Last 30 days
    
    this.dateRange = {
      start: startDate,
      end: new Date()
    };
    
    // Apply filters with the new date range
    this.currentPage = 1;
    this.applyFilters();
  }

  exportPurchaseOrderData() {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      csvContent += 'PO #,Date,Vendor,Total,Status\n';
      
      // Add each row of data
      this.filteredPOs.forEach(po => {
        const row = [
          po.id,
          this.formatDate(po.date),
          po.vendor,
          this.formatAmount(po.total, po.currency),
          po.status
        ].map(cell => `"${cell}"`).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `purchase_orders_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      document.body.removeChild(link);
      
      alert('Purchase order data exported successfully');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data: ' + error.message);
    }
  }

  showSettings() {
    // Get available currencies
    const currencies = ['USD', 'CAD', 'EUR', 'GBP', 'AUD', 'JPY'];
    
    const settingsHtml = `
      <div class="p-6">
        <h3 class="text-lg font-medium mb-4">Purchase Order Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="id" ${this.sortField === 'id' ? 'selected' : ''}>PO #</option>
            <option value="date" ${this.sortField === 'date' ? 'selected' : ''}>Date</option>
            <option value="vendor" ${this.sortField === 'vendor' ? 'selected' : ''}>Vendor</option>
            <option value="total" ${this.sortField === 'total' ? 'selected' : ''}>Total</option>
            <option value="status" ${this.sortField === 'status' ? 'selected' : ''}>Status</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
            </label>
          </div>
        </div>
        
        <!-- Currency Conversion Settings -->
        <div class="mb-4 border-t pt-4 mt-6">
          <div class="flex items-center mb-2">
            <h4 class="text-md font-medium">Currency Conversion</h4>
            <div class="ml-auto">
              <label class="inline-flex items-center">
                <input type="checkbox" id="settings-currency-enabled" ${this.currencyConversion.enabled ? 'checked' : ''} class="mr-2">
                Enable conversion
              </label>
            </div>
          </div>
          
          <div class="grid grid-cols-2 gap-4 mt-3">
            <div>
              <label class="block text-sm font-medium mb-1">From Currency</label>
              <select id="settings-from-currency" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                ${currencies.map(currency => `
                  <option value="${currency}" ${this.currencyConversion.fromCurrency === currency ? 'selected' : ''}>${currency}</option>
                `).join('')}
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium mb-1">To Currency</label>
              <select id="settings-to-currency" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
                ${currencies.map(currency => `
                  <option value="${currency}" ${this.currencyConversion.toCurrency === currency ? 'selected' : ''}>${currency}</option>
                `).join('')}
              </select>
            </div>
          </div>
          
          <div class="mt-3">
            <label class="block text-sm font-medium mb-1">Conversion Rate</label>
            <div class="flex items-center">
              <span class="mr-2">1 <span id="display-from-currency">${this.currencyConversion.fromCurrency}</span> =</span>
              <input type="number" step="0.01" min="0.01" id="settings-currency-rate" value="${this.currencyConversion.rate}" class="w-24 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
              <span class="ml-2" id="display-to-currency">${this.currencyConversion.toCurrency}</span>
            </div>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="settings-save" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
          </button>
        </div>
      </div>
    `;
    
    // Create modal dialog
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalOverlay.id = 'settings-modal';
    
    const modalContent = document.createElement('div');
    modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-md w-full';
    modalContent.innerHTML = settingsHtml;
    
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);
    
    // Update currency display when selections change
    const fromCurrencySelect = document.getElementById('settings-from-currency');
    const toCurrencySelect = document.getElementById('settings-to-currency');
    const displayFromCurrency = document.getElementById('display-from-currency');
    const displayToCurrency = document.getElementById('display-to-currency');
    
    if (fromCurrencySelect && displayFromCurrency) {
      fromCurrencySelect.addEventListener('change', () => {
        displayFromCurrency.textContent = fromCurrencySelect.value;
      });
    }
    
    if (toCurrencySelect && displayToCurrency) {
      toCurrencySelect.addEventListener('change', () => {
        displayToCurrency.textContent = toCurrencySelect.value;
      });
    }
    
    // Setup event listeners for the modal
    const cancelButton = document.getElementById('settings-cancel');
    const saveButton = document.getElementById('settings-save');
    
    const closeModal = () => {
      document.body.removeChild(modalOverlay);
    };
    
    if (cancelButton) {
      cancelButton.addEventListener('click', closeModal);
    }
    
    if (saveButton) {
      saveButton.addEventListener('click', async () => {
        // Get settings values
        const itemsPerPageSelect = document.getElementById('settings-items-per-page');
        const defaultSortSelect = document.getElementById('settings-default-sort');
        const sortDirectionRadios = document.getElementsByName('settings-sort-direction');
        
        if (itemsPerPageSelect) {
          this.itemsPerPage = parseInt(itemsPerPageSelect.value);
        }
        
        if (defaultSortSelect) {
          this.sortField = defaultSortSelect.value;
        }
        
        let selectedDirection = 'desc';
        sortDirectionRadios.forEach(radio => {
          if (radio.checked) {
            selectedDirection = radio.value;
          }
        });
        this.sortDirection = selectedDirection;
        
        // Get currency conversion settings
        const currencyEnabled = document.getElementById('settings-currency-enabled');
        const fromCurrency = document.getElementById('settings-from-currency');
        const toCurrency = document.getElementById('settings-to-currency');
        const currencyRate = document.getElementById('settings-currency-rate');
        
        // Update currency conversion settings
        const oldSettings = { ...this.currencyConversion };
        
        if (currencyEnabled) {
          this.currencyConversion.enabled = currencyEnabled.checked;
        }
        
        if (fromCurrency) {
          this.currencyConversion.fromCurrency = fromCurrency.value;
        }
        
        if (toCurrency) {
          this.currencyConversion.toCurrency = toCurrency.value;
        }
        
        if (currencyRate) {
          const rate = parseFloat(currencyRate.value);
          if (!isNaN(rate) && rate > 0) {
            this.currencyConversion.rate = rate;
          }
        }
        
        // Check if currency settings changed
        const settingsChanged = 
          oldSettings.enabled !== this.currencyConversion.enabled ||
          oldSettings.fromCurrency !== this.currencyConversion.fromCurrency ||
          oldSettings.toCurrency !== this.currencyConversion.toCurrency ||
          oldSettings.rate !== this.currencyConversion.rate;
        
        // Save settings to IndexedDB
        await this.saveSettings();
        
        // Apply settings and re-render
        this.calculateTotalPages();
        this.applyFilters();
        
        // Close modal
        closeModal();
        
        // Show success message
        if (settingsChanged) {
          this.notificationSystem.addNotification("Currency settings updated successfully", "success");
        }
      });
    }
  }

  formatDate(date) {
    if (!(date instanceof Date) || isNaN(date.getTime())) return 'N/A'; // Check if it's a valid Date object
    
    try {
      // Get the UTC components of the date to avoid timezone shifting during formatting
      const year = date.getUTCFullYear();
      const month = date.getUTCMonth(); // 0-indexed month
      const day = date.getUTCDate();
      
      // Create an array of month names for formatting
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      
      // Format the date using UTC components
      return `${monthNames[month]} ${day}, ${year}`;
    } catch (e) {
      console.error("Error formatting date:", e);
      return 'N/A';
    }
  }

  formatAmount(amount, currencyCode) {
    if (amount === null || amount === undefined) return 'N/A';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode || 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  }

  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'received':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  async loadSettings() {
    try {
      // Open database without specifying version
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      console.log("Successfully opened database for loading settings, version:", db.version);
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Get currency settings
      const currencySettings = await new Promise((resolve) => {
        const request = store.get("currencySettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading currency settings:", event.target.error);
          resolve(null);
        };
      });
      
      if (currencySettings) {
        console.log("Loaded currency settings:", currencySettings);
        this.currencyConversion = currencySettings.value;
      } else {
        console.log("No saved currency settings, using defaults");
      }
      
      db.close();
    } catch (error) {
      console.error("Error loading settings:", error);
      // Continue with defaults if settings can't be loaded
    }
  }
  
  async saveSettings() {
    try {
      // First update all POs with the new conversion rate
      const oldSettings = { ...this.currencyConversion };
      
      // Only recalculate if conversion settings actually changed
      if (oldSettings.enabled !== this.currencyConversion.enabled ||
          oldSettings.fromCurrency !== this.currencyConversion.fromCurrency ||
          oldSettings.toCurrency !== this.currencyConversion.toCurrency ||
          oldSettings.rate !== this.currencyConversion.rate) {
          
          // Recalculate convertedTotal for all POs
          this.poData.forEach(po => {
            // Only recalculate if currency is different from target and no custom rate
            if (po.currency !== this.currencyConversion.toCurrency) {
              // Use custom rate if available, otherwise use global rate
              const rateToUse = po.customRate !== null ? po.customRate : this.currencyConversion.rate;
              po.convertedTotal = po.total * rateToUse;
            } else {
              po.convertedTotal = po.total; // No conversion needed
            }
            po.convertedCurrency = this.currencyConversion.toCurrency;
          });
          
          // Store the updated POs
          await this.storePOsInIndexedDB(this.poData);
      }
      
      // Open database without specifying version
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });
      
      console.log("Successfully opened database for settings, version:", db.version);
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Save currency settings
      store.put({
        id: "currencySettings",
        value: this.currencyConversion
      });
      
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          console.log("Settings transaction completed successfully");
          resolve();
        };
        transaction.onerror = (event) => {
          console.error("Settings transaction error:", event.target.error);
          reject(event.target.error);
        };
      });
      
      console.log("Settings saved successfully");
      db.close();
      
      // Update the display
      this.applyFilters();
      
      // Show notification for user feedback
      this.notificationSystem.addNotification("Settings saved and applied successfully", "success");
      
    } catch (error) {
      console.error("Error saving settings:", error);
      this.notificationSystem.addNotification("Error saving settings: " + error.message, "error");
    }
  }

  // Helper method to calculate converted amount based on PO's settings
  calculateConvertedAmount(po) {
    // If currency is already the target currency, no conversion needed
    if (po.currency === this.currencyConversion.toCurrency) {
      return po.total;
    }
    
    // If conversion is disabled, return original amount
    if (!this.currencyConversion.enabled) {
      return po.total;
    }
    
    // Use custom rate if available, otherwise use global rate
    const rate = po.customRate !== null ? po.customRate : this.currencyConversion.rate;
    return po.total * rate;
  }

  // Method to update a single PO with a custom rate
  async updatePOCustomRate(poId, customRate) {
    try {
      // Find PO in the data
      const poIndex = this.poData.findIndex(po => po.id === poId);
      if (poIndex === -1) {
        console.error(`PO with ID ${poId} not found`);
        return false;
      }
      
      // Update custom rate
      this.poData[poIndex].customRate = customRate;
      
      // Recalculate converted total
      if (this.poData[poIndex].currency !== this.currencyConversion.toCurrency) {
        this.poData[poIndex].convertedTotal = this.poData[poIndex].total * customRate;
      }
      
      // Update in IndexedDB
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for updating PO:", event.target.error);
          reject(new Error("Could not open database for updating PO"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.storeName)) {
        console.error("PO store not found, cannot update PO");
        db.close();
        return false;
      }
      
      const transaction = db.transaction([this.storeName], "readwrite");
      const store = transaction.objectStore(this.storeName);
      
      // First get the current PO from the database
      const currentPO = await new Promise((resolve) => {
        const getRequest = store.get(poId);
        getRequest.onsuccess = () => resolve(getRequest.result);
        getRequest.onerror = () => {
          console.error("Error getting PO for update:", getRequest.error);
          resolve(null);
        };
      });
      
      if (!currentPO) {
        console.error(`PO with ID ${poId} not found in database`);
        db.close();
        return false;
      }
      
      // Update the PO with new custom rate and converted total
      currentPO.customRate = customRate;
      if (currentPO.currency !== this.currencyConversion.toCurrency) {
        currentPO.convertedTotal = currentPO.total * customRate;
      }
      
      // Put the updated PO back in the database
      const updateResult = await new Promise((resolve) => {
        const putRequest = store.put(currentPO);
        putRequest.onsuccess = () => resolve(true);
        putRequest.onerror = (event) => {
          console.error("Error updating PO custom rate:", event.target.error);
          resolve(false);
        };
      });
      
      db.close();
      
      // If successful, update filtered POs and re-render
      if (updateResult) {
        // Find and update in filtered POs if present
        const filteredIndex = this.filteredPOs.findIndex(po => po.id === poId);
        if (filteredIndex !== -1) {
          this.filteredPOs[filteredIndex].customRate = customRate;
          this.filteredPOs[filteredIndex].convertedTotal = this.poData[poIndex].convertedTotal;
        }
        
        this.render();
        this.setupEventListeners();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error("Error updating PO custom rate:", error);
      return false;
    }
  }
} 