// Sales Order Components component for MRP Dashboard
export class SalesOrderComponents {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.components = [];
    this.filteredComponents = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'asc';
    this.isLoading = false;

    // Database configuration for production orders
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';

    // Database configuration for sales orders (same as parent component)
    this.salesOrderDbName = 'salesOrderDb';
    this.salesOrderStoreName = 'salesOrders';
  }

  async init() {
    console.log("=== SALES ORDER COMPONENTS INIT STARTED ===");
    console.log("Container:", this.container);
    console.log("Parent component:", this.parentComponent);
    console.log("Parent sales orders count:", this.parentComponent?.salesOrders?.length || 0);

    this.isLoading = true;
    this.render();

    try {
      console.log("About to call buildComponentsTable...");
      // Build hierarchical components table from sales orders and production data
      await this.buildComponentsTable();
      console.log("buildComponentsTable completed");

      this.isLoading = false;
      this.render();
      console.log("=== SALES ORDER COMPONENTS INIT COMPLETED ===");
    } catch (error) {
      console.error("=== ERROR IN SALES ORDER COMPONENTS INIT ===");
      console.error("Error initializing components:", error);
      console.error("Stack trace:", error.stack);
      this.isLoading = false;
      this.showError("Failed to initialize components: " + error.message);
      this.render();
    }
  }

  async buildComponentsTable() {
    try {
      console.log("=== STARTING buildComponentsTable ===");

      // First, try to get sales orders from parent component
      let salesOrders = this.parentComponent?.salesOrders || [];
      console.log(`Found ${salesOrders.length} sales orders from parent component`);
      
      // If no sales orders from parent, try IndexedDB
      if (salesOrders.length === 0) {
        console.log("No sales orders from parent, trying IndexedDB...");
        salesOrders = await this.getSalesOrdersFromIndexedDB();
        console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);
      }

      if (salesOrders.length === 0) {
        console.warn("NO SALES ORDERS FOUND! User needs to load sales orders first.");
        this.showError("No sales orders found. Please navigate to the 'Sales Orders' tab and click 'Refresh' to load data from Acumatica first.");
        this.components = [];
        this.filteredComponents = [];
        return;
      }

      // Debug: Check sales order structure
      if (salesOrders.length > 0) {
        const firstOrder = salesOrders[0];
        console.log("First sales order structure:", {
          OrderNbr: firstOrder.OrderNbr,
          hasLineItems: Array.isArray(firstOrder.LineItems),
          lineItemsCount: firstOrder.LineItems ? firstOrder.LineItems.length : 0,
          firstLineItemBOM: firstOrder.LineItems?.[0]?.BOMItems ? firstOrder.LineItems[0].BOMItems.length : 0,
          hasProduction: firstOrder.LineItems?.[0]?.HasProduction,
          sampleBOMItem: firstOrder.LineItems?.[0]?.BOMItems?.[0]
        });
      }

      // Get production orders from IndexedDB
      const productionOrders = await this.getProductionOrdersFromIndexedDB();
      console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);

      if (productionOrders.length === 0) {
        console.warn("NO PRODUCTION ORDERS FOUND! User needs to load production data first.");
        this.showError("No production orders found. Please navigate to the MRP → Production tab and click 'Refresh' to load production data from Acumatica first.");
        this.components = [];
        this.filteredComponents = [];
        return;
      }

      // Debug: Check production order structure
      if (productionOrders.length > 0) {
        const firstProd = productionOrders[0];
        console.log("First production order structure:", {
          MainProductionNbr: firstProd.MainProductionNbr,
          hasMaterials: Array.isArray(firstProd.Materials),
          materialsCount: firstProd.Materials ? firstProd.Materials.length : 0,
          sampleMaterial: firstProd.Materials?.[0]
        });
      }

      // Build hierarchical component structure
      this.components = this.buildHierarchicalBOM(salesOrders, productionOrders);
      console.log(`Built hierarchical BOM with ${this.components.length} total components`);

      if (this.components.length === 0) {
        console.warn("No hierarchical components were built - possible data mismatch");
        this.showError("No BOM components found. This means either: 1) Sales orders don't have BOM/Production data, or 2) Production numbers in sales orders don't match production order numbers. Check the browser console for detailed debugging information.");
      }

      this.filteredComponents = [...this.components];
      this.calculateTotalPages();
      this.applyFilters();

    } catch (error) {
      console.error("Error building components table:", error);
      this.showError("Error building components: " + error.message);
      this.components = [];
      this.filteredComponents = [];
    }
  }

  buildHierarchicalBOM(salesOrders, productionOrders) {
    console.log("🔨 Building hierarchical BOM...");
    console.log(`Input: ${salesOrders.length} sales orders, ${productionOrders.length} production orders`);
    
    const hierarchicalComponents = [];
    const componentIdRef = { value: 0 };
    let totalLinesWithBOM = 0;
    let totalBOMItemsFound = 0;

    // Process each sales order
    salesOrders.forEach((order, orderIndex) => {
      console.log(`Processing sales order ${orderIndex + 1}/${salesOrders.length}: ${order.OrderNbr}`);
      
      if (!Array.isArray(order.LineItems)) {
        console.warn(`⚠️ Order ${order.OrderNbr} has no LineItems array`);
        return;
      }

      order.LineItems.forEach((lineItem, lineIndex) => {
        console.log(`  Processing line item ${lineIndex + 1}/${order.LineItems.length}: ${lineItem.InventoryID}`);
        
        // Add the main sales order line item
        const mainComponent = {
          id: `comp-${++componentIdRef.value}`,
          type: 'sales_line_item',
          level: 0,
          OrderNbr: order.OrderNbr,
          InventoryID: lineItem.InventoryID || 'N/A',
          Description: lineItem.LineDescription || '',
          Quantity: lineItem.Quantity || 0,
          ProductionNbr: '',
          ComponentOfProduction: '',
          ParentInventoryID: '',
          UOM: lineItem.UOM || '',
          UnitPrice: lineItem.UnitPrice || 0,
          ExtAmount: lineItem.ExtAmount || 0,
          hasChildren: lineItem.HasProduction || (lineItem.BOMItems && lineItem.BOMItems.length > 0)
        };

        hierarchicalComponents.push(mainComponent);
        console.log(`    ✅ Added main component: ${mainComponent.InventoryID}`);

        // Check for BOM expansion
        if (lineItem.HasProduction && Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0) {
          totalLinesWithBOM++;
          console.log(`    🔧 Line item has ${lineItem.BOMItems.length} BOM items, expanding...`);
          
          lineItem.BOMItems.forEach((bomItem, bomIndex) => {
            const productionNbr = bomItem.ProductionNbr;
            console.log(`      Processing BOM item ${bomIndex + 1}: ProductionNbr = ${productionNbr}`);
            
            if (productionNbr && productionNbr.trim()) {
              totalBOMItemsFound++;
              const expansionResult = this.expandProductionBOM(
                productionNbr.trim(),
                productionOrders,
                hierarchicalComponents,
                componentIdRef,
                order.OrderNbr,
                1, // level
                lineItem.InventoryID,
                productionNbr.trim()
              );
              
              if (expansionResult > 0) {
                console.log(`        ✅ Expanded ${expansionResult} materials from production ${productionNbr}`);
              } else {
                console.log(`        ⚠️ No materials found for production ${productionNbr}`);
              }
            } else {
              console.log(`        ⚠️ BOM item has no ProductionNbr`);
            }
          });
        } else {
          console.log(`    ℹ️ Line item has no BOM (HasProduction: ${lineItem.HasProduction}, BOMItems: ${lineItem.BOMItems?.length || 0})`);
        }
      });
    });

    console.log(`🎯 BOM building complete:`);
    console.log(`  - Total components created: ${hierarchicalComponents.length}`);
    console.log(`  - Lines with BOM: ${totalLinesWithBOM}`);
    console.log(`  - BOM items processed: ${totalBOMItemsFound}`);

    return hierarchicalComponents;
  }

  expandProductionBOM(productionNbr, productionOrders, componentsArray, componentIdRef, orderNbr, level, parentInventoryID, componentOfProduction) {
    // Find the production order
    const productionOrder = productionOrders.find(po => 
      po.MainProductionNbr === productionNbr
    );

    if (!productionOrder) {
      console.log(`        ❌ No production order found for ProductionNbr: ${productionNbr}`);
      return 0;
    }

    if (!Array.isArray(productionOrder.Materials)) {
      console.log(`        ❌ Production order ${productionNbr} has no Materials array`);
      return 0;
    }

    if (productionOrder.Materials.length === 0) {
      console.log(`        ❌ Production order ${productionNbr} has empty Materials array`);
      return 0;
    }

    console.log(`        🔧 Expanding production ${productionNbr} with ${productionOrder.Materials.length} materials at level ${level}`);
    let materialsAdded = 0;

    // Process each material in this production order
    productionOrder.Materials.forEach((material, materialIndex) => {
      console.log(`          Processing material ${materialIndex + 1}/${productionOrder.Materials.length}: ${material.InventoryID}`);
      
      const materialComponent = {
        id: `comp-${++componentIdRef.value}`,
        type: 'production_material',
        level: level,
        OrderNbr: orderNbr,
        InventoryID: material.InventoryID || 'N/A',
        Description: material.Description || '',
        Quantity: material.QtyRequired || 0,
        ProductionNbr: productionNbr,
        ComponentOfProduction: componentOfProduction,
        ParentInventoryID: parentInventoryID,
        UOM: material.UOM || '',
        UnitPrice: material.UnitCost || 0,
        ExtAmount: (material.QtyRequired || 0) * (material.UnitCost || 0),
        OperationNbr: material.OperationNbr || '',
        QtyIssued: material.QtyIssued || 0,
        TotalCost: material.TotalCost || 0,
        hasChildren: false
      };

      componentsArray.push(materialComponent);
      materialsAdded++;
      console.log(`            ✅ Added material: ${materialComponent.InventoryID} (Qty: ${materialComponent.Quantity})`);

      // Check if this material itself has a production number (nested BOM)
      // Look for production orders where MainInventoryID matches this material's InventoryID
      const nestedProductionOrders = productionOrders.filter(po => 
        po.MainInventoryID === material.InventoryID
      );

      if (nestedProductionOrders.length > 0) {
        materialComponent.hasChildren = true;
        console.log(`            🔍 Found ${nestedProductionOrders.length} nested production orders for material ${material.InventoryID}`);
        
        // Recursively expand each nested production order
        nestedProductionOrders.forEach(nestedPo => {
          console.log(`            🔄 Recursively expanding nested production ${nestedPo.MainProductionNbr}`);
          const nestedCount = this.expandProductionBOM(
            nestedPo.MainProductionNbr,
            productionOrders,
            componentsArray,
            componentIdRef,
            orderNbr,
            level + 1,
            material.InventoryID,
            nestedPo.MainProductionNbr
          );
          materialsAdded += nestedCount;
        });
      }
    });

    console.log(`        ✅ Completed expanding production ${productionNbr}, added ${materialsAdded} total materials`);
    return materialsAdded;
  }

  async getSalesOrdersFromIndexedDB() {
    console.log("=== getSalesOrdersFromIndexedDB called ===");
    console.log("Database name:", this.salesOrderDbName);
    console.log("Store name:", this.salesOrderStoreName);

    return new Promise((resolve, reject) => {
      try {
        console.log("Opening sales order database...");
        const request = indexedDB.open(this.salesOrderDbName);

        request.onerror = (event) => {
          console.error("Error opening sales order database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Sales order database opened successfully, version:", db.version);
          console.log("Available stores:", Array.from(db.objectStoreNames));

          if (!db.objectStoreNames.contains(this.salesOrderStoreName)) {
            console.warn("Sales order store not found!");
            console.warn("Looking for store:", this.salesOrderStoreName);
            console.warn("Available stores:", Array.from(db.objectStoreNames));
            db.close();
            resolve([]);
            return;
          }

          try {
            const transaction = db.transaction([this.salesOrderStoreName], 'readonly');
            const store = transaction.objectStore(this.salesOrderStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const salesOrders = event.target.result || [];
              console.log(`✅ Retrieved ${salesOrders.length} sales orders from IndexedDB`);

              // Enhanced debugging for first sales order
              if (salesOrders.length > 0) {
                const firstOrder = salesOrders[0];
                console.log("📋 First sales order debug info:", {
                  OrderNbr: firstOrder.OrderNbr,
                  hasLineItems: Array.isArray(firstOrder.LineItems),
                  lineItemsCount: firstOrder.LineItems?.length || 0,
                  totalBOMItems: firstOrder.TotalBOMItems || 0,
                  hasProductionOrders: firstOrder.HasProductionOrders
                });

                // Check first line item for BOM data
                if (firstOrder.LineItems?.length > 0) {
                  const firstLineItem = firstOrder.LineItems[0];
                  console.log("🔧 First line item BOM data:", {
                    InventoryID: firstLineItem.InventoryID,
                    hasBOMItems: Array.isArray(firstLineItem.BOMItems),
                    bomItemsCount: firstLineItem.BOMItems?.length || 0,
                    hasProduction: firstLineItem.HasProduction,
                    firstBOMItem: firstLineItem.BOMItems?.[0]
                  });
                }
              }

              db.close();
              resolve(salesOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("❌ Error retrieving sales orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("❌ Error creating sales order transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("❌ Error in getSalesOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  async getProductionOrdersFromIndexedDB() {
    console.log("=== getProductionOrdersFromIndexedDB called ===");
    console.log("Database name:", this.productionDbName);
    console.log("Store name:", this.productionStoreName);

    return new Promise((resolve, reject) => {
      try {
        console.log("Opening production database...");
        const request = indexedDB.open(this.productionDbName);

        request.onerror = (event) => {
          console.error("❌ Error opening production database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Production database opened successfully, version:", db.version);
          console.log("Available stores:", Array.from(db.objectStoreNames));

          if (!db.objectStoreNames.contains(this.productionStoreName)) {
            console.warn("❌ Production store not found!");
            console.warn("Looking for store:", this.productionStoreName);
            console.warn("Available stores:", Array.from(db.objectStoreNames));
            db.close();
            resolve([]);
            return;
          }

          try {
            const transaction = db.transaction([this.productionStoreName], 'readonly');
            const store = transaction.objectStore(this.productionStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const productionOrders = event.target.result || [];
              console.log(`✅ Retrieved ${productionOrders.length} production orders from IndexedDB`);

              // Enhanced debugging for first production order
              if (productionOrders.length > 0) {
                const firstProd = productionOrders[0];
                console.log("🏭 First production order debug info:", {
                  MainProductionNbr: firstProd.MainProductionNbr,
                  MainInventoryID: firstProd.MainInventoryID,
                  hasMaterials: Array.isArray(firstProd.Materials),
                  materialsCount: firstProd.Materials?.length || 0,
                  firstMaterial: firstProd.Materials?.[0]
                });
              }

              db.close();
              resolve(productionOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("❌ Error retrieving production orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("❌ Error creating production transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("❌ Error in getProductionOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredComponents.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          comp.OrderNbr.toLowerCase().includes(searchLower) ||
          comp.InventoryID.toLowerCase().includes(searchLower) ||
          comp.Description.toLowerCase().includes(searchLower) ||
          comp.ProductionNbr.toLowerCase().includes(searchLower) ||
          comp.ComponentOfProduction.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      // First sort by OrderNbr, then by level, then by the selected field
      if (a.OrderNbr !== b.OrderNbr) {
        return a.OrderNbr.localeCompare(b.OrderNbr);
      }

      if (a.level !== b.level) {
        return a.level - b.level;
      }

      let aValue, bValue;
      switch (this.sortField) {
        case 'InventoryID':
          aValue = a.InventoryID;
          bValue = b.InventoryID;
          break;
        case 'ProductionNbr':
          aValue = a.ProductionNbr;
          bValue = b.ProductionNbr;
          break;
        case 'ComponentOfProduction':
          aValue = a.ComponentOfProduction;
          bValue = b.ComponentOfProduction;
          break;
        case 'Quantity':
          aValue = a.Quantity;
          bValue = b.Quantity;
          break;
        case 'ExtAmount':
          aValue = a.ExtAmount;
          bValue = b.ExtAmount;
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }



  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading components data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    // Debug information
    console.log("Rendering components content:");
    console.log("- Total components:", this.components.length);
    console.log("- Filtered components:", this.filteredComponents.length);
    console.log("- Parent sales orders:", this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0);

    if (this.filteredComponents.length === 0) {
      tableContainer.innerHTML = `
        <div class="flex flex-col items-center justify-center p-8">
          <div class="text-gray-600 dark:text-gray-400">
            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            <p class="text-center">No components data available</p>
            <p class="text-center text-sm mt-2">Load sales orders with BOM/Production data to view component breakdown</p>
          </div>
        </div>
      `;
      return;
    }

    // Calculate pagination
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredComponents.length);
    const displayedComponents = this.filteredComponents.slice(startIndex, endIndex);

    tableContainer.innerHTML = `
      <!-- Multi-Level BOM + Production Components Table -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Multi-Level BOM + Production Components
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Hierarchical breakdown of sales order components and their production materials
          </p>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                  Order # <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="InventoryID">
                  Inventory ID <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ProductionNbr">
                  Production # <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ComponentOfProduction">
                  Component of Production <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Description
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Quantity">
                  Quantity <span class="sort-indicator"></span>
                </th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ExtAmount">
                  Extended Amount <span class="sort-indicator"></span>
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${this.renderHierarchicalRows(displayedComponents)}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <div class="bg-gray-50 dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-700 sm:px-6">
          <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing ${Math.min(startIndex + 1, this.filteredComponents.length)} to 
              ${Math.min(endIndex, this.filteredComponents.length)} of 
              ${this.filteredComponents.length} components
            </div>
            
            <div class="flex items-center space-x-1">
              <button id="components-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
              </button>
              <button id="components-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
              </button>
              
              <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
                ${this.currentPage} of ${this.totalPages}
              </span>
              
              <button id="components-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
              </button>
              <button id="components-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupComponentsEventListeners();
  }

  renderHierarchicalRows(components) {
    if (!components || components.length === 0) {
      return `
        <tr>
          <td colspan="7" class="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <p class="text-lg font-medium mb-2">No components found</p>
              <p class="text-sm">No BOM or production components to display</p>
            </div>
          </td>
        </tr>
      `;
    }

    return components.map(component => {
      const indentLevel = component.level || 0;
      const indentPx = indentLevel * 20; // 20px per level
      
      // Get appropriate icons and styling based on component type
      const typeInfo = this.getComponentTypeInfo(component);
      
      return `
        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 ${indentLevel > 0 ? 'bg-gray-25 dark:bg-gray-850' : ''}">
          <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
            ${this.escapeHtml(component.OrderNbr)}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm">
            <div style="margin-left: ${indentPx}px" class="flex items-center">
              ${typeInfo.icon}
              <span class="ml-2 ${typeInfo.textClass}">
                ${this.escapeHtml(component.InventoryID)}
              </span>
              ${component.hasChildren ? `
                <span class="ml-2 inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full dark:bg-blue-900 dark:text-blue-200">
                  <i class="fas fa-sitemap mr-1"></i>
                  Has BOM
                </span>
              ` : ''}
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white">
            ${component.ProductionNbr ? `
              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                <i class="fas fa-industry mr-1"></i>
                ${this.escapeHtml(component.ProductionNbr)}
              </span>
            ` : ''}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
            ${component.ComponentOfProduction ? `
              <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900 dark:text-purple-200">
                <i class="fas fa-arrow-up mr-1"></i>
                ${this.escapeHtml(component.ComponentOfProduction)}
              </span>
            ` : ''}
          </td>
          <td class="px-4 py-3 text-sm text-gray-900 dark:text-white">
            <div class="max-w-xs truncate" title="${this.escapeHtml(component.Description)}">
              ${this.escapeHtml(component.Description)}
            </div>
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatNumber(component.Quantity)} ${this.escapeHtml(component.UOM || '')}
          </td>
          <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
            ${this.formatCurrency(component.ExtAmount)}
          </td>
        </tr>
      `;
    }).join('');
  }

  getComponentTypeInfo(component) {
    switch (component.type) {
      case 'sales_line_item':
        return {
          icon: '<i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400"></i>',
          textClass: 'font-semibold text-blue-900 dark:text-blue-100'
        };
      case 'production_material':
        if (component.level === 1) {
          return {
            icon: '<i class="fas fa-cogs text-green-600 dark:text-green-400"></i>',
            textClass: 'font-medium text-green-800 dark:text-green-200'
          };
        } else {
          return {
            icon: '<i class="fas fa-cube text-orange-600 dark:text-orange-400"></i>',
            textClass: 'text-orange-800 dark:text-orange-200'
          };
        }
      default:
        return {
          icon: '<i class="fas fa-box text-gray-600 dark:text-gray-400"></i>',
          textClass: 'text-gray-800 dark:text-gray-200'
        };
    }
  }



  setupComponentsEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Sort headers
      const sortHeaders = document.querySelectorAll('th[data-sort]');
      sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
          const field = header.getAttribute('data-sort');
          if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
          } else {
            this.sortField = field;
            this.sortDirection = 'asc';
          }
          this.applyFilters();
          this.render();
        });
      });

      // Pagination event handlers
      const firstPageBtn = document.getElementById('components-first-page');
      if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage = 1;
            this.render();
          }
        });
      }

      const prevPageBtn = document.getElementById('components-prev-page');
      if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
          }
        });
      }

      const nextPageBtn = document.getElementById('components-next-page');
      if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.render();
          }
        });
      }

      const lastPageBtn = document.getElementById('components-last-page');
      if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage = this.totalPages;
            this.render();
          }
        });
      }

      console.log('Components event listeners set up');
    }, 50);
  }

  // Utility methods for formatting
  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  formatCurrency(value) {
    if (value === null || value === undefined || isNaN(value)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  escapeHtml(text) {
    if (!text) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  showError(message) {
    console.error("Components error:", message);

    const tableContainer = document.getElementById('sales-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }
}
