// Sales Order Components component for MRP Dashboard
export class SalesOrderComponents {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.components = [];
    this.filteredComponents = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'asc';
    this.isLoading = false;

    // Database configuration for production orders
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';

    // Database configuration for sales orders (same as parent component)
    this.salesOrderDbName = 'salesOrderDb';
    this.salesOrderStoreName = 'salesOrders';
  }

  async init() {
    console.log("=== SALES ORDER COMPONENTS INIT STARTED ===");
    console.log("Container:", this.container);
    console.log("Parent component:", this.parentComponent);
    console.log("Parent sales orders count:", this.parentComponent?.salesOrders?.length || 0);

    this.isLoading = true;
    this.render();

    try {
      console.log("About to call buildComponentsTable...");
      // Build hierarchical components table from sales orders and production data
      await this.buildComponentsTable();
      console.log("buildComponentsTable completed");

      this.isLoading = false;
      this.render();
      console.log("=== SALES ORDER COMPONENTS INIT COMPLETED ===");
    } catch (error) {
      console.error("=== ERROR IN SALES ORDER COMPONENTS INIT ===");
      console.error("Error initializing components:", error);
      console.error("Stack trace:", error.stack);
      this.isLoading = false;
      this.showError("Failed to initialize components: " + error.message);
      this.render();
    }
  }

  async buildComponentsTable() {
    try {
      console.log("=== STARTING buildComponentsTable ===");

      // First, let's try using parent component data directly since we know it has 45 orders
      console.log("Using parent component data directly for now...");
      let salesOrders = this.parentComponent.salesOrders || [];
      console.log(`Using ${salesOrders.length} sales orders from parent component`);

      if (salesOrders.length === 0) {
        console.error("NO SALES ORDERS FOUND FROM PARENT COMPONENT!");

        // Try IndexedDB as backup
        console.log("Attempting to get sales orders from IndexedDB...");
        salesOrders = await this.getSalesOrdersFromIndexedDB();
        console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);

        if (salesOrders.length === 0) {
          console.error("NO SALES ORDERS FOUND FROM EITHER SOURCE!");
          this.components = [];
          this.filteredComponents = [];
          return;
        }
      }

      console.log("Sample sales order structure:", salesOrders[0] ? {
        OrderNbr: salesOrders[0].OrderNbr,
        LineItemsCount: salesOrders[0].LineItems ? salesOrders[0].LineItems.length : 0,
        SampleLineItem: salesOrders[0].LineItems ? salesOrders[0].LineItems[0] : null,
        AllKeys: Object.keys(salesOrders[0])
      } : "No sales orders");

      // Get production orders from IndexedDB
      const productionOrders = await this.getProductionOrdersFromIndexedDB();
      console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);
      console.log("Sample production order structure:", productionOrders[0] ? {
        MainProductionNbr: productionOrders[0].MainProductionNbr,
        MaterialsCount: productionOrders[0].Materials ? productionOrders[0].Materials.length : 0,
        SampleMaterial: productionOrders[0].Materials ? productionOrders[0].Materials[0] : null
      } : "No production orders");

      const componentsArray = [];

      // Process each sales order
      salesOrders.forEach((order, orderIndex) => {
        console.log(`Processing order ${orderIndex + 1}/${salesOrders.length}: ${order.OrderNbr}`);
        console.log("Order structure:", {
          OrderNbr: order.OrderNbr,
          hasLineItems: !!order.LineItems,
          LineItemsType: Array.isArray(order.LineItems) ? 'array' : typeof order.LineItems,
          LineItemsLength: order.LineItems ? order.LineItems.length : 'N/A'
        });

        if (Array.isArray(order.LineItems)) {
          console.log(`Processing ${order.LineItems.length} line items for order ${order.OrderNbr}`);

          order.LineItems.forEach((lineItem, itemIndex) => {
            console.log(`Processing line item ${itemIndex + 1}: ${lineItem.InventoryID}`);

            // Add the sales order inventory item as a component
            const salesOrderComponent = {
              id: `so-${order.OrderNbr}-${lineItem.id}`,
              type: 'sales_order',
              level: 1,
              OrderNbr: order.OrderNbr,
              InventoryID: lineItem.InventoryID || 'N/A',
              Description: lineItem.LineDescription || '',
              Quantity: lineItem.Quantity || 0,
              ProductionNbr: '',
              IsFromBOM: false,
              ParentOrderNbr: order.OrderNbr,
              ParentInventoryID: '',
              UOM: lineItem.UOM || '',
              UnitPrice: lineItem.UnitPrice || 0,
              ExtAmount: lineItem.ExtAmount || 0
            };

            componentsArray.push(salesOrderComponent);
            console.log(`Added sales order component: ${salesOrderComponent.InventoryID} for order ${order.OrderNbr}`);

            // Check if this item has BOM items (production orders)
            if (Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0) {
              console.log(`Found ${lineItem.BOMItems.length} BOM items for ${lineItem.InventoryID}`);

              lineItem.BOMItems.forEach(bomItem => {
                const productionNbr = bomItem.ProductionNbr || '';
                console.log(`Processing BOM item with ProductionNbr: ${productionNbr}`);

                if (productionNbr) {
                  // Find matching production order
                  const productionOrder = productionOrders.find(po =>
                    po.MainProductionNbr === productionNbr
                  );

                  if (productionOrder && Array.isArray(productionOrder.Materials)) {
                    console.log(`Found matching production order ${productionNbr} with ${productionOrder.Materials.length} materials`);

                    // Add production order materials as components
                    productionOrder.Materials.forEach(material => {
                      const productionComponent = {
                        id: `prod-${order.OrderNbr}-${lineItem.id}-${material.id}`,
                        type: 'production_material',
                        level: 2,
                        OrderNbr: order.OrderNbr,
                        InventoryID: material.InventoryID || 'N/A',
                        Description: material.Description || '',
                        Quantity: material.QtyRequired || 0,
                        ProductionNbr: productionNbr,
                        IsFromBOM: true,
                        ParentOrderNbr: order.OrderNbr,
                        ParentInventoryID: lineItem.InventoryID || '',
                        UOM: material.UOM || '',
                        UnitPrice: material.UnitCost || 0,
                        ExtAmount: (material.QtyRequired || 0) * (material.UnitCost || 0),
                        OperationNbr: material.OperationNbr || '',
                        QtyIssued: material.QtyIssued || 0,
                        TotalCost: material.TotalCost || 0
                      };

                      componentsArray.push(productionComponent);
                      console.log(`Added production material: ${productionComponent.InventoryID} from production ${productionNbr}`);
                    });
                  } else {
                    console.log(`No matching production order found for ProductionNbr: ${productionNbr}`);
                  }
                }
              });
            } else {
              console.log(`No BOM items found for ${lineItem.InventoryID}`);
            }
          });
        } else {
          console.log(`Order ${order.OrderNbr} has no LineItems or LineItems is not an array:`, {
            LineItems: order.LineItems,
            type: typeof order.LineItems
          });
        }
      });

      this.components = componentsArray;
      console.log(`Built components table with ${this.components.length} total components`);

      this.filteredComponents = [...this.components];
      this.calculateTotalPages();
      this.applyFilters();

    } catch (error) {
      console.error("Error building components table:", error);
      this.components = [];
      this.filteredComponents = [];
    }
  }

  async getSalesOrdersFromIndexedDB() {
    console.log("=== getSalesOrdersFromIndexedDB called ===");
    console.log("Database name:", this.salesOrderDbName);
    console.log("Store name:", this.salesOrderStoreName);

    return new Promise((resolve, reject) => {
      try {
        console.log("Opening IndexedDB...");
        const request = indexedDB.open(this.salesOrderDbName);

        request.onerror = (event) => {
          console.error("Error opening sales order database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          console.log("IndexedDB opened successfully");
          const db = event.target.result;
          console.log("Database version:", db.version);
          console.log("Available stores:", Array.from(db.objectStoreNames));

          if (!db.objectStoreNames.contains(this.salesOrderStoreName)) {
            console.warn("Sales order store not found");
            console.warn("Looking for store:", this.salesOrderStoreName);
            console.warn("Available stores:", Array.from(db.objectStoreNames));
            db.close();
            resolve([]);
            return;
          }

          console.log("Sales order store found, creating transaction...");

          try {
            const transaction = db.transaction([this.salesOrderStoreName], 'readonly');
            const store = transaction.objectStore(this.salesOrderStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const salesOrders = event.target.result || [];
              console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);

              // Log detailed structure of first sales order
              if (salesOrders.length > 0) {
                const firstOrder = salesOrders[0];
                console.log("First sales order structure from IndexedDB:", {
                  OrderNbr: firstOrder.OrderNbr,
                  id: firstOrder.id,
                  allKeys: Object.keys(firstOrder),
                  LineItems: firstOrder.LineItems,
                  LineItemsType: Array.isArray(firstOrder.LineItems) ? 'array' : typeof firstOrder.LineItems,
                  LineItemsLength: firstOrder.LineItems ? firstOrder.LineItems.length : 'N/A',
                  LineItemCount: firstOrder.LineItemCount,
                  HasProductionOrders: firstOrder.HasProductionOrders,
                  TotalBOMItems: firstOrder.TotalBOMItems
                });

                // Log structure of first line item if it exists
                if (firstOrder.LineItems && firstOrder.LineItems.length > 0) {
                  const firstLineItem = firstOrder.LineItems[0];
                  console.log("First line item structure:", {
                    InventoryID: firstLineItem.InventoryID,
                    LineDescription: firstLineItem.LineDescription,
                    Quantity: firstLineItem.Quantity,
                    BOMItems: firstLineItem.BOMItems,
                    BOMItemsType: Array.isArray(firstLineItem.BOMItems) ? 'array' : typeof firstLineItem.BOMItems,
                    BOMItemsLength: firstLineItem.BOMItems ? firstLineItem.BOMItems.length : 'N/A',
                    HasProduction: firstLineItem.HasProduction,
                    allLineItemKeys: Object.keys(firstLineItem)
                  });
                }
              }

              db.close();
              resolve(salesOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("Error retrieving sales orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("Error creating sales order transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("Error in getSalesOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  async getProductionOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.productionDbName);

        request.onerror = (event) => {
          console.error("Error opening production database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const db = event.target.result;

          if (!db.objectStoreNames.contains(this.productionStoreName)) {
            console.warn("Production store not found");
            db.close();
            resolve([]);
            return;
          }

          try {
            const transaction = db.transaction([this.productionStoreName], 'readonly');
            const store = transaction.objectStore(this.productionStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const productionOrders = event.target.result || [];
              console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);
              db.close();
              resolve(productionOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("Error retrieving production orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("Error creating transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("Error in getProductionOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredComponents.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          comp.OrderNbr.toLowerCase().includes(searchLower) ||
          comp.InventoryID.toLowerCase().includes(searchLower) ||
          comp.Description.toLowerCase().includes(searchLower) ||
          comp.ProductionNbr.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      // First sort by OrderNbr, then by level, then by the selected field
      if (a.OrderNbr !== b.OrderNbr) {
        return a.OrderNbr.localeCompare(b.OrderNbr);
      }

      if (a.level !== b.level) {
        return a.level - b.level;
      }

      let aValue, bValue;
      switch (this.sortField) {
        case 'InventoryID':
          aValue = a.InventoryID;
          bValue = b.InventoryID;
          break;
        case 'Quantity':
          aValue = a.Quantity;
          bValue = b.Quantity;
          break;
        case 'ProductionNbr':
          aValue = a.ProductionNbr;
          bValue = b.ProductionNbr;
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading components data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    // Debug information
    console.log("Rendering components content:");
    console.log("- Total components:", this.components.length);
    console.log("- Filtered components:", this.filteredComponents.length);
    console.log("- Parent sales orders:", this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0);
    console.log("- Components are now loaded directly from IndexedDB");

    // Calculate summary statistics
    const totalComponents = this.filteredComponents.length;
    const salesOrderCount = new Set(this.filteredComponents.map(c => c.OrderNbr)).size;
    const productionOrderCount = new Set(this.filteredComponents.filter(c => c.ProductionNbr).map(c => c.ProductionNbr)).size;

    tableContainer.innerHTML = `
      <!-- Summary Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Components</div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${totalComponents}</div>
        </div>
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Sales Orders</div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${salesOrderCount}</div>
        </div>
        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Production Orders</div>
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${productionOrderCount}</div>
        </div>
      </div>

      <!-- Components Search and Controls -->
      <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <div class="relative mb-4 md:mb-0">
          <input
            type="text"
            id="components-search"
            placeholder="Search orders, inventory, production..."
            class="w-full sm:w-80 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
            value="${this.searchTerm || ''}"
          >
          <button id="clear-components-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Components Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                Order # <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="InventoryID">
                Inventory ID <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Description
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="ProductionNbr">
                Production # <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Quantity">
                Quantity <span class="sort-indicator"></span>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                UOM
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Unit Price
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ext. Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${this.renderTableRows()}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredComponents.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredComponents.length)} of
          ${this.filteredComponents.length} components
        </div>

        <div class="flex items-center space-x-1">
          <button id="components-first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="components-prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="components-next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="components-last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;

    this.setupComponentsEventListeners();
  }

  renderTableRows() {
    if (this.filteredComponents.length === 0) {
      // Provide debugging information
      const salesOrdersCount = this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0;
      const debugInfo = `
        <div class="text-sm text-gray-600 dark:text-gray-400 mt-2">
          <p>Debug Information:</p>
          <ul class="list-disc list-inside mt-1">
            <li>Sales Orders Available: ${salesOrdersCount}</li>
            <li>Total Components Built: ${this.components.length}</li>
            <li>Search Term: "${this.searchTerm || 'none'}"</li>
          </ul>
          ${salesOrdersCount === 0 ? '<p class="mt-2 text-orange-600">No sales orders loaded. Please refresh the sales orders data first.</p>' : ''}
        </div>
      `;

      return `
        <tr>
          <td colspan="8" class="px-6 py-8 text-center">
            <div class="text-gray-500 dark:text-gray-400">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <p class="text-lg font-medium">No components found</p>
              ${debugInfo}
            </div>
          </td>
        </tr>
      `;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredComponents.length);
    const pageComponents = this.filteredComponents.slice(startIndex, endIndex);

    return pageComponents.map(component => {
      // Determine row styling based on component type and level
      let rowClass = 'hover:bg-gray-50 dark:hover:bg-gray-800';
      let levelIndicator = '';
      let typeIcon = '';

      if (component.type === 'sales_order') {
        rowClass += ' bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500';
        typeIcon = '<i class="fas fa-shopping-cart text-blue-600 mr-2"></i>';
      } else if (component.type === 'production_material') {
        rowClass += ' bg-green-50 dark:bg-green-900/20 border-l-4 border-green-500';
        levelIndicator = '<span class="text-gray-400 mr-2">└─</span>';
        typeIcon = '<i class="fas fa-cogs text-green-600 mr-2"></i>';
      }

      return `
        <tr class="${rowClass}">
          <td class="px-3 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              ${typeIcon}${this.escapeHtml(component.OrderNbr)}
            </div>
          </td>
          <td class="px-3 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              ${levelIndicator}${this.escapeHtml(component.InventoryID)}
            </div>
            ${component.ParentInventoryID && component.type === 'production_material' ? `
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Parent: ${this.escapeHtml(component.ParentInventoryID)}
              </div>
            ` : ''}
          </td>
          <td class="px-3 py-4 text-sm text-gray-900 dark:text-white">
            <div class="max-w-xs truncate" title="${this.escapeHtml(component.Description)}">
              ${this.escapeHtml(component.Description || '-')}
            </div>
          </td>
          <td class="px-3 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-900 dark:text-white">
              ${component.ProductionNbr ? this.escapeHtml(component.ProductionNbr) : '-'}
            </div>
            ${component.OperationNbr ? `
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Op: ${this.escapeHtml(component.OperationNbr)}
              </div>
            ` : ''}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
            ${this.formatNumber(component.Quantity)}
            ${component.QtyIssued !== undefined ? `
              <div class="text-xs text-gray-500 dark:text-gray-400">
                Issued: ${this.formatNumber(component.QtyIssued)}
              </div>
            ` : ''}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
            ${this.escapeHtml(component.UOM || '-')}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
            ${this.formatCurrency(component.UnitPrice)}
          </td>
          <td class="px-3 py-4 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
            ${this.formatCurrency(component.ExtAmount)}
          </td>
        </tr>
      `;
    }).join('');
  }

  setupComponentsEventListeners() {
    // Search input
    const searchInput = document.getElementById('components-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-components-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination buttons
    const firstPageBtn = document.getElementById('components-first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.render();
      });
    }

    const prevPageBtn = document.getElementById('components-prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('components-next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('components-last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.render();
      });
    }
  }

  // Utility methods for formatting
  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  formatCurrency(value) {
    if (value === null || value === undefined || isNaN(value)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  escapeHtml(text) {
    if (!text) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  showError(message) {
    console.error("Components error:", message);

    const tableContainer = document.getElementById('sales-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }
}
