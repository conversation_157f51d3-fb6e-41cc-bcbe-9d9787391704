// Sales Order Components component for MRP Dashboard
export class SalesOrderComponents {
  constructor(container, parentComponent) {
    this.container = container;
    this.parentComponent = parentComponent;
    this.components = [];
    this.filteredComponents = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'OrderNbr';
    this.sortDirection = 'asc';
    this.filterType = 'all'; // Filter by component type
    this.isLoading = false;

    // Database configuration for production orders
    this.productionDbName = 'productionDb';
    this.productionStoreName = 'productionOrders';

    // Database configuration for sales orders (same as parent component)
    this.salesOrderDbName = 'salesOrderDb';
    this.salesOrderStoreName = 'salesOrders';
  }

  async init() {
    console.log("=== SALES ORDER COMPONENTS INIT STARTED ===");
    console.log("Container:", this.container);
    console.log("Parent component:", this.parentComponent);
    console.log("Parent sales orders count:", this.parentComponent?.salesOrders?.length || 0);

    this.isLoading = true;
    this.render();

    try {
      console.log("About to call buildComponentsTable...");
      // Build hierarchical components table from sales orders and production data
      await this.buildComponentsTable();
      console.log("buildComponentsTable completed");

      this.isLoading = false;
      this.render();
      console.log("=== SALES ORDER COMPONENTS INIT COMPLETED ===");
    } catch (error) {
      console.error("=== ERROR IN SALES ORDER COMPONENTS INIT ===");
      console.error("Error initializing components:", error);
      console.error("Stack trace:", error.stack);
      this.isLoading = false;
      this.showError("Failed to initialize components: " + error.message);
      this.render();
    }
  }

  async buildComponentsTable() {
    try {
      console.log("=== STARTING buildComponentsTable ===");

      // Get sales orders from IndexedDB (primary source for consistent data)
      console.log("Getting sales orders from IndexedDB...");
      let salesOrders = await this.getSalesOrdersFromIndexedDB();
      console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);

      // Fallback to parent component if IndexedDB is empty
      if (salesOrders.length === 0 && this.parentComponent && this.parentComponent.salesOrders) {
        console.log("No data in IndexedDB, using parent component data...");
        salesOrders = this.parentComponent.salesOrders || [];
        console.log(`Using ${salesOrders.length} sales orders from parent component`);
      }

      if (salesOrders.length === 0) {
        console.error("NO SALES ORDERS FOUND FROM ANY SOURCE!");
        this.components = [];
        this.filteredComponents = [];
        return;
      }

      console.log("Sample sales order structure:", salesOrders[0] ? {
        OrderNbr: salesOrders[0].OrderNbr,
        LineItemsCount: salesOrders[0].LineItems ? salesOrders[0].LineItems.length : 0,
        HasProductionOrders: salesOrders[0].HasProductionOrders,
        TotalBOMItems: salesOrders[0].TotalBOMItems
      } : "No sales orders");

      // Get production orders from IndexedDB
      const productionOrders = await this.getProductionOrdersFromIndexedDB();
      console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);

      // Create a lookup map for production orders by MainProductionNbr for faster access
      const productionLookup = {};
      productionOrders.forEach(po => {
        if (po.MainProductionNbr) {
          productionLookup[po.MainProductionNbr] = po;
        }
      });
      console.log(`Created production lookup with ${Object.keys(productionLookup).length} entries`);

      const componentsArray = [];
      let totalComponentsProcessed = 0;
      let totalBOMItemsFound = 0;
      let totalMaterialsAdded = 0;

      // Process each sales order
      salesOrders.forEach((order, orderIndex) => {
        console.log(`Processing order ${orderIndex + 1}/${salesOrders.length}: ${order.OrderNbr}`);

        if (!Array.isArray(order.LineItems)) {
          console.log(`Order ${order.OrderNbr} has no valid LineItems array`);
          return;
        }

        console.log(`Processing ${order.LineItems.length} line items for order ${order.OrderNbr}`);

        order.LineItems.forEach((lineItem, itemIndex) => {
          console.log(`Processing line item ${itemIndex + 1}: ${lineItem.InventoryID || 'N/A'}`);

          // Check if this line item has BOM items (production orders)
          const hasBOM = Array.isArray(lineItem.BOMItems) && lineItem.BOMItems.length > 0;
          const hasProduction = lineItem.HasProduction || false;

          if (hasBOM || hasProduction) {
            console.log(`Line item ${lineItem.InventoryID} has BOM - checking for production materials`);
            totalBOMItemsFound++;

            // This item has BOM, so we don't add it as a regular component
            // Instead, we add its materials from the production order
            let foundProductionMaterials = false;

            if (hasBOM) {
              lineItem.BOMItems.forEach(bomItem => {
                const productionNbr = bomItem.ProductionNbr || '';
                console.log(`Looking for production order: ${productionNbr}`);

                if (productionNbr && productionLookup[productionNbr]) {
                  const productionOrder = productionLookup[productionNbr];
                  console.log(`Found production order ${productionNbr} with ${productionOrder.Materials ? productionOrder.Materials.length : 0} materials`);

                  if (Array.isArray(productionOrder.Materials) && productionOrder.Materials.length > 0) {
                    foundProductionMaterials = true;

                    // Add each material as a component
                    productionOrder.Materials.forEach((material, matIndex) => {
                      const materialComponent = {
                        id: `mat-${order.OrderNbr}-${lineItem.id || itemIndex}-${material.id || matIndex}`,
                        type: 'production_material',
                        level: 1, // All components are at level 1 for flat counting
                        OrderNbr: order.OrderNbr,
                        InventoryID: material.InventoryID || 'N/A',
                        Description: material.Description || '',
                        Quantity: material.QtyRequired || 0,
                        ProductionNbr: productionNbr,
                        IsFromBOM: true,
                        ParentOrderNbr: order.OrderNbr,
                        ParentInventoryID: lineItem.InventoryID || '',
                        ParentDescription: lineItem.LineDescription || '',
                        UOM: material.UOM || '',
                        UnitPrice: material.UnitCost || 0,
                        ExtAmount: (material.QtyRequired || 0) * (material.UnitCost || 0),
                        OperationNbr: material.OperationNbr || '',
                        QtyIssued: material.QtyIssued || 0,
                        TotalCost: material.TotalCost || 0
                      };

                      componentsArray.push(materialComponent);
                      totalMaterialsAdded++;
                      console.log(`Added material component: ${materialComponent.InventoryID} from production ${productionNbr}`);
                    });
                  }
                } else {
                  console.log(`Production order not found for ProductionNbr: ${productionNbr}`);
                }
              });
            }

            // If we couldn't find production materials, add the original item as a fallback
            if (!foundProductionMaterials) {
              console.log(`No production materials found for ${lineItem.InventoryID}, adding as regular component`);
              const fallbackComponent = {
                id: `so-${order.OrderNbr}-${lineItem.id || itemIndex}`,
                type: 'sales_order_fallback',
                level: 1,
                OrderNbr: order.OrderNbr,
                InventoryID: lineItem.InventoryID || 'N/A',
                Description: lineItem.LineDescription || '',
                Quantity: lineItem.Quantity || 0,
                ProductionNbr: '',
                IsFromBOM: false,
                ParentOrderNbr: order.OrderNbr,
                ParentInventoryID: '',
                UOM: lineItem.UOM || '',
                UnitPrice: lineItem.UnitPrice || 0,
                ExtAmount: lineItem.ExtAmount || 0
              };

              componentsArray.push(fallbackComponent);
              totalComponentsProcessed++;
            }
          } else {
            // Regular line item without BOM - add as a component
            const regularComponent = {
              id: `so-${order.OrderNbr}-${lineItem.id || itemIndex}`,
              type: 'sales_order',
              level: 1,
              OrderNbr: order.OrderNbr,
              InventoryID: lineItem.InventoryID || 'N/A',
              Description: lineItem.LineDescription || '',
              Quantity: lineItem.Quantity || 0,
              ProductionNbr: '',
              IsFromBOM: false,
              ParentOrderNbr: order.OrderNbr,
              ParentInventoryID: '',
              UOM: lineItem.UOM || '',
              UnitPrice: lineItem.UnitPrice || 0,
              ExtAmount: lineItem.ExtAmount || 0
            };

            componentsArray.push(regularComponent);
            totalComponentsProcessed++;
            console.log(`Added regular component: ${regularComponent.InventoryID} for order ${order.OrderNbr}`);
          }
        });
      });

      this.components = componentsArray;
      console.log(`=== COMPONENTS TABLE BUILT ===`);
      console.log(`Total components: ${this.components.length}`);
      console.log(`Regular components: ${totalComponentsProcessed}`);
      console.log(`BOM items found: ${totalBOMItemsFound}`);
      console.log(`Production materials added: ${totalMaterialsAdded}`);
      console.log(`Final component count: ${totalComponentsProcessed + totalMaterialsAdded}`);

      this.filteredComponents = [...this.components];
      this.calculateTotalPages();

    } catch (error) {
      console.error("Error building components table:", error);
      this.components = [];
      this.filteredComponents = [];
    }
  }

  async getSalesOrdersFromIndexedDB() {
    console.log("=== getSalesOrdersFromIndexedDB called ===");
    console.log("Database name:", this.salesOrderDbName);
    console.log("Store name:", this.salesOrderStoreName);

    return new Promise((resolve, reject) => {
      try {
        console.log("Opening IndexedDB...");
        const request = indexedDB.open(this.salesOrderDbName);

        request.onerror = (event) => {
          console.error("Error opening sales order database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          console.log("IndexedDB opened successfully");
          const db = event.target.result;
          console.log("Database version:", db.version);
          console.log("Available stores:", Array.from(db.objectStoreNames));

          if (!db.objectStoreNames.contains(this.salesOrderStoreName)) {
            console.warn("Sales order store not found");
            console.warn("Looking for store:", this.salesOrderStoreName);
            console.warn("Available stores:", Array.from(db.objectStoreNames));
            db.close();
            resolve([]);
            return;
          }

          console.log("Sales order store found, creating transaction...");

          try {
            const transaction = db.transaction([this.salesOrderStoreName], 'readonly');
            const store = transaction.objectStore(this.salesOrderStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const salesOrders = event.target.result || [];
              console.log(`Retrieved ${salesOrders.length} sales orders from IndexedDB`);

              // Log detailed structure of first sales order
              if (salesOrders.length > 0) {
                const firstOrder = salesOrders[0];
                console.log("First sales order structure from IndexedDB:", {
                  OrderNbr: firstOrder.OrderNbr,
                  id: firstOrder.id,
                  allKeys: Object.keys(firstOrder),
                  LineItems: firstOrder.LineItems,
                  LineItemsType: Array.isArray(firstOrder.LineItems) ? 'array' : typeof firstOrder.LineItems,
                  LineItemsLength: firstOrder.LineItems ? firstOrder.LineItems.length : 'N/A',
                  LineItemCount: firstOrder.LineItemCount,
                  HasProductionOrders: firstOrder.HasProductionOrders,
                  TotalBOMItems: firstOrder.TotalBOMItems
                });

                // Log structure of first line item if it exists
                if (firstOrder.LineItems && firstOrder.LineItems.length > 0) {
                  const firstLineItem = firstOrder.LineItems[0];
                  console.log("First line item structure:", {
                    InventoryID: firstLineItem.InventoryID,
                    LineDescription: firstLineItem.LineDescription,
                    Quantity: firstLineItem.Quantity,
                    BOMItems: firstLineItem.BOMItems,
                    BOMItemsType: Array.isArray(firstLineItem.BOMItems) ? 'array' : typeof firstLineItem.BOMItems,
                    BOMItemsLength: firstLineItem.BOMItems ? firstLineItem.BOMItems.length : 'N/A',
                    HasProduction: firstLineItem.HasProduction,
                    allLineItemKeys: Object.keys(firstLineItem)
                  });
                }
              }

              db.close();
              resolve(salesOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("Error retrieving sales orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("Error creating sales order transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("Error in getSalesOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  async getProductionOrdersFromIndexedDB() {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open(this.productionDbName);

        request.onerror = (event) => {
          console.error("Error opening production database:", event.target.error);
          resolve([]); // Return empty array instead of rejecting
        };

        request.onsuccess = (event) => {
          const db = event.target.result;

          if (!db.objectStoreNames.contains(this.productionStoreName)) {
            console.warn("Production store not found");
            db.close();
            resolve([]);
            return;
          }

          try {
            const transaction = db.transaction([this.productionStoreName], 'readonly');
            const store = transaction.objectStore(this.productionStoreName);
            const getAllRequest = store.getAll();

            getAllRequest.onsuccess = (event) => {
              const productionOrders = event.target.result || [];
              console.log(`Retrieved ${productionOrders.length} production orders from IndexedDB`);
              db.close();
              resolve(productionOrders);
            };

            getAllRequest.onerror = (event) => {
              console.error("Error retrieving production orders:", event.target.error);
              db.close();
              resolve([]);
            };

          } catch (error) {
            console.error("Error creating transaction:", error);
            db.close();
            resolve([]);
          }
        };

      } catch (error) {
        console.error("Error in getProductionOrdersFromIndexedDB:", error);
        resolve([]);
      }
    });
  }

  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredComponents.length / this.itemsPerPage));

    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          comp.OrderNbr.toLowerCase().includes(searchLower) ||
          comp.InventoryID.toLowerCase().includes(searchLower) ||
          comp.Description.toLowerCase().includes(searchLower) ||
          comp.ProductionNbr.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply sorting
    filtered.sort((a, b) => {
      // First sort by OrderNbr, then by level, then by the selected field
      if (a.OrderNbr !== b.OrderNbr) {
        return a.OrderNbr.localeCompare(b.OrderNbr);
      }

      if (a.level !== b.level) {
        return a.level - b.level;
      }

      let aValue, bValue;
      switch (this.sortField) {
        case 'InventoryID':
          aValue = a.InventoryID;
          bValue = b.InventoryID;
          break;
        case 'Quantity':
          aValue = a.Quantity;
          bValue = b.Quantity;
          break;
        case 'ProductionNbr':
          aValue = a.ProductionNbr;
          bValue = b.ProductionNbr;
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
    this.currentPage = 1; // Reset to first page when filtering
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    tableContainer.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading components data...</p>
      </div>
    `;
  }

  renderContent() {
    const tableContainer = document.getElementById('sales-table-container');
    if (!tableContainer) return;

    // Debug information
    console.log("Rendering components content:");
    console.log("- Total components:", this.components.length);
    console.log("- Filtered components:", this.filteredComponents.length);
    console.log("- Parent sales orders:", this.parentComponent && this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0);

    // Count component types for summary
    const regularComponents = this.components.filter(c => c.type === 'sales_order' || c.type === 'sales_order_fallback').length;
    const materialComponents = this.components.filter(c => c.type === 'production_material').length;
    const uniqueOrders = [...new Set(this.components.map(c => c.OrderNbr))].length;
    const uniqueProductions = [...new Set(this.components.filter(c => c.ProductionNbr).map(c => c.ProductionNbr))].length;

    tableContainer.innerHTML = `
      <!-- Components Summary -->
      <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
          <div class="text-blue-600 dark:text-blue-400 text-sm font-medium">Total Components</div>
          <div class="text-2xl font-bold text-blue-900 dark:text-blue-100">${this.filteredComponents.length}</div>
          <div class="text-xs text-blue-600 dark:text-blue-400">of ${this.components.length} total</div>
        </div>
        
        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
          <div class="text-green-600 dark:text-green-400 text-sm font-medium">Production Materials</div>
          <div class="text-2xl font-bold text-green-900 dark:text-green-100">${materialComponents}</div>
          <div class="text-xs text-green-600 dark:text-green-400">from BOM</div>
        </div>
        
        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg border border-purple-200 dark:border-purple-800">
          <div class="text-purple-600 dark:text-purple-400 text-sm font-medium">Sales Orders</div>
          <div class="text-2xl font-bold text-purple-900 dark:text-purple-100">${uniqueOrders}</div>
          <div class="text-xs text-purple-600 dark:text-purple-400">with components</div>
        </div>
        
        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg border border-orange-200 dark:border-orange-800">
          <div class="text-orange-600 dark:text-orange-400 text-sm font-medium">Production Orders</div>
          <div class="text-2xl font-bold text-orange-900 dark:text-orange-100">${uniqueProductions}</div>
          <div class="text-xs text-orange-600 dark:text-orange-400">referenced</div>
        </div>
      </div>

      <!-- Search and Filter Controls -->
      <div class="mb-4 flex flex-col sm:flex-row gap-4">
        <div class="flex-1 relative">
          <input 
            type="text" 
            id="components-search" 
            placeholder="Search components by order #, inventory ID, or description..." 
            class="w-full px-4 py-2 pl-10 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value="${this.searchTerm || ''}"
          >
          <div class="absolute left-3 top-1/2 transform -translate-y-1/2">
            <i class="fas fa-search text-gray-400"></i>
          </div>
          <button id="clear-components-search" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
            <i class="fas fa-times"></i>
          </button>
        </div>
        
        <div class="flex gap-2">
          <select id="component-type-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
            <option value="all">All Types</option>
            <option value="sales_order">Sales Order Items</option>
            <option value="production_material">Production Materials</option>
          </select>
          
          <button id="refresh-components" class="px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm flex items-center">
            <i class="fas fa-sync-alt mr-2"></i>
            Refresh
          </button>
        </div>
      </div>

      <!-- Components Table -->
      <div class="overflow-x-auto bg-white dark:bg-gray-900 rounded-lg shadow">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="OrderNbr">
                <div class="flex items-center">
                  Order #
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="InventoryID">
                <div class="flex items-center">
                  Inventory ID
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Description
              </th>
              <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="ProductionNbr">
                <div class="flex items-center">
                  Production #
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="Quantity">
                <div class="flex items-center justify-end">
                  Quantity
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                UOM
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="UnitPrice">
                <div class="flex items-center justify-end">
                  Unit Price
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700" data-sort="ExtAmount">
                <div class="flex items-center justify-end">
                  Ext. Amount
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            ${this.renderTableRows()}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="flex flex-col sm:flex-row justify-between items-center mt-6 space-y-3 sm:space-y-0">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredComponents.length)} to
          ${Math.min(this.currentPage * this.itemsPerPage, this.filteredComponents.length)} of
          ${this.filteredComponents.length} components
          ${this.filteredComponents.length !== this.components.length ? ` (filtered from ${this.components.length} total)` : ''}
        </div>

        <div class="flex items-center space-x-1">
          <button id="components-first-page" class="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300 text-sm" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-double-left"></i>
          </button>
          <button id="components-prev-page" class="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300 text-sm" ${this.currentPage === 1 ? 'disabled' : ''}>
            <i class="fas fa-angle-left"></i>
          </button>

          <span class="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 rounded">
            ${this.currentPage} of ${this.totalPages}
          </span>

          <button id="components-next-page" class="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300 text-sm" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-right"></i>
          </button>
          <button id="components-last-page" class="px-3 py-2 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300 text-sm" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
            <i class="fas fa-angle-double-right"></i>
          </button>
        </div>
      </div>
    `;

    this.setupComponentsEventListeners();
  }

  renderTableRows() {
    if (this.filteredComponents.length === 0) {
      // Get stats for debugging information
      const salesOrdersCount = this.parentComponent && this.parentComponent.salesOrders ? this.parentComponent.salesOrders.length : 0;
      const debugInfo = `
        <div class="text-sm text-gray-600 dark:text-gray-400 mt-4">
          <p class="font-medium mb-2">Debug Information:</p>
          <div class="grid grid-cols-2 gap-4 text-left">
            <div>
              <p>• Sales Orders Available: ${salesOrdersCount}</p>
              <p>• Total Components Built: ${this.components.length}</p>
              <p>• Filtered Components: ${this.filteredComponents.length}</p>
            </div>
            <div>
              <p>• Search Term: "${this.searchTerm || 'none'}"</p>
              <p>• Database Source: IndexedDB + Production DB</p>
              <p>• Component Types: Sales Orders + Production Materials</p>
            </div>
          </div>
          ${salesOrdersCount === 0 ? '<p class="mt-3 p-2 bg-orange-100 dark:bg-orange-900 text-orange-800 dark:text-orange-200 rounded">No sales orders loaded. Please refresh the sales orders data first.</p>' : ''}
          ${this.components.length === 0 ? '<p class="mt-3 p-2 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded">No components were built from the available sales orders. This could mean no BOM items were found or production orders are missing.</p>' : ''}
        </div>
      `;

      return `
        <tr>
          <td colspan="8" class="px-6 py-8 text-center">
            <div class="text-gray-500 dark:text-gray-400">
              <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              <p class="text-xl font-medium mb-2">No components found</p>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Components are built from sales orders with BOM items and their corresponding production materials.
              </p>
              ${debugInfo}
            </div>
          </td>
        </tr>
      `;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredComponents.length);
    const pageComponents = this.filteredComponents.slice(startIndex, endIndex);

    // Group components by order for better visual organization
    const componentsByOrder = {};
    pageComponents.forEach(component => {
      if (!componentsByOrder[component.OrderNbr]) {
        componentsByOrder[component.OrderNbr] = [];
      }
      componentsByOrder[component.OrderNbr].push(component);
    });

    let rowsHtml = '';
    let rowIndex = 0;

    Object.keys(componentsByOrder).forEach(orderNbr => {
      const orderComponents = componentsByOrder[orderNbr];
      
      orderComponents.forEach((component, componentIndex) => {
        // Determine row styling and icons based on component type
        let rowClass = 'hover:bg-gray-50 dark:hover:bg-gray-800';
        let levelIndicator = '';
        let typeIcon = '';
        let rowBorder = '';

        if (component.type === 'sales_order' || component.type === 'sales_order_fallback') {
          // Regular sales order items
          rowClass += ' bg-blue-50 dark:bg-blue-900/20';
          rowBorder = 'border-l-4 border-blue-500';
          typeIcon = '<i class="fas fa-box text-blue-600 mr-2" title="Sales Order Item"></i>';
        } else if (component.type === 'production_material') {
          // Production materials (from BOM)
          rowClass += ' bg-green-50 dark:bg-green-900/20';
          rowBorder = 'border-l-4 border-green-500';
          levelIndicator = '<span class="text-gray-400 mr-2" title="Material from Production Order">├─</span>';
          typeIcon = '<i class="fas fa-cogs text-green-600 mr-2" title="Production Material"></i>';
        }

        // Add alternating background for better readability
        if (rowIndex % 2 === 1) {
          rowClass += ' bg-opacity-30';
        }

        rowsHtml += `
          <tr class="${rowClass} ${rowBorder}">
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                ${typeIcon}${this.escapeHtml(component.OrderNbr)}
              </div>
            </td>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                ${levelIndicator}${this.escapeHtml(component.InventoryID)}
              </div>
              ${component.ParentInventoryID && component.type === 'production_material' ? `
                <div class="text-xs text-gray-500 dark:text-gray-400 ml-6">
                  <i class="fas fa-level-up-alt mr-1"></i>Parent: ${this.escapeHtml(component.ParentInventoryID)}
                </div>
              ` : ''}
            </td>
            <td class="px-3 py-3 text-sm text-gray-900 dark:text-white">
              <div class="max-w-xs" title="${this.escapeHtml(component.Description)}">
                ${this.escapeHtml(component.Description || '-')}
              </div>
              ${component.ParentDescription && component.type === 'production_material' ? `
                <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  <i class="fas fa-info-circle mr-1"></i>For: ${this.escapeHtml(component.ParentDescription)}
                </div>
              ` : ''}
            </td>
            <td class="px-3 py-3 whitespace-nowrap">
              <div class="text-sm text-gray-900 dark:text-white">
                ${component.ProductionNbr ? this.escapeHtml(component.ProductionNbr) : '-'}
              </div>
              ${component.OperationNbr ? `
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  <i class="fas fa-wrench mr-1"></i>Op: ${this.escapeHtml(component.OperationNbr)}
                </div>
              ` : ''}
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
              <div class="font-medium">${this.formatNumber(component.Quantity)}</div>
              ${component.QtyIssued !== undefined && component.QtyIssued > 0 ? `
                <div class="text-xs text-green-600 dark:text-green-400">
                  <i class="fas fa-check-circle mr-1"></i>Issued: ${this.formatNumber(component.QtyIssued)}
                </div>
              ` : ''}
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-white text-center">
              <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                ${this.escapeHtml(component.UOM || '-')}
              </span>
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-right text-sm text-gray-900 dark:text-white">
              ${this.formatCurrency(component.UnitPrice)}
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-right text-sm font-medium text-gray-900 dark:text-white">
              ${this.formatCurrency(component.ExtAmount)}
              ${component.TotalCost && component.TotalCost !== component.ExtAmount ? `
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  Total: ${this.formatCurrency(component.TotalCost)}
                </div>
              ` : ''}
            </td>
          </tr>
        `;
        
        rowIndex++;
      });
    });

    return rowsHtml;
  }

  setupComponentsEventListeners() {
    // Search input
    const searchInput = document.getElementById('components-search');
    if (searchInput) {
      searchInput.addEventListener('input', this.debounce(() => {
        this.searchTerm = searchInput.value.trim();
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      }, 300));
    }

    // Clear search
    const clearSearchBtn = document.getElementById('clear-components-search');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      });
    }

    // Component type filter
    const typeFilter = document.getElementById('component-type-filter');
    if (typeFilter) {
      typeFilter.addEventListener('change', () => {
        this.filterType = typeFilter.value;
        this.currentPage = 1;
        this.applyFilters();
        this.render();
      });
    }

    // Refresh components
    const refreshBtn = document.getElementById('refresh-components');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', async () => {
        console.log('Refreshing components...');
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Refreshing...';
        
        try {
          // Rebuild the components table from fresh data
          await this.buildComponentsTable();
          this.applyFilters();
          this.render();
        } catch (error) {
          console.error('Error refreshing components:', error);
          this.showError('Failed to refresh components: ' + error.message);
        } finally {
          // Restore button state
          setTimeout(() => {
            if (refreshBtn) {
              refreshBtn.disabled = false;
              refreshBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Refresh';
            }
          }, 500);
        }
      });
    }

    // Sort headers
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    sortHeaders.forEach(header => {
      header.addEventListener('click', () => {
        const field = header.getAttribute('data-sort');
        if (this.sortField === field) {
          this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
          this.sortField = field;
          this.sortDirection = 'asc';
        }
        this.applyFilters();
        this.render();
      });
    });

    // Pagination buttons
    const firstPageBtn = document.getElementById('components-first-page');
    if (firstPageBtn) {
      firstPageBtn.addEventListener('click', () => {
        this.currentPage = 1;
        this.render();
      });
    }

    const prevPageBtn = document.getElementById('components-prev-page');
    if (prevPageBtn) {
      prevPageBtn.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    const nextPageBtn = document.getElementById('components-next-page');
    if (nextPageBtn) {
      nextPageBtn.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }

    const lastPageBtn = document.getElementById('components-last-page');
    if (lastPageBtn) {
      lastPageBtn.addEventListener('click', () => {
        this.currentPage = this.totalPages;
        this.render();
      });
    }
  }

  applyFilters() {
    let filtered = [...this.components];

    // Apply search filter
    if (this.searchTerm && this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(comp => {
        return (
          comp.OrderNbr.toLowerCase().includes(searchLower) ||
          comp.InventoryID.toLowerCase().includes(searchLower) ||
          comp.Description.toLowerCase().includes(searchLower) ||
          comp.ProductionNbr.toLowerCase().includes(searchLower) ||
          (comp.ParentInventoryID && comp.ParentInventoryID.toLowerCase().includes(searchLower)) ||
          (comp.ParentDescription && comp.ParentDescription.toLowerCase().includes(searchLower))
        );
      });
    }

    // Apply type filter
    if (this.filterType && this.filterType !== 'all') {
      if (this.filterType === 'sales_order') {
        filtered = filtered.filter(comp => comp.type === 'sales_order' || comp.type === 'sales_order_fallback');
      } else if (this.filterType === 'production_material') {
        filtered = filtered.filter(comp => comp.type === 'production_material');
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      // First sort by OrderNbr for grouping
      if (a.OrderNbr !== b.OrderNbr) {
        return a.OrderNbr.localeCompare(b.OrderNbr);
      }

      // Then sort by the selected field
      let aValue, bValue;
      switch (this.sortField) {
        case 'InventoryID':
          aValue = a.InventoryID || '';
          bValue = b.InventoryID || '';
          break;
        case 'Quantity':
          aValue = parseFloat(a.Quantity || 0);
          bValue = parseFloat(b.Quantity || 0);
          break;
        case 'UnitPrice':
          aValue = parseFloat(a.UnitPrice || 0);
          bValue = parseFloat(b.UnitPrice || 0);
          break;
        case 'ExtAmount':
          aValue = parseFloat(a.ExtAmount || 0);
          bValue = parseFloat(b.ExtAmount || 0);
          break;
        case 'ProductionNbr':
          aValue = a.ProductionNbr || '';
          bValue = b.ProductionNbr || '';
          break;
        default:
          aValue = a[this.sortField] || '';
          bValue = b[this.sortField] || '';
      }

      // Handle different data types
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      } else {
        // String comparison
        const aStr = String(aValue).toLowerCase();
        const bStr = String(bValue).toLowerCase();
        if (this.sortDirection === 'asc') {
          return aStr.localeCompare(bStr);
        } else {
          return bStr.localeCompare(aStr);
        }
      }
    });

    this.filteredComponents = filtered;
    this.calculateTotalPages();
  }

  // Utility methods for formatting
  formatNumber(value) {
    if (value === null || value === undefined || isNaN(value)) return '0';
    return parseFloat(value).toFixed(2);
  }

  formatCurrency(value) {
    if (value === null || value === undefined || isNaN(value)) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  }

  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  escapeHtml(text) {
    if (!text) return '';

    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  showError(message) {
    console.error("Components error:", message);

    const tableContainer = document.getElementById('sales-table-container');
    if (tableContainer) {
      tableContainer.innerHTML = `
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4 dark:bg-red-900 dark:border-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-red-500 dark:text-red-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">${message}</p>
            </div>
          </div>
        </div>
      `;
    }
  }
}
