// Purchasing component for MRP Dashboard
import { connectionManager } from '../../core/connection.js';

export class PurchasingComponent {
  constructor(container) {
    this.container = container;
    this.purchaseOrders = [];
    this.filteredOrders = [];
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.totalPages = 1;
    this.searchTerm = '';
    this.sortField = 'Date';  // Default sort by date
    this.sortDirection = 'desc';  // Default newest first
    this.filterStatus = 'Open';
    this.isLoading = true;
    this.dbName = 'purchasingDb';
    this.storeName = 'purchaseOrders';
    this.settingsStoreName = 'appSettings';
    this.dataSource = null; // 'acumatica', 'indexedDB'
    this.lastSyncTime = null;
    
    // Default date range - last 3 months to now
    const now = new Date();
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(now.getMonth() - 3);
    
    this.dateRange = {
      start: threeMonthsAgo,
      end: now
    };
  }

  async init() {
    console.log("Initializing Purchasing component");
    
    // Only use a single loading indicator
    this.isLoading = true;
    this.render();
    
    try {
      // Initialize IndexedDB
      await this.initDatabase();
      
      // Check Acumatica connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      const isConnected = connectionStatus && connectionStatus.acumatica && connectionStatus.acumatica.isConnected;
      
      // Load purchase order data - fetch from Acumatica if connected
      await this.loadData(isConnected);
    
      // Update loading state and render again
      this.isLoading = false;
      this.render();
    
      // Set up event listeners
      this.setupEventListeners();
    } catch (error) {
      console.error("Error initializing purchase orders:", error);
      this.isLoading = false;
      this.showError("Failed to initialize: " + error.message);
      this.render();
    }
  }

  async initDatabase() {
    return new Promise((resolve, reject) => {
      // Try to open the database without specifying version
      const checkRequest = indexedDB.open(this.dbName);
      
      checkRequest.onsuccess = (event) => {
        const db = event.target.result;
        let needsUpgrade = false;
        
        // Check if all required stores exist
        if (!db.objectStoreNames.contains(this.storeName)) {
          needsUpgrade = true;
        }
        if (!db.objectStoreNames.contains(this.settingsStoreName)) {
          needsUpgrade = true;
        }
        
        const currentVersion = db.version;
        db.close();
        
        if (!needsUpgrade) {
          // Database exists and has all needed stores
          console.log("Purchasing database already exists with correct schema, version:", currentVersion);
          resolve();
          return;
        }
        
        // Need to upgrade the database
        console.log("Need to upgrade purchasing database schema");
        
        // Open with a new version number
        const request = indexedDB.open(this.dbName, currentVersion + 1);
        
        request.onerror = (event) => {
          console.error("Error opening IndexedDB:", event.target.error);
          reject(new Error("Could not open purchasing database"));
        };
        
        request.onsuccess = (event) => {
          console.log("Successfully opened purchasing database");
          resolve();
        };
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Upgrading purchasing database schema to version", db.version);
          
          // Create object store for purchase orders if it doesn't exist
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: "id" });
            
            // Create indices with unique: false
            store.createIndex("OrderNbr", "OrderNbr", { unique: false });
            store.createIndex("Date", "Date", { unique: false });
            store.createIndex("PromisedOn", "PromisedOn", { unique: false });
            store.createIndex("Status", "Status", { unique: false });
            store.createIndex("VendorID", "VendorID", { unique: false });
            store.createIndex("AccountName", "AccountName", { unique: false });
            
            console.log("Created purchase orders store");
          }
          
          // Create object store for app settings if it doesn't exist
          if (!db.objectStoreNames.contains(this.settingsStoreName)) {
            db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
            console.log("Created settings store");
          }
          
          console.log("Purchasing database schema upgrade complete");
        };
      };
      
      checkRequest.onerror = (event) => {
        console.error("Error checking database:", event.target.error);
        
        // If we can't open the database at all, try creating it from scratch
        const freshRequest = indexedDB.open(this.dbName, 1);
        
        freshRequest.onerror = (event) => {
          console.error("Error creating new database:", event.target.error);
          reject(new Error("Could not create purchasing database"));
        };
        
        freshRequest.onsuccess = (event) => {
          console.log("Successfully created fresh purchasing database");
          resolve();
        };
        
        freshRequest.onupgradeneeded = (event) => {
          const db = event.target.result;
          console.log("Creating new purchasing database schema");
          
          // Create object store for purchase orders
          const store = db.createObjectStore(this.storeName, { keyPath: "id" });
          
          // Create indices
          store.createIndex("OrderNbr", "OrderNbr", { unique: false });
          store.createIndex("Date", "Date", { unique: false });
          store.createIndex("PromisedOn", "PromisedOn", { unique: false });
          store.createIndex("Status", "Status", { unique: false });
          store.createIndex("VendorID", "VendorID", { unique: false });
          store.createIndex("AccountName", "AccountName", { unique: false });
          
          // Create object store for app settings
          db.createObjectStore(this.settingsStoreName, { keyPath: "id" });
          
          console.log("Purchasing database schema created");
        };
      };
    });
  }

  render() {
    if (this.isLoading) {
      this.renderLoading();
    } else {
      this.renderContent();
    }
  }

  renderLoading() {
    this.container.innerHTML = `
      <div class="flex flex-col items-center justify-center p-8">
        <div class="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p class="mt-4 text-gray-600 dark:text-gray-400">Loading purchase data...</p>
      </div>
    `;
  }

  renderContent() {
    this.container.innerHTML = `
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
        ${this.renderDataSourceNotice()}
        <div class="flex flex-col md:flex-row justify-between items-center mb-6">
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Purchase Orders</h2>
          
          <div class="flex flex-wrap items-center gap-2">
            <div class="relative">
              <input 
                type="text" 
                id="purchase-search" 
                placeholder="Search purchase orders..." 
                class="w-full sm:w-64 px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm"
                value="${this.searchTerm || ''}"
              >
              <button id="clear-search" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${!this.searchTerm ? 'hidden' : ''}">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <select id="order-status-filter" class="px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm">
              <option value="all" ${this.filterStatus === 'all' ? 'selected' : ''}>All Orders</option>
              <option value="Open" ${this.filterStatus === 'Open' ? 'selected' : ''}>Open</option>
              <option value="Closed" ${this.filterStatus === 'Closed' ? 'selected' : ''}>Closed</option>
              <option value="Cancelled" ${this.filterStatus === 'Cancelled' ? 'selected' : ''}>Cancelled</option>
            </select>
            
            <div class="flex gap-2">
              <!-- Purchase Orders Button -->
              <button id="purchase-button" class="p-2 bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600 rounded-md flex items-center" title="Purchase Orders">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <span class="ml-1 text-sm font-medium">Purchase</span>
              </button>

              <!-- Date Range Button -->
              <button id="date-range-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Date Range">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </button>

              <!-- Refresh Button -->
              <button id="refresh-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Refresh Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
              
              <!-- Export Button -->
              <button id="export-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Export Data">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
              </button>
              
              <!-- Settings Button -->
              <button id="settings-button" class="p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md" title="Settings">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <div id="purchase-table-container">
          <!-- Purchase Orders Table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderNbr">
                    Order # <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="AccountName">
                    Vendor Name <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="OrderTotal">
                    Order Total <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Status">
                    Status <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="Date">
                    Order Date <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer" data-sort="PromisedOn">
                    Promised Date <span class="sort-indicator"></span>
                  </th>
                  <th class="px-3 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                ${this.renderTableRows()}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="flex flex-col sm:flex-row justify-between items-center mt-4 space-y-3 sm:space-y-0">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Showing ${Math.min((this.currentPage - 1) * this.itemsPerPage + 1, this.filteredOrders.length)} to 
              ${Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length)} of 
              ${this.filteredOrders.length} results
            </div>
            
            <div class="flex items-center space-x-1">
              <button id="first-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
              </button>
              <button id="prev-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === 1 ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
              </button>
              
              <span class="px-2 py-1 text-sm text-gray-700 dark:text-gray-300">
                ${this.currentPage} of ${this.totalPages}
              </span>
              
              <button id="next-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
              </button>
              <button id="last-page" class="px-2 py-1 rounded border border-gray-300 dark:border-gray-600 ${this.currentPage === this.totalPages ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'} text-gray-700 dark:text-gray-300" ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    this.setupEventListeners();
  }

  renderDataSourceNotice() {
    if (!this.dataSource || this.purchaseOrders.length === 0) {
      return `
        <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4 dark:bg-blue-900 dark:border-blue-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-blue-500 dark:text-blue-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-blue-800 dark:text-blue-200">
                No purchase data available. Connect to Acumatica and click the refresh button to load purchase orders.
              </p>
            </div>
          </div>
        </div>
      `;
    }
    
    if (this.dataSource === 'acumatica') {
      return `
        <div class="bg-green-50 border-l-4 border-green-500 p-4 mb-4 dark:bg-green-900 dark:border-green-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 text-green-500 dark:text-green-400">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-green-700 dark:text-green-200">
                Displaying live data from Acumatica. Last synced: ${this.lastSyncTime || 'Unknown'}
              </p>
            </div>
          </div>
        </div>
      `;
    }
    
    if (this.dataSource === 'indexedDB') {
      // We're keeping this minimal to match the production.js style
      return '';
    }
    
    return '';
  }

  renderTableRows() {
    if (this.filteredOrders.length === 0) {
      return `
        <tr>
          <td colspan="7" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
            No purchase orders found. Click the refresh button to load data.
          </td>
        </tr>
      `;
    }

    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredOrders.length);
    const displayedOrders = this.filteredOrders.slice(start, end);

    return displayedOrders.map(order => `
      <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
        <td class="px-3 py-4 whitespace-nowrap text-sm text-blue-600 dark:text-blue-400 font-medium">
          ${this.escapeHtml(order.OrderNbr || '')}
        </td>
        <td class="px-3 py-4 text-sm text-gray-800 dark:text-gray-200">
          <div class="line-clamp-2">${this.escapeHtml(order.AccountName || '')}</div>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-right text-gray-800 dark:text-gray-200">
          ${this.formatCurrency(order.OrderTotal || 0)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-center">
          <span class="px-2 py-1 text-xs font-semibold rounded-full ${this.getStatusClass(order.Status || '')}">
            ${this.escapeHtml(order.Status || '')}
          </span>
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.Date)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${this.formatDate(order.PromisedOn)}
        </td>
        <td class="px-3 py-4 whitespace-nowrap text-right text-sm font-medium">
          <button data-id="${order.id}" class="view-order text-blue-600 hover:text-blue-900 dark:hover:text-blue-400">
            <i class="fas fa-eye"></i>
          </button>
        </td>
      </tr>
    `).join('');
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.id = 'purchase-error-message';
    errorElement.className = 'mt-4 p-4 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-md';
    errorElement.innerHTML = `
      <div class="flex items-center">
        <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
        <span>${message}</span>
      </div>
      <button class="mt-2 px-2 py-1 bg-red-200 dark:bg-red-800 rounded text-sm" id="dismiss-error">Dismiss</button>
    `;
    
    if (this.container) {
      this.container.appendChild(errorElement);
      
      // Add event listener to dismiss button
      const dismissButton = document.getElementById('dismiss-error');
      if (dismissButton) {
        dismissButton.addEventListener('click', () => {
          const errorMsg = document.getElementById('purchase-error-message');
          if (errorMsg) {
            errorMsg.remove();
          }
        });
      }
    } else {
      console.error("Container not available to show error:", message);
    }
  }

  setupEventListeners() {
    // Add a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Search input
      const searchInput = document.getElementById('purchase-search');
      if (searchInput) {
        searchInput.addEventListener('input', this.debounce(() => {
          this.searchTerm = searchInput.value.trim();
          this.currentPage = 1;
          this.applyFilters();
        }, 300));
      }

      // Clear search
      const clearSearchBtn = document.getElementById('clear-search');
      if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', () => {
          this.searchTerm = '';
          if (searchInput) searchInput.value = '';
          this.currentPage = 1;
          this.applyFilters();
        });
      }

      // Status filter
      const statusFilter = document.getElementById('order-status-filter');
      if (statusFilter) {
        statusFilter.addEventListener('change', () => {
          this.filterStatus = statusFilter.value;
          this.currentPage = 1;
          this.applyFilters();
        });
      }
      
      // Refresh button
      const refreshButton = document.getElementById('refresh-button');
      if (refreshButton) {
        refreshButton.addEventListener('click', () => {
          console.log('Refresh button clicked');
          this.loadData(true);
        });
      }

      // Export button
      const exportButton = document.getElementById('export-button');
      if (exportButton) {
        exportButton.addEventListener('click', () => {
          console.log('Export button clicked');
          this.exportPurchaseData();
        });
      }

      // Settings button
      const settingsButton = document.getElementById('settings-button');
      if (settingsButton) {
        settingsButton.addEventListener('click', () => {
          console.log('Settings button clicked');
          this.showSettings();
        });
      }

      // Sort headers
      const sortHeaders = document.querySelectorAll('th[data-sort]');
      sortHeaders.forEach(header => {
        header.addEventListener('click', () => {
          const field = header.getAttribute('data-sort');
          if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
          } else {
            this.sortField = field;
            this.sortDirection = 'asc';
          }
          this.applyFilters();
          
          // Update sort indicators
          sortHeaders.forEach(h => {
            const indicator = h.querySelector('.sort-indicator');
            if (indicator) {
              if (h.getAttribute('data-sort') === this.sortField) {
                indicator.innerHTML = this.sortDirection === 'asc' ? ' ▲' : ' ▼';
              } else {
                indicator.innerHTML = '';
              }
            }
          });
        });
        
        // Set initial indicators
        const indicator = header.querySelector('.sort-indicator');
        if (indicator) {
          if (header.getAttribute('data-sort') === this.sortField) {
            indicator.innerHTML = this.sortDirection === 'asc' ? ' ▲' : ' ▼';
          }
        }
      });

      // Pagination event handlers
      const firstPageBtn = document.getElementById('first-page');
      if (firstPageBtn) {
        firstPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage = 1;
            this.render();
          }
        });
      }

      const prevPageBtn = document.getElementById('prev-page');
      if (prevPageBtn) {
        prevPageBtn.addEventListener('click', () => {
          if (this.currentPage > 1) {
            this.currentPage--;
            this.render();
          }
        });
      }

      const nextPageBtn = document.getElementById('next-page');
      if (nextPageBtn) {
        nextPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.render();
          }
        });
      }

      const lastPageBtn = document.getElementById('last-page');
      if (lastPageBtn) {
        lastPageBtn.addEventListener('click', () => {
          if (this.currentPage < this.totalPages) {
            this.currentPage = this.totalPages;
            this.render();
          }
        });
      }

      // View order buttons
      const viewButtons = document.querySelectorAll('.view-order');
      viewButtons.forEach(button => {
        button.addEventListener('click', (event) => {
          event.preventDefault();
          event.stopPropagation();
          const orderId = button.getAttribute('data-id');
          this.viewOrderDetails(orderId);
        });
      });

      // Date Range button
      const dateRangeButton = document.getElementById('date-range-button');
      if (dateRangeButton) {
        dateRangeButton.addEventListener('click', () => {
          this.showDateRangePicker();
        });
      }
      
      console.log('All purchase component event listeners have been set up');
    }, 50); // Small delay to ensure DOM is ready
  }

  applyFilters() {
    // Filter by search term
    let filtered = this.purchaseOrders;
    
    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(order => 
        (order.OrderNbr?.toLowerCase().includes(term)) ||
        (order.VendorID?.toLowerCase().includes(term)) ||
        (order.AccountName?.toLowerCase().includes(term)) ||
        (order.Status?.toLowerCase().includes(term))
      );
    }
    
    // Filter by status
    if (this.filterStatus !== 'all') {
      filtered = filtered.filter(order => 
        order.Status?.toLowerCase() === this.filterStatus.toLowerCase()
      );
    }
    
    // Sort the data
    filtered.sort((a, b) => {
      let comparison = 0;
      const fieldA = a[this.sortField];
      const fieldB = b[this.sortField];

      // Handle null/undefined values
      const valA = fieldA ?? '';
      const valB = fieldB ?? '';

      switch (this.sortField) {
        case 'OrderNbr':
        case 'VendorID':
        case 'AccountName':
        case 'Status':
          comparison = String(valA).localeCompare(String(valB));
          break;
        case 'OrderTotal':
          comparison = parseFloat(valA) - parseFloat(valB);
          break;
        case 'Date':
        case 'PromisedOn':
          // Properly handle our custom date objects
          if (valA && valA.isAcumaticaDate && valB && valB.isAcumaticaDate) {
            // Compare by year, month, day directly
            if (valA.year !== valB.year) {
              comparison = valA.year - valB.year;
            } else if (valA.month !== valB.month) {
              comparison = valA.month - valB.month;
            } else {
              comparison = valA.day - valB.day;
            }
          } else if (valA instanceof Date && valB instanceof Date) {
            // Standard Date object comparison
            comparison = valA.getTime() - valB.getTime();
          } else if (valA && valB) {
            // Fallback to string comparison if we can't determine the format
            comparison = String(valA).localeCompare(String(valB));
          } else {
            // Handle cases where one value might be null/undefined
            comparison = valA ? 1 : (valB ? -1 : 0);
          }
          break;
        default:
          comparison = String(valA).localeCompare(String(valB));
      }

      return this.sortDirection === 'asc' ? comparison : -comparison;
    });
    
    this.filteredOrders = filtered;
    this.calculateTotalPages();
    this.render();
  }

  async loadData(forceRefresh = false) {
    try {
      this.isLoading = true;
      this.render();
      
      // Check connection status
      const connectionStatus = connectionManager.getConnectionStatus();
      console.log("Acumatica connection status:", connectionStatus.acumatica);
      
      // When forceRefresh is true, always try to fetch from Acumatica first if connected
      if (forceRefresh && connectionStatus.acumatica.isConnected) {
        console.log("Force refreshing - fetching latest purchase data from Acumatica");
        try {
          const result = await this.fetchAcumaticaPurchases(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            console.log("Successfully fetched data from Acumatica");
            this.purchaseOrders = this.parseAcumaticaPurchases(result.data);
            console.log(`Parsed ${this.purchaseOrders.length} purchase orders`);
            
            // Verify line items are properly parsed
            let totalLineItems = 0;
            this.purchaseOrders.forEach(order => {
              if (Array.isArray(order.LineItems)) {
                totalLineItems += order.LineItems.length;
              }
            });
            console.log(`Total line items: ${totalLineItems}`);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            // Store in IndexedDB for offline access
            await this.storePurchasesInIndexedDB(this.purchaseOrders);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime,
              dateRange: {
                start: this.dateRange.start.toISOString(),
                end: this.dateRange.end.toISOString()
              }
            });
            
            console.log(`Stored ${this.purchaseOrders.length} purchase orders in IndexedDB`);
            
            // Continue with filtering
            this.filteredOrders = [...this.purchaseOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error refreshing from Acumatica:", result.error);
            // Fall through to IndexedDB fallback
          }
        } catch (fetchError) {
          console.error("Error refreshing purchases from Acumatica:", fetchError);
          // Fall through to IndexedDB fallback
        }
      }
      
      // Try to get data from IndexedDB first
      console.log("Attempting to fetch data from IndexedDB...");
      this.purchaseOrders = await this.getPurchasesFromIndexedDB();
      
      // Verify line items in retrieved data
      if (this.purchaseOrders.length > 0) {
        let totalLineItems = 0;
        this.purchaseOrders.forEach(order => {
          if (Array.isArray(order.LineItems)) {
            totalLineItems += order.LineItems.length;
          }
        });
        console.log(`Retrieved from IndexedDB - Total line items: ${totalLineItems}`);
      }
      
      // Also try to get the last sync time and date range
      const settings = await this.loadSettings();
      if (settings) {
        if (settings.lastSyncTime) {
          this.lastSyncTime = settings.lastSyncTime;
        }
        if (settings.dateRange) {
          this.dateRange = {
            start: new Date(settings.dateRange.start),
            end: new Date(settings.dateRange.end)
          };
        }
      }
      
      // If we have data in IndexedDB, use it
      if (this.purchaseOrders.length > 0) {
        console.log(`Retrieved ${this.purchaseOrders.length} purchase orders from IndexedDB`);
        this.dataSource = 'indexedDB';
        this.filteredOrders = [...this.purchaseOrders];
        this.calculateTotalPages();
        this.isLoading = false;
        this.render();
        return;
      }
      
      // If no data in IndexedDB and connected to Acumatica, try fetching from there
      if (connectionStatus.acumatica.isConnected && !forceRefresh) {
        console.log("No data in IndexedDB, trying to fetch from Acumatica");
        try {
          const result = await this.fetchAcumaticaPurchases(connectionStatus.acumatica.instance);
          
          if (result.success) {
            // Parse the data and store in IndexedDB
            this.purchaseOrders = this.parseAcumaticaPurchases(result.data);
            
            // Update data source and sync time
            this.dataSource = 'acumatica';
            this.lastSyncTime = new Date().toLocaleString();
            
            await this.storePurchasesInIndexedDB(this.purchaseOrders);
            
            // Also store the sync info in settings
            await this.saveSettings({
              lastSyncTime: this.lastSyncTime,
              dateRange: {
                start: this.dateRange.start.toISOString(),
                end: this.dateRange.end.toISOString()
              }
            });
            
            console.log(`Stored ${this.purchaseOrders.length} purchase orders in IndexedDB`);
            this.filteredOrders = [...this.purchaseOrders];
            this.calculateTotalPages();
            this.isLoading = false;
            this.render();
            return;
          } else {
            console.warn("Error fetching from Acumatica:", result.error);
          }
        } catch (fetchError) {
          console.error("Error fetching purchases from Acumatica:", fetchError);
        }
      }
      
      // If we reach here, we couldn't get data from either Acumatica or IndexedDB
      this.purchaseOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("No purchase data available. Please connect to Acumatica to fetch data.");
      
    } catch (error) {
      console.error('Error loading purchase data:', error);
      this.purchaseOrders = [];
      this.filteredOrders = [];
      this.dataSource = 'empty';
      this.calculateTotalPages();
      this.isLoading = false;
      this.render();
      this.showError("Error loading purchase data: " + error.message);
    }
  }

  async fetchAcumaticaPurchases(instance) {
    try {
      if (!instance) {
        const connectionStatus = connectionManager.getConnectionStatus();
        if (!connectionStatus.acumatica.instance) {
          throw new Error('No Acumatica instance URL available. Please check connection.');
        }
        instance = connectionStatus.acumatica.instance;
      }
      
      // Format dates as YYYY-MM-DD for the API request
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      let startDateStr = formatDate(this.dateRange.start);
      let endDateStr = formatDate(this.dateRange.end);
      
      // Build the API URL per requirements
      const apiUrl = `${instance}/entity/Enventbridge/22.200.001/PurchaseQty?$expand=PartsQty&$filter=Status eq 'Open'`;
      
      console.log("Fetching purchase orders with URL:", apiUrl);
      
      // Get cookies for authentication
      let cookieString = '';
      if (chrome?.cookies?.getAll) {
        try {
          const url = new URL(instance);
          const cookies = await chrome.cookies.getAll({ domain: url.hostname });
          cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
          console.log("Retrieved cookies for authentication");
        } catch (cookieError) {
          console.warn("Could not retrieve cookies:", cookieError);
        }
      }
      
      // Make request with cookies through the connection manager
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...(cookieString ? { 'Cookie': cookieString } : {})
        },
        credentials: 'include'  // Include cookies for authentication
      });
      
      // Log the response status for debugging
      console.log("Acumatica API response status:", response.status);
      
      // Check response
      if (!response.ok) {
        if (response.status === 401) {
          // Update connection status in connection manager
          connectionManager.connections.acumatica.isConnected = false;
          if (chrome?.storage?.local) {
            await chrome.storage.local.set({ 'connections': connectionManager.connections });
          }
          throw new Error('Authentication failed. Please reconnect to Acumatica.');
        }
        
        // Get response text for better error diagnostics
        const responseText = await response.text();
        throw new Error(`Failed to fetch purchase orders: ${response.status} ${response.statusText}. Details: ${responseText.substring(0, 200)}`);
      }
      
      // Parse response
      const data = await response.json();
      console.log(`Received ${data.length} purchase orders from Acumatica API`);
      
      // Validate the data structure
      if (data.length > 0) {
        const firstItem = data[0];
        console.log("First item structure check:", {
          hasOrderNbr: !!firstItem.OrderNbr,
          hasPartsQty: !!firstItem.PartsQty,
          partsQtyCount: firstItem.PartsQty ? firstItem.PartsQty.length : 0
        });
        
        // Log the full first item to debug (limiting nested objects for readability)
        console.log("First item sample:", this.simplifyForLogging(firstItem));
        
        // Log the first line item if available
        if (firstItem.PartsQty && firstItem.PartsQty.length > 0) {
          const firstLine = firstItem.PartsQty[0];
          console.log("First line item:", {
            id: firstLine.id,
            InventoryID: firstLine.InventoryID?.value,
            BaseOrderQty: firstLine.BaseOrderQty?.value,
            UnitCost: firstLine.UnitCost?.value
          });
        }
      }
      
      return { success: true, data };
    } catch (error) {
      console.error("Error fetching purchases from Acumatica:", error);
      return { success: false, error: error.message };
    }
  }
  
  // Helper method to simplify objects for logging
  simplifyForLogging(obj, depth = 0) {
    if (depth > 1) return "[Nested Object]";
    if (!obj || typeof obj !== 'object') return obj;
    
    const result = {};
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        result[key] = `[Array: ${obj[key].length} items]`;
        if (obj[key].length > 0) {
          result[`${key}[0]`] = this.simplifyForLogging(obj[key][0], depth + 1);
        }
      } else if (typeof obj[key] === 'object') {
        if (key === 'value') {
          result[key] = obj[key];
        } else {
          result[key] = this.simplifyForLogging(obj[key], depth + 1);
        }
      } else {
        result[key] = obj[key];
      }
    }
    return result;
  }

  parseAcumaticaPurchases(purchaseData) {
    try {
      console.log("Parsing Acumatica purchase data, raw sample:", this.simplifyForLogging(purchaseData[0]));
      
      return purchaseData.map(order => {
        try {
          console.log("Processing order:", order.OrderNbr?.value);
          
          // Generate unique ID if needed
          const id = order.id || `po-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
          
          // Extract main purchase order fields
          const orderNbr = order.OrderNbr?.value || '';
          const status = order.Status?.value || '';
          const accountName = order.AccountName?.value || '';
          const orderTotal = parseFloat(order.OrderTotal?.value || 0);
          const taxTotal = parseFloat(order.TaxTotal?.value || 0);
          const orderQty = parseFloat(order.OrderQty?.value || 0);
          const description = order.Description?.value || '';
          const project = order.Project?.value || '';
          const orderType = order.Type?.value || '';
          const currency = order.Currency?.value || '';
          
          // Parse dates - completely avoid Date objects for API dates
          const parseDate = dateStr => {
            if (!dateStr) return null;
            try {
              // Just store the raw string value without creating a Date object
              // This avoids any timezone conversion issues
              const rawDateString = dateStr.value || dateStr;
              
              // For sorting and other operations, extract date parts directly from string
              let year, month, day;
              
              if (typeof rawDateString === 'string' && rawDateString.includes('T')) {
                // Extract date parts from ISO format (2025-04-29T00:00:00+00:00)
                const datePart = rawDateString.split('T')[0];
                [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
              }
              
              return {
                // Keep the raw string exactly as it came from API
                rawValue: rawDateString,
                // Store date parts for direct access
                year: year,
                month: month,
                day: day,
                // Identifiable type
                isAcumaticaDate: true
              };
            } catch (e) {
              console.error("Error parsing date:", dateStr, e);
              return null;
            }
          };
          
          const date = parseDate(order.Date?.value);
          const promisedOn = parseDate(order.PromisedOn?.value);
          
          // Process line items from PartsQty
          const lineItems = [];
          let totalItemAmount = 0;
          
          if (Array.isArray(order.PartsQty) && order.PartsQty.length > 0) {
            order.PartsQty.forEach(item => {
              const baseOrderQty = parseFloat(item.BaseOrderQty?.value || 0);
              const unitCost = parseFloat(item.UnitCost?.value || 0);
              const extCost = baseOrderQty * unitCost;
              
              totalItemAmount += extCost;
              
              const lineItem = {
                id: item.id || `line-${id}-${Math.random().toString(36).substring(2, 10)}`,
                LineNbr: item.rowNumber?.toString() || '',
                InventoryID: item.InventoryID?.value || '',
                Description: item.LineDescription?.value || '',
                UOM: item.UOM?.value || '',
                BaseOrderQty: baseOrderQty,
                OpenQty: parseFloat(item.OpenQty?.value || 0),
                UnitCost: unitCost,
                ExtCost: extCost,
                PromisedOn: parseDate(item.PromisedOn?.value)
              };
              
              lineItems.push(lineItem);
            });
          }
          
          // Build the purchase order object with all available data
          const result = {
            id,
            OrderNbr: orderNbr,
            Status: status,
            AccountName: accountName,
            OrderTotal: orderTotal,
            TaxTotal: taxTotal,
            ItemTotal: totalItemAmount,
            OrderQty: orderQty,
            Description: description,
            Project: project,
            OrderType: orderType,
            Currency: currency,
            Date: date,
            PromisedOn: promisedOn,
            LineItems: lineItems,
            LineItemCount: lineItems.length
          };
          
          // Validate result before returning
          if (!Array.isArray(result.LineItems)) {
            console.error(`LineItems is not an array for ${orderNbr}`, result.LineItems);
            result.LineItems = [];
            result.LineItemCount = 0;
          }
          
          // Log the final object structure for debugging
          console.log("Final purchase order structure for", orderNbr, ":", {
            id: result.id,
            LineItemCount: result.LineItemCount
          });
          
          return result;
        } catch (orderError) {
          console.error("Error parsing individual purchase order:", orderError, order);
          return null;
        }
      }).filter(order => order !== null); // Filter out any orders that failed to parse
    } catch (error) {
      console.error("Error parsing Acumatica purchase orders:", error);
      return [];
    }
  }

  async getPurchasesFromIndexedDB() {
    return new Promise((resolve, reject) => {
      // Open without specifying version to use existing version
      const request = indexedDB.open(this.dbName);
      
      request.onerror = (event) => {
        console.error("Error opening database:", event.target.error);
        reject(new Error("Could not open purchasing database"));
      };
      
      request.onsuccess = (event) => {
        const db = event.target.result;
        console.log("Successfully opened database for reading, version:", db.version);
        
        // Check if the store exists
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.close();
          resolve([]);
          return;
        }
        
        const transaction = db.transaction([this.storeName], "readonly");
        const store = transaction.objectStore(this.storeName);
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const purchaseOrders = getAllRequest.result;
          console.log(`Retrieved ${purchaseOrders.length} purchase orders from IndexedDB`);
          resolve(purchaseOrders);
        };
        
        getAllRequest.onerror = (event) => {
          console.error("Error retrieving purchase orders:", event.target.error);
          reject(new Error("Failed to retrieve purchase orders from database"));
        };
        
        transaction.oncomplete = () => {
          db.close();
        };
        
        transaction.onerror = (event) => {
          console.error("Transaction error retrieving purchase orders:", event.target.error);
          db.close();
        };
      };
      
      request.onupgradeneeded = (event) => {
        // This shouldn't happen when opening an existing database without specifying version
        console.warn("Unexpected database upgrade needed during read operation");
        event.target.transaction.abort();
        reject(new Error("Database schema needs upgrade, please reload the page"));
      };
    });
  }

  async storePurchasesInIndexedDB(purchaseOrders) {
    return new Promise((resolve, reject) => {
      try {
        // Open database
        const request = indexedDB.open(this.dbName);
        
        request.onerror = (event) => {
          console.error("Error opening database for storing:", event.target.error);
          reject(new Error("Could not open purchasing database for storing"));
        };
        
        request.onsuccess = (event) => {
          const db = event.target.result;
          console.log("Successfully opened database for writing, version:", db.version);
          
          // Check if the store exists
          if (!db.objectStoreNames.contains(this.storeName)) {
            db.close();
            reject(new Error("Purchase store not found for storing"));
            return;
          }
          
          try {
            // Verify we have data to store
            console.log(`Preparing to store ${purchaseOrders.length} purchase orders`);
            
            // Debug log some of the orders
            if (purchaseOrders.length > 0) {
              const firstOrder = purchaseOrders[0];
              console.log("First order to store:", {
                id: firstOrder.id,
                OrderNbr: firstOrder.OrderNbr,
                LineItemCount: firstOrder.LineItems ? firstOrder.LineItems.length : 0
              });
              
              // Check if LineItems is an array
              console.log("LineItems is array:", Array.isArray(firstOrder.LineItems));
              
              // Deep clone each order to ensure all properties are properly stored
              const clonedOrders = purchaseOrders.map(order => {
                // Create a deep clone
                const clone = JSON.parse(JSON.stringify(order));
                
                // Ensure LineItems is an array
                if (!Array.isArray(clone.LineItems)) {
                  console.warn(`Order ${clone.OrderNbr} has invalid LineItems - fixing`);
                  clone.LineItems = [];
                }
                
                // Update count to match array
                clone.LineItemCount = clone.LineItems.length;
                
                return clone;
              });
              
              // Start transaction
              const transaction = db.transaction([this.storeName], "readwrite");
              const store = transaction.objectStore(this.storeName);
              
              // Set up transaction event handlers
              transaction.oncomplete = () => {
                console.log("Transaction completed successfully");
                db.close();
                resolve(true);
              };
              
              transaction.onerror = (event) => {
                console.error("Transaction error:", event.target.error);
                db.close();
                reject(new Error("Failed to store purchase data: " + event.target.error.message));
              };
              
              // Clear existing data
              const clearRequest = store.clear();
              
              clearRequest.onsuccess = () => {
                console.log(`Cleared existing data. Storing ${clonedOrders.length} orders.`);
                
                // If no orders to store, we're done
                if (clonedOrders.length === 0) {
                  db.close();
                  resolve(true);
                  return;
                }
                
                // Counter for completed operations
                let completedCount = 0;
                const totalCount = clonedOrders.length;
                
                // Process each order
                clonedOrders.forEach((order) => {
                  try {
                    // Log order before storing
                    console.log(`Storing order ${order.OrderNbr} with ${order.LineItemCount} line items`);
                    
                    const putRequest = store.put(order);
                    
                    putRequest.onsuccess = () => {
                      completedCount++;
                      if (completedCount === totalCount) {
                        console.log(`Successfully stored all ${completedCount} orders`);
                      }
                    };
                    
                    putRequest.onerror = (event) => {
                      console.error(`Error storing order ${order.OrderNbr}:`, event.target.error);
                      completedCount++;
                      event.stopPropagation(); // Prevent transaction abort
                      
                      if (completedCount === totalCount) {
                        console.log(`Completed storage operations with some errors`);
                      }
                    };
                  } catch (putError) {
                    console.error("Error in put operation:", putError);
                    completedCount++;
                    
                    if (completedCount === totalCount) {
                      console.log(`Completed storage operations with some errors`);
                    }
                  }
                });
              };
              
              clearRequest.onerror = (event) => {
                console.error("Error clearing store:", event.target.error);
                db.close();
                reject(new Error("Failed to clear existing data"));
              };
            } else {
              console.warn("No purchase orders to store");
              db.close();
              resolve(true);
            }
          } catch (transactionError) {
            console.error("Error setting up transaction:", transactionError);
            db.close();
            reject(new Error("Failed to set up database transaction"));
          }
        };
        
      } catch (error) {
        console.error("Unexpected error in storePurchasesInIndexedDB:", error);
        reject(new Error("Unexpected error storing purchase data"));
      }
    });
  }

  async saveSettings(settings) {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for saving settings:", event.target.error);
          reject(new Error("Could not open database for saving settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.error("Settings store not found, cannot save settings");
        db.close();
        return;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readwrite");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Save purchasing settings
      store.put({
        id: "purchasingSettings",
        ...settings
      });
      
      await new Promise((resolve, reject) => {
        transaction.oncomplete = () => {
          resolve();
        };
        transaction.onerror = (event) => {
          reject(event.target.error);
        };
      });
      
      db.close();
      return true;
    } catch (error) {
      console.error("Error saving settings:", error);
      return false;
    }
  }

  async loadSettings() {
    try {
      // Open database
      const request = indexedDB.open(this.dbName);
      
      const db = await new Promise((resolve, reject) => {
        request.onsuccess = (event) => resolve(event.target.result);
        request.onerror = (event) => {
          console.error("Error opening database for loading settings:", event.target.error);
          reject(new Error("Could not open database for loading settings"));
        };
      });
      
      if (!db.objectStoreNames.contains(this.settingsStoreName)) {
        console.log("Settings store not found, using defaults");
        db.close();
        return null;
      }
      
      const transaction = db.transaction([this.settingsStoreName], "readonly");
      const store = transaction.objectStore(this.settingsStoreName);
      
      // Get purchasing settings
      const purchasingSettings = await new Promise((resolve) => {
        const request = store.get("purchasingSettings");
        request.onsuccess = () => resolve(request.result);
        request.onerror = (event) => {
          console.error("Error reading purchasing settings:", event.target.error);
          resolve(null);
        };
      });
      
      // Apply settings if they exist
      if (purchasingSettings) {
        if (purchasingSettings.itemsPerPage) {
          this.itemsPerPage = purchasingSettings.itemsPerPage;
        }
        
        if (purchasingSettings.sortField) {
          this.sortField = purchasingSettings.sortField;
        }
        
        if (purchasingSettings.sortDirection) {
          this.sortDirection = purchasingSettings.sortDirection;
        }
        
        if (purchasingSettings.lastSyncTime) {
          this.lastSyncTime = purchasingSettings.lastSyncTime;
        }
        
        if (purchasingSettings.dateRange) {
          try {
            this.dateRange = {
              start: new Date(purchasingSettings.dateRange.start),
              end: new Date(purchasingSettings.dateRange.end)
            };
          } catch (e) {
            console.error("Error parsing date range from settings:", e);
          }
        }
      }
      
      db.close();
      return purchasingSettings;
    } catch (error) {
      console.error("Error loading settings:", error);
      return null;
    }
  }

  // Helper methods
  getStatusClass(status) {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'closed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'cancelled':
      case 'canceled':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  }

  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  formatDate(dateObj) {
    if (!dateObj) return 'N/A';
    
    try {
      // Handle our special Acumatica date object format
      if (dateObj && typeof dateObj === 'object' && dateObj.isAcumaticaDate) {
        // If we have extracted year, month, day properties, use them directly
        if (dateObj.year && dateObj.month && dateObj.day) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[dateObj.month-1]} ${dateObj.day}, ${dateObj.year}`;
        }
        
        // If not, try to extract from the raw value
        if (dateObj.rawValue && typeof dateObj.rawValue === 'string') {
          if (dateObj.rawValue.includes('T')) {
            const datePart = dateObj.rawValue.split('T')[0];
            const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
            
            if (year && month && day) {
              const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
              return `${monthNames[month-1]} ${day}, ${year}`;
            }
          }
        }
      }
      
      // Handle string date format directly
      if (typeof dateObj === 'string') {
        if (dateObj.includes('T')) {
          // ISO format: "2025-04-29T00:00:00+00:00"
          const datePart = dateObj.split('T')[0];
          const [year, month, day] = datePart.split('-').map(num => parseInt(num, 10));
          
          if (year && month && day) {
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            return `${monthNames[month-1]} ${day}, ${year}`;
          }
        }
      }
      
      // Fallback for Date objects - use UTC methods to avoid timezone issues
      if (dateObj instanceof Date) {
        if (!isNaN(dateObj.getTime())) {
          const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          return `${monthNames[dateObj.getUTCMonth()]} ${dateObj.getUTCDate()}, ${dateObj.getUTCFullYear()}`;
        }
      }
      
      return 'N/A';
    } catch (e) {
      console.error("Error formatting date:", e, dateObj);
      return 'N/A';
    }
  }

  formatCurrency(value) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(value);
  }
  
  calculateTotalPages() {
    this.totalPages = Math.max(1, Math.ceil(this.filteredOrders.length / this.itemsPerPage));
    
    // Adjust current page if it's out of bounds
    if (this.currentPage > this.totalPages) {
      this.currentPage = this.totalPages;
    }
  }

  debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }

  async viewOrderDetails(orderId) {
    const order = this.purchaseOrders.find(o => o.id === orderId);
    if (!order) return;
    
    console.log("Displaying order details:", {
      id: order.id,
      OrderNbr: order.OrderNbr,
      LineItemCount: order.LineItems ? order.LineItems.length : 0
    });
    
    // Ensure LineItems is an array
    const lineItems = Array.isArray(order.LineItems) ? order.LineItems : [];
    
    // Build the modal content
    const modalContent = document.createElement('div');
    modalContent.className = 'modal-content overflow-y-auto bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[80vh] shadow-xl';
    modalContent.innerHTML = `
      <div class="flex flex-col md:flex-row justify-between mb-6">
        <h2 class="text-xl font-semibold mb-2">Purchase Order ${this.escapeHtml(order.OrderNbr)}</h2>
        <div class="flex items-center">
          <span class="px-2 py-1 text-sm font-semibold rounded-full ${this.getStatusClass(order.Status)}">
            ${this.escapeHtml(order.Status)}
          </span>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- Order Details -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Order Information</h3>
          <div class="space-y-2">
            <p class="text-sm flex justify-between">
              <span class="font-medium">Purchase Order #:</span> 
              <span>${this.escapeHtml(order.OrderNbr)}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Order Type:</span> 
              <span>${this.escapeHtml(order.OrderType || '')}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Status:</span> 
              <span>${this.escapeHtml(order.Status)}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Currency:</span> 
              <span>${this.escapeHtml(order.Currency || '')}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Project:</span> 
              <span>${this.escapeHtml(order.Project || '')}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Order Date:</span> 
              <span>${this.formatDate(order.Date)}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Promised Date:</span> 
              <span>${this.formatDate(order.PromisedOn)}</span>
            </p>
          </div>
        </div>

        <!-- Vendor Details -->
        <div class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Vendor Information</h3>
          <div class="space-y-2">
            <p class="text-sm">
              <span class="font-medium">Vendor Name:</span><br> 
              <span>${this.escapeHtml(order.AccountName)}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Order Total:</span> 
              <span>${this.formatCurrency(order.OrderTotal || 0)}</span>
            </p>
            <p class="text-sm flex justify-between">
              <span class="font-medium">Tax:</span> 
              <span>${this.formatCurrency(order.TaxTotal || 0)}</span>
            </p>
          </div>
        </div>
      </div>

      <!-- Line Items -->
      <div class="mt-6">
        <h3 class="text-md font-medium mb-3 text-gray-700 dark:text-gray-300">Line Items (${lineItems.length})</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Line #</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Item #</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">UOM</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Unit Cost</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Ext. Cost</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              ${lineItems.length > 0 ? lineItems.map(item => `
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(item.LineNbr)}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">${this.escapeHtml(item.InventoryID)}</td>
                  <td class="px-3 py-2 text-sm text-gray-800 dark:text-gray-200">${item.Description ? this.escapeHtml(item.Description) : '<em class="text-gray-500">No description</em>'}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${item.BaseOrderQty.toFixed(2)}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">${this.escapeHtml(item.UOM)}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(item.UnitCost)}</td>
                  <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500 dark:text-gray-400">${this.formatCurrency(item.ExtCost)}</td>
                </tr>
              `).join('') : `
                <tr>
                  <td colspan="7" class="px-3 py-4 text-center text-gray-500 dark:text-gray-400">
                    No line items found for this purchase order
                  </td>
                </tr>
              `}
            </tbody>
            <tfoot class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th colspan="6" class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">Total:</th>
                <th class="px-3 py-2 text-right text-xs font-medium text-gray-800 dark:text-gray-200">
                  ${this.formatCurrency(order.ItemTotal || 0)}
                </th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex justify-end gap-2 mt-6">
        <button id="order-details-close-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
          Close
        </button>
      </div>
    `;
    
    // Create modal with the new helper method
    const modal = this.createModal(modalContent);
    
    // Bind the close button
    this.bindButton(
      modalContent.querySelector('#order-details-close-button'), 
      () => modal.close()
    );
  }

  exportPurchaseData() {
    try {
      console.log("Starting export with", this.filteredOrders.length, "orders");
      
      if (!this.filteredOrders || this.filteredOrders.length === 0) {
        alert("No data available to export");
        return;
      }
      
      const fileName = `purchase_orders_${new Date().toISOString().slice(0, 10)}`;
      
      // Export as CSV
      this.exportToCSV(fileName);
    } catch (error) {
      console.error('Error exporting data:', error);
      alert("Error exporting data: " + error.message);
    }
  }
  
  exportToCSV(fileName) {
    try {
      // Create CSV content
      let csvContent = 'data:text/csv;charset=utf-8,';
      
      // Add header row with BOM for Excel compatibility
      csvContent += '\uFEFF';
      csvContent += 'Order #,Vendor ID,Vendor Name,Status,Order Date,Promised Date,Order Total,Tax Total,Item Total,Line Items\n';
      
      console.log(`Exporting ${this.filteredOrders.length} purchase orders to CSV`);
      
      // Add each row of data
      this.filteredOrders.forEach((order, index) => {
        // Debug log every 10th order
        if (index % 10 === 0) {
          console.log(`Processing order ${index}:`, order.OrderNbr);
        }
        
        const row = [
          order.OrderNbr || '',
          order.VendorID || '',
          order.AccountName || '',
          order.Status || '',
          this.formatDate(order.Date) || '',
          this.formatDate(order.PromisedOn) || '',
          order.OrderTotal || 0,
          order.TaxTotal || 0,
          order.ItemTotal || 0,
          order.LineItemCount || 0
        ].map(cell => {
          // Properly escape values for CSV
          if (cell === null || cell === undefined) {
            return '""';
          }
          // Escape quotes and wrap in quotes
          const cellStr = String(cell).replace(/"/g, '""');
          return `"${cellStr}"`;
        }).join(',');
        
        csvContent += row + '\n';
      });
      
      // Create and use a single download link
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement('a');
      link.setAttribute('href', encodedUri);
      link.setAttribute('download', `${fileName}.csv`);
      document.body.appendChild(link);
      
      console.log("Triggering CSV download");
      link.click();
      
      // Clean up
      setTimeout(() => {
        document.body.removeChild(link);
        console.log("Export completed and link removed");
      }, 100);
      
      alert("Purchase data exported successfully");
    } catch (error) {
      console.error("Error in CSV export:", error);
      alert("Error exporting to CSV: " + error.message);
    }
  }

  async showDateRangePicker() {
    try {
      // Format dates for input fields
      const formatDateForInput = (date) => {
        if (!date) return '';
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      // Format current dates for the modal
      const initialStartDateValue = formatDateForInput(this.dateRange.start);
      const initialEndDateValue = formatDateForInput(this.dateRange.end);
      
      // Build the modal content
      const modalContent = document.createElement('div');
      modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full shadow-xl';
      modalContent.innerHTML = `
        <h3 class="text-lg font-medium mb-3">Select Date Range</h3>
        <p class="mb-4 text-sm text-gray-600 dark:text-gray-400">
          Filter purchase orders based on order date within this range.
        </p>
        
        <div class="mb-4 flex flex-wrap gap-2">
          <button type="button" class="date-preset px-3 py-1 text-sm rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300" data-days="7">
            Last 7 Days
          </button>
          <button type="button" class="date-preset px-3 py-1 text-sm rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300" data-days="30">
            Last 30 Days
          </button>
          <button type="button" class="date-preset px-3 py-1 text-sm rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300" data-days="90">
            Last 90 Days
          </button>
          <button type="button" class="date-preset px-3 py-1 text-sm rounded border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300" data-days="365">
            Last Year
          </button>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium mb-1">Start Date</label>
            <input 
              type="date" 
              id="date-range-start" 
              class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
              value="${initialStartDateValue}"
            >
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-1">End Date</label>
            <input 
              type="date" 
              id="date-range-end" 
              class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md"
              value="${initialEndDateValue}"
            >
          </div>
        </div>
        
        <div class="flex justify-between">
          <div>
            <button id="date-range-clear-button" class="px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md">
              Clear Filter
            </button>
          </div>
          <div class="flex gap-2">
            <button id="date-range-cancel-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
              Cancel
            </button>
            <button id="date-range-apply-button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
              Apply Filter
            </button>
          </div>
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button id="date-range-fetch-button" class="w-full px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md flex items-center justify-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Fetch Orders from Acumatica
          </button>
        </div>
      `;
      
      // Create modal
      const modal = this.createModal(modalContent);
      
      // Get references to inputs
      const startDateInput = modalContent.querySelector('#date-range-start');
      const endDateInput = modalContent.querySelector('#date-range-end');
      
      // Process dates and actions
      const processAction = (action) => {
        const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
        
        modal.close();
        
        switch(action) {
          case 'clear':
            // Reset to default date range - last 3 months
            const now = new Date();
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(now.getMonth() - 3);
            
            this.dateRange = {
              start: threeMonthsAgo,
              end: now
            };
            
            // Apply to existing data
            this.applyFilters();
            break;
          
          case 'apply':
            // Validate dates
            if (!startDate || !endDate) {
              this.showError("Please select both start and end dates");
              return;
            }
            
            if (startDate > endDate) {
              this.showError("Start date cannot be after end date");
              return;
            }
            
            // Update date range
            this.dateRange = {
              start: startDate,
              end: endDate
            };
            
            // Apply to existing data
            this.applyFilters();
            break;
            
          case 'fetch':
            // Validate dates
            if (!startDate || !endDate) {
              this.showError("Please select both start and end dates");
              return;
            }
            
            if (startDate > endDate) {
              this.showError("Start date cannot be after end date");
              return;
            }
            
            // Update date range
            this.dateRange = {
              start: startDate,
              end: endDate
            };
            
            // Show loading state
            this.isLoading = true;
            this.render();
            
            // Get the connection status and fetch data
            const connectionStatus = connectionManager.getConnectionStatus();
            if (!connectionStatus.acumatica.isConnected) {
              this.isLoading = false;
              this.render();
              this.showError('Not connected to Acumatica. Please connect and try again.');
              return;
            }
            
            // Fetch data from Acumatica with the specified date range
            this.loadData(true).catch(error => {
              console.error('Error fetching data from Acumatica:', error);
              this.isLoading = false;
              this.render();
              this.showError(`Failed to fetch data from Acumatica: ${error.message}`);
            });
            break;
        }
      };
      
      // Set up date preset buttons
      const presetButtons = modalContent.querySelectorAll('.date-preset');
      presetButtons.forEach(button => {
        this.bindButton(button, () => {
          const days = parseInt(button.dataset.days);
          const end = new Date();
          const start = new Date();
          start.setDate(end.getDate() - days);
          
          // Update input values
          startDateInput.value = formatDateForInput(start);
          endDateInput.value = formatDateForInput(end);
        });
      });
      
      // Bind action buttons
      this.bindButton(
        modalContent.querySelector('#date-range-cancel-button'), 
        () => modal.close()
      );
      
      this.bindButton(
        modalContent.querySelector('#date-range-clear-button'), 
        () => processAction('clear')
      );
      
      this.bindButton(
        modalContent.querySelector('#date-range-apply-button'), 
        () => processAction('apply')
      );
      
      this.bindButton(
        modalContent.querySelector('#date-range-fetch-button'), 
        () => processAction('fetch')
      );
      
    } catch (error) {
      console.error("Error in showDateRangePicker:", error);
      this.showError(`Error with date picker: ${error.message}`);
    }
  }

  async showSettings() {
    try {
      // Build settings content
      const modalContent = document.createElement('div');
      modalContent.className = 'bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full shadow-xl';
      modalContent.innerHTML = `
        <h3 class="text-lg font-medium mb-4">Purchase Settings</h3>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Items per page</label>
          <select id="settings-items-per-page" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="5" ${this.itemsPerPage === 5 ? 'selected' : ''}>5</option>
            <option value="10" ${this.itemsPerPage === 10 ? 'selected' : ''}>10</option>
            <option value="25" ${this.itemsPerPage === 25 ? 'selected' : ''}>25</option>
            <option value="50" ${this.itemsPerPage === 50 ? 'selected' : ''}>50</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Default sort</label>
          <select id="settings-default-sort" class="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md">
            <option value="OrderNbr" ${this.sortField === 'OrderNbr' ? 'selected' : ''}>Order #</option>
            <option value="AccountName" ${this.sortField === 'AccountName' ? 'selected' : ''}>Vendor Name</option>
            <option value="OrderTotal" ${this.sortField === 'OrderTotal' ? 'selected' : ''}>Order Total</option>
            <option value="Status" ${this.sortField === 'Status' ? 'selected' : ''}>Status</option>
            <option value="Date" ${this.sortField === 'Date' ? 'selected' : ''}>Order Date</option>
            <option value="PromisedOn" ${this.sortField === 'PromisedOn' ? 'selected' : ''}>Promised Date</option>
          </select>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-1">Sort direction</label>
          <div class="flex gap-4">
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="asc" ${this.sortDirection === 'asc' ? 'checked' : ''} class="mr-2">
              Ascending
            </label>
            <label class="inline-flex items-center">
              <input type="radio" name="settings-sort-direction" value="desc" ${this.sortDirection === 'desc' ? 'checked' : ''} class="mr-2">
              Descending
            </label>
          </div>
        </div>
        
        <div class="flex justify-end gap-2 mt-6">
          <button id="settings-cancel-button" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-md">
            Cancel
          </button>
          <button id="settings-save-button" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md">
            Save Changes
          </button>
        </div>
      `;
      
      // Create modal with helper method
      const modal = this.createModal(modalContent);
      
      // Get references to form elements
      const itemsPerPageSelect = modalContent.querySelector('#settings-items-per-page');
      const sortFieldSelect = modalContent.querySelector('#settings-default-sort');
      const sortDirectionRadios = modalContent.querySelectorAll('input[name="settings-sort-direction"]');
      
      // Function to handle saving settings
      const saveSettings = async () => {
        try {
          // Get current values
          const itemsPerPage = parseInt(itemsPerPageSelect.value);
          const sortField = sortFieldSelect.value;
          let sortDirection = this.sortDirection; // Default to current
          
          // Close modal first to prevent issues
          modal.close();
          
          // Find selected radio button
          sortDirectionRadios.forEach(radio => {
            if (radio.checked) {
              sortDirection = radio.value;
            }
          });
          
          // Apply new settings
          this.itemsPerPage = itemsPerPage;
          this.sortField = sortField;
          this.sortDirection = sortDirection;
          
          // Save settings to IndexedDB
          await this.saveSettings({
            itemsPerPage: this.itemsPerPage,
            sortField: this.sortField,
            sortDirection: this.sortDirection,
            lastSyncTime: this.lastSyncTime,
            dateRange: {
              start: this.dateRange.start.toISOString(),
              end: this.dateRange.end.toISOString()
            }
          });
          
          // Apply new settings and re-render
          this.calculateTotalPages();
          this.applyFilters();
          
        } catch (error) {
          console.error("Error saving settings:", error);
          this.showError("Failed to save settings: " + error.message);
        }
      };
      
      // Bind action buttons with reliable helper method
      this.bindButton(
        modalContent.querySelector('#settings-cancel-button'),
        () => modal.close()
      );
      
      this.bindButton(
        modalContent.querySelector('#settings-save-button'),
        saveSettings
      );
      
    } catch (error) {
      console.error("Error in showSettings:", error);
      this.showError(`Error with settings: ${error.message}`);
    }
  }

  // Add this helper method at the end of the class
  createModal(content, onClose = null) {
    // First remove any existing modals to prevent stacking
    const existingModals = document.querySelectorAll('.purchasing-modal-overlay');
    existingModals.forEach(modal => modal.remove());
    
    // Create modal overlay with proper event isolation
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'purchasing-modal-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
    modalOverlay.style.pointerEvents = 'auto';
    
    // Append content to overlay
    modalOverlay.appendChild(content);
    
    // Add to DOM
    document.body.appendChild(modalOverlay);
    
    // Set up click outside handler
    const handleOutsideClick = (e) => {
      if (e.target === modalOverlay) {
        e.preventDefault();
        e.stopPropagation();
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscKey);
        if (onClose) onClose();
      }
    };
    
    // Handle escape key
    const handleEscKey = (e) => {
      if (e.key === 'Escape') {
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscKey);
        if (onClose) onClose();
      }
    };
    
    // Use capture phase for better responsiveness
    modalOverlay.addEventListener('mousedown', handleOutsideClick, true);
    document.addEventListener('keydown', handleEscKey);
    
    // Return for possible future reference
    return { 
      overlay: modalOverlay,
      close: () => {
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscKey);
        if (onClose) onClose();
      }
    };
  }
  
  // Helper method to bind button click handlers reliably
  bindButton(button, action) {
    if (!button) return;
    
    // Remove any existing event listeners
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    // Use mousedown and click to ensure capture
    newButton.addEventListener('mousedown', (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      setTimeout(() => action(), 0);
    }, true);
    
    newButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      e.stopImmediatePropagation();
      setTimeout(() => action(), 0);
    }, true);
    
    return newButton;
  }
} 