// purchase-order-barcode.js - Component for Purchase Order barcode scanning and PO creation
import { NotificationSystem } from "../core/notifications.js";
import { connectionManager } from "../core/connection.js";

export class PurchaseOrderBarcodeComponent {
  constructor(container) {
    this.container = container;
    this.notificationSystem = new NotificationSystem();
    this.isLoading = false;
    this.scanData = [];
    this.filteredData = [];
    this.deletedItems = new Set(); // Track deleted items
    this.currentPage = 1;
    this.itemsPerPage = 10;
    this.searchTerm = '';
    this.dateFilter = null;
    this.currentSort = { field: 'timestamp', direction: 'desc' };
    this.apiUrl = 'https://sheetdb.io/api/v1/d8qcr1cvib5ac';
    this.apiToken = '97nqlzzg2puo4l1js59c7r9a0guzv8zy2lzoya7q'; // Updated token
    this.storageKey = 'purchaseOrderBarcodeData';
    
    // Debounce function for search
    this.debounce = (func, delay) => {
      let debounceTimer;
      return function(...args) {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(() => func.apply(this, args), delay);
      };
    };
  }

  async init() {
    try {
      console.log("Initializing Purchase Order Barcode component");
      this.isLoading = true;
      this.render();
      
      // Try to load data from Chrome storage first
      await this.loadFromStorage();
      
      // If no data in storage or it's older than 24 hours, fetch new data
      const storageTimestamp = localStorage.getItem(this.storageKey + '_timestamp');
      const currentTime = new Date().getTime();
      const isDataStale = !storageTimestamp || (currentTime - parseInt(storageTimestamp)) > 24 * 60 * 60 * 1000;
      
      if (this.scanData.length === 0 || isDataStale) {
        await this.fetchScanData();
      } else {
        this.isLoading = false;
        this.render();
        this.setupEventListeners(); // Make sure event listeners are setup regardless of data source
      }
      
    } catch (error) {
      console.error('Error initializing PurchaseOrderBarcode component:', error);
      this.showError(`Failed to initialize: ${error.message}`);
      this.isLoading = false;
    }
  }
  
  async loadFromStorage() {
    try {
      // Load scan data
      const storedData = localStorage.getItem(this.storageKey);
      if (storedData) {
        this.scanData = JSON.parse(storedData);
        this.filteredData = [...this.scanData];
        
        // Load deleted items
        const storedDeletedItems = localStorage.getItem(this.storageKey + '_deleted');
        if (storedDeletedItems) {
          const deletedArray = JSON.parse(storedDeletedItems);
          this.deletedItems = new Set(deletedArray);
        }
        
        console.log(`Loaded ${this.scanData.length} scan records from storage`);
      }
    } catch (error) {
      console.error('Error loading from storage:', error);
      // If there's an error loading from storage, start fresh
      this.scanData = [];
      this.filteredData = [];
      this.deletedItems = new Set();
    }
  }
  
  saveToStorage() {
    try {
      // Save scan data
      localStorage.setItem(this.storageKey, JSON.stringify(this.scanData));
      
      // Save deleted items
      localStorage.setItem(this.storageKey + '_deleted', JSON.stringify(Array.from(this.deletedItems)));
      
      // Save timestamp
      localStorage.setItem(this.storageKey + '_timestamp', new Date().getTime().toString());
      
      console.log('Data saved to storage');
    } catch (error) {
      console.error('Error saving to storage:', error);
      this.addNotification('Failed to save data to storage', 'error');
    }
  }
  
  async fetchScanData() {
    try {
      this.showLoading();
      
      const response = await fetch(this.apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiToken}`,
          'Content-Type': 'application/json',
        }
      });
      
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }
      
      const data = await response.json();
      
      // Process the scanned data
      this.scanData = data.map(item => {
        // Parse the barcode data
        const parsedData = this.parseBarcodeText(item.BarCode || '');
        
        // Store both the SheetDB ID and Timestamp as identifiers
        const recordId = item.id || '';
        const timestamp = item.Timestamp || item.created_at || new Date().toISOString();
        
        return {
          ...parsedData,
          rawBarcode: item.BarCode,
          timestamp: timestamp,
          sheetTimestamp: timestamp, // Original timestamp from the sheet
          id: recordId, // Store the original database ID
          uniqueId: recordId || this.generateUniqueId(item)
        };
      });
      
      // Filter out empty records and items that have been deleted
      this.scanData = this.scanData.filter(item => {
        return item.vendorPartNumber && !this.deletedItems.has(item.uniqueId);
      });
      
      // Sort by timestamp (newest first)
      this.scanData.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      this.filteredData = [...this.scanData];
      this.addNotification(`Loaded ${this.scanData.length} scan records`, "success");
      
      // Save to storage
      this.saveToStorage();
      
      this.isLoading = false;
      this.render();
      this.setupEventListeners(); // Ensure event listeners are setup after data refresh
      
    } catch (error) {
      console.error('Error fetching scan data:', error);
      this.addNotification(`Failed to load scan data: ${error.message}`, "error");
      this.scanData = [];
      this.filteredData = [];
      this.isLoading = false;
      this.render();
      this.setupEventListeners(); // Setup event listeners even after error
    }
  }
  
  // Generate a unique ID for an item if it doesn't have one
  generateUniqueId(item) {
    return `${item.BarCode}_${item.created_at}_${Math.random().toString(36).substring(2, 10)}`;
  }
  
  parseBarcodeText(barcodeText) {
    // Default empty result
    const result = {
      vendorPartNumber: '',
      vendorCreatedDate: '',
      pkgQty: '',
      qty: ''
    };
    
    if (!barcodeText) return result;
    
    try {
      // First, remove non-printable characters from the entire string
      // This regex removes control characters but keeps normal whitespace
      barcodeText = barcodeText.replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '');
      
      // Split by caret (^) and hash (#)
      const parts = barcodeText.split('^#');
      
      // Process first part specially as it might have a different format
      let startIndex = 0;
      if (parts[0].includes('[)>06PIK005')) {
        startIndex = 1; // Skip the header part
      }
      
      // Iterate through parts to extract values
      for (let i = startIndex; i < parts.length; i++) {
        const part = parts[i];
        const [key, value] = part.split('^');
        
        // Skip if key or value is undefined
        if (!key || !value) continue;
        
        // Clean up values by removing leading zeros and control characters
        const cleanValue = (val, fieldKey) => {
          if (!val) return '';
          
          // Remove any remaining control characters
          val = val.replace(/[\x00-\x1F\x7F-\x9F]/g, '');
          
          // Remove leading zeros from numeric values but keep date formats
          if (fieldKey !== '03') {
            return val.replace(/^0+/, '') || val;
          }
          return val;
        };
        
        // Map keys to properties and clean values
        const cleanedKey = key.trim();
        switch (cleanedKey) {
          case '01':
            result.vendorPartNumber = cleanValue(value, cleanedKey) || '';
            break;
          case '03':
            result.vendorCreatedDate = cleanValue(value, cleanedKey) || '';
            break;
          case '12':
            result.pkgQty = cleanValue(value, cleanedKey) || '';
            break;
          case '13':
            result.qty = cleanValue(value, cleanedKey) || '';
            break;
        }
      }
      
      return result;
    } catch (error) {
      console.error('Error parsing barcode:', error, barcodeText);
      return result;
    }
  }
  
  render() {
    console.log("Rendering Purchase Order Barcode component");
    
    // Content for the tabContentContainer
    const tabContentContainer = this.container.querySelector('#tabContentContainer');
    if (!tabContentContainer) {
      console.error("Tab content container not found");
      return;
    }
    
    const content = `
      <!-- Search and filters -->
      <div class="mb-4 flex flex-wrap items-center">
        <div class="relative flex-grow mr-2">
          <input type="text" id="poSearchInput" class="w-full pl-9 pr-4 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white" placeholder="Search scans...">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <button id="poClearSearchBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ${this.searchTerm ? '' : 'hidden'}">
            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <!-- Date filter -->
        <input type="date" id="poDateFilter" class="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white mr-2">
        
        <!-- Create PO button -->
        <button id="createPoBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 relative text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </button>
        
        <!-- Refresh button -->
        <button id="refreshPoBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 mr-2 relative text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
        
        <!-- Settings button -->
        <button id="poSettingsBtn" class="border border-gray-300 dark:border-gray-600 rounded-md px-1 py-1 bg-transparent dark:bg-transparent focus:outline-none focus:ring-blue-500 focus:border-blue-500 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-400 dark:text-gray-400 flex items-center justify-center" style="height: 30px; width: 28px;">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.75" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
        </button>
      </div>
      
      <!-- Content area -->
      <div id="poContent" class="overflow-auto" style="max-height: 500px;">
        ${this.renderScanTable()}
      </div>
      
      <!-- Pagination -->
      <div class="flex items-center justify-between mt-2 text-xs">
        <div class="text-gray-500 dark:text-gray-400">
          Showing <span id="poShowingStart">0</span> to <span id="poShowingEnd">0</span> of <span id="poTotalItems">0</span> scans
        </div>
        <div class="flex space-x-1">
          <button id="poPrevPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage <= 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <div id="poPagination" class="flex space-x-1">
            <!-- Page numbers will be inserted here -->
          </div>
          <button id="poNextPageBtn" class="px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 ${this.currentPage >= this.getTotalPages() ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100 dark:hover:bg-gray-700'}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    `;
    
    tabContentContainer.innerHTML = content;
    
    // Update pagination
    this.updatePagination();
  }
  
  renderScanTable() {
    if (this.isLoading) {
      return `
        <div class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      `;
    }

    if (this.filteredData.length === 0) {
      return `
        <div class="text-center py-12 text-gray-500 dark:text-gray-400">
          <svg class="w-12 h-12 mx-auto mb-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
          <p class="text-lg">No scan records found</p>
          <p class="text-sm">Try adjusting your search criteria or scan new items.</p>
        </div>
      `;
    }

    // Get page data
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = Math.min(start + this.itemsPerPage, this.filteredData.length);
    const pageItems = this.filteredData.slice(start, end);

    // Create table header row - Added Actions column
    const headers = [
      { id: 'vendorPartNumber', label: 'Vendor P/N', width: '20%' },
      { id: 'vendorCreatedDate', label: 'Date', width: '15%' },
      { id: 'pkgQty', label: 'PKG', width: '10%' },
      { id: 'qty', label: 'Qty', width: '10%' },
      { id: 'timestamp', label: 'Scanned', width: '25%' },
      { id: 'actions', label: 'Actions', width: '20%' }
    ];

    return `
      <div>
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0">
            <tr>
              ${headers.map(header => {
                const isSorted = this.currentSort.field === header.id;
                const sortDirection = isSorted ? this.currentSort.direction : '';
                const isActions = header.id === 'actions';
                return `
                  <th scope="col" class="px-2 py-2 ${isActions ? 'text-center' : 'text-left'} text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${!isActions ? 'cursor-pointer po-sort-header' : ''}" 
                      ${!isActions ? `data-field="${header.id}"` : ''} style="width: ${header.width}">
                    <div class="flex items-center ${isActions ? 'justify-center' : ''}">
                      ${header.label}
                      ${isSorted ? 
                        `<svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          ${sortDirection === 'asc' ? 
                            `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>` : 
                            `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>`}
                         </svg>` : ''}
                    </div>
                  </th>
                `;
              }).join('')}
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
            ${pageItems.map((scan, idx) => {
              const timestamp = new Date(scan.timestamp);
              const formattedTimestamp = timestamp.toLocaleString();
              
              return `
                <tr class="${idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'} hover:bg-blue-50 dark:hover:bg-blue-900">
                  <td class="px-2 py-1 whitespace-nowrap">
                    <div class="text-xs font-medium text-blue-600 dark:text-blue-400">${scan.vendorPartNumber || '-'}</div>
                  </td>
                  <td class="px-2 py-1">
                    <div class="text-xs text-gray-900 dark:text-gray-300">${scan.vendorCreatedDate || '-'}</div>
                  </td>
                  <td class="px-2 py-1 text-center">
                    <div class="text-xs text-gray-900 dark:text-gray-300">${scan.pkgQty || '-'}</div>
                  </td>
                  <td class="px-2 py-1 text-center">
                    <div class="text-xs text-gray-900 dark:text-gray-300">${scan.qty || '-'}</div>
                  </td>
                  <td class="px-2 py-1">
                    <div class="text-xs text-gray-500 dark:text-gray-400">${formattedTimestamp}</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500 truncate" title="${scan.sheetTimestamp || ''}">${scan.sheetTimestamp || ''}</div>
                  </td>
                  <td class="px-2 py-1 text-center">
                    <div class="flex space-x-2 justify-center">
                      <button class="edit-scan-btn text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 p-1" 
                              data-index="${idx}" data-scan-id="${scan.id || ''}" data-timestamp="${scan.sheetTimestamp || ''}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                      </button>
                      <button class="delete-scan-btn text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 p-1"
                              data-index="${idx}" data-scan-id="${scan.id || ''}" data-timestamp="${scan.sheetTimestamp || ''}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              `;
            }).join('')}
          </tbody>
        </table>
      </div>
    `;
  }
  
  setupEventListeners() {
    console.log("Setting up event listeners");
    
    // Search input
    const searchInput = this.container.querySelector('#poSearchInput');
    if (searchInput) {
      searchInput.value = this.searchTerm;
      const debouncedSearch = this.debounce(() => {
        this.searchTerm = searchInput.value;
        this.updateClearButton();
        this.applySearch();
      }, 300); // 300ms debounce
      
      // Remove any existing listeners before adding new ones
      searchInput.removeEventListener('input', debouncedSearch);
      searchInput.addEventListener('input', debouncedSearch);
    }

    // Clear search button
    const clearSearchBtn = this.container.querySelector('#poClearSearchBtn');
    if (clearSearchBtn) {
      const clearHandler = (e) => {
        e.preventDefault();
        this.searchTerm = '';
        if (searchInput) searchInput.value = '';
        this.updateClearButton();
        this.applySearch();
      };
      
      // Remove any existing listeners before adding new ones
      clearSearchBtn.removeEventListener('click', clearHandler);
      clearSearchBtn.addEventListener('click', clearHandler);
    }
    
    // Date filter
    const dateFilter = this.container.querySelector('#poDateFilter');
    if (dateFilter) {
      dateFilter.value = this.dateFilter || '';
      const dateChangeHandler = () => {
        this.dateFilter = dateFilter.value || null;
        this.applySearch();
      };
      
      // Remove any existing listeners before adding new ones
      dateFilter.removeEventListener('change', dateChangeHandler);
      dateFilter.addEventListener('change', dateChangeHandler);
    }
    
    // Create PO button
    const createPoBtn = this.container.querySelector('#createPoBtn');
    if (createPoBtn) {
      const createPoHandler = (e) => {
        e.preventDefault();
        this.showCreatePODialog();
      };
      
      // Remove any existing listeners before adding new ones
      createPoBtn.removeEventListener('click', createPoHandler);
      createPoBtn.addEventListener('click', createPoHandler);
    }
    
    // Refresh button
    const refreshBtn = this.container.querySelector('#refreshPoBtn');
    if (refreshBtn) {
      const refreshHandler = (e) => {
        e.preventDefault();
        this.addNotification('Refreshing data...', 'info');
        this.fetchScanData();
      };
      
      // Remove any existing listeners before adding new ones
      refreshBtn.removeEventListener('click', refreshHandler);
      refreshBtn.addEventListener('click', refreshHandler);
    }
    
    // Settings button
    const settingsBtn = this.container.querySelector('#poSettingsBtn');
    if (settingsBtn) {
      const settingsHandler = (e) => {
        e.preventDefault();
        this.showSettings();
      };
      
      // Remove any existing listeners before adding new ones
      settingsBtn.removeEventListener('click', settingsHandler);
      settingsBtn.addEventListener('click', settingsHandler);
    }
    
    // Pagination buttons
    const prevPageBtn = this.container.querySelector('#poPrevPageBtn');
    if (prevPageBtn) {
      const prevPageHandler = (e) => {
        e.preventDefault();
        if (this.currentPage > 1) {
          this.currentPage--;
          this.updateTable();
        }
      };
      
      // Remove any existing listeners before adding new ones
      prevPageBtn.removeEventListener('click', prevPageHandler);
      prevPageBtn.addEventListener('click', prevPageHandler);
    }

    const nextPageBtn = this.container.querySelector('#poNextPageBtn');
    if (nextPageBtn) {
      const nextPageHandler = (e) => {
        e.preventDefault();
        if (this.currentPage < this.getTotalPages()) {
          this.currentPage++;
          this.updateTable();
        }
      };
      
      // Remove any existing listeners before adding new ones
      nextPageBtn.removeEventListener('click', nextPageHandler);
      nextPageBtn.addEventListener('click', nextPageHandler);
    }

    // Page number buttons
    const pageButtons = this.container.querySelectorAll('.po-page-btn');
    pageButtons.forEach(button => {
      const pageHandler = (e) => {
        e.preventDefault();
        const page = parseInt(button.getAttribute('data-page'));
        if (page !== this.currentPage) {
          this.currentPage = page;
          this.updateTable();
        }
      };
      
      // Remove any existing listeners before adding new ones
      button.removeEventListener('click', pageHandler);
      button.addEventListener('click', pageHandler);
    });

    // Sort headers
    const sortHeaders = this.container.querySelectorAll('.po-sort-header');
    sortHeaders.forEach(header => {
      const sortHandler = (e) => {
        e.preventDefault();
        const field = header.getAttribute('data-field');
        this.sortTable(field);
      };
      
      // Remove any existing listeners before adding new ones
      header.removeEventListener('click', sortHandler);
      header.addEventListener('click', sortHandler);
    });
    
    // Delegate event handling for table actions (edit and delete buttons)
    const contentArea = this.container.querySelector('#poContent');
    if (contentArea) {
      // First remove any existing click handlers by cloning and replacing the node
      const newContentArea = contentArea.cloneNode(true);
      contentArea.parentNode.replaceChild(newContentArea, contentArea);
      
      // Now add the event listener to the new node
      newContentArea.addEventListener('click', (e) => {
        // Handle edit button clicks
        if (e.target.closest('.edit-scan-btn')) {
          e.preventDefault();
          e.stopPropagation();
          const button = e.target.closest('.edit-scan-btn');
          const index = parseInt(button.getAttribute('data-index'));
          const scanId = button.getAttribute('data-scan-id');
          const scan = this.getFilteredItemAtIndex(index);
          if (scan) {
            this.showEditScanDialog(scan, index);
          }
        }
        
        // Handle delete button clicks
        if (e.target.closest('.delete-scan-btn')) {
          e.preventDefault();
          e.stopPropagation();
          const button = e.target.closest('.delete-scan-btn');
          const index = parseInt(button.getAttribute('data-index'));
          const scanId = button.getAttribute('data-scan-id');
          const scan = this.getFilteredItemAtIndex(index);
          if (scan) {
            this.showDeleteConfirmation(scan, index);
          }
        }
      });
    }
    
    console.log("Event listeners setup complete");
  }
  
  // Helper method to safely get an item from filteredData by index
  getFilteredItemAtIndex(index) {
    if (index >= 0 && index < this.filteredData.length) {
      return this.filteredData[index];
    }
    return null;
  }
  
  // Calculate total pages
  getTotalPages() {
    return Math.ceil(this.filteredData.length / this.itemsPerPage);
  }
  
  // Update the pagination controls
  updatePagination() {
    const totalPages = this.getTotalPages();
    const pagination = this.container.querySelector('#poPagination');
    const showingStart = this.container.querySelector('#poShowingStart');
    const showingEnd = this.container.querySelector('#poShowingEnd');
    const totalItems = this.container.querySelector('#poTotalItems');
    
    if (pagination) {
      let html = '';
      
      // Determine range of pages to show
      let startPage = Math.max(1, this.currentPage - 2);
      let endPage = Math.min(totalPages, startPage + 4);
      
      // Adjust if we're near the end
      if (endPage - startPage < 4 && startPage > 1) {
        startPage = Math.max(1, endPage - 4);
      }
      
      // First page
      if (startPage > 1) {
        html += `
          <button class="po-page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="1">1</button>
        `;
        
        if (startPage > 2) {
          html += `
            <span class="px-2 py-1 text-gray-500 dark:text-gray-400">...</span>
          `;
        }
      }
      
      // Page numbers
      for (let i = startPage; i <= endPage; i++) {
        html += `
          <button class="po-page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md ${i === this.currentPage ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'}" data-page="${i}">${i}</button>
        `;
      }
      
      // Last page
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          html += `
            <span class="px-2 py-1 text-gray-500 dark:text-gray-400">...</span>
          `;
        }
        
        html += `
          <button class="po-page-btn px-2 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700" data-page="${totalPages}">${totalPages}</button>
        `;
      }
      
      pagination.innerHTML = html;
      
      // Add event listeners
      const pageButtons = pagination.querySelectorAll('.po-page-btn');
      pageButtons.forEach(button => {
        button.addEventListener('click', (e) => {
          e.preventDefault();
          const page = parseInt(button.getAttribute('data-page'));
          if (page !== this.currentPage) {
            this.currentPage = page;
            this.updateTable();
          }
        });
      });
    }
    
    // Update showing text
    if (showingStart && showingEnd && totalItems) {
      const start = this.filteredData.length > 0 ? 
        (this.currentPage - 1) * this.itemsPerPage + 1 : 0;
      const end = Math.min(start + this.itemsPerPage - 1, this.filteredData.length);
      
      showingStart.textContent = start;
      showingEnd.textContent = end;
      totalItems.textContent = this.filteredData.length;
    }
    
    // Update prev/next buttons
    const prevBtn = this.container.querySelector('#poPrevPageBtn');
    const nextBtn = this.container.querySelector('#poNextPageBtn');
    
    if (prevBtn) {
      if (this.currentPage <= 1) {
        prevBtn.classList.add('opacity-50', 'cursor-not-allowed');
        prevBtn.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      } else {
        prevBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        prevBtn.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      }
    }
    
    if (nextBtn) {
      if (this.currentPage >= totalPages) {
        nextBtn.classList.add('opacity-50', 'cursor-not-allowed');
        nextBtn.classList.remove('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      } else {
        nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        nextBtn.classList.add('hover:bg-gray-100', 'dark:hover:bg-gray-700');
      }
    }
  }
  
  // Update the scan table
  updateTable() {
    const contentArea = this.container.querySelector('#poContent');
    if (contentArea) {
      contentArea.innerHTML = this.renderScanTable();
      this.updatePagination();
      // No need to reattach event listeners here, as we're using delegation on the container
    }
  }
  
  // Apply search and filter
  applySearch() {
    // Start with all data
    let filtered = [...this.scanData];
    
    // Apply search term filter
    if (this.searchTerm) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(scan => {
        return (
          (scan.vendorPartNumber && scan.vendorPartNumber.toLowerCase().includes(searchLower)) ||
          (scan.vendorCreatedDate && scan.vendorCreatedDate.toLowerCase().includes(searchLower))
        );
      });
    }
    
    // Apply date filter
    if (this.dateFilter) {
      const filterDate = new Date(this.dateFilter);
      filterDate.setHours(0, 0, 0, 0);
      
      filtered = filtered.filter(scan => {
        const scanDate = new Date(scan.timestamp);
        scanDate.setHours(0, 0, 0, 0);
        return scanDate.getTime() === filterDate.getTime();
      });
    }
    
    this.filteredData = filtered;
    this.currentPage = 1;
    this.updateTable();
  }
  
  // Sort table by field
  sortTable(field) {
    if (this.currentSort.field === field) {
      // Toggle direction if same field
      this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
      // New field, default to ascending
      this.currentSort.field = field;
      this.currentSort.direction = 'asc';
    }
    
    this.filteredData.sort((a, b) => {
      const aVal = a[field] ? a[field].toString() : '';
      const bVal = b[field] ? b[field].toString() : '';
      
      // Special handling for dates
      if (field === 'timestamp' || field === 'vendorCreatedDate') {
        const aDate = new Date(aVal);
        const bDate = new Date(bVal);
        
        return this.currentSort.direction === 'asc' ? 
          aDate - bDate : 
          bDate - aDate;
      }
      
      // Special handling for numeric fields
      if (field === 'qty' || field === 'pkgQty') {
        const aNum = parseFloat(aVal) || 0;
        const bNum = parseFloat(bVal) || 0;
        
        return this.currentSort.direction === 'asc' ? 
          aNum - bNum : 
          bNum - aNum;
      }
      
      // Default string comparison
      return this.currentSort.direction === 'asc' ? 
        aVal.localeCompare(bVal) : 
        bVal.localeCompare(aVal);
    });
    
    this.updateTable();
  }
  
  // Update the clear search button visibility
  updateClearButton() {
    const clearBtn = this.container.querySelector('#poClearSearchBtn');
    if (clearBtn) {
      if (this.searchTerm) {
        clearBtn.classList.remove('hidden');
      } else {
        clearBtn.classList.add('hidden');
      }
    }
  }
  
  // Show the create PO dialog
  showCreatePODialog() {
    // Check if there are any items to create a PO
    if (this.filteredData.length === 0) {
      this.addNotification('No items to create a Purchase Order', 'warning');
      return;
    }
    
    // Remove any existing modal
    const existingModal = document.querySelector('#createPOModal');
    if (existingModal) {
      existingModal.remove();
    }
    
    // Get unique items (by vendor part number)
    const uniqueItems = [];
    const addedPartNumbers = new Set();
    
    // For each part number, get the total qty from all scans
    const partQtys = new Map();
    
    this.filteredData.forEach(scan => {
      if (scan.vendorPartNumber) {
        if (!addedPartNumbers.has(scan.vendorPartNumber)) {
          addedPartNumbers.add(scan.vendorPartNumber);
          uniqueItems.push(scan);
        }
        
        // Sum quantities for the same part number
        const currentQty = partQtys.get(scan.vendorPartNumber) || 0;
        // Ensure scan.qty is treated as a number
        const scanQty = parseInt(scan.qty) || 0;
        partQtys.set(scan.vendorPartNumber, currentQty + scanQty);
      }
    });
    
    // Create modal HTML
    const modalHtml = `
      <div id="createPOModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-11/12 max-h-[540px] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Create Purchase Order</h3>
            <button id="closeCreatePOModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="mb-4 overflow-y-auto" style="max-height: 340px;">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 text-xs">
                <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
                  <tr>
                    <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Vendor P/N</th>
                    <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                    <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Qty</th>
                    <th scope="col" class="px-2 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Include</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
                  ${uniqueItems.map((item, idx) => {
                    // Use the calculated total qty for this part
                    const totalQty = partQtys.get(item.vendorPartNumber) || parseInt(item.qty) || 1;
                    
                    return `
                    <tr class="${idx % 2 === 0 ? 'bg-white dark:bg-gray-900' : 'bg-gray-50 dark:bg-gray-800'}">
                      <td class="px-2 py-1 whitespace-nowrap">
                        <div class="text-xs font-medium text-blue-600 dark:text-blue-400">${item.vendorPartNumber || '-'}</div>
                      </td>
                      <td class="px-2 py-1 text-center">
                        <div class="text-xs text-gray-900 dark:text-gray-300">${item.vendorCreatedDate || '-'}</div>
                      </td>
                      <td class="px-2 py-1 text-center">
                        <input type="number" min="1" value="${totalQty}" 
                          class="w-16 px-1 py-1 text-xs text-center border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white"
                          data-part-number="${item.vendorPartNumber}">
                      </td>
                      <td class="px-2 py-1 text-center">
                        <input type="checkbox" class="po-item-checkbox" data-part-number="${item.vendorPartNumber}" checked>
                      </td>
                    </tr>
                  `}).join('')}
                </tbody>
              </table>
            </div>
          </div>
          
          <div class="flex justify-end mt-auto pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="space-x-3">
              <button id="createPOConfirmBtn" class="px-3 py-1 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm">
                Create PO
              </button>
              <button id="cancelCreatePOBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Set up event listeners
    const closeButtons = document.querySelectorAll('#closeCreatePOModalBtn, #cancelCreatePOBtn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const modal = document.querySelector('#createPOModal');
        if (modal) modal.remove();
      });
    });
    
    // Create PO button
    const createPOBtn = document.querySelector('#createPOConfirmBtn');
    if (createPOBtn) {
      createPOBtn.addEventListener('click', () => {
        // Get selected items and quantities
        const selectedItems = [];
        const checkboxes = document.querySelectorAll('.po-item-checkbox:checked');
        
        checkboxes.forEach(checkbox => {
          const partNumber = checkbox.getAttribute('data-part-number');
          const qtyInput = document.querySelector(`input[type="number"][data-part-number="${partNumber}"]`);
          const qty = qtyInput ? parseInt(qtyInput.value) || 1 : 1;
          
          const item = uniqueItems.find(i => i.vendorPartNumber === partNumber);
          if (item) {
            selectedItems.push({
              ...item,
              qty
            });
          }
        });
        
        // Close the modal
        const modal = document.querySelector('#createPOModal');
        if (modal) modal.remove();
        
        // Create the PO
        if (selectedItems.length > 0) {
          this.createPurchaseOrder(selectedItems);
        } else {
          this.addNotification('No items selected for Purchase Order', 'warning');
        }
      });
    }
  }
  
  // Create actual purchase order
  async createPurchaseOrder(items) {
    try {
      // Since PO creation in Acumatica is not yet implemented,
      // just show a notification about the future implementation
      
      this.addNotification(`PO creation with ${items.length} items will be implemented in the next phase`, 'info');
      
      // In the future, we would implement the API call to Acumatica here
      // const poDetails = {
      //   items: items,
      //   date: new Date().toISOString(),
      //   // Additional PO details as needed
      // };
      
      // console.log('PO Details for future implementation:', poDetails);
      
      // Show items in console for debugging
      console.log('Items for Purchase Order:', items);
      
    } catch (error) {
      console.error('Error creating purchase order:', error);
      this.addNotification(`Failed to create Purchase Order: ${error.message}`, 'error');
    }
  }
  
  // Show settings dialog
  showSettings() {
    // Settings functionality to be implemented in the future
    this.addNotification('Settings functionality will be implemented in the next phase', 'info');
  }
  
  // Show loading indicator
  showLoading() {
    const contentArea = this.container.querySelector('#poContent');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      `;
    }
  }

  // Show error message
  showError(message) {
    const contentArea = this.container.querySelector('#poContent');
    if (contentArea) {
      contentArea.innerHTML = `
        <div class="flex items-center justify-center h-64">
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative max-w-md">
            <div class="flex">
              <div class="py-1 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <div>
                <p class="font-bold">Error</p>
                <p class="text-sm">${message}</p>
              </div>
            </div>
            <div class="mt-3 flex justify-center">
              <button id="poRetryBtn" class="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded">
                Retry
              </button>
            </div>
          </div>
        </div>
      `;
      
      const retryBtn = this.container.querySelector('#poRetryBtn');
      if (retryBtn) {
        retryBtn.addEventListener('click', () => this.init());
      }
    }
  }

  // Add notification
  addNotification(message, type = 'info') {
    this.notificationSystem.addNotification(message, type);
  }

  // Show dialog to edit a scan
  showEditScanDialog(scan, index) {
    // Remove any existing modal
    const existingModal = document.querySelector('#editScanModal');
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal HTML
    const modalHtml = `
      <div id="editScanModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96 max-h-[540px] overflow-hidden flex flex-col">
          <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Edit Scan</h3>
            <button id="closeEditScanModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="mb-4 space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Vendor Part Number</label>
              <input type="text" id="editVendorPartNumber" value="${scan.vendorPartNumber || ''}" 
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Date</label>
              <input type="text" id="editVendorCreatedDate" value="${scan.vendorCreatedDate || ''}" 
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">PKG</label>
              <input type="number" id="editPkgQty" value="${scan.pkgQty || ''}" 
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Qty</label>
              <input type="number" id="editQty" value="${scan.qty || ''}" 
                class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
            </div>
          </div>
          
          <div class="flex justify-end mt-auto pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="space-x-3">
              <button id="saveEditScanBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                Save Changes
              </button>
              <button id="cancelEditScanBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Set up event listeners
    const closeButtons = document.querySelectorAll('#closeEditScanModalBtn, #cancelEditScanBtn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const modal = document.querySelector('#editScanModal');
        if (modal) modal.remove();
      });
    });
    
    // Save changes button
    const saveBtn = document.querySelector('#saveEditScanBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        // Get updated values
        const updatedScan = {
          ...scan,
          vendorPartNumber: document.querySelector('#editVendorPartNumber').value,
          vendorCreatedDate: document.querySelector('#editVendorCreatedDate').value,
          pkgQty: document.querySelector('#editPkgQty').value,
          qty: document.querySelector('#editQty').value
        };
        
        // Close the modal
        const modal = document.querySelector('#editScanModal');
        if (modal) modal.remove();
        
        // Update the scan data in the local array
        this.updateScan(updatedScan, index);
      });
    }
  }
  
  // Update a scan in the local array and refresh the table
  updateScan(updatedScan, index) {
    try {
      // Update in filteredData
      this.filteredData[index] = updatedScan;
      
      // Find and update in scanData
      const scanDataIndex = this.scanData.findIndex(s => 
        s.timestamp === updatedScan.timestamp && 
        s.rawBarcode === updatedScan.rawBarcode
      );
      
      if (scanDataIndex !== -1) {
        this.scanData[scanDataIndex] = updatedScan;
      }
      
      // For a real app, here we would send the update to the server
      // In this prototype, we're just updating the local data
      
      // Update the table display
      this.updateTable();
      this.addNotification('Scan updated successfully', 'success');
      
    } catch (error) {
      console.error('Error updating scan:', error);
      this.addNotification(`Failed to update scan: ${error.message}`, 'error');
    }
  }
  
  // Show delete confirmation dialog
  showDeleteConfirmation(scan, index) {
    // Remove any existing modal
    const existingModal = document.querySelector('#deleteScanModal');
    if (existingModal) {
      existingModal.remove();
    }
    
    // Create modal HTML
    const modalHtml = `
      <div id="deleteScanModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 w-96 overflow-hidden flex flex-col">
          <div class="flex justify-between items-center mb-3 pb-2 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Confirm Deletion</h3>
            <button id="closeDeleteScanModalBtn" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-700 dark:text-gray-300">
              Are you sure you want to delete the scan record for <span class="font-semibold">${scan.vendorPartNumber}</span>?
            </p>
            <p class="text-xs text-red-600 dark:text-red-400 mt-2">
              This action cannot be undone.
            </p>
          </div>
          
          <div class="flex justify-end pt-3 border-t border-gray-200 dark:border-gray-700">
            <div class="space-x-3">
              <button id="confirmDeleteScanBtn" class="px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 text-sm">
                Delete
              </button>
              <button id="cancelDeleteScanBtn" class="px-3 py-1 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-700 text-sm">
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Set up event listeners
    const closeButtons = document.querySelectorAll('#closeDeleteScanModalBtn, #cancelDeleteScanBtn');
    closeButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        const modal = document.querySelector('#deleteScanModal');
        if (modal) modal.remove();
      });
    });
    
    // Confirm delete button
    const confirmBtn = document.querySelector('#confirmDeleteScanBtn');
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => {
        // Close the modal
        const modal = document.querySelector('#deleteScanModal');
        if (modal) modal.remove();
        
        // Delete the scan
        this.deleteScan(scan, index);
      });
    }
  }
  
  // Delete a scan and refresh the table
  async deleteScan(scan, index) {
    try {
      const deletingNotification = this.addNotification('Deleting scan...', 'info');
      
      // Track the raw record ID from SheetDB if available and the timestamp from sheet
      const recordId = scan.id || '';
      const sheetTimestamp = scan.sheetTimestamp || scan.timestamp || '';
      
      // Add to deletedItems set using uniqueId
      if (scan.uniqueId) {
        this.deletedItems.add(scan.uniqueId);
      } else {
        // Generate a unique ID if it doesn't exist
        scan.uniqueId = this.generateUniqueId(scan);
        this.deletedItems.add(scan.uniqueId);
      }
      
      // Remove from filteredData array
      this.filteredData.splice(index, 1);
      
      // Find and remove from scanData array
      const scanDataIndex = this.scanData.findIndex(s => 
        (s.uniqueId === scan.uniqueId) || 
        (s.timestamp === scan.timestamp && s.rawBarcode === scan.rawBarcode)
      );
      
      if (scanDataIndex !== -1) {
        this.scanData.splice(scanDataIndex, 1);
      }
      
      // Delete from the Google Sheet database
      let databaseDeletionSuccess = false;
      
      console.log('Attempting to delete record with timestamp:', sheetTimestamp);
      
      try {
        // Method 1: Delete using timestamp as the primary identifier
        if (sheetTimestamp) {
          const timestampDeleteResponse = await fetch(`${this.apiUrl}/Timestamp/${encodeURIComponent(sheetTimestamp)}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${this.apiToken}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (timestampDeleteResponse.ok) {
            databaseDeletionSuccess = true;
            console.log('Successfully deleted record using Timestamp');
          } else {
            console.warn('Timestamp delete failed:', await timestampDeleteResponse.text());
            
            // Method 2: Try SheetDB's search API to find and delete the record
            const searchData = {
              "Timestamp": sheetTimestamp
            };
            
            const searchResponse = await fetch(`${this.apiUrl}/search`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${this.apiToken}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(searchData)
            });
            
            if (searchResponse.ok) {
              const matchingRecords = await searchResponse.json();
              console.log('Found matching records:', matchingRecords);
              
              if (matchingRecords && matchingRecords.length > 0) {
                // Delete the first matching record found
                const matchId = matchingRecords[0].id;
                
                if (matchId) {
                  const deleteMatchResponse = await fetch(`${this.apiUrl}/id/${matchId}`, {
                    method: 'DELETE',
                    headers: {
                      'Authorization': `Bearer ${this.apiToken}`,
                      'Content-Type': 'application/json'
                    }
                  });
                  
                  if (deleteMatchResponse.ok) {
                    databaseDeletionSuccess = true;
                    console.log('Successfully deleted record found via search');
                  } else {
                    console.warn('Failed to delete record found via search');
                  }
                }
              }
            }
            
            // Method 3: If all else fails, try to update with empty data
            if (!databaseDeletionSuccess) {
              console.warn('Trying to soft delete by updating with empty data');
              
              // Create an empty data object
              const emptyData = {
                "BarCode": "",
                "Timestamp": new Date().toISOString() + " (deleted)" // Mark as deleted
              };
              
              // Try to update by ID if available
              if (recordId) {
                const updateResponse = await fetch(`${this.apiUrl}/id/${recordId}`, {
                  method: 'PATCH',
                  headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(emptyData)
                });
                
                if (updateResponse.ok) {
                  databaseDeletionSuccess = true;
                  console.log('Successfully "soft deleted" by updating with empty data');
                } else {
                  console.error('Failed to soft delete:', await updateResponse.text());
                }
              } 
              // Otherwise try to update by timestamp
              else if (sheetTimestamp) {
                const updateByTimestampResponse = await fetch(`${this.apiUrl}/Timestamp/${encodeURIComponent(sheetTimestamp)}`, {
                  method: 'PATCH',
                  headers: {
                    'Authorization': `Bearer ${this.apiToken}`,
                    'Content-Type': 'application/json'
                  },
                  body: JSON.stringify(emptyData)
                });
                
                if (updateByTimestampResponse.ok) {
                  databaseDeletionSuccess = true;
                  console.log('Successfully "soft deleted" by timestamp with empty data');
                } else {
                  console.error('All deletion methods failed');
                }
              }
            }
          }
        }
      } catch (dbError) {
        console.error('Error deleting from database:', dbError);
        // Continue with local deletion even if database deletion fails
      }
      
      // Save to local storage
      this.saveToStorage();
      
      // Update the table display
      this.updateTable();
      
      // Show success message
      if (databaseDeletionSuccess) {
        this.addNotification('Scan deleted from local data and database', 'success');
      } else {
        this.addNotification('Scan deleted from local data only', 'warning');
      }
      
    } catch (error) {
      console.error('Error deleting scan:', error);
      this.addNotification(`Failed to delete scan: ${error.message}`, 'error');
    }
  }
} 