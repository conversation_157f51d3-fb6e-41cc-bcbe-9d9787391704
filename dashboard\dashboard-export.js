// dashboard-export.js - Printing and Export functionality

export class ExportSystem {
  constructor(dashboard) {
    this.dashboard = dashboard;
  }
  
  // Show print menu with export options
  printPreviewData(data) {
    if (!data) return;

    const printMenu = document.createElement("div");
    printMenu.className = "absolute right-4 top-16 bg-white shadow-lg rounded-md border border-gray-200 p-2 z-10";
    printMenu.innerHTML = `
      <div class="text-xs font-medium mb-1 text-gray-700">Export as:</div>
      <button class="text-xs w-full text-left px-2 py-1 hover:bg-gray-100 rounded" data-format="pdf">
        <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
        </svg>
        PDF Document
      </button>
      <button class="text-xs w-full text-left px-2 py-1 hover:bg-gray-100 rounded" data-format="excel">
        <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        Excel Spreadsheet
      </button>
      <button class="text-xs w-full text-left px-2 py-1 hover:bg-gray-100 rounded" data-format="json">
        <svg class="inline-block w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
        </svg>
        JSON Format
      </button>
    `;

    document.body.appendChild(printMenu);

    const pdfBtn = printMenu.querySelector('[data-format="pdf"]');
    const excelBtn = printMenu.querySelector('[data-format="excel"]');
    const jsonBtn = printMenu.querySelector('[data-format="json"]');

    pdfBtn.addEventListener("click", () => {
      this.exportAsPDF(data);
      document.body.removeChild(printMenu);
    });

    excelBtn.addEventListener("click", () => {
      this.exportAsExcel(data);
      document.body.removeChild(printMenu);
    });
    
    jsonBtn.addEventListener("click", () => {
      this.exportAsJSON(data);
      document.body.removeChild(printMenu);
    });

    document.addEventListener("click", function closeMenu(e) {
      if (!printMenu.contains(e.target) && e.target.id !== "printBtn") {
        document.body.removeChild(printMenu);
        document.removeEventListener("click", closeMenu);
      }
    });
  }

  // Export as PDF
  exportAsPDF(data) {
    console.log("Exporting as PDF:", data);

    const printWindow = window.open("", "_blank");

    printWindow.document.write(`
      <html>
      <head>
        <title>Order ${data.orderNumber || ""} - Print Preview</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { font-size: 18px; margin-bottom: 10px; }
          h2 { font-size: 16px; margin-top: 20px; margin-bottom: 10px; }
          .section { margin-bottom: 20px; }
          .grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; }
          .item { margin-bottom: 8px; }
          .label { font-weight: bold; font-size: 12px; color: #555; }
          .value { font-size: 14px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
          th { background: #f1f1f1; padding: 8px; text-align: left; font-size: 12px; }
          td { padding: 8px; border-bottom: 1px solid #ddd; font-size: 12px; }
          .part-details { margin-left: 20px; margin-bottom: 15px; padding: 10px; background: #f9f9f9; border-left: 3px solid #ddd; }
          .part-detail-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px; }
          .part-detail-item { margin-bottom: 5px; }
          .part-detail-label { font-weight: bold; font-size: 11px; color: #666; }
          .part-detail-value { font-size: 12px; }
        </style>
      </head>
      <body>
        <h1>Order Details: ${data.orderNumber || "N/A"}</h1>

        <div class="section">
          <div class="grid">
            <div class="item">
              <div class="label">Customer:</div>
              <div class="value">${data.customer || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Reference:</div>
              <div class="value">${data.reference || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Order Type:</div>
              <div class="value">${data.orderType || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Order Number:</div>
              <div class="value">${data.orderNumber || "N/A"}</div>
            </div>
          </div>
        </div>

        <div class="section">
          <h2>Parts</h2>
          <table>
            <thead>
              <tr>
                <th>Inventory ID</th>
                <th>Description</th>
                <th>Qty</th>
                <th>Serial Number</th>
              </tr>
            </thead>
            <tbody>
              ${data.parts
                .map(
                  (part) => `
                <tr>
                  <td>${part.inventoryID || "N/A"}</td>
                  <td>${part.description || "N/A"}</td>
                  <td>${part.shippedQuantity || "N/A"}</td>
                  <td>${part.lotSerialNumber || "N/A"}</td>
                </tr>
                ${part.masterPartData ? `
                <tr>
                  <td colspan="4">
                    <div class="part-details">
                      <div class="part-detail-grid">
                        <div class="part-detail-item">
                          <div class="part-detail-label">Country of Origin:</div>
                          <div class="part-detail-value">${part.masterPartData['Country of Origin'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">HS Code:</div>
                          <div class="part-detail-value">${part.masterPartData['HS Code'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">Supplier:</div>
                          <div class="part-detail-value">${part.masterPartData['Supplier'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">Supplier P/N:</div>
                          <div class="part-detail-value">${part.masterPartData['Supplier P/N'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">UOM:</div>
                          <div class="part-detail-value">${part.masterPartData['UOM'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">Manufacturer:</div>
                          <div class="part-detail-value">${part.masterPartData['Manufacturer'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">USMCA/CASMA:</div>
                          <div class="part-detail-value">${part.masterPartData['USMCA/CASMA'] || 'N/A'}</div>
                        </div>
                        <div class="part-detail-item">
                          <div class="part-detail-label">Default Price:</div>
                          <div class="part-detail-value">${part.masterPartData['Default Price'] || 'N/A'}</div>
                        </div>
                      </div>
                      <div class="part-detail-item" style="grid-column: span 2;">
                        <div class="part-detail-label">Manufacturer & Address:</div>
                        <div class="part-detail-value">${part.masterPartData['Manufacturer & Address'] || 'N/A'}</div>
                      </div>
                      <div class="part-detail-item" style="grid-column: span 2;">
                        <div class="part-detail-label">Customs Description:</div>
                        <div class="part-detail-value">${part.masterPartData['Customs Description'] || 'N/A'}</div>
                      </div>
                      <div class="part-detail-item" style="grid-column: span 2;">
                        <div class="part-detail-label">Preference Criteria:</div>
                        <div class="part-detail-value">${part.masterPartData['Preference Criteria'] || 'N/A'}</div>
                      </div>
                      <div class="part-detail-item">
                        <div class="part-detail-label">NMFC Code:</div>
                        <div class="part-detail-value">${part.masterPartData['NMFC Code'] || 'N/A'}</div>
                      </div>
                    </div>
                  </td>
                </tr>
                ` : ''}
              `,
                )
                .join("")}
            </tbody>
          </table>
        </div>
        
        ${data.shipmentInfo ? `
        <div class="section">
          <h2>Shipping Information</h2>
          <div class="grid">
            <div class="item">
              <div class="label">Company Name:</div>
              <div class="value">${data.shipmentInfo.companyName || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Attention:</div>
              <div class="value">${data.shipmentInfo.attention || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Phone:</div>
              <div class="value">${data.shipmentInfo.phone || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Email:</div>
              <div class="value">${data.shipmentInfo.email || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Address:</div>
              <div class="value">${data.shipmentInfo.addressLine1 || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Address Line 2:</div>
              <div class="value">${data.shipmentInfo.addressLine2 || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">City:</div>
              <div class="value">${data.shipmentInfo.city || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">State:</div>
              <div class="value">${data.shipmentInfo.state || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Postal Code:</div>
              <div class="value">${data.shipmentInfo.postalCode || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Country:</div>
              <div class="value">${data.shipmentInfo.country || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Shipping Method:</div>
              <div class="value">${data.shipmentInfo.shippingMethod || "N/A"}</div>
            </div>
            <div class="item">
              <div class="label">Shipping Terms:</div>
              <div class="value">${data.shipmentInfo.shippingTerms || "N/A"}</div>
            </div>
          </div>
          <div class="item" style="margin-top: 10px;">
            <div class="label">Shipment Description:</div>
            <div class="value">${data.shipmentInfo.shipmentDescription || "N/A"}</div>
          </div>
        </div>
        ` : ''}
      </body>
      </html>
    `);

    printWindow.document.close();

    setTimeout(() => {
      printWindow.print();
    }, 500);
  }

  // Export as Excel (CSV)
  exportAsExcel(data) {
    console.log("Exporting as CSV:", data);

    try {
      // Create workbook-like structure for better organization
      const csvSections = [];
      
      // SECTION 1: Order Details
      csvSections.push("ORDER DETAILS");
      csvSections.push("Customer,Reference,Order Type,Order Number");
      csvSections.push(`"${(data.customer || "N/A").replace(/"/g, '""')}","${(data.reference || "N/A").replace(/"/g, '""')}","${(data.orderType || "N/A").replace(/"/g, '""')}","${(data.orderNumber || "N/A").replace(/"/g, '""')}"`);
      csvSections.push("");
      
      // SECTION 2: Parts Basic Info
      csvSections.push("PARTS SUMMARY");
      csvSections.push("Inventory ID,Description,Qty,Serial Number");
      
      data.parts.forEach((part) => {
        csvSections.push(`"${(part.inventoryID || "N/A").replace(/"/g, '""')}","${(part.description || "N/A").replace(/"/g, '""')}","${(part.shippedQuantity || "N/A").replace(/"/g, '""')}","${(part.lotSerialNumber || "N/A").replace(/"/g, '""')}"`);
      });
      csvSections.push("");
      
      // SECTION 3: Part Details
      csvSections.push("DETAILED PART INFORMATION");
      csvSections.push("Inventory ID,Country of Origin,HS Code,Supplier,Supplier P/N,UOM,USMCA/CASMA,Default Price,NMFC Code");
      
      data.parts.forEach((part) => {
        if (part.masterPartData) {
          const md = part.masterPartData;
          const row = [
            part.inventoryID || "N/A",
            md['Country of Origin'] || "N/A",
            md['HS Code'] || "N/A",
            md['Supplier'] || "N/A",
            md['Supplier P/N'] || "N/A",
            md['UOM'] || "N/A",
            md['USMCA/CASMA'] || "N/A",
            md['Default Price'] || "N/A",
            md['NMFC Code'] || "N/A"
          ];
          csvSections.push(row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','));
        } else {
          csvSections.push(`"${(part.inventoryID || "N/A").replace(/"/g, '""')}","N/A","N/A","N/A","N/A","N/A","N/A","N/A","N/A"`);
        }
      });
      csvSections.push("");
      
      // SECTION 4: Extended Part Details (fields that are longer)
      csvSections.push("EXTENDED PART DETAILS");
      csvSections.push("Inventory ID,Manufacturer,Manufacturer & Address,Customs Description,Preference Criteria");
      
      data.parts.forEach((part) => {
        if (part.masterPartData) {
          const md = part.masterPartData;
          const row = [
            part.inventoryID || "N/A",
            md['Manufacturer'] || "N/A",
            md['Manufacturer & Address'] || "N/A",
            md['Customs Description'] || "N/A",
            md['Preference Criteria'] || "N/A"
          ];
          csvSections.push(row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','));
        } else {
          csvSections.push(`"${(part.inventoryID || "N/A").replace(/"/g, '""')}","N/A","N/A","N/A","N/A"`);
        }
      });
      
      // SECTION 5: Shipment Info (if available)
      if (data.shipmentInfo) {
        csvSections.push("");
        csvSections.push("SHIPMENT INFORMATION");
        csvSections.push("Company Name,Attention,Phone,Email,Address Line 1,Address Line 2,City,State,Postal Code,Country,Shipping Method,Shipping Terms");
        
        const si = data.shipmentInfo;
        const shipmentRow = [
          si.companyName || "N/A",
          si.attention || "N/A",
          si.phone || "N/A",
          si.email || "N/A",
          si.addressLine1 || "N/A",
          si.addressLine2 || "N/A",
          si.city || "N/A",
          si.state || "N/A",
          si.postalCode || "N/A",
          si.country || "N/A",
          si.shippingMethod || "N/A",
          si.shippingTerms || "N/A"
        ];
        csvSections.push(shipmentRow.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','));
        
        // Add shipment description on its own line
        csvSections.push("");
        csvSections.push("Shipment Description");
        csvSections.push(`"${(si.shipmentDescription || "N/A").replace(/"/g, '""')}"`);
      }
      
      // Create CSV content from sections
      const csvContent = "data:text/csv;charset=utf-8," + csvSections.join('\n');
      
      // Download the CSV
      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", `Order_${data.orderNumber || "Export"}_${new Date().toISOString().slice(0, 10)}.csv`);
      document.body.appendChild(link);
      
      link.click();
      
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error exporting to CSV:", error);
      alert("Failed to export to CSV. Try printing as PDF instead.");
    }
  }
  
  // Export as JSON - new function
  exportAsJSON(data) {
    console.log("Exporting as JSON:", data);
    
    try {
      // Create a cleaned copy of the data
      const cleanedData = JSON.parse(JSON.stringify(data));
      
      // Format as formatted JSON with indentation
      const jsonContent = "data:text/json;charset=utf-8," + 
                          encodeURIComponent(JSON.stringify(cleanedData, null, 2));
      
      // Create a download link
      const link = document.createElement("a");
      link.setAttribute("href", jsonContent);
      link.setAttribute("download", `Order_${data.orderNumber || "Export"}_${new Date().toISOString().slice(0, 10)}.json`);
      document.body.appendChild(link);
      
      // Trigger download
      link.click();
      
      // Clean up
      document.body.removeChild(link);
      
      this.dashboard.addNotification("JSON exported successfully", "success");
    } catch (error) {
      console.error("Error exporting to JSON:", error);
      alert("Failed to export to JSON. Please try another format.");
      this.dashboard.addNotification("Failed to export JSON", "danger");
    }
  }
}

// Function to initialize export system
export function initializeExports(dashboard) {
  return new ExportSystem(dashboard);
}
