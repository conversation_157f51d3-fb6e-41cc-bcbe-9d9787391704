// Analytics Data Service - Centralized data management for analytics components
// This class handles all data operations and provides consistent access to analytics data

export class AnalyticsDataService {
  constructor() {
    this.historyData = [];
    this.lastUpdate = null;
    this.isLoading = false;
  }

  /**
   * Load data from storage (Chrome storage or localStorage)
   * @returns {Promise<boolean>} True if data was loaded successfully
   */
  async loadDataFromStorage() {
    console.log("Attempting to load data from storage");
    
    return new Promise((resolve) => {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['analyticsHistoryData', 'analyticsLastUpdate'], (result) => {
          if (chrome.runtime.lastError) {
            console.error('Chrome storage error:', chrome.runtime.lastError);
            resolve(false);
            return;
          }
          
          if (result && result.analyticsHistoryData && Array.isArray(result.analyticsHistoryData) && result.analyticsHistoryData.length > 0) {
            this.historyData = this.sortDataByDate(result.analyticsHistoryData);
            this.lastUpdate = result.analyticsLastUpdate ? new Date(result.analyticsLastUpdate) : new Date();
            console.log(`✅ Successfully loaded ${this.historyData.length} records from chrome storage`);
            resolve(true);
          } else {
            console.log('⚠️ No valid data found in chrome storage');
            resolve(false);
          }
        });
      } else {
        try {
          const storedData = localStorage.getItem('analyticsHistoryData');
          const storedDate = localStorage.getItem('analyticsLastUpdate');
          
          if (storedData) {
            const parsedData = JSON.parse(storedData);
            if (Array.isArray(parsedData) && parsedData.length > 0) {
              this.historyData = this.sortDataByDate(parsedData);
              this.lastUpdate = storedDate ? new Date(storedDate) : new Date();
              console.log(`✅ Successfully loaded ${this.historyData.length} records from localStorage`);
              resolve(true);
            } else {
              console.log('⚠️ Invalid data format in localStorage');
              resolve(false);
            }
          } else {
            console.log('⚠️ No data found in localStorage');
            resolve(false);
          }
        } catch (e) {
          console.error('Error loading from localStorage:', e);
          resolve(false);
        }
      }
    });
  }

  /**
   * Fetch fresh history data from API with improved error handling and retries
   * @returns {Promise<Array>} The history data
   */
  async fetchHistoryData() {
    if (this.isLoading) {
      console.log("Already fetching data, skipping duplicate request");
      return this.historyData;
    }
    
    this.isLoading = true;
    console.log("Fetching fresh history data from API");
    
    try {
      const apiEndpoint = "https://sheetdb.io/api/v1/ygn268dcacru7";
      // Add cache-busting parameter
      const cacheBuster = `_cb=${Date.now()}`;
      const url = `${apiEndpoint}?${cacheBuster}`;
      
      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Authorization": "Bearer tgive5whsoypqz9f6lq7ggno3xkamhp1dhqhc9ed",
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          "Pragma": "no-cache"
        },
        // Add timeout to prevent hanging requests
        signal: AbortSignal.timeout ? AbortSignal.timeout(10000) : new AbortController().signal
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const newData = await response.json();
      
      if (!Array.isArray(newData)) {
        throw new Error("API returned non-array data");
      }
      
      console.log(`✅ Successfully fetched ${newData.length} records from API`);
      
      if (newData.length > 0) {
        // Sort data with newest first before storing
        this.historyData = this.sortDataByDate(newData);
        this.lastUpdate = new Date();
        
        // Save to storage immediately
        this.saveDataToStorage();
      } else {
        console.warn("API returned empty data array");
      }
      
      return this.historyData;
    } catch (error) {
      console.error("❌ Error fetching history data:", error);
      return this.historyData; // Return existing data
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Sort data with newest entries first
   * @param {Array} data - The data to sort
   * @returns {Array} Sorted data
   */
  sortDataByDate(data) {
    return [...data].sort((a, b) => {
      const dateA = new Date(a.Date || 0);
      const dateB = new Date(b.Date || 0);
      return dateB - dateA; // Descending order (newest first)
    });
  }

  /**
   * Save data to storage with verification
   */
  saveDataToStorage() {
    if (!this.historyData || !Array.isArray(this.historyData) || this.historyData.length === 0) {
      console.warn("No valid data to save to storage");
      return;
    }

    console.log(`Saving ${this.historyData.length} records to storage`);
    
    // Prepare data for storage
    const storageData = {
      analyticsHistoryData: this.historyData,
      analyticsLastUpdate: this.lastUpdate.toISOString()
    };
    
    // For debugging
    const dataSize = new Blob([JSON.stringify(storageData)]).size;
    console.log(`Data size for storage: ${(dataSize / 1024).toFixed(2)} KB`);

    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.set(storageData, () => {
        if (chrome.runtime.lastError) {
          console.error("❌ Error saving to chrome storage:", chrome.runtime.lastError);
        } else {
          console.log(`✅ Successfully saved ${this.historyData.length} records to chrome storage`);
        }
      });
    } else {
      try {
        localStorage.setItem('analyticsHistoryData', JSON.stringify(this.historyData));
        localStorage.setItem('analyticsLastUpdate', this.lastUpdate.toISOString());
        console.log(`✅ Successfully saved ${this.historyData.length} records to localStorage`);
      } catch (e) {
        console.error('❌ Error saving to localStorage:', e);
      }
    }
  }

  /**
   * Filter data based on time period
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Filtered data
   */
  getTimeFilteredData(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'day':
        // Today only
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'week':
        // This week (starting from Monday)
        const day = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
        const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
        startDate.setDate(diff);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        // This month
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'quarter':
        // This quarter
        const quarter = Math.floor(today.getMonth() / 3);
        startDate.setMonth(quarter * 3, 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'year':
        // This year
        startDate.setMonth(0, 1);
        startDate.setHours(0, 0, 0, 0);
        break;
      default:
        // Default to this month
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
    }
    
    // Filter records by date
    return this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
  }
  
  /**
   * Get cost data filtered by time
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Filtered cost data
   */
  getTimeFilteredCostData(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'week':
        startDate.setDate(today.getDate() - 7);
        break;
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by date and calculate costs
    const dailyCosts = {};
    
    filteredRecords.forEach(record => {
      const date = record.Date;
      let cost = parseFloat(record["Freight Cost"]) || 0;
      if (isNaN(cost)) cost = 0;
      
      if (!dailyCosts[date]) {
        dailyCosts[date] = { date: date, cost: 0, count: 0 };
      }
      
      dailyCosts[date].cost += cost;
      dailyCosts[date].count += 1;
    });
    
    // Convert to array and sort by date
    return Object.values(dailyCosts)
      .sort((a, b) => new Date(a.date) - new Date(b.date));
  }
  
  /**
   * Get grouped shipment volume data
   * @param {string} groupBy - How to group the data
   * @returns {Array} Grouped shipment volume data
   */
  getGroupedShipmentVolume(groupBy) {
    // Get date range based on group
    const today = new Date();
    let startDate = new Date();
    let format = {};
    
    switch (groupBy) {
      case 'week':
        startDate.setDate(today.getDate() - 7 * 8); // Last 8 weeks
        format = { month: 'short', day: 'numeric' };
        break;
      case 'month':
        startDate.setMonth(today.getMonth() - 12); // Last 12 months
        format = { month: 'short', year: '2-digit' };
        break;
      case 'quarter':
        startDate.setFullYear(today.getFullYear() - 2); // Last 8 quarters (2 years)
        format = { month: 'short', year: 'numeric' };
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
        format = { month: 'short', day: 'numeric' };
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by period
    const groupedData = {};
    
    filteredRecords.forEach(record => {
      let groupKey;
      const recordDate = new Date(record.Date);
      
      switch (groupBy) {
        case 'week':
          // Group by week - use Monday as start of week
          const weekStart = new Date(recordDate);
          const day = recordDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
          const diff = recordDate.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
          weekStart.setDate(diff);
          groupKey = weekStart.toISOString().split('T')[0];
          break;
        case 'month':
          // Group by month
          groupKey = `${recordDate.getFullYear()}-${String(recordDate.getMonth() + 1).padStart(2, '0')}`;
          break;
        case 'quarter':
          // Group by quarter
          const quarter = Math.floor(recordDate.getMonth() / 3) + 1;
          groupKey = `${recordDate.getFullYear()}-Q${quarter}`;
          break;
        default:
          groupKey = record.Date;
      }
      
      if (!groupedData[groupKey]) {
        let label = '';
        
        // Create human-readable label
        switch (groupBy) {
          case 'week':
            const weekStart = new Date(groupKey);
            label = `${weekStart.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}`;
            break;
          case 'month':
            const [year, month] = groupKey.split('-');
            label = new Date(parseInt(year), parseInt(month) - 1, 1).toLocaleDateString(undefined, { month: 'short', year: '2-digit' });
            break;
          case 'quarter':
            label = groupKey;
            break;
          default:
            label = new Date(groupKey).toLocaleDateString(undefined, format);
        }
        
        groupedData[groupKey] = { 
          key: groupKey, 
          label: label, 
          count: 0 
        };
      }
      
      groupedData[groupKey].count += 1;
    });
    
    // Convert to array and sort by key
    return Object.values(groupedData)
      .sort((a, b) => a.key.localeCompare(b.key));
  }

  /**
   * Get shipments by day of week
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Shipments grouped by day of week
   */
  getShipmentsByDayOfWeek(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'week':
        startDate.setDate(today.getDate() - 14); // Last 2 weeks
        break;
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      default:
        startDate.setDate(today.getDate() - 14); // Default to 2 weeks
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Map day names to their full names for better readability
    const dayNames = [
      { dayIndex: 1, day: 'Monday' },
      { dayIndex: 2, day: 'Tuesday' },
      { dayIndex: 3, day: 'Wednesday' },
      { dayIndex: 4, day: 'Thursday' },
      { dayIndex: 5, day: 'Friday' },
      { dayIndex: 6, day: 'Saturday' },
      { dayIndex: 0, day: 'Sunday' }
    ];
    
    // Initialize the counts array with all days set to 0
    const dayCounts = dayNames.map(item => ({ ...item, count: 0 }));
    
    // Count shipments by day of week using the date object's getDay method
    filteredRecords.forEach(record => {
      if (!record.Date) return;
      try {
        const recordDate = new Date(record.Date);
        const dayOfWeek = recordDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
        
        // Find the corresponding day in our array and increment its count
        const dayIndex = dayCounts.findIndex(item => item.dayIndex === dayOfWeek);
        if (dayIndex !== -1) {
          dayCounts[dayIndex].count += 1;
        }
      } catch (error) {
        console.error(`Error processing date: ${record.Date}`, error);
      }
    });
    
    // Sort by day of week (Monday first)
    return dayCounts
      .sort((a, b) => {
        // Special sorting to put Monday first (index 1)
        const indexA = a.dayIndex === 0 ? 7 : a.dayIndex;
        const indexB = b.dayIndex === 0 ? 7 : b.dayIndex;
        return indexA - indexB;
      })
      .map(item => ({
        day: item.day,
        count: item.count
      }));
  }

  /**
   * Get shipments by carrier
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Shipments grouped by carrier
   */
  getShipmentsByCarrier(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by carrier
    const carrierCounts = {};
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      
      if (!carrierCounts[carrier]) {
        carrierCounts[carrier] = 0;
      }
      
      carrierCounts[carrier] += 1;
    });
    
    // Convert to array and sort by count (descending)
    return Object.entries(carrierCounts)
      .filter(([carrier]) => carrier !== 'Unknown' && carrier !== 'N/A')
      .map(([carrier, count]) => ({ carrier, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 6); // Top 6 carriers
  }
  
  /**
   * Get carrier costs filtered by time
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Carrier costs
   */
  getTimeFilteredCarrierCosts(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Group by carrier and calculate costs
    const carrierData = {};
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      let cost = parseFloat(record["Freight Cost"]) || 0;
      if (isNaN(cost)) cost = 0;
      
      if (!carrierData[carrier]) {
        carrierData[carrier] = { carrier: carrier, totalCost: 0, count: 0 };
      }
      
      carrierData[carrier].totalCost += cost;
      carrierData[carrier].count += 1;
    });
    
    // Calculate average costs and format
    return Object.values(carrierData)
      .map(item => ({
        carrier: item.carrier,
        totalCost: item.totalCost,
        avgCost: item.count > 0 ? item.totalCost / item.count : 0,
        count: item.count
      }))
      .filter(item => item.carrier !== 'Unknown' && item.carrier !== 'N/A')
      .sort((a, b) => b.avgCost - a.avgCost)
      .slice(0, 5); // Top 5 carriers
  }
  
  /**
   * Calculate carrier utilization
   * @returns {Array} Carrier utilization data
   */
  calculateCarrierUtilization() {
    // Count shipments by carrier in the last 90 days
    const carrierCounts = {};
    let totalShipments = 0;
    
    // Get records from last 90 days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 90);
    
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate;
    });
    
    filteredRecords.forEach(record => {
      const carrier = record.Carrier || 'Unknown';
      
      if (!carrierCounts[carrier]) {
        carrierCounts[carrier] = 0;
      }
      
      carrierCounts[carrier] += 1;
      totalShipments += 1;
    });
    
    // Calculate percentages
    return Object.keys(carrierCounts)
      .filter(carrier => carrier !== 'Unknown' && carrier !== 'N/A')
      .map(carrier => ({
        carrier: carrier,
        count: carrierCounts[carrier],
        percentage: Math.round((carrierCounts[carrier] / totalShipments) * 100)
      }))
      .sort((a, b) => b.count - a.count);
  }
  
  /**
   * Get top products filtered by time
   * @param {string} timeFilter - The time filter to apply
   * @param {number} limit - Maximum number of products to return
   * @returns {Array} Top products
   */
  getTimeFilteredTopProducts(timeFilter, limit = 5) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        // For "All Time", we don't filter by date, so no need to set startDate
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date (skip filtering if "All Time" is selected)
    const filteredRecords = timeFilter === 'year' 
      ? this.historyData
      : this.historyData.filter(record => {
          if (!record.Date) return false;
          const recordDate = new Date(record.Date);
          return recordDate >= startDate && recordDate <= today;
        });
    
    // Parse all inventory IDs and count occurrences
    const productCounts = {};
    
    filteredRecords.forEach(record => {
      if (!record["Inventory ID"]) return;
      
      // Split the comma-separated inventory IDs
      const inventoryIds = record["Inventory ID"].split(",").map(id => id.trim());
      
      inventoryIds.forEach(id => {
        if (!id || id === 'N/A') return;
        
        if (!productCounts[id]) {
          productCounts[id] = 0;
        }
        
        productCounts[id] += 1;
      });
    });
    
    // Convert to array and sort
    return Object.keys(productCounts)
      .map(id => ({
        id: id,
        count: productCounts[id]
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }

  /**
   * Get country distribution
   * @param {string} timeFilter - The time filter to apply
   * @returns {Array} Country distribution data
   */
  getCountryDistribution(timeFilter) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by country
    const countryCounts = {};
    let totalShipments = 0;
    
    filteredRecords.forEach(record => {
      const country = record.Country || 'Unknown';
      if (country !== 'Unknown' && country !== 'N/A') {
        countryCounts[country] = (countryCounts[country] || 0) + 1;
        totalShipments++;
      }
    });
    
    // Convert to array, calculate percentages, and format for pie chart
    const countryData = Object.entries(countryCounts)
      .map(([country, count]) => ({
        label: country,
        value: Math.round((count / totalShipments) * 100),
        tooltip: `${country}: ${count} shipments (${Math.round((count / totalShipments) * 100)}%)`
      }))
      .sort((a, b) => b.value - a.value);
    
    // Limit to top countries and group others
    if (countryData.length > 6) {
      const topCountries = countryData.slice(0, 5);
      const otherCountries = countryData.slice(5);
      
      const otherValue = otherCountries.reduce((sum, item) => sum + item.value, 0);
      const otherCount = otherCountries.reduce((sum, country) => {
        // Extract the count from the tooltip (format: "Country: X shipments (Y%)")
        const match = country.tooltip.match(/: (\d+) shipment/);
        return sum + (match ? parseInt(match[1]) : 0);
      }, 0);
      
      topCountries.push({
        label: 'Other Countries',
        value: otherValue,
        tooltip: `Other Countries: ${otherCount} shipments (${otherValue}%)`
      });
      
      return topCountries;
    }
    
    return countryData;
  }

  /**
   * Get customer shipments
   * @param {string} timeFilter - The time filter to apply
   * @param {number} limit - Maximum number of customers to return
   * @returns {Array} Customer shipment data
   */
  getCustomerShipments(timeFilter, limit = 6) {
    // Calculate date range based on filter
    const today = new Date();
    const startDate = new Date();
    
    switch (timeFilter) {
      case 'month':
        startDate.setDate(today.getDate() - 30);
        break;
      case 'quarter':
        startDate.setDate(today.getDate() - 90);
        break;
      case 'year':
        startDate.setDate(today.getDate() - 365);
        break;
      default:
        startDate.setDate(today.getDate() - 30); // Default to 30 days
    }
    
    // Filter records by date
    const filteredRecords = this.historyData.filter(record => {
      if (!record.Date) return false;
      const recordDate = new Date(record.Date);
      return recordDate >= startDate && recordDate <= today;
    });
    
    // Count shipments by customer
    const customerCounts = {};
    
    filteredRecords.forEach(record => {
      const customer = record["Customer Name"] || record["Company Name"] || 'Unknown';
      
      if (!customerCounts[customer]) {
        customerCounts[customer] = 0;
      }
      
      customerCounts[customer] += 1;
    });
    
    // Convert to array and sort by count
    return Object.entries(customerCounts)
      .filter(([customer]) => customer !== 'Unknown' && customer !== 'N/A')
      .map(([customer, count]) => ({ customer, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }
  
  // Helper methods for formatting
  shortenCarrierName(name) {
    // Handle common carrier names to make them fit better in charts
    const carrierMap = {
      'FedEx': 'FedEx',
      'UPS': 'UPS',
      'USPS': 'USPS',
      'Federal Express': 'FedEx',
      'United Parcel Service': 'UPS',
      'Postal Service': 'USPS',
      'DHL': 'DHL',
      'Purolator': 'Purolator',
      'Manitoulin': 'Manitoulin',
      'Loomis': 'Loomis',
      'JazooExpress': 'Jazoo',
      'Rosenau': 'Rosenau',
    };
    
    return carrierMap[name] || (name.length > 8 ? name.substring(0, 7) + '...' : name);
  }
  
  shortenProductId(id) {
    // Shorten product IDs for display
    if (!id) return '';
    
    // If the ID has a dash, keep the part before the dash
    if (id.includes('-')) {
      const parts = id.split('-');
      return parts[0].length > 5 ? parts[0].substring(0, 5) + '...' : parts[0];
    }
    
    // Otherwise shorten to first 8 chars
    return id.length > 7 ? id.substring(0, 7) + '...' : id;
  }
}