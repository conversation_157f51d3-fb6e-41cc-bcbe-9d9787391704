/**
 * modern-pie-charts.js - Enhanced modern pie/donut charts for the shipping analytics dashboard
 * Provides ApexCharts-based pie and donut chart implementations for more modern visualizations
 */

/**
 * Creates a modern donut chart using ApexCharts
 * @param {HTMLElement} container - The container element
 * @param {string} title - The chart title
 * @param {Array} series - The data series values
 * @param {Array} labels - The data labels
 * @param {Array} colors - Array of colors to use
 * @param {Function} tooltipFormatter - Optional custom tooltip formatter
 * @returns {ApexCharts} The chart instance
 */
export function createModernDonutChart(container, title, series, labels, colors, tooltipFormatter) {
  if (!container) return null;
  
  // Clear the container
  container.innerHTML = '';
  
  // Create chart options
  const options = {
    series: series,
    chart: {
      type: 'donut',
      height: container.clientHeight || 350,
      fontFamily: 'Inter, Segoe UI, sans-serif',
      animations: {
        enabled: true,
        speed: 700,
        animateGradually: {
          enabled: true,
          delay: 150
        },
        dynamicAnimation: {
          enabled: true,
          speed: 350
        }
      },
      toolbar: {
        show: false
      }
    },
    title: {
      text: title,
      align: 'center',
      style: {
        fontSize: '18px',
        fontWeight: 600,
        fontFamily: 'Inter, Segoe UI, sans-serif',
        color: '#111827'
      }
    },
    labels: labels,
    colors: colors || ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899', '#14B8A6', '#F97316'],
    plotOptions: {
      pie: {
        donut: {
          size: '55%',
          background: 'transparent',
          labels: {
            show: true,
            name: {
              show: true,
              fontSize: '14px',
              fontFamily: 'Inter, Segoe UI, sans-serif',
              fontWeight: 600,
              color: '#1F2937',
              offsetY: -10
            },
            value: {
              show: true,
              fontSize: '26px',
              fontFamily: 'Inter, Segoe UI, sans-serif',
              fontWeight: 700,
              color: '#1F2937',
              offsetY: 5,
              formatter: (val) => val
            },
            total: {
              show: true,
              showAlways: true,
              label: 'Total',
              fontSize: '16px',
              fontFamily: 'Inter, Segoe UI, sans-serif',
              fontWeight: 600,
              color: '#6B7280',
              formatter: (w) => w.globals.seriesTotals.reduce((a, b) => a + b, 0)
            }
          }
        }
      }
    },
    stroke: {
      width: 0
    },
    dataLabels: {
      enabled: true,
      style: {
        fontSize: '13px',
        fontFamily: 'Inter, Segoe UI, sans-serif',
        fontWeight: 500,
        colors: ['#fff']
      },
      dropShadow: {
        enabled: true,
        top: 1,
        left: 1,
        blur: 2,
        opacity: 0.2
      }
    },
    legend: {
      position: 'bottom',
      horizontalAlign: 'center',
      fontSize: '13px',
      fontFamily: 'Inter, Segoe UI, sans-serif',
      offsetY: 5,
      itemMargin: {
        horizontal: 8,
        vertical: 5
      },
      markers: {
        width: 10,
        height: 10,
        radius: 3
      }
    },
    tooltip: {
      enabled: true,
      custom: tooltipFormatter || undefined
    },
    responsive: [{
      breakpoint: 480,
      options: {
        chart: {
          height: 280
        },
        legend: {
          position: 'bottom',
          offsetY: 0
        }
      }
    }]
  };
  
  // Create and render the chart
  const chart = new ApexCharts(container, options);
  chart.render();
  
  return chart;
}

/**
 * Creates a modern pie chart using ApexCharts
 * @param {HTMLElement} container - The container element
 * @param {string} title - The chart title
 * @param {Array} series - The data series values
 * @param {Array} labels - The data labels
 * @param {Array} colors - Array of colors to use
 * @param {Function} tooltipFormatter - Optional custom tooltip formatter
 * @returns {ApexCharts} The chart instance
 */
export function createModernPieChart(container, title, series, labels, colors, tooltipFormatter) {
  // Use the donut chart but with size 0%
  const chart = createModernDonutChart(container, title, series, labels, colors, tooltipFormatter);
  
  // If we got a chart, update the donut size to 0% to make it a pie
  if (chart) {
    chart.updateOptions({
      plotOptions: {
        pie: {
          donut: {
            size: '0%'
          }
        }
      }
    });
  }
  
  return chart;
} 