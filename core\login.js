document.addEventListener("DOMContentLoaded", () => {
  // Constants for API access
  const SHEETDB_API_URL = "https://sheetdb.io/api/v1/lcon68jvxh23y";
  const SHEETDB_API_TOKEN = "Bearer w5icozbrzltd65ilssma8sk2al9i9zu7xpjkr6cc";
  
  // Updated expected fields from UserConfig sheet - for validation
  const EXPECTED_FIELDS = [
    "Email", "Password", "Role", "User Name", "Avatar", 
    "Adminmessage-Name", "Adminmessage-Avatar", "Adminmessage-Messge", 
    "Monday API Key", 
    "SH- Board ID", "SH-Group ID", "SH-Order Number", "SH-Order Type", 
    "SH-Customer Name", "SH-Reference Number", "SH-Country", "SH-Package Qty", 
    "SH-Package Type", "SH-Dims", "SH-Weight", "SH-Shipment Method", 
    "SH-Freight Cost", "SH-Shipment Description", "SH-Date", "SH_Package note", 
    "SH-Carrier", "SH-Tracking Number", "SH-Tracking Link", "SH-Inventory ID", 
    "SH-Part Description", "SH-Shipped Qty", "SH-Serial Nbr", "SH-HS Code", 
    "SH-Country of Origin", 
    "PO- Board ID", "PO-Group ID", "PO-Reference Number", "PO-Order Type", 
    "PO-Customer Name", "PO-Shipment Description", "PO-Country", "PO-Package Qty",
    "PO-Package Type", "PO-Dims", "PO-Weight", "PO-Shipment Method",
    "PO-Freight Cost", "PO-Carrier", "PO-Tracking No", "PO-Tracking Link",
    "PO-Package Note", "PO-Inventory ID", "PO-Part Description", "PO-Part Qty", 
    "PO-Serial Nbr", "PO-Supplier P/N", "PO-HS Code", "PO-COO", 
    "SO- Board ID", "SO-Group ID", "SO-Customer Name", "SO-Sales Order Number", 
    "SO-Order Type", "SO-Inventory ID", "SO-Description", "SO-Order Qty", 
    "SO-Production Order Type", "SO-Production Number"
  ];

  // Check if already logged in
  chrome.storage.local.get(["loggedIn", "user"], (result) => {
    if (result.loggedIn && result.user) {
      console.log("User already logged in, redirecting to index.html");
      window.location.href = "index.html";
    }
  });

  // DOM element references
  const getStartedButton = document.getElementById("getStartedButton");
  const loginDialog = document.getElementById("loginDialog");
  const profileDialog = document.getElementById("profileDialog");
  const loginForm = document.getElementById("loginForm");
  const termsCheckbox = document.getElementById("terms");
  const loginButton = document.getElementById("loginButton");
  const profileGrid = document.getElementById("profileGrid");
  const outlookLogin = document.getElementById("outlookLogin");
  const teamsLogin = document.getElementById("teamsLogin");
  const loginNotification = document.getElementById("loginNotification");

  let currentUser = null;
  let allUserFields = []; // Will store all fields from the UserConfig sheet

  // Get Started button functionality
  getStartedButton.addEventListener("click", () => {
    loginDialog.style.display = "flex";
  });

  // Handle terms checkbox
  termsCheckbox.addEventListener("change", () => {
    loginButton.disabled = !termsCheckbox.checked;
    loginButton.classList.toggle("active", termsCheckbox.checked);
  });

  // Handle login form submission
  loginForm.addEventListener("submit", async (e) => {
    e.preventDefault();
    const email = document.getElementById("email").value;
    const password = document.getElementById("password").value;

    if (!email || !password) {
      showLoginNotification("Please enter both email and password", "warning");
      return;
    }

    try {
      showLoginNotification("Connecting to server...", "info");

      // First, fetch all users to get the complete field list
      const response = await fetchUserData();
      
      if (!response.ok) {
        throw new Error(`Failed to fetch user data: ${response.status} ${response.statusText}`);
      }
      
      const users = await response.json();

      // Extract all possible field names from users
      if (users.length > 0) {
        allUserFields = Object.keys(users[0]);
        console.log(`Found ${allUserFields.length} fields in UserConfig`);
        
        // Check if all expected fields are present
        const missingFields = EXPECTED_FIELDS.filter(field => !allUserFields.includes(field));
        
        if (missingFields.length > 0) {
          console.warn("Missing expected fields in UserConfig:", missingFields);
        }
      } else {
        console.warn("No users found in UserConfig sheet");
      }

      // Find the user with matching credentials
      const user = users.find((u) => u.Email === email && u.Password === password);

      if (user) {
        // Store current user
        currentUser = user;
        console.log(`User logged in: ${user["User Name"]}, Role: ${user.Role}`);
        
        // Check for critical missing fields
        const criticalFields = ["Email", "Role", "User Name", "Monday API Key"];
        const missingCriticalFields = criticalFields.filter(field => !user[field]);
        
        if (missingCriticalFields.length > 0) {
          console.warn(`User is missing critical fields: ${missingCriticalFields.join(", ")}`);
          showLoginNotification("Account missing required information. Please contact admin.", "warning");
        }

        // Set user data in storage including ALL fields
        setLoggedInState(user);
        loginDialog.style.display = "none";
        showUserInfoDialog();
      } else {
        showLoginNotification("Invalid credentials. Please try again.", "error");
      }
    } catch (error) {
      console.error("Login error:", error);
      showLoginNotification(`Login failed: ${error.message}`, "error");
    }
  });

  // Fetch user data from SheetDB API
  async function fetchUserData() {
    console.log("Fetching all user data from SheetDB API");
    return fetch(SHEETDB_API_URL, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": SHEETDB_API_TOKEN,
      },
    });
  }

  // User info dialog with avatar selection
  function showUserInfoDialog() {
    const userInfoDialog = document.createElement("div");
    userInfoDialog.className = "dialog";
    userInfoDialog.style.display = "flex";
    userInfoDialog.innerHTML = `
      <div class="dialog-content">
        <div class="avatar-preview">
          <img src="images/avatars/avatar_${Math.floor(Math.random() * 100) + 1}.jpg" alt="Random Avatar" id="previewAvatar">
        </div>
        <h2>${currentUser["User Name"]} (${currentUser.Role})</h2>
        <h3>Choose your avatar</h3>
        <div id="avatarGrid" class="profile-grid"></div>
        <button id="showMoreAvatars" class="login-button active">Show More Avatars</button>
        <button id="selectAvatarButton" class="login-button" disabled>Select Avatar</button>
      </div>
    `;
    document.body.appendChild(userInfoDialog);

    const avatarGrid = document.getElementById("avatarGrid");
    const showMoreAvatarsButton = document.getElementById("showMoreAvatars");
    const selectAvatarButton = document.getElementById("selectAvatarButton");
    const previewAvatar = document.getElementById("previewAvatar");
    let selectedAvatar = null;

    function createAvatarElement(i) {
      const avatarElement = document.createElement("div");
      avatarElement.className = "profile-option";
      avatarElement.innerHTML = `
        <img src="images/avatars/avatar_${i}.jpg" alt="Avatar ${i}">
      `;
      avatarElement.addEventListener("click", () => {
        if (selectedAvatar) {
          selectedAvatar.classList.remove("selected");
        }
        avatarElement.classList.add("selected");
        selectedAvatar = avatarElement;
        selectAvatarButton.disabled = false;
        selectAvatarButton.classList.add("active");
        previewAvatar.src = `images/avatars/avatar_${i}.jpg`;
      });
      return avatarElement;
    }

    function shuffleArray(array) {
      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }
      return array;
    }

    const avatarIndices = shuffleArray([...Array(100)].map((_, i) => i + 1));
    let displayedAvatars = 8; // Start with 8 avatars (2 rows of 4)

    function displayAvatars(start, end) {
      for (let i = start; i < end && i < avatarIndices.length; i++) {
        avatarGrid.appendChild(createAvatarElement(avatarIndices[i]));
      }
      // Force layout recalculation to ensure proper scrolling
      avatarGrid.style.display = "none";
      avatarGrid.offsetHeight; // Force reflow
      avatarGrid.style.display = "grid";
    }

    displayAvatars(0, displayedAvatars);

    showMoreAvatarsButton.addEventListener("click", () => {
      if (displayedAvatars < 100) {
        const previousHeight = avatarGrid.scrollHeight;
        displayAvatars(displayedAvatars, displayedAvatars + 8);
        displayedAvatars += 8;

        // Scroll to show new content
        requestAnimationFrame(() => {
          avatarGrid.scrollTop = previousHeight;
        });

        if (displayedAvatars >= 100) {
          showMoreAvatarsButton.style.display = "none";
        }
      }
    });

    selectAvatarButton.addEventListener("click", async () => {
      if (selectedAvatar) {
        const avatarId = selectedAvatar.querySelector("img").src.split("_")[1].split(".")[0];

        // Show notification during the update
        showLoginNotification("Updating your profile...", "info");

        try {
          await updateUserAvatar(avatarId);

          // Update the user data in storage after avatar selection
          setLoggedInState(currentUser);
          userInfoDialog.remove();

          // Show success notification
          showLoginNotification("Profile updated successfully!", "success");

          // Redirect to index.html after profile update
          redirectToApp();
        } catch (error) {
          console.error("Error during avatar selection:", error);
          showLoginNotification(`Error updating profile: ${error.message}`, "error");
        }
      }
    });
  }

  // Update user avatar
  async function updateUserAvatar(avatarId) {
    console.log(`Updating avatar for user ${currentUser.Email} to ${avatarId}`);
    const response = await fetch(`${SHEETDB_API_URL}/Email/${encodeURIComponent(currentUser.Email)}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "Authorization": SHEETDB_API_TOKEN,
      },
      body: JSON.stringify({ data: { Avatar: avatarId } }),
    });

    if (!response.ok) {
      throw new Error(`Failed to update avatar: ${response.status} ${response.statusText}`);
    }

    currentUser.Avatar = avatarId;
    console.log(`Avatar updated for user: ${currentUser["User Name"]}, New avatar: ${avatarId}`);

    // After updating avatar, fetch the complete user data again to ensure we have the latest
    const refreshResult = await refreshUserData(currentUser.Email);
    
    if (!refreshResult) {
      console.warn("Failed to refresh user data after avatar update");
    }
    
    return true;
  }

  // Refresh user data to ensure we have the latest from the sheet
  async function refreshUserData(email) {
    console.log(`Refreshing user data for ${email}`);
    try {
      const response = await fetch(`${SHEETDB_API_URL}/Email/${encodeURIComponent(email)}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": SHEETDB_API_TOKEN,
        },
      });

      if (!response.ok) {
        console.error(`Error refreshing user data: ${response.status} ${response.statusText}`);
        return false;
      }

      const userData = await response.json();
      if (userData && userData.length > 0) {
        // Update the current user with all fields from the sheet
        currentUser = userData[0];
        console.log("User data refreshed successfully");
        
        // Check if any expected fields are missing
        const availableFields = Object.keys(currentUser);
        const missingFields = EXPECTED_FIELDS.filter(field => !availableFields.includes(field));
        
        if (missingFields.length > 0) {
          console.warn("After refresh, still missing fields:", missingFields);
        }
        
        return true;
      } else {
        console.error("Refresh returned no user data");
        return false;
      }
    } catch (error) {
      console.error("Error refreshing user data:", error);
      return false;
    }
  }

  // Store user data in Chrome storage
  function setLoggedInState(user) {
    // Create a metadata object to track when the user data was last updated
    const userMetadata = {
      lastUpdated: new Date().toISOString(),
      availableFields: allUserFields,
    };

    console.log(`Saving ${Object.keys(user).length} fields to Chrome storage`);
    
    chrome.storage.local.set(
      {
        loggedIn: true,
        user: user,
        userMetadata: userMetadata,
      },
      () => {
        if (chrome.runtime.lastError) {
          console.error("Error saving login state:", chrome.runtime.lastError.message);
          showLoginNotification("Error saving user data", "error");
        } else {
          console.log(`Login state saved for user: ${user["User Name"]}, Role: ${user.Role}`);
          
          // Verify key field groups
          verifyFieldGroups(user);
        }
      },
    );
  }

  // Verify that important field groups are present
  function verifyFieldGroups(user) {
    // Check SH field group
    const shFields = Object.keys(user).filter(key => key.startsWith("SH-") || key.startsWith("SH "));
    console.log(`Found ${shFields.length} SH fields`);
    
    // Check PO field group
    const poFields = Object.keys(user).filter(key => key.startsWith("PO-") || key.startsWith("PO "));
    console.log(`Found ${poFields.length} PO fields`);
    
    // Check SO field group
    const soFields = Object.keys(user).filter(key => key.startsWith("SO-") || key.startsWith("SO "));
    console.log(`Found ${soFields.length} SO fields`);
    
    // Check admin message fields
    const hasAdminMessage = user["Adminmessage-Name"] && user["Adminmessage-Messge"];
    console.log(`Admin message fields present: ${hasAdminMessage}`);
    
    // Check Monday.com integration fields
    const hasMondayFields = !!user["Monday API Key"];
    console.log(`Monday.com integration fields present: ${hasMondayFields}`);
    
    // Check specific new fields
    const newPOFields = [
      "PO-Country", "PO-Package Qty", "PO-Package Type", "PO-Dims", 
      "PO-Weight", "PO-Shipment Method", "PO-Tracking Link"
    ];
    
    const missingNewPOFields = newPOFields.filter(field => !user[field]);
    if (missingNewPOFields.length > 0) {
      console.warn(`Missing some new PO fields: ${missingNewPOFields.join(", ")}`);
    } else {
      console.log(`All new PO fields are present`);
    }
  }

  // Helper function to redirect to the main app
  function redirectToApp() {
    chrome.action.setPopup({ popup: "index.html" }, () => {
      // Double-check that all user data was saved before redirecting
      chrome.storage.local.get(["user"], (result) => {
        if (result.user) {
          console.log("Verified user data is saved, redirecting to app");
          window.location.href = "index.html";
        } else {
          console.error("User data not found in storage before redirect");
          showLoginNotification("Error: User data not saved. Please try again.", "error");
        }
      });
    });
  }

  // Close dialogs when clicking outside
  window.addEventListener("click", (event) => {
    if (event.target.classList.contains("dialog")) {
      event.target.style.display = "none";
    }
  });

  // Handle Outlook and Teams login
  outlookLogin.addEventListener("click", () => {
    showLoginNotification("Outlook login feature coming soon!", "info");
  });

  teamsLogin.addEventListener("click", () => {
    showLoginNotification("Teams login feature coming soon!", "info");
  });

  // Enhanced notification function with types
  function showLoginNotification(message, type = "default") {
    console.log(`Notification (${type}): ${message}`);
    
    loginNotification.textContent = message;

    // Reset all type-specific classes
    loginNotification.classList.remove("info", "success", "error", "warning");

    // Add the appropriate class for styling
    if (type !== "default") {
      loginNotification.classList.add(type);
    }

    loginNotification.style.opacity = "1";
    setTimeout(() => {
      loginNotification.style.opacity = "0";
    }, 3000);
  }
});