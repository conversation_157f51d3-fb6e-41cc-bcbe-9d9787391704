/**
 * Opportunity Calendar Component
 * Shows opportunities in a Bootstrap-based calendar view
 */

export class OpportunityCalendar {
  constructor(container, parent) {
    this.container = container;
    this.parent = parent;
    this.opportunities = [];
    this.filterStatus = 'all';
    this.currentMonth = new Date();
    this.renderHeader = true;
    this.dateRange = {
      start: null,
      end: null
    };
    this.activeCategories = {
      'new': true,
      'in progress': true,
      'pending': true,
      'won': true,
      'lost': true
    };
    this.todayOpportunities = [];
    this.loadBootstrap();
  }

  /**
   * Load Bootstrap if it's not already available but scope it to only this component
   */
  loadBootstrap() {
    if (typeof bootstrap !== 'undefined') return;

    // Create a unique ID for the scoped style element
    const scopeId = 'bootstrap-calendar-scope-' + Math.random().toString(36).substring(2, 9);
    
    // Add Bootstrap CSS if not already present, but scope it
    if (!document.querySelector('#opportunity-calendar-bootstrap-css')) {
      // Create a style element to hold the scoped styles
      const styleEl = document.createElement('style');
      styleEl.id = 'opportunity-calendar-bootstrap-css';
      
      // Fetch the Bootstrap CSS and scope it to our component
      fetch('https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css')
        .then(response => response.text())
        .then(css => {
          // Scope all CSS rules to the calendar container using a regex replacement
          // This regex finds CSS selectors and adds our container class in front
          const scopedCss = css
            // Replace all CSS selectors to scope them
            .replace(/([^\r\n,{}]+)(,(?=[^}]*{)|\s*{)/g, (matchString, selector, delimiter) => {
              // Skip @media, @keyframes and other non-selectors
              if (selector.trim().startsWith('@')) {
                return matchString;
              }
              // Scope the selector to our calendar container
              return `.opportunity-calendar-container ${selector}${delimiter}`;
            });
          
          // Set the scoped CSS to the style element
          styleEl.textContent = scopedCss;
          document.head.appendChild(styleEl);
        })
        .catch(error => console.error('Error loading Bootstrap CSS:', error));
    }

    // Add Bootstrap JS if not already present - this doesn't affect styling
    if (!document.querySelector('script[src*="bootstrap"]')) {
      const bootstrapJs = document.createElement('script');
      bootstrapJs.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';
      bootstrapJs.integrity = 'sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL';
      bootstrapJs.crossOrigin = 'anonymous';
      document.body.appendChild(bootstrapJs);
    }

    // Add Font Awesome for modern icons if not already present
    if (!document.querySelector('link[href*="fontawesome"]')) {
      const fontAwesome = document.createElement('link');
      fontAwesome.rel = 'stylesheet';
      fontAwesome.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css';
      fontAwesome.integrity = 'sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==';
      fontAwesome.crossOrigin = 'anonymous';
      document.head.appendChild(fontAwesome);
    }

    // Add custom calendar styles - already properly scoped
    const calendarStyles = document.createElement('style');
    calendarStyles.textContent = `
      .calendar-container {
        width: 100%;
        padding: 0;
        margin-bottom: 1.5rem;
        display: flex;
        gap: 20px;
      }
      .calendar-main {
        flex: 1;
      }
      .calendar-sidebar {
        width: 270px;
        flex-shrink: 0;
        margin-right: 20px;
      }
      .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #e2e8f0;
      }
      .calendar-control-btn {
        background-color: transparent;
        border: 1px solid #e2e8f0;
        color: #4b5563;
        border-radius: 4px;
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
        font-weight: 500;
        transition: background-color 0.2s, color 0.2s;
      }
      .calendar-control-btn:hover {
        background-color: #f8fafc;
        color: #1f2937;
      }
      .calendar-month-display {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
      }
      .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        margin-bottom: 1rem;
      }
      .calendar-day-name {
        font-weight: 600;
        text-align: center;
        padding: 8px 0;
        background-color: transparent;
        color: #64748b;
        font-size: 0.8rem;
        margin-bottom: 4px;
        border-bottom: 1px solid #f1f5f9;
      }
      .calendar-day {
        min-height: 120px;
        padding: 6px;
        border-radius: 4px;
        background: white;
        border: 1px solid #e5e7eb;
        overflow-y: auto;
      }
      .calendar-day.today {
        background-color: #eff6ff;
        border: 1px solid #6366f1;
      }
      .calendar-day.other-month {
        background-color: #f8fafc;
        color: #94a3b8;
      }
      .day-number {
        font-weight: 600;
        font-size: 0.85rem;
        margin-bottom: 6px;
        padding-bottom: 4px;
        border-bottom: 1px solid #f1f5f9;
        color: #1e293b;
      }
      .opportunity-item {
        padding: 5px 8px;
        margin-bottom: 6px;
        border-radius: 4px;
        font-size: 0.75rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: transform 0.15s;
      }
      .opportunity-item:hover {
        transform: translateY(-1px);
      }
      .opportunity-icon {
        margin-right: 4px;
        font-size: 0.7rem;
      }
      .status-new { 
        background-color: rgba(147, 197, 253, 0.65); 
        color: #1e3a8a; 
      }
      .status-in-progress { 
        background-color: rgba(252, 211, 77, 0.65); 
        color: #92400e; 
      }
      .status-pending { 
        background-color: rgba(203, 213, 225, 0.65); 
        color: #334155; 
      }
      .status-won { 
        background-color: rgba(134, 239, 172, 0.65); 
        color: #14532d; 
      }
      .status-lost { 
        background-color: rgba(252, 165, 165, 0.65); 
        color: #7f1d1d; 
      }
      
      /* Badge styles for opportunity count */
      .day-badge {
        display: inline-block;
        width: 20px;
        height: 20px;
        background-color: rgba(99, 102, 241, 0.8);
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        font-size: 0.7rem;
        margin-left: 5px;
      }
      
      /* View all link */
      .view-all-link {
        display: block;
        text-align: center;
        font-size: 0.75rem;
        color: #6366f1;
        font-weight: 500;
        padding: 2px 0;
        margin-top: 2px;
        cursor: pointer;
        border-top: 1px dashed #e5e7eb;
      }
      .view-all-link:hover {
        color: #4f46e5;
        text-decoration: underline;
      }
      
      /* Mini calendar styles */
      .mini-calendar {
        margin-bottom: 15px;
      }
      
      .mini-calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }
      
      .mini-calendar-title {
        font-size: 1rem;
        font-weight: 600;
        color: #1f2937;
      }
      
      .mini-calendar-nav {
        display: flex;
        gap: 8px;
      }
      
      .mini-calendar-btn {
        background: none;
        border: none;
        color: #4b5563;
        cursor: pointer;
        font-size: 0.9rem;
      }
      
      .mini-calendar-btn:hover {
        color: #1f2937;
      }
      
      .mini-calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
      }
      
      .mini-calendar-day-name {
        font-size: 0.7rem;
        text-align: center;
        color: #64748b;
        font-weight: 500;
        padding: 2px 0;
      }
      
      .mini-calendar-day {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 28px;
        width: 28px;
        border-radius: 50%;
        font-size: 0.75rem;
        color: #334155;
        cursor: pointer;
      }
      
      .mini-calendar-day:hover {
        background-color: rgba(241, 245, 249, 0.8);
      }
      
      .mini-calendar-day.other-month {
        color: #94a3b8;
      }
      
      .mini-calendar-day.today {
        background-color: rgba(99, 102, 241, 0.8);
        color: white;
      }
      
      .mini-calendar-day.selected {
        background-color: rgba(221, 214, 254, 0.8);
        color: #5b21b6;
        font-weight: 600;
      }
      
      .mini-calendar-day.has-events::after {
        content: '';
        position: absolute;
        bottom: 2px;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background-color: #6366f1;
      }
      
      /* Sidebar styles */
      .calendar-sidebar {
        display: flex;
        flex-direction: column;
        gap: 24px;
      }
      
      .sidebar-panel {
        background-color: white;
        border-radius: 10px;
        padding: 16px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        border: 1px solid #e2e8f0;
        margin-bottom: 16px;
      }
      
      .sidebar-panel-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 16px;
        color: #1f2937;
        padding-bottom: 8px;
        border-bottom: 1px solid #e2e8f0;
      }
      
      .category-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        cursor: pointer;
      }
      
      /* Custom checkbox styling */
      .category-checkbox-wrapper {
        position: relative;
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
      
      .category-checkbox {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
      }
      
      .category-checkbox-custom {
        position: absolute;
        top: 0;
        left: 0;
        height: 18px;
        width: 18px;
        border-radius: 4px;
        border: 1px solid #cbd5e1;
        background-color: white;
      }
      
      /* Styles for the color checkbox backgrounds */
      .category-checkbox-wrapper.new .category-checkbox-custom {
        background-color: rgba(147, 197, 253, 0.25);
        border-color: rgba(147, 197, 253, 0.8);
      }
      
      .category-checkbox-wrapper.in-progress .category-checkbox-custom {
        background-color: rgba(252, 211, 77, 0.25);
        border-color: rgba(252, 211, 77, 0.8);
      }
      
      .category-checkbox-wrapper.pending .category-checkbox-custom {
        background-color: rgba(203, 213, 225, 0.25);
        border-color: rgba(203, 213, 225, 0.8);
      }
      
      .category-checkbox-wrapper.won .category-checkbox-custom {
        background-color: rgba(134, 239, 172, 0.25);
        border-color: rgba(134, 239, 172, 0.8);
      }
      
      .category-checkbox-wrapper.lost .category-checkbox-custom {
        background-color: rgba(252, 165, 165, 0.25);
        border-color: rgba(252, 165, 165, 0.8);
      }
      
      /* Checkmark styles */
      .category-checkbox-custom:after {
        content: "";
        position: absolute;
        display: none;
        left: 6px;
        top: 2px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
      
      /* Active checkbox states */
      .category-checkbox:checked ~ .category-checkbox-custom.new {
        background-color: rgba(147, 197, 253, 0.9);
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom.in-progress {
        background-color: rgba(252, 211, 77, 0.9);
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom.pending {
        background-color: rgba(203, 213, 225, 0.9);
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom.won {
        background-color: rgba(134, 239, 172, 0.9);
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom.lost {
        background-color: rgba(252, 165, 165, 0.9);
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom:after {
        display: block;
      }
      
      .category-checkbox:checked ~ .category-checkbox-custom.new:after,
      .category-checkbox:checked ~ .category-checkbox-custom.in-progress:after,
      .category-checkbox:checked ~ .category-checkbox-custom.won:after {
        border-color: #1e293b;
      }
      
      .category-label {
        flex: 1;
        font-size: 0.875rem;
        color: #334155;
      }
      
      .category-count {
        font-size: 0.75rem;
        color: #64748b;
        text-align: right;
      }
      
      /* Today's opportunities */
      .today-list {
        max-height: 300px;
        overflow-y: auto;
      }
      
      .today-item {
        padding: 8px 10px;
        border-radius: 6px;
        margin-bottom: 8px;
        font-size: 0.8rem;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        gap: 3px;
        transition: transform 0.15s;
      }
      
      .today-item:hover {
        transform: translateY(-1px);
      }
      
      .today-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .today-item-id {
        font-weight: 600;
      }
      
      .today-item-amount {
        font-weight: 500;
      }
      
      .today-item-subject {
        font-size: 0.75rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      
      /* Opportunity modal */
      .opportunity-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1050;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s, visibility 0.2s;
      }
      .opportunity-modal.active {
        opacity: 1;
        visibility: visible;
      }
      .opportunity-modal-content {
        background-color: white;
        border-radius: 6px;
        width: 90%;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        padding: 1rem;
        border: 1px solid #e2e8f0;
      }
      .opportunity-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #e2e8f0;
        padding-bottom: 0.75rem;
        margin-bottom: 1rem;
      }
      .opportunity-modal-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0;
      }
      .opportunity-modal-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #64748b;
        cursor: pointer;
      }
      .opportunity-modal-close:hover {
        color: #1e293b;
      }
      
      /* Media query for responsive layout */
      @media (max-width: 992px) {
        .calendar-container {
          flex-direction: column;
        }
        
        .calendar-sidebar {
          width: 100%;
          margin-top: 20px;
          margin-right: 0;
        }
      }
      
      /* Dark mode styles */
      .dark-mode .calendar-day-name {
        background-color: transparent;
        color: #94a3b8;
        border-bottom-color: #334155;
      }
      .dark-mode .calendar-control-btn {
        background-color: transparent;
        border-color: #334155;
        color: #a5b4fc;
      }
      .dark-mode .calendar-control-btn:hover {
        background-color: #1e293b;
        color: #c7d2fe;
      }
      .dark-mode .calendar-day {
        border-color: #334155;
        background-color: #1e293b;
        color: #f1f5f9;
      }
      .dark-mode .calendar-day.today {
        background-color: rgba(67, 56, 202, 0.7);
        border-color: #6366f1;
      }
      .dark-mode .calendar-day.other-month {
        background-color: #0f172a;
        color: #64748b;
      }
      .dark-mode .day-number {
        border-bottom-color: #334155;
        color: #f1f5f9;
      }
      .dark-mode .status-new { 
        background-color: rgba(59, 130, 246, 0.5); 
        color: white;
      }
      .dark-mode .status-in-progress { 
        background-color: rgba(234, 179, 8, 0.5); 
        color: #f1f5f9;
      }
      .dark-mode .status-pending { 
        background-color: rgba(71, 85, 105, 0.5); 
        color: white;
      }
      .dark-mode .status-won { 
        background-color: rgba(16, 185, 129, 0.5); 
        color: white;
      }
      .dark-mode .status-lost { 
        background-color: rgba(239, 68, 68, 0.5); 
        color: white;
      }
      
      .dark-mode .calendar-month-display {
        color: #a5b4fc;
      }
      
      .dark-mode .calendar-header {
        border-bottom-color: #334155;
      }
      
      .dark-mode .view-all-link {
        color: #a5b4fc;
        border-top-color: #334155;
      }
      .dark-mode .view-all-link:hover {
        color: #c7d2fe;
      }
      
      .dark-mode .sidebar-panel {
        background-color: #1e293b;
        border-color: #334155;
      }
      
      .dark-mode .sidebar-panel-title {
        color: #f1f5f9;
        border-bottom-color: #334155;
      }
      
      .dark-mode .category-label {
        color: #e2e8f0;
      }
      
      .dark-mode .category-count,
      .dark-mode .mini-calendar-day-name {
        color: #94a3b8;
      }
      
      .dark-mode .category-checkbox-custom {
        border-color: #475569;
        background-color: #1e293b;
      }
      
      .dark-mode .category-checkbox-wrapper.new .category-checkbox-custom {
        background-color: rgba(59, 130, 246, 0.2);
        border-color: rgba(59, 130, 246, 0.6);
      }
      
      .dark-mode .category-checkbox-wrapper.in-progress .category-checkbox-custom {
        background-color: rgba(234, 179, 8, 0.2);
        border-color: rgba(234, 179, 8, 0.6);
      }
      
      .dark-mode .category-checkbox-wrapper.pending .category-checkbox-custom {
        background-color: rgba(71, 85, 105, 0.2);
        border-color: rgba(71, 85, 105, 0.6);
      }
      
      .dark-mode .category-checkbox-wrapper.won .category-checkbox-custom {
        background-color: rgba(16, 185, 129, 0.2);
        border-color: rgba(16, 185, 129, 0.6);
      }
      
      .dark-mode .category-checkbox-wrapper.lost .category-checkbox-custom {
        background-color: rgba(239, 68, 68, 0.2);
        border-color: rgba(239, 68, 68, 0.6);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom.new {
        background-color: rgba(59, 130, 246, 0.8);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom.in-progress {
        background-color: rgba(234, 179, 8, 0.8);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom.pending {
        background-color: rgba(71, 85, 105, 0.8);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom.won {
        background-color: rgba(16, 185, 129, 0.8);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom.lost {
        background-color: rgba(239, 68, 68, 0.8);
      }
      
      .dark-mode .category-checkbox:checked ~ .category-checkbox-custom:after {
        border-color: white;
      }
      
      .dark-mode .mini-calendar-day {
        color: #e2e8f0;
      }
      
      .dark-mode .mini-calendar-day.other-month {
        color: #64748b;
      }
      
      .dark-mode .mini-calendar-day.today {
        background-color: rgba(99, 102, 241, 0.8);
      }
      
      .dark-mode .mini-calendar-day.selected {
        background-color: rgba(91, 33, 182, 0.6);
        color: white;
      }
      
      .dark-mode .mini-calendar-btn {
        color: #a5b4fc;
      }
      
      .dark-mode .mini-calendar-title {
        color: #f1f5f9;
      }
      
      .dark-mode .opportunity-modal-content {
        background-color: #1e293b;
        color: #f1f5f9;
        border-color: #334155;
      }
      .dark-mode .opportunity-modal-header {
        border-bottom-color: #334155;
      }
      .dark-mode .opportunity-modal-title {
        color: #f1f5f9;
      }
      .dark-mode .opportunity-modal-close {
        color: #94a3b8;
      }
      .dark-mode .opportunity-modal-close:hover {
        color: #f1f5f9;
      }
      
      @media (max-width: 768px) {
        .calendar-header {
          flex-direction: column;
          gap: 10px;
        }
        .calendar-day {
          min-height: 100px;
          padding: 4px;
        }
        .day-number {
          font-size: 0.8rem;
        }
        .opportunity-item {
          padding: 4px;
          font-size: 0.7rem;
        }
      }
    `;
    document.head.appendChild(calendarStyles);
  }

  /**
   * Initialize the calendar component
   */
  async init() {
    try {
      // Calculate status counts before rendering
      this.calculateStatusCounts();
      
      // Get today's opportunities
      this.getTodayOpportunities();
      
      this.render();
      this.setupEventListeners();
      this.appendModalToBody();
    } catch (error) {
      console.error("Error initializing Calendar view:", error);
      if (this.container) {
        this.container.innerHTML = `<div class="alert alert-danger">Error loading calendar: ${error.message}</div>`;
      }
    }
  }
  
  /**
   * Calculate the count of opportunities for each status
   */
  calculateStatusCounts() {
    this.statusCounts = {
      'new': 0,
      'in progress': 0,
      'pending': 0,
      'won': 0,
      'lost': 0
    };
    
    this.opportunities.forEach(opp => {
      const status = (opp.Status || '').toLowerCase();
      if (this.statusCounts.hasOwnProperty(status)) {
        this.statusCounts[status]++;
      }
    });
  }

  /**
   * Append the opportunity modal to the body
   */
  appendModalToBody() {
    // Check if modal already exists
    if (document.getElementById('opportunity-day-modal')) return;
    
    const modal = document.createElement('div');
    modal.id = 'opportunity-day-modal';
    modal.className = 'opportunity-modal';
    modal.innerHTML = `
      <div class="opportunity-modal-content">
        <div class="opportunity-modal-header">
          <h3 class="opportunity-modal-title">Opportunities</h3>
          <button type="button" class="opportunity-modal-close" id="opportunity-modal-close">&times;</button>
        </div>
        <div class="opportunity-modal-body" id="opportunity-modal-body">
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Add event listener to close button
    const closeBtn = document.getElementById('opportunity-modal-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        modal.classList.remove('active');
      });
    }
    
    // Close when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.classList.remove('active');
      }
    });
  }

  /**
   * Show the opportunity modal with all opportunities for a specific date
   */
  showOpportunitiesModal(date, opportunities) {
    const modal = document.getElementById('opportunity-day-modal');
    const modalBody = document.getElementById('opportunity-modal-body');
    const modalTitle = modal.querySelector('.opportunity-modal-title');
    
    if (!modal || !modalBody || !modalTitle) return;
    
    // Format date for display
    const formattedDate = date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
    
    // Update modal title
    modalTitle.textContent = `Opportunities for ${formattedDate}`;
    
    // Populate modal body with opportunities
    modalBody.innerHTML = opportunities.map(opp => {
      const statusClass = this.getStatusClass(opp.Status);
      const statusIcon = this.getStatusIcon(opp.Status);
      
      return `
        <div class="opportunity-item ${statusClass} mb-2" data-id="${opp.id || opp.OpportunityID}" 
            title="${this.escapeHtml(opp.Subject || '')}">
          <div>
            <i class="${statusIcon} opportunity-icon"></i>
            <strong>${this.escapeHtml(opp.OpportunityID || '')}</strong>
            ${opp.Subject ? `- ${this.escapeHtml(opp.Subject)}` : ''}
          </div>
          <div>${this.formatAmount(opp.Amount, opp.CurrencyID)}</div>
        </div>
      `;
    }).join('');
    
    // Add event listeners to opportunity items
    setTimeout(() => {
      const opportunityItems = modalBody.querySelectorAll('.opportunity-item');
      opportunityItems.forEach(item => {
        item.addEventListener('click', () => {
          const oppId = item.getAttribute('data-id');
          if (oppId && this.parent && typeof this.parent.viewOpportunity === 'function') {
            modal.classList.remove('active');
            this.parent.viewOpportunity(oppId);
          }
        });
      });
    }, 0);
    
    // Show modal
    modal.classList.add('active');
  }

  /**
   * Render the calendar view
   */
  render() {
    if (!this.container) return;

    let content = '';
    
    // Add the header if needed
    if (this.renderHeader) {
      content += `
        <div class="mb-3">
          <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Opportunity Calendar</h2>
        </div>
      `;
    }

    // Calendar container with main content and sidebar - add opportunity-calendar-container class for CSS scoping
    content += `
      <div class="opportunity-calendar-container">
        <div class="calendar-container">
          <div class="calendar-sidebar">
            ${this.renderMiniCalendar()}
            ${this.renderCategoriesPanel()}
            ${this.renderTodayPanel()}
          </div>
          
          <div class="calendar-main">
            <div class="calendar-header">
              <div class="btn-group">
                <button id="prev-month-btn" class="calendar-control-btn">
                  <i class="fas fa-chevron-left me-1"></i> Prev
                </button>
                <button id="current-month-btn" class="calendar-control-btn mx-2">
                  <i class="fas fa-calendar-day me-1"></i> Today
                </button>
                <button id="next-month-btn" class="calendar-control-btn">
                  Next <i class="fas fa-chevron-right ms-1"></i>
                </button>
              </div>
              
              <h3 id="month-display" class="calendar-month-display">${this.formatMonthYear(this.currentMonth)}</h3>
            </div>

            <div class="calendar-grid">
              <div class="calendar-day-name">Sun</div>
              <div class="calendar-day-name">Mon</div>
              <div class="calendar-day-name">Tue</div>
              <div class="calendar-day-name">Wed</div>
              <div class="calendar-day-name">Thu</div>
              <div class="calendar-day-name">Fri</div>
              <div class="calendar-day-name">Sat</div>
              
              ${this.renderMonthDays()}
            </div>
          </div>
        </div>
      </div>
    `;

    this.container.innerHTML = content;
  }
  
  /**
   * Render the categories panel for the sidebar
   */
  renderCategoriesPanel() {
    return `
      <div class="sidebar-panel">
        <h3 class="sidebar-panel-title">Categories</h3>
        
        <div class="category-list">
          <div class="category-item">
            <label class="category-checkbox-wrapper new">
              <input type="checkbox" class="category-checkbox" id="category-new" 
                ${this.activeCategories['new'] ? 'checked' : ''} data-status="new">
              <span class="category-checkbox-custom new"></span>
            </label>
            <label class="category-label" for="category-new">New</label>
            <span class="category-count">${this.formatTimeString(this.statusCounts['new'])}</span>
          </div>
          
          <div class="category-item">
            <label class="category-checkbox-wrapper in-progress">
              <input type="checkbox" class="category-checkbox" id="category-in-progress" 
                ${this.activeCategories['in progress'] ? 'checked' : ''} data-status="in progress">
              <span class="category-checkbox-custom in-progress"></span>
            </label>
            <label class="category-label" for="category-in-progress">In Progress</label>
            <span class="category-count">${this.formatTimeString(this.statusCounts['in progress'])}</span>
          </div>
          
          <div class="category-item">
            <label class="category-checkbox-wrapper pending">
              <input type="checkbox" class="category-checkbox" id="category-pending" 
                ${this.activeCategories['pending'] ? 'checked' : ''} data-status="pending">
              <span class="category-checkbox-custom pending"></span>
            </label>
            <label class="category-label" for="category-pending">Pending</label>
            <span class="category-count">${this.formatTimeString(this.statusCounts['pending'])}</span>
          </div>
          
          <div class="category-item">
            <label class="category-checkbox-wrapper won">
              <input type="checkbox" class="category-checkbox" id="category-won" 
                ${this.activeCategories['won'] ? 'checked' : ''} data-status="won">
              <span class="category-checkbox-custom won"></span>
            </label>
            <label class="category-label" for="category-won">Won</label>
            <span class="category-count">${this.formatTimeString(this.statusCounts['won'])}</span>
          </div>
          
          <div class="category-item">
            <label class="category-checkbox-wrapper lost">
              <input type="checkbox" class="category-checkbox" id="category-lost" 
                ${this.activeCategories['lost'] ? 'checked' : ''} data-status="lost">
              <span class="category-checkbox-custom lost"></span>
            </label>
            <label class="category-label" for="category-lost">Lost</label>
            <span class="category-count">${this.formatTimeString(this.statusCounts['lost'])}</span>
          </div>
        </div>
      </div>
    `;
  }
  
  /**
   * Render the Today's Opportunities panel
   */
  renderTodayPanel() {
    const todayOpps = this.todayOpportunities;
    
    let todayContent = '';
    
    if (todayOpps.length === 0) {
      todayContent = `<p class="text-center text-gray-500 my-4">No opportunities for today</p>`;
    } else {
      todayContent = todayOpps.map(opp => {
        const statusClass = this.getStatusClass(opp.Status);
        return `
          <div class="today-item ${statusClass}" data-id="${opp.id || opp.OpportunityID}">
            <div class="today-item-header">
              <div class="today-item-id">${this.escapeHtml(opp.OpportunityID || '')}</div>
              <div class="today-item-amount">${this.formatAmount(opp.Amount, opp.CurrencyID)}</div>
            </div>
            <div class="today-item-subject">${this.escapeHtml(opp.Subject || 'No subject')}</div>
          </div>
        `;
      }).join('');
    }
    
    return `
      <div class="sidebar-panel">
        <h3 class="sidebar-panel-title">Today's Opportunities</h3>
        <div class="today-list">
          ${todayContent}
        </div>
      </div>
    `;
  }
  
  /**
   * Format time string from count (mocking the time format seen in the example)
   */
  formatTimeString(count) {
    // For demonstration, convert counts to time strings
    const hours = Math.floor(count / 10) || 1;
    return `${hours}h${count % 3 === 0 ? '00' : count % 2 === 0 ? '30' : '15'}`;
  }

  /**
   * Format the month and year for display
   */
  formatMonthYear(date) {
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  }

  /**
   * Render the days of the month in the calendar
   */
  renderMonthDays() {
    // Get the first day of the month
    const firstDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 1);
    
    // Get the last day of the month
    const lastDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 0);
    
    // Get the day of the week of the first day (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfWeek = firstDay.getDay();
    
    // Calculate the total number of days to show (including days from previous/next month)
    const totalDays = 42; // Always show 6 weeks
    
    let daysHtml = '';
    
    // Get today for highlighting current day
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Determine how many days from the previous month to show
    const daysFromPrevMonth = firstDayOfWeek;
    
    // Get the last day of the previous month
    const prevMonthLastDay = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), 0).getDate();
    
    // Add days from the previous month
    for (let i = daysFromPrevMonth - 1; i >= 0; i--) {
      const day = prevMonthLastDay - i;
      const date = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, day);
      daysHtml += this.renderCalendarDay(date, 'other-month');
    }
    
    // Add days from the current month
    for (let day = 1; day <= lastDay.getDate(); day++) {
      const date = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth(), day);
      const isToday = date.getTime() === today.getTime();
      daysHtml += this.renderCalendarDay(date, isToday ? 'today' : '');
    }
    
    // Calculate how many days from the next month to show
    const daysFromNextMonth = totalDays - daysFromPrevMonth - lastDay.getDate();
    
    // Add days from the next month
    for (let day = 1; day <= daysFromNextMonth; day++) {
      const date = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, day);
      daysHtml += this.renderCalendarDay(date, 'other-month');
    }
    
    return daysHtml;
  }

  /**
   * Render a single day cell in the calendar
   */
  renderCalendarDay(date, extraClass) {
    // Format date as YYYY-MM-DD for comparison
    const dateStr = this.formatDateForComparison(date);
    
    // Filter opportunities for this date
    const dayOpportunities = this.opportunities.filter(opp => {
      // Check if creation date matches
      const oppDate = opp.LastModified;
      if (!oppDate) return false;
      
      // Convert to date string for comparison
      const oppDateStr = this.formatDateForComparison(oppDate);
      
      // Check if status matches the active categories
      const statusMatch = this.activeCategories[opp.Status.toLowerCase()] === true;
      
      return oppDateStr === dateStr && statusMatch;
    });

    // Badge for opportunity count
    const opportunityCountBadge = dayOpportunities.length > 0 ? 
      `<span class="day-badge">${dayOpportunities.length}</span>` : '';
    
    // Determine if we need to show a "View All" link (if more than 3 opportunities)
    const hasViewAllLink = dayOpportunities.length > 3;
    
    // Get the first 3 opportunities to display
    const displayOpportunities = dayOpportunities.slice(0, 3);
    
    // Generate opportunity HTML
    const opportunitiesHtml = displayOpportunities.map(opp => this.renderOpportunityItem(opp)).join('');
    
    // Generate "View All" link HTML if needed
    const viewAllHtml = hasViewAllLink ? 
      `<div class="view-all-link" data-date="${dateStr}">View all ${dayOpportunities.length}</div>` : '';

    return `
      <div class="calendar-day ${extraClass || ''}" data-date="${dateStr}">
        <div class="day-number d-flex justify-content-between align-items-center">
          <span>${date.getDate()}</span>
          ${opportunityCountBadge}
        </div>
        <div class="opportunity-list">
          ${opportunitiesHtml}
          ${viewAllHtml}
        </div>
      </div>
    `;
  }

  /**
   * Render a single opportunity item for the calendar
   */
  renderOpportunityItem(opp) {
    const statusClass = this.getStatusClass(opp.Status);
    const statusIcon = this.getStatusIcon(opp.Status);
    
    return `
      <div class="opportunity-item ${statusClass}" data-id="${opp.id || opp.OpportunityID}" title="${this.escapeHtml(opp.Subject || '')}">
        <div>
          <i class="${statusIcon} opportunity-icon"></i>
          <strong>${this.escapeHtml(opp.OpportunityID || '')}</strong>
        </div>
        <div>${this.formatAmount(opp.Amount, opp.CurrencyID)}</div>
      </div>
    `;
  }

  /**
   * Get icon class based on opportunity status
   */
  getStatusIcon(status) {
    if (!status) return 'fas fa-circle';
    
    const statusLower = status.toLowerCase();
    
    if (statusLower === 'new') return 'fas fa-star';
    if (statusLower === 'in progress') return 'fas fa-spinner';
    if (statusLower === 'pending') return 'fas fa-clock';
    if (statusLower === 'won') return 'fas fa-trophy';
    if (statusLower === 'lost') return 'fas fa-times-circle';
    
    return 'fas fa-circle';
  }

  /**
   * Setup event listeners for the calendar
   */
  setupEventListeners() {
    // Mini calendar prev/next month buttons
    const miniPrevMonthBtn = document.getElementById('mini-prev-month');
    if (miniPrevMonthBtn) {
      miniPrevMonthBtn.addEventListener('click', () => {
        this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);
        this.render();
        this.setupEventListeners();
      });
    }

    const miniNextMonthBtn = document.getElementById('mini-next-month');
    if (miniNextMonthBtn) {
      miniNextMonthBtn.addEventListener('click', () => {
        this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);
        this.render();
        this.setupEventListeners();
      });
    }
    
    // Mini calendar day selection
    const miniCalendarDays = document.querySelectorAll('.mini-calendar-day:not(.other-month)');
    miniCalendarDays.forEach(day => {
      day.addEventListener('click', () => {
        const dateStr = day.getAttribute('data-date');
        if (!dateStr) return;
        
        // Parse the date
        const [year, month, dayNum] = dateStr.split('-').map(Number);
        this.currentMonth = new Date(year, month - 1, dayNum);
        
        this.render();
        this.setupEventListeners();
      });
    });
    
    // Today items click events
    const todayItems = document.querySelectorAll('.today-item');
    todayItems.forEach(item => {
      item.addEventListener('click', () => {
        const oppId = item.getAttribute('data-id');
        if (oppId && this.parent && typeof this.parent.viewOpportunity === 'function') {
          this.parent.viewOpportunity(oppId);
        }
      });
    });
    
    // Previous month button
    const prevMonthBtn = document.getElementById('prev-month-btn');
    if (prevMonthBtn) {
      prevMonthBtn.addEventListener('click', () => {
        this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() - 1, 1);
        this.render();
        this.setupEventListeners();
      });
    }

    // Next month button
    const nextMonthBtn = document.getElementById('next-month-btn');
    if (nextMonthBtn) {
      nextMonthBtn.addEventListener('click', () => {
        this.currentMonth = new Date(this.currentMonth.getFullYear(), this.currentMonth.getMonth() + 1, 1);
        this.render();
        this.setupEventListeners();
      });
    }

    // Current month (today) button
    const currentMonthBtn = document.getElementById('current-month-btn');
    if (currentMonthBtn) {
      currentMonthBtn.addEventListener('click', () => {
        this.currentMonth = new Date();
        this.render();
        this.setupEventListeners();
      });
    }

    // View All link click events
    const viewAllLinks = document.querySelectorAll('.view-all-link');
    viewAllLinks.forEach(link => {
      link.addEventListener('click', () => {
        const dateStr = link.getAttribute('data-date');
        if (!dateStr) return;
        
        // Parse the date
        const [year, month, day] = dateStr.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        
        // Get opportunities for this date
        const dayOpportunities = this.opportunities.filter(opp => {
          const oppDate = opp.LastModified;
          if (!oppDate) return false;
          
          const oppDateStr = this.formatDateForComparison(oppDate);
          const statusMatch = this.activeCategories[opp.Status.toLowerCase()] === true;
          
          return oppDateStr === dateStr && statusMatch;
        });
        
        // Show modal with opportunities
        this.showOpportunitiesModal(date, dayOpportunities);
      });
    });
    
    // Category checkboxes
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');
    categoryCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        const status = checkbox.getAttribute('data-status');
        if (status) {
          this.activeCategories[status] = checkbox.checked;
          this.render();
          this.setupEventListeners();
        }
      });
    });
    
    // Opportunity item click events
    const opportunityItems = document.querySelectorAll('.opportunity-item');
    opportunityItems.forEach(item => {
      item.addEventListener('click', () => {
        const oppId = item.getAttribute('data-id');
        if (oppId && this.parent && typeof this.parent.viewOpportunity === 'function') {
          this.parent.viewOpportunity(oppId);
        }
      });
    });
  }

  /**
   * Format a date for string comparison (YYYY-MM-DD)
   */
  formatDateForComparison(date) {
    if (!date) return '';
    
    // If it's already a Date object
    if (date instanceof Date) {
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
    
    // If it's a string, try to parse it
    try {
      const d = new Date(date);
      if (!isNaN(d.getTime())) {
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
      }
    } catch (e) {
      console.error('Error parsing date:', e);
    }
    
    return '';
  }

  /**
   * Format amount with currency
   */
  formatAmount(amount, currencyCode) {
    if (!amount && amount !== 0) return '';
    
    try {
      const numAmount = parseFloat(amount);
      
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currencyCode || 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      }).format(numAmount);
    } catch (e) {
      console.error('Error formatting amount:', e);
      return amount;
    }
  }

  /**
   * Get CSS class based on opportunity status
   */
  getStatusClass(status) {
    if (!status) return '';
    
    const statusLower = status.toLowerCase();
    
    if (statusLower === 'new') return 'status-new';
    if (statusLower === 'in progress') return 'status-in-progress';
    if (statusLower === 'pending') return 'status-pending';
    if (statusLower === 'won') return 'status-won';
    if (statusLower === 'lost') return 'status-lost';
    
    return '';
  }

  /**
   * Escape HTML to prevent XSS
   */
  escapeHtml(text) {
    if (!text) return '';
    
    return text
      .toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }

  /**
   * Get today's opportunities
   */
  getTodayOpportunities() {
    const today = new Date();
    const todayStr = this.formatDateForComparison(today);
    
    this.todayOpportunities = this.opportunities.filter(opp => {
      const oppDate = opp.LastModified;
      if (!oppDate) return false;
      
      const oppDateStr = this.formatDateForComparison(oppDate);
      return oppDateStr === todayStr;
    });
  }

  /**
   * Render the mini calendar
   */
  renderMiniCalendar() {
    const currentMonth = this.currentMonth;
    const today = new Date();
    
    // Get the first day of the month
    const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    
    // Get the day of the week of the first day (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfWeek = firstDay.getDay();
    
    // Get the number of days in the month
    const daysInMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).getDate();
    
    // Get the last day of the previous month
    const prevMonthLastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0).getDate();
    
    let miniCalendarDays = '';
    
    // Day names
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    let dayNamesHtml = '';
    
    dayNames.forEach(day => {
      dayNamesHtml += `<div class="mini-calendar-day-name">${day.charAt(0)}</div>`;
    });
    
    // Add days from the previous month
    for (let i = 0; i < firstDayOfWeek; i++) {
      const day = prevMonthLastDay - firstDayOfWeek + i + 1;
      miniCalendarDays += `<div class="mini-calendar-day other-month">${day}</div>`;
    }
    
    // Add days from the current month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const isToday = date.getDate() === today.getDate() && 
                     date.getMonth() === today.getMonth() && 
                     date.getFullYear() === today.getFullYear();
      
      const isSelected = day === currentMonth.getDate();
      
      miniCalendarDays += `
        <div class="mini-calendar-day ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}" 
             data-date="${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}">
          ${day}
        </div>`;
    }
    
    // Calculate remaining days to fill the grid (6 rows x 7 columns = 42 cells)
    const remainingDays = 42 - (firstDayOfWeek + daysInMonth);
    
    // Add days from the next month
    for (let day = 1; day <= remainingDays; day++) {
      miniCalendarDays += `<div class="mini-calendar-day other-month">${day}</div>`;
    }
    
    return `
      <div class="sidebar-panel mini-calendar">
        <div class="mini-calendar-header">
          <h3 class="mini-calendar-title">${this.formatMonthYear(currentMonth)}</h3>
          <div class="mini-calendar-nav">
            <button class="mini-calendar-btn" id="mini-prev-month">
              <i class="fas fa-chevron-left"></i>
            </button>
            <button class="mini-calendar-btn" id="mini-next-month">
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
        <div class="mini-calendar-grid">
          ${dayNamesHtml}
          ${miniCalendarDays}
        </div>
      </div>
    `;
  }
} 