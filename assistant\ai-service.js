// AI Service - Rule-based AI capabilities for the assistant
export class AiService {
  constructor() {
    this.initialized = false;
    this.fallbackMode = true;
    this.embeddingsCache = new Map();
  }

  // Initialize the AI service
  async init() {
    try {
      // Simple initialization - always use fallback mode
      this.initialized = true;
      this.fallbackMode = true;
      console.log("AI Service initialized in basic mode");
      return true;
    } catch (error) {
      console.error("Error initializing AI service:", error);
      this.initialized = false;
      return false;
    }
  }

  // Get the current status of the AI service
  getStatus() {
    return {
      initialized: this.initialized,
      fallbackMode: this.fallbackMode,
      huggingfaceAvailable: false,
      openaiAvailable: false,
      localModelsAvailable: false
    };
  }

  // Clear the cache
  clearCache() {
    this.embeddingsCache.clear();
    console.log("AI service cache cleared");
  }

  // Rule-based intent classification
  async classifyIntent(text) {
    if (!this.initialized) {
      await this.init();
    }

    const normalizedText = text.toLowerCase();
    
    // Part search intent
    if (
      normalizedText.includes("find part") ||
      normalizedText.includes("search part") ||
      normalizedText.includes("find me") ||
      normalizedText.includes("looking for part") ||
      normalizedText.includes("part number") ||
      normalizedText.includes("search for") ||
      /\b\d{5,8}\b/.test(normalizedText) || // Contains a part number format
      normalizedText.includes("tubing") ||
      normalizedText.includes("valve") ||
      normalizedText.includes("fitting")
    ) {
      return "search_parts";
    }
    
    // Order tracking intent
    if (
      normalizedText.includes("track") ||
      normalizedText.includes("order") ||
      normalizedText.includes("shipment") ||
      normalizedText.includes("delivery") ||
      normalizedText.includes("shipping") ||
      normalizedText.includes("where is") ||
      normalizedText.includes("status") ||
      /\bpo\d+\b/i.test(text) || // PO followed by numbers
      /\bwo\d+\b/i.test(text)    // WO followed by numbers
    ) {
      return "track_order";
    }
    
    // Monday search intent
    if (
      normalizedText.includes("monday") ||
      normalizedText.includes("board") ||
      normalizedText.match(/monday.*search/i) ||
      normalizedText.match(/search.*monday/i)
    ) {
      return "monday_search";
    }
    
    // Company info intent
    if (
      normalizedText.includes("company") ||
      normalizedText.includes("envent") ||
      normalizedText.includes("about us") ||
      normalizedText.includes("history") ||
      normalizedText.includes("who are you")
    ) {
      return "company_info";
    }
    
    // Technical question intent
    if (
      normalizedText.includes("how does") ||
      normalizedText.includes("what is") ||
      normalizedText.includes("explain") ||
      normalizedText.includes("manual") ||
      normalizedText.includes("instructions") ||
      normalizedText.includes("specifications") ||
      normalizedText.includes("specs") ||
      normalizedText.includes("technical") ||
      normalizedText.includes("?")
    ) {
      return "technical_question";
    }
    
    // Image request intent
    if (
      normalizedText.includes("image") ||
      normalizedText.includes("picture") ||
      normalizedText.includes("photo") ||
      normalizedText.includes("diagram") ||
      normalizedText.includes("drawing") ||
      normalizedText.includes("show me")
    ) {
      return "image_request";
    }
    
    // Monday update intent
    if (
      normalizedText.includes("update") ||
      normalizedText.includes("change status") ||
      normalizedText.includes("modify") ||
      normalizedText.includes("edit item")
    ) {
      return "update_item";
    }
    
    // Greeting intent
    if (
      normalizedText.includes("hello") ||
      normalizedText.includes("hi") ||
      normalizedText.includes("hey") ||
      normalizedText.includes("good morning") ||
      normalizedText.includes("good afternoon") ||
      normalizedText.includes("good evening") ||
      normalizedText.includes("greetings")
    ) {
      return "greeting";
    }
    
    // Help request intent
    if (
      normalizedText.includes("help") ||
      normalizedText.includes("assist") ||
      normalizedText.includes("support") ||
      normalizedText.includes("what can you do") ||
      normalizedText.includes("how to use")
    ) {
      return "help_request";
    }
    
    // Math question intent
    if (
      normalizedText.includes("calculate") ||
      normalizedText.includes("compute") ||
      normalizedText.includes("+") ||
      normalizedText.includes("-") ||
      normalizedText.includes("*") ||
      normalizedText.includes("/") ||
      normalizedText.includes("=") ||
      normalizedText.includes("sum of") ||
      normalizedText.includes("difference between") ||
      normalizedText.includes("product of") ||
      normalizedText.includes("divided by")
    ) {
      return "math_question";
    }
    
    // Default - unknown intent
    return "unknown";
  }

  // Simple embedding function - creates a basic bag-of-words representation
  async createEmbedding(text) {
    if (!text || typeof text !== 'string') return new Array(100).fill(0);
    
    // Check cache first
    const cacheKey = text.toLowerCase().trim();
    if (this.embeddingsCache.has(cacheKey)) {
      return this.embeddingsCache.get(cacheKey);
    }
    
    // Create a simple embedding - just a random vector based on the text
    // This is a very simplistic approach just for demonstration
    const words = text.toLowerCase().trim()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .split(/\s+/)             // Split by whitespace
      .filter(word => word.length > 2); // Filter out very short words
    
    // Initialize a fixed-size vector with zeros (100 dimensions)
    const embedding = new Array(100).fill(0);
    
    // Simple hashing of words to vector positions
    words.forEach(word => {
      let hash = 0;
      for (let i = 0; i < word.length; i++) {
        hash = ((hash << 5) - hash) + word.charCodeAt(i);
        hash = hash & hash; // Convert to 32bit integer
      }
      
      // Use the hash to determine a position and value in the embedding
      const position = Math.abs(hash) % 100;
      embedding[position] += 1;
    });
    
    // Normalize the vector (L2 norm)
    const sum = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    const normalized = embedding.map(val => sum > 0 ? val / sum : 0);
    
    // Cache the result
    this.embeddingsCache.set(cacheKey, normalized);
    
    return normalized;
  }
  
  // Calculate cosine similarity between two vectors
  cosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }
  
  // Semantic search implementation
  async semanticSearch(query, items, textExtractor, limit = 5) {
    if (!query || !items || !Array.isArray(items) || items.length === 0) {
      return [];
    }
    
    try {
      // Get query embedding
      const queryEmbedding = await this.createEmbedding(query);
      
      // Calculate similarity for each item
      const scoredItems = await Promise.all(items.map(async (item) => {
        // Extract text from the item
        const text = typeof textExtractor === 'function' 
          ? textExtractor(item) 
          : String(item);
          
        // Get item embedding
        const itemEmbedding = await this.createEmbedding(text);
        
        // Calculate similarity
        const score = this.cosineSimilarity(queryEmbedding, itemEmbedding);
        
        return { item, score };
      }));
      
      // Filter out bad scores and sort by score (descending)
      return scoredItems
        .filter(({ score }) => !isNaN(score) && score > 0.1) // Filter out low scores
        .sort((a, b) => b.score - a.score) // Sort by descending score
        .slice(0, limit) // Take top N results
        .map(({ item }) => item); // Return just the items
    } catch (error) {
      console.error('Error in semantic search:', error);
      return [];
    }
  }
  
  // Simple entity extraction using regex patterns
  async extractEntities(text) {
    if (!text || typeof text !== 'string') {
      return { companies: [] };
    }
    
    const companies = [];
    
    // Simple regex patterns for company names (very basic approach)
    const companyPatterns = [
      /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+(?:Inc|LLC|Ltd|GmbH|Corp|Corporation|Company)\b/g,
      /\b([A-Z][A-Za-z]*(?:\s+[A-Z][A-Za-z]*)+)\b/g
    ];
    
    // Extract companies using regex
    for (const pattern of companyPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        companies.push(match[1]);
      }
    }
    
    // Return unique companies
    return {
      companies: [...new Set(companies)]
    };
  }
  
  // Answer questions based on provided context
  async answerQuestion(question, context) {
    if (!question || !context) {
      return "I don't have enough information to answer that question.";
    }
    
    try {
      // Extract keywords from the question
      const keywords = question.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .split(/\s+/)
        .filter(word => 
          word.length > 3 && 
          !['what', 'when', 'where', 'which', 'who', 'whom', 'whose', 'why', 'how', 'does', 'is', 'are', 'was', 'were', 'will', 'would', 'can', 'could', 'should', 'shall', 'must', 'might', 'have', 'had', 'has', 'having', 'been', 'be', 'being', 'do', 'did', 'doing', 'the', 'and', 'but', 'or', 'nor', 'for', 'yet', 'so', 'such', 'as', 'in', 'on', 'at', 'to', 'by', 'with', 'from', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below', 'under', 'over', 'between', 'among', 'this', 'that', 'these', 'those'].includes(word)
        );
      
      if (keywords.length === 0) {
        return "I'm not sure what you're asking about. Can you rephrase your question?";
      }
      
      // Split context into sentences
      const sentences = context.split(/[.!?]+/)
        .map(s => s.trim())
        .filter(s => s.length > 10);
      
      // Score each sentence based on keyword matches
      const scoredSentences = sentences.map(sentence => {
        const lowerSentence = sentence.toLowerCase();
        let score = 0;
        
        // Count keyword occurrences
        for (const keyword of keywords) {
          if (lowerSentence.includes(keyword)) {
            score += 1;
          }
        }
        
        return { sentence, score };
      });
      
      // Sort by score and take the top sentences
      const topSentences = scoredSentences
        .filter(item => item.score > 0)
        .sort((a, b) => b.score - a.score)
        .slice(0, 3)
        .map(item => item.sentence);
      
      if (topSentences.length === 0) {
        return "I couldn't find any relevant information in my knowledge base.";
      }
      
      // Simply return the best matching sentences
      return topSentences.join(' ');
    } catch (error) {
      console.error('Error in question answering:', error);
      return "I encountered an error while trying to answer your question.";
    }
  }
  
  // Analyze sentiment of text - simplified rule-based approach
  async analyzeSentiment(text) {
    if (!text || typeof text !== 'string') {
      return { sentiment: 'neutral', score: 0 };
    }
    
    const normalizedText = text.toLowerCase();
    
    // Simple lists of positive and negative words
    const positiveWords = ['good', 'great', 'excellent', 'awesome', 'amazing', 'wonderful', 'fantastic', 'terrific', 'outstanding', 'superb', 'nice', 'love', 'happy', 'pleased', 'delighted', 'satisfied', 'thank', 'thanks', 'appreciate', 'positive', 'perfect', 'best'];
    
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'poor', 'disappointing', 'dissatisfied', 'unhappy', 'hate', 'dislike', 'negative', 'worst', 'problem', 'issue', 'fail', 'failed', 'failure', 'broken', 'mistake', 'error', 'wrong', 'not working', 'doesn\'t work', 'doesn\'t help'];
    
    // Calculate sentiment score
    let score = 0;
    const words = normalizedText.split(/\s+/);
    
    for (const word of words) {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    }
    
    // Normalize score to range from -1 to 1
    const normalizedScore = words.length > 0 ? score / Math.sqrt(words.length) : 0;
    const clampedScore = Math.max(-1, Math.min(1, normalizedScore));
    
    // Determine sentiment label
    let sentiment = 'neutral';
    if (clampedScore > 0.2) sentiment = 'positive';
    else if (clampedScore < -0.2) sentiment = 'negative';
    
    return {
      sentiment,
      score: clampedScore
    };
  }
} 