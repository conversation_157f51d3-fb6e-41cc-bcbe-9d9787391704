// analytics-fullscreen-integration.js
// Integrates chart management and filter modules with the main dashboard

import { ChartManager } from './analytics-fullscreen-charts.js';
import { FilterManager } from './analytics-fullscreen-filters.js';

export class DashboardEnhancer {
  constructor(dashboard) {
    this.dashboard = dashboard;
    this.chartManager = new ChartManager();
    this.filterManager = new FilterManager(dashboard);
    this.initialized = false;
  }

  /**
   * Initialize the dashboard enhancer
   */
  async init() {
    if (this.initialized) return;
    
    try {
      // Wait for the dashboard to finish initial rendering
      await this.waitForDashboardReady();
      
      // Initialize filter manager first
      this.filterManager.init();
      
      // Then initialize chart manager
      this.chartManager.init();
      
      // Extend dashboard functionality
      this.extendDashboard();
      
      // Add CSS styles
      this.addStyles();
      
      // Add responsive features
      this.setupResponsiveFeatures();
      
      console.log('Dashboard enhancer initialized successfully');
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing dashboard enhancer:', error);
    }
  }

  /**
   * Wait for dashboard to be fully loaded and rendered
   */
  waitForDashboardReady() {
    return new Promise((resolve) => {
      const checkInterval = 100; // ms
      const maxWaitTime = 10000; // ms
      let elapsedTime = 0;
      
      const checkDashboard = () => {
        // Check if dashboard elements are rendered
        const dashboardContent = document.getElementById('dashboardContent');
        const charts = document.querySelectorAll('[id$="Chart"]');
        
        if (dashboardContent && !dashboardContent.classList.contains('hidden') && charts.length > 0) {
          resolve();
        } else if (elapsedTime >= maxWaitTime) {
          console.warn('Timed out waiting for dashboard to be ready');
          resolve();
        } else {
          elapsedTime += checkInterval;
          setTimeout(checkDashboard, checkInterval);
        }
      };
      
      checkDashboard();
    });
  }

  /**
   * Extend dashboard functionality by patching methods
   */
  extendDashboard() {
    if (!this.dashboard) return;
    
    // Store original methods
    const originalRenderCharts = this.dashboard.renderCharts;
    const originalRefreshData = this.dashboard.refreshData;
    
    // Extend renderCharts to register charts with chart manager
    this.dashboard.renderCharts = async function() {
      try {
        // Call original method
        const result = await originalRenderCharts.apply(this, arguments);
        
        // Register charts with chart manager
        setTimeout(() => {
          document.querySelectorAll('[id$="Chart"]').forEach(chart => {
            if (chart.__apexChart) {
              this.chartManager.registerChart(chart.id, chart.__apexChart);
            }
          });
        }, 500);
        
        return result;
      } catch (error) {
        console.error('Error in extended renderCharts:', error);
      }
    }.bind(this.dashboard);
    
    // Attach chart manager
    this.dashboard.chartManager = this.chartManager;
    
    // Extend refreshData to show loading UI
    this.dashboard.refreshData = async function() {
      try {
        this.filterManager.showLoading();
        const result = await originalRefreshData.apply(this, arguments);
        return result;
      } catch (error) {
        console.error('Error in extended refreshData:', error);
        this.filterManager.showError('Error refreshing data: ' + error.message);
      } finally {
        this.filterManager.hideLoading();
      }
    }.bind({
      filterManager: this.filterManager,
      refreshData: originalRefreshData.bind(this.dashboard)
    });
    
    // Add method to update single chart
    this.dashboard.updateSingleChart = function(chartId) {
      const chart = document.getElementById(chartId);
      if (!chart) return;
      
      const chartType = chartId.replace('Chart', '');
      const renderMethodName = `render${chartType.charAt(0).toUpperCase() + chartType.slice(1)}Chart`;
      
      if (typeof this[renderMethodName] === 'function') {
        this[renderMethodName]();
      } else {
        console.warn(`No render method found for chart: ${chartId}`);
      }
    }.bind(this.dashboard);
  }

  /**
   * Add required CSS styles
   */
  addStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
      /* Chart controls */
      .chart-container {
        position: relative;
        transition: box-shadow 0.3s ease, transform 0.3s ease;
      }
      
      .chart-container:hover {
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
      
      .chart-controls button {
        opacity: 0;
        transition: opacity 0.2s ease;
      }
      
      .chart-container:hover .chart-controls button {
        opacity: 1;
      }
      
      .resize-handle {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 14px;
        height: 14px;
        cursor: nwse-resize;
        z-index: 10;
      }
      
      /* Loading and overlays */
      #filterLoadingOverlay {
        backdrop-filter: blur(2px);
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-spinner {
        animation: spin 1s linear infinite;
        border-radius: 50%;
        border: 3px solid rgba(229, 231, 235, 1);
        border-top-color: rgba(59, 130, 246, 1);
      }
      
      /* Fullscreen chart */
      #chartFullscreenOverlay {
        backdrop-filter: blur(3px);
      }
      
      /* Chart library panel */
      #chartLibraryPanel {
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
      }
      
      /* Responsive adjustments */
      @media (max-width: 768px) {
        .chart-container {
          min-height: 250px;
        }
      }
    `;
    
    document.head.appendChild(styleElement);
  }

  /**
   * Set up responsive features
   */
  setupResponsiveFeatures() {
    // Handle window resize
    window.addEventListener('resize', () => {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
      }
      
      this.resizeTimeout = setTimeout(() => {
        this.chartManager.resizeAllCharts();
      }, 250);
    });
    
    // Handle orientation change for mobile devices
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.chartManager.resizeAllCharts();
      }, 300);
    });
  }

  /**
   * Create a customized layout with specific charts
   */
  createCustomLayout(chartIds) {
    if (!chartIds || !Array.isArray(chartIds)) return;
    
    // Find dashboard content
    const dashboardContent = document.getElementById('dashboardContent');
    if (!dashboardContent) return;
    
    // Create custom layout container
    const customLayout = document.createElement('div');
    customLayout.id = 'customDashboardLayout';
    customLayout.className = 'grid grid-cols-1 md:grid-cols-2 gap-6 mb-6';
    
    // Add selected charts
    chartIds.forEach(chartId => {
      const chartContainer = document.querySelector(`.chart-container:has(#${chartId})`);
      if (chartContainer) {
        customLayout.appendChild(chartContainer.cloneNode(true));
      }
    });
    
    // Replace tabs with custom layout
    const tabContent = document.querySelector('.tab-content.active');
    if (tabContent) {
      tabContent.innerHTML = '';
      tabContent.appendChild(customLayout);
    }
  }

  /**
   * Add a button to save the current dashboard layout
   */
  addSaveLayoutButton() {
    // This is now handled in the unified settings panel
    console.log("Layout management is handled in the dashboard settings panel");
  }

  /**
   * Save the current dashboard layout
   */
  saveCurrentLayout() {
    // Get visible charts
    const visibleCharts = Array.from(document.querySelectorAll('.chart-container')).filter(
      container => container.style.display !== 'none'
    );
    
    // Create layout object
    const layout = {
      timestamp: Date.now(),
      name: `Layout ${new Date().toLocaleDateString()}`,
      charts: visibleCharts.map(container => {
        const chartElement = container.querySelector('[id$="Chart"]');
        return {
          id: chartElement?.id || '',
          title: container.querySelector('h3')?.textContent || 'Unnamed Chart',
          position: {
            left: container.style.left || '',
            top: container.style.top || '',
            width: container.style.width || '',
            height: container.style.height || ''
          }
        };
      })
    };
    
    // Save to localStorage
    try {
      // Get existing layouts
      const existingLayouts = JSON.parse(localStorage.getItem('dashboardLayouts') || '[]');
      existingLayouts.push(layout);
      
      // Save updated layouts
      localStorage.setItem('dashboardLayouts', JSON.stringify(existingLayouts));
      
      // Show success message
      this.showNotification('Dashboard layout saved successfully', 'success');
      return true;
    } catch (error) {
      console.error('Error saving layout:', error);
      this.showNotification('Failed to save dashboard layout', 'error');
      return false;
    }
  }

  /**
   * Show a notification message
   */
  showNotification(message, type = 'info') {
    // Create notification element if it doesn't exist
    let notificationContainer = document.getElementById('notification-container');
    
    if (!notificationContainer) {
      notificationContainer = document.createElement('div');
      notificationContainer.id = 'notification-container';
      notificationContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col items-end space-y-2';
      document.body.appendChild(notificationContainer);
    }
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `px-4 py-3 rounded-lg shadow-lg text-white transform transition-all duration-300 ease-in-out translate-x-full`;
    
    // Set background color based on type
    switch (type) {
      case 'success':
        notification.classList.add('bg-green-500');
        break;
      case 'error':
        notification.classList.add('bg-red-500');
        break;
      case 'warning':
        notification.classList.add('bg-yellow-500');
        break;
      default:
        notification.classList.add('bg-blue-500');
    }
    
    // Add message
    notification.textContent = message;
    
    // Add to container
    notificationContainer.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 10);
    
    // Remove after delay
    setTimeout(() => {
      notification.classList.add('opacity-0', 'translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, 3000);
  }

  /**
   * Load a saved dashboard layout
   */
  loadLayout(layoutId) {
    try {
      // Get saved layouts
      const layouts = JSON.parse(localStorage.getItem('dashboardLayouts') || '[]');
      const layout = layouts.find(l => l.timestamp === layoutId);
      
      if (!layout) {
        console.error('Layout not found:', layoutId);
        return;
      }
      
      // Apply layout
      layout.charts.forEach(chartInfo => {
        const chartElement = document.getElementById(chartInfo.id);
        if (!chartElement) return;
        
        const container = chartElement.closest('.chart-container');
        if (!container) return;
        
        // Restore position and size
        if (chartInfo.position.left) container.style.left = chartInfo.position.left;
        if (chartInfo.position.top) container.style.top = chartInfo.position.top;
        if (chartInfo.position.width) container.style.width = chartInfo.position.width;
        if (chartInfo.position.height) container.style.height = chartInfo.position.height;
        
        // Make visible
        container.style.display = '';
        
        // Update chart instance
        const chart = this.chartManager.getChart(chartInfo.id);
        if (chart && chart.updateOptions) {
          chart.updateOptions({
            chart: {
              width: container.offsetWidth,
              height: container.offsetHeight
            }
          });
        }
      });
      
      // Hide charts not in layout
      document.querySelectorAll('.chart-container').forEach(container => {
        const chartElement = container.querySelector('[id$="Chart"]');
        if (!chartElement) return;
        
        const chartInLayout = layout.charts.some(c => c.id === chartElement.id);
        if (!chartInLayout) {
          container.style.display = 'none';
        }
      });
      
    } catch (error) {
      console.error('Error loading layout:', error);
    }
  }

  /**
   * Build the main dashboard layout with core components
   */
  buildDashboardLayout() {
    console.log('Building dashboard layout');
    
    // Create the wrapper container for the entire dashboard
    const dashboardContainer = document.getElementById('dashboardContent');
    if (!dashboardContainer) {
      console.error('Dashboard container not found');
      return;
    }
    
    // Clear any existing content
    dashboardContainer.innerHTML = '';
    
    // Create the dashboard header
    const dashboardHeader = document.createElement('div');
    dashboardHeader.className = 'dashboard-header flex justify-between items-center p-4 bg-white border-b shadow-sm';
    dashboardHeader.innerHTML = `
      <div class="dashboard-title">
        <h1 class="text-xl font-semibold text-gray-800">Analytics Dashboard</h1>
        <p id="lastUpdated" class="text-sm text-gray-500">Last updated: ${new Date().toLocaleString()}</p>
      </div>
      <div id="dashboardControlButtons" class="flex space-x-2">
        <button id="printReport" class="px-3 py-1.5 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
          </svg>
          Print Report
        </button>
        <button id="dashboardSettingsBtn" class="px-3 py-1.5 bg-gray-500 text-white text-sm rounded-md hover:bg-gray-600 transition-colors duration-150 flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Settings
        </button>
      </div>
    `;
    
    dashboardContainer.appendChild(dashboardHeader);
    
    // Create tabs container for chart organization
    const tabsContainer = document.createElement('div');
    tabsContainer.className = 'tabs-container p-4 bg-gray-50';
    
    // Create tab buttons
    let tabButtonsHTML = '<div class="tab-buttons flex mb-4 space-x-2">';
    this.tabsConfig.forEach((tab, index) => {
      tabButtonsHTML += `
        <button class="tab-button px-4 py-2 text-gray-700 bg-white rounded-md shadow-sm hover:bg-gray-200 ${index === 0 ? 'active' : ''}" data-tab="${tab.id}">
          ${tab.label}
        </button>
      `;
    });
    tabButtonsHTML += '</div>';
    
    // Create tab contents container
    const tabContentHTML = '<div class="tab-contents bg-white rounded-md shadow-sm">';
    
    tabsContainer.innerHTML = tabButtonsHTML + tabContentHTML;
    dashboardContainer.appendChild(tabsContainer);
    
    // Create tab content divs
    const tabContents = tabsContainer.querySelector('.tab-contents');
    this.tabsConfig.forEach((tab, index) => {
      const tabContent = document.createElement('div');
      tabContent.className = `tab-content ${index === 0 ? 'active' : ''}`;
      tabContent.id = `tab-${tab.id}`;
      tabContent.innerHTML = `
        <div class="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-${tab.columns} gap-4">
          <!-- Content will be inserted here -->
        </div>
      `;
      tabContents.appendChild(tabContent);
    });
    
    // Build settings panel
    this.buildSettingsPanel();
    
    // Create notification container
    const notificationContainer = document.createElement('div');
    notificationContainer.id = 'notificationContainer';
    notificationContainer.className = 'fixed top-4 right-4 z-50 w-72';
    document.body.appendChild(notificationContainer);
    
    // Add event listeners
    this.setupTabEventListeners();
  }
  
  /**
   * Build settings panel with organized sections
   */
  buildSettingsPanel() {
    const settingsPanel = document.createElement('div');
    settingsPanel.id = 'dashboardSettingsPanel';
    settingsPanel.className = 'fixed inset-y-0 right-0 w-80 bg-white shadow-lg z-40 transform translate-x-full transition-transform duration-300 ease-in-out overflow-y-auto';
    
    settingsPanel.innerHTML = `
      <div class="p-4 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div class="flex justify-between items-center">
          <h3 class="font-semibold text-lg">Dashboard Settings</h3>
          <button id="closeSettingsBtn" class="p-1 hover:bg-blue-700 rounded-full transition-colors duration-150">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      
      <!-- Dashboard Actions -->
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Dashboard Actions</h4>
        <div class="grid grid-cols-2 gap-2">
          <button id="refreshData" class="px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center justify-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh Data
          </button>
          <button id="exportCSV" class="px-3 py-1.5 bg-green-500 text-white text-sm rounded-md hover:bg-green-600 transition-colors duration-150 flex items-center justify-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Export Data
          </button>
        </div>
      </div>
      
      <!-- Print Settings -->
      <div id="printSettingsContainer" class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Print Settings</h4>
        <div class="space-y-3">
          <div class="flex items-center">
            <input type="checkbox" id="includeKPIs" checked class="mr-2">
            <label for="includeKPIs" class="text-sm text-gray-700">Include KPI Cards</label>
          </div>
          <div class="flex items-center">
            <input type="checkbox" id="includeDataTable" class="mr-2">
            <label for="includeDataTable" class="text-sm text-gray-700">Include Data Table</label>
          </div>
          <div class="flex items-center">
            <input type="checkbox" id="includePageNumbers" checked class="mr-2">
            <label for="includePageNumbers" class="text-sm text-gray-700">Include Page Numbers</label>
          </div>
          <div class="flex items-center">
            <input type="checkbox" id="landscapeOrientation" checked class="mr-2">
            <label for="landscapeOrientation" class="text-sm text-gray-700">Landscape Orientation</label>
          </div>
          <div>
            <label for="chartSelectorList" class="text-sm text-gray-700 block mb-1">Select Charts to Print</label>
            <div id="chartSelectorList" class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2 mb-2">
              <!-- Chart selection checkboxes will be added dynamically -->
              <div class="flex justify-between pb-2 mb-2 border-b border-gray-200">
                <button id="selectAllChartsPrintBtn" class="px-2 py-1 text-xs bg-blue-500 text-white rounded">Select All</button>
                <button id="deselectAllChartsPrintBtn" class="px-2 py-1 text-xs bg-gray-500 text-white rounded">Deselect All</button>
              </div>
            </div>
          </div>
          <button id="printFromSettings" class="w-full px-3 py-1.5 bg-purple-500 text-white text-sm rounded-md hover:bg-purple-600 transition-colors duration-150 flex items-center justify-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            Print Report
          </button>
        </div>
      </div>
      
      <!-- Chart Library -->
      <div id="chartLibrarySection" class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Chart Library</h4>
        <div id="hiddenChartsList" class="mb-3">
          <p class="text-sm text-gray-600 mb-2">Hidden charts:</p>
          <div id="hiddenChartsContainer" class="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-2">
            <p class="text-sm text-gray-500 italic">No hidden charts</p>
          </div>
          <button id="restoreAllChartsBtn" class="mt-2 px-3 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 transition-colors duration-150 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Restore All Charts
          </button>
        </div>
      </div>
      
      <!-- Display Settings -->
      <div class="p-4 border-b border-gray-200">
        <h4 class="font-medium text-gray-800 mb-3">Display Settings</h4>
        <div class="space-y-3">
          <div>
            <label for="chartSizeSelect" class="text-sm text-gray-700 block mb-1">Chart Size</label>
            <select id="chartSizeSelect" class="w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm">
              <option value="sm">Small</option>
              <option value="md" selected>Medium</option>
              <option value="lg">Large</option>
              <option value="xl">Extra Large</option>
            </select>
          </div>
          <div>
            <label for="colorThemeSelect" class="text-sm text-gray-700 block mb-1">Color Theme</label>
            <select id="colorThemeSelect" class="w-full border border-gray-300 rounded-md px-3 py-1.5 text-sm">
              <option value="default" selected>Default</option>
              <option value="cool">Cool</option>
              <option value="warm">Warm</option>
              <option value="monochrome">Monochrome</option>
            </select>
          </div>
          <div class="flex items-center">
            <input type="checkbox" id="darkModeToggle" class="mr-2">
            <label for="darkModeToggle" class="text-sm text-gray-700">Dark Mode</label>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(settingsPanel);
  }
}

// Auto-initialize when imported in a module script
document.addEventListener('DOMContentLoaded', async () => {
  // Wait for main dashboard to initialize
  const waitForDashboard = () => {
    return new Promise(resolve => {
      const checkDashboard = () => {
        if (window.dashboardInstance) {
          resolve(window.dashboardInstance);
        } else {
          setTimeout(checkDashboard, 100);
        }
      };
      checkDashboard();
    });
  };
  
  try {
    const dashboard = await waitForDashboard();
    const enhancer = new DashboardEnhancer(dashboard);
    await enhancer.init();
    
    // Make enhancer available globally for debugging
    window.dashboardEnhancer = enhancer;
  } catch (error) {
    console.error('Failed to initialize dashboard enhancer:', error);
  }
}); 