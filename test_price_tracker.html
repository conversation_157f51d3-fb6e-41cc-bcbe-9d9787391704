<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Tracker Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Price Tracker Component Test</h1>

        <!-- Test Controls -->
        <div class="mb-6 p-4 bg-gray-100 rounded-lg">
            <h2 class="text-lg font-semibold mb-4">Test Configuration</h2>
            <div class="flex gap-4">
                <button id="test-sample-data" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                    Test with Sample Data
                </button>
                <button id="test-real-data" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                    Test with Real Acumatica Data
                </button>
                <button id="reload-component" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                    Reload Component
                </button>
            </div>
            <div id="test-status" class="mt-2 text-sm text-gray-600"></div>
        </div>

        <!-- Container for the Price Tracker component -->
        <div id="price-tracker-container" class="bg-white rounded-lg shadow-lg p-6">
            <!-- Component will be rendered here -->
        </div>
    </div>

    <!-- Mock Chrome extension API for testing -->
    <script>
        // Mock chrome.storage.local for testing
        window.chrome = {
            storage: {
                local: {
                    get: (keys, callback) => {
                        // Mock stored connections
                        const mockData = {
                            connections: {
                                acumatica: {
                                    isConnected: false, // Set to false to use sample data
                                    instance: 'https://envent-eng.acumatica.com',
                                    credentials: {
                                        username: 'test',
                                        password: 'test'
                                    }
                                }
                            }
                        };
                        callback(mockData);
                    },
                    set: (data, callback) => {
                        console.log('Mock chrome.storage.local.set called with:', data);
                        if (callback) callback();
                    }
                }
            }
        };

        // Mock IndexedDB for testing
        if (!window.indexedDB) {
            console.warn('IndexedDB not available, using mock');
        }
    </script>

    <!-- Load the components -->
    <script type="module">
        // Import the connection manager
        import { connectionManager } from './core/connection.js';
        
        // Import the Price Tracker component
        import { PriceTrackerComponent } from './kpi/purchasing/price_tracker.js';

        // Initialize the connection manager
        await connectionManager.init();

        let currentPriceTracker = null;

        // Function to initialize the Price Tracker component
        async function initializePriceTracker() {
            const container = document.getElementById('price-tracker-container');
            container.innerHTML = '<div class="text-center p-8">Initializing Price Tracker...</div>';

            try {
                if (currentPriceTracker) {
                    currentPriceTracker.destroy();
                }

                currentPriceTracker = new PriceTrackerComponent(container);
                await currentPriceTracker.init();
                console.log('Price Tracker component initialized successfully');
                document.getElementById('test-status').textContent = 'Component initialized successfully';
            } catch (error) {
                console.error('Error initializing Price Tracker component:', error);
                container.innerHTML = `
                    <div class="text-red-600 p-4">
                        <h3 class="font-bold">Error initializing Price Tracker:</h3>
                        <p>${error.message}</p>
                        <pre class="mt-2 text-xs bg-gray-100 p-2 rounded">${error.stack}</pre>
                    </div>
                `;
                document.getElementById('test-status').textContent = `Error: ${error.message}`;
            }
        }

        // Set up test buttons
        document.getElementById('test-sample-data').addEventListener('click', () => {
            // Set to use sample data
            window.chrome.storage.local.get = (keys, callback) => {
                const mockData = {
                    connections: {
                        acumatica: {
                            isConnected: false, // Use sample data
                            instance: 'https://envent-eng.acumatica.com'
                        }
                    }
                };
                callback(mockData);
            };
            document.getElementById('test-status').textContent = 'Configured for sample data';
            initializePriceTracker();
        });

        document.getElementById('test-real-data').addEventListener('click', () => {
            // Set to use real Acumatica data
            window.chrome.storage.local.get = (keys, callback) => {
                const mockData = {
                    connections: {
                        acumatica: {
                            isConnected: true, // Use real data
                            instance: 'https://envent-eng.acumatica.com'
                        }
                    }
                };
                callback(mockData);
            };
            document.getElementById('test-status').textContent = 'Configured for real Acumatica data';
            initializePriceTracker();
        });

        document.getElementById('reload-component').addEventListener('click', () => {
            initializePriceTracker();
        });

        // Initialize with sample data by default
        document.getElementById('test-status').textContent = 'Initializing with sample data...';
        await initializePriceTracker();
    </script>
</body>
</html>
