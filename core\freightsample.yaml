openapi: 3.0.0
info:
  title: FreightSimple API documentation (Alpha)
  version: '0.1'
  x-logo:
    url: /api-logo.png
    altText: FreightSimple API Logo
  description: |
    # Features
    This API allows you to:
    - Initiate quoting for a shipment
    - Book the shipment
    - Retrieve shipment details, quotes, and status

    # Setup

    ## Authentication

    <blockquote class="redoc-alert redoc-alert-important">
    <span class="redoc-alert__title">
    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6" d="M7.556 8.5h8m-8 3.5H12m7.111-7H4.89a.896.896 0 0 0-.629.256.868.868 0 0 0-.26.619v9.25c0 .232.094.455.26.619A.896.896 0 0 0 4.89 16H9l3 4 3-4h4.111a.896.896 0 0 0 .629-.256.868.868 0 0 0 .26-.619v-9.25a.868.868 0 0 0-.26-.619.896.896 0 0 0-.63-.256Z"/>
    </svg>
      Important
    </span>
    Please reach out to your account manager to begin the process of setting up API access. </br>
    If approved, you will be able to generate API keys from the 'Manage Company' menu, in the 'API Access' section of your dashboard.
    </blockquote>

    Your API key must be included in the Authorization header for all requests.

    Example: 'Authorization: Bearer fs_prod_xJ7HwF2p9K5mN3vQ8rT4yU9wB1cE5gL9'

    Security recommendations:
    - Keep your API key secure and never share it
    - Rotate keys periodically
    - Monitor key usage in the dashboard

    ## Environments

    <blockquote class="redoc-alert redoc-alert-danger">
    <span class="redoc-alert__title">
    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 13V8m0 8h.01M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
    </svg>
      Alert
    </span>
    Please perform all <strong>testing</strong> in the <strong>staging</strong> environment.
    </blockquote>

    - Staging: `https://api.freightsimpledemo.com/v1`

    - Production: `https://api.freightsimple.com/v1`


    ## Rate Limits

    | **Endpoint**                              | **Rate Limit**               |
    |-------------------------------------------|------------------------------|
    | `POST /shipments/start-quoting`           | 10 requests per hour         |
    | `GET /shipments/get-shipment`             | 100 requests per hour        |
    | `POST /shipments/start-booking`           | 10 requests per hour         |

    <blockquote class="redoc-alert redoc-alert-tip">
    <span class="redoc-alert__title">
    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6" d="M9 9a3 3 0 0 1 3-3m-2 15h4m0-3c0-4.1 4-4.9 4-9A6 6 0 1 0 6 9c0 4 4 5 4 9h4Z"/>
    </svg>
      Notes
    </span>
     Rate limits are applied per company, not per API key. </br>
     If these rate limits are exceeded, you will receive a 429 error. </br>
     If you need your rate limits increased, please reach out to your account manager. </br>
    </blockquote>




    # Basic Usage

    1. **Start the quoting process**  
       Call the `startShipmentQuoting` operation to create a shipment and begin fetching quotes.

    <blockquote class="redoc-alert redoc-alert-tip" style="margin-left: 28px;">
    <span class="redoc-alert__title">
    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6" d="M9 9a3 3 0 0 1 3-3m-2 15h4m0-3c0-4.1 4-4.9 4-9A6 6 0 1 0 6 9c0 4 4 5 4 9h4Z"/>
    </svg>
      Tip
    </span>
    Quoting typically takes around <strong>30 seconds</strong>, so we recommend you sleep for <strong>30 seconds</strong> at this point to allow quoting to complete.
    </blockquote>

    2. **Check the shipment status**  
       Poll the `getShipment` operation to verify if quoting has finished.
    3. **Display quotes**  
       Once quotes are available, present them as options in your ERP system.
    4. **Book the shipment**  
       After selecting a quote, call `startShipmentBooking` to confirm and book the shipment.

    <blockquote class="redoc-alert redoc-alert-tip" style="margin-left: 28px;">
    <span class="redoc-alert__title">
    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24">
      <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.6" d="M9 9a3 3 0 0 1 3-3m-2 15h4m0-3c0-4.1 4-4.9 4-9A6 6 0 1 0 6 9c0 4 4 5 4 9h4Z"/>
    </svg>
      Tip
    </span>
    Booking usually takes around <strong>15 seconds</strong>, so we recommend sleeping for <strong>15 seconds</strong> before checking the status.
    </blockquote>

    5. **Verify booking completion**  
       Poll the `getShipment` operation again to check if the shipment has been booked.

    6. **Retrieve shipment documents**  
       Once booked, call `getShipment` to get a link to the pickup package PDF.

    ## JavaScript Code Example

    <input type="checkbox" id="showMore" class="show-more-checkbox">
    <div class="code-container">

    ```javascript
    async function quoteAndBook() {
      // Start quoting and wait 30 seconds
      // Sends a POST request to /shipments/start-quoting
      const startQuotingResponse = await startShipmentQuoting({
        // See the examples provided in the `startShipmentQuoting` operation.
      });
      const shipmentId = startQuotingResponse.shipmentId;
      await new Promise((r) => setTimeout(r, 30000));

      // Poll shipment data until it's quoted
      const maxRetries = 10;
      let attempts = 0;
      let getShipmentResponse;
      do {
        if (attempts++ >= maxRetries) {
          throw new Error("Polling timed out: Quoted status not reached.");
        }
        // Sends a GET request to /shipments/get-shipment?shipmentId=${shipmentId}
        getShipmentResponse = await getShipment(shipmentId);
        await new Promise((r) => setTimeout(r, 5000));
      } while (getShipmentResponse.shipmentStatus !== "Quoted");

      // Select a quote to begin booking and wait 15 seconds.
      // Sends a POST request to /shipments/start-booking
      startShipmentBooking({
        quoteId: getShipmentResponse.quotes[0].quoteId,
        pickupDate: new Date(2025, 1, 31),
      });
      await new Promise((r) => setTimeout(r, 15000));

      // Poll shipment data until booking is confirmed
      attempts = 0;
      do {
        if (attempts++ >= maxRetries) {
          throw new Error("Polling timed out: BookingConfirmed status not reached.");
        }
        // Sends a GET request to /shipments/get-shipment?shipmentId=${shipmentId}
        getShipmentResponse = await getShipment(shipmentId);
        await new Promise((r) => setTimeout(r, 5000));
      } while (getShipmentResponse.shipmentStatus !== "BookingConfirmed");

      console.log("Booking Confirmed!");
    }
    ```

    </div>
    <label for="showMore" class="show-more-btn"></label>

    ## Flow Diagram
    ![API Flow Diagram](/api-state-diagram.svg)
  contact:
    name: FreightSimple Support
    url: https://freightsimple.com/
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
servers:
  - url: https://api.freightsimple.com
    description: Production endpoint
  - url: https://api.freightsimpledemo.com
    description: Staging endpoint
security:
  - ApiKeyAuth: []
tags:
  - name: shipmentsApi
    x-displayName: Shipments
    description: Shipments Operations.
paths:
  /v1/shipments/start-quoting:
    post:
      security:
        - ApiKeyAuth: []
      operationId: startShipmentQuoting
      tags:
        - shipmentsApi
      summary: Start a shipment quoting process
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartShipmentQuotingRequest'
            examples:
              Basic:
                summary: Basic
                value:
                  pickupDate: '2025-01-31'
                  pickup:
                    locationType: Warehouse
                    businessName: Pickup Business
                    addressLine: 123 Main Street
                    addressLine2: Suite 100
                    city: Barrie
                    stateOrProvinceCode: 'ON'
                    postalCode: L4N9A7
                    countryCode: Canada
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: John Doe
                    contactPhoneNumber: '9999999999'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                  delivery:
                    locationType: Warehouse
                    businessName: Delivery Business
                    addressLine: 456 Main Street
                    addressLine2: Suite 101
                    city: Surrey
                    stateOrProvinceCode: BC
                    postalCode: V4N4G4
                    countryCode: Canada
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: Jane Doe
                    contactPhoneNumber: '1234567890'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                    specialServiceAppointmentRequired: false
                  lineItems:
                    - handlingUnitType: Pallet
                      numberHandlingUnits: 1
                      description: Line Item Description
                      lengthInches: 48
                      widthInches: 48
                      heightInches: 42
                      weightPerHandlingUnitPounds: 400
                      temperatureHandling: NoSpecialHandling
                      isDangerous: false
                      isStackable: false
              DangerousGoods:
                summary: Dangerous Goods
                value:
                  pickupDate: '2025-01-31'
                  pickup:
                    locationType: Warehouse
                    businessName: Pickup Business
                    addressLine: 123 Main Street
                    addressLine2: Suite 100
                    city: Barrie
                    stateOrProvinceCode: 'ON'
                    postalCode: L4N9A7
                    countryCode: Canada
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: John Doe
                    contactPhoneNumber: '9999999999'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                  delivery:
                    locationType: Warehouse
                    businessName: Delivery Business
                    addressLine: 456 Main Street
                    addressLine2: Suite 101
                    city: Surrey
                    stateOrProvinceCode: BC
                    postalCode: V4N4G4
                    countryCode: Canada
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: Jane Doe
                    contactPhoneNumber: '1234567890'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                    specialServiceAppointmentRequired: false
                  lineItems:
                    - handlingUnitType: Pallet
                      numberHandlingUnits: 1
                      description: Line Item Description
                      lengthInches: 48
                      widthInches: 48
                      heightInches: 42
                      weightPerHandlingUnitPounds: 400
                      temperatureHandling: NoSpecialHandling
                      isDangerous: true
                      isStackable: false
                      dangerousUnNumber: UN1220
                      dangerousClassification: '3'
                      dangerousPackingGroup: II
                      dangerousProperShippingName: ISOPROPYL ACETATE
                      dangerousTechnicalName: Technical Name
                      dangerousNumberPackages: 20
                      dangerousPackagingType: bag
                  emergencyContact:
                    contactName: Dangerous Name
                    contactPhoneNumber: '0987654321'
                    contactPhoneNumberExtension: '123'
              CrossBorder:
                summary: Cross Border Shipment
                value:
                  pickupDate: '2025-01-31'
                  pickup:
                    locationType: Warehouse
                    businessName: Pickup Business
                    addressLine: 123 Main Street
                    addressLine2: Suite 100
                    city: Houston
                    stateOrProvinceCode: TX
                    postalCode: '77012'
                    countryCode: UnitedStates
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: John Doe
                    contactPhoneNumber: '9999999999'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                  delivery:
                    locationType: Warehouse
                    businessName: Delivery Business
                    addressLine: 456 Main Street
                    addressLine2: Suite 101
                    city: Coquitlam
                    stateOrProvinceCode: BC
                    postalCode: V3K 5Y5
                    countryCode: Canada
                    openFrom: '09:00'
                    openUntil: '17:00'
                    notes: Notes...
                    contactName: Jane Doe
                    contactPhoneNumber: '1234567890'
                    specialServiceInsideRequired: false
                    specialServiceLiftGateRequired: false
                    specialServiceAppointmentRequired: false
                  lineItems:
                    - handlingUnitType: Pallet
                      numberHandlingUnits: 1
                      description: Line Item Description
                      lengthInches: 48
                      widthInches: 48
                      heightInches: 42
                      weightPerHandlingUnitPounds: 400
                      temperatureHandling: NoSpecialHandling
                      isDangerous: false
                      isStackable: false
                  broker:
                    businessName: Broker Business
                    address:
                      city: Vancouver
                      stateOrProvinceCode: BC
                      postalCode: V6B 4R5
                      countryCode: Canada
                      addressLine: 123 Main Street
                      addressLine2: ''
                    contact:
                      emailAddress: <EMAIL>
                      contactName: Contact Name
                      phoneNumber: '0987654321'
      responses:
        '200':
          description: Shipment created successfully
          headers:
            X-Trace-Id:
              $ref: '#/components/headers/TraceId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartShipmentQuotingResponse'
        '400':
          description: Invalid start quote request
          headers:
            X-Trace-Id:
              $ref: '#/components/headers/TraceId'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartShipmentQuotingError'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /v1/shipments/get-shipment:
    get:
      security:
        - ApiKeyAuth: []
      summary: Get shipment details and quotes
      description: Retrieve shipment details including quotes if available
      operationId: getShipment
      tags:
        - shipmentsApi
      parameters:
        - schema:
            type: string
            format: uuid
          in: query
          name: shipmentId
          required: true
      responses:
        '200':
          description: Shipment details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShipmentResponse'
          headers:
            X-Trace-Id:
              $ref: '#/components/headers/TraceId'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /v1/shipments/start-booking:
    post:
      security:
        - ApiKeyAuth: []
      summary: Start a shipment booking process
      description: Start a shipment booking process using a selected quote.
      operationId: startShipmentBooking
      tags:
        - shipmentsApi
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartShipmentBookingRequest'
      responses:
        '200':
          description: Shipment booking successfully started
        '400':
          description: Invalid booking request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StartShipmentBookingError'
          headers:
            X-Trace-Id:
              $ref: '#/components/headers/TraceId'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
components:
  securitySchemes:
    ApiKeyAuth:
      type: http
      scheme: bearer
      description: |
        Your API key must be included in the Authorization header.
        Example: 'Authorization: Bearer fs_prod_xJ7HwF2p9K5mN3vQ8rT4yU9wB1cE5gL9'

        API keys can be generated from your FreightSimple dashboard.

        Security recommendations:
        - Keep your API key secure and never share it
        - Rotate keys periodically
        - Monitor key usage in the dashboard
  headers:
    TraceId:
      description: Unique identifier for tracing this request through our systems
      schema:
        type: string
        format: uuid
      example: 550e8400-e29b-41d4-a716-************
  schemas:
    LocationType:
      type: string
      title: LocationType
      enum:
        - Warehouse
        - DistributionWarehouse
        - Residential
        - ConstructionSite
        - School
        - TradeShow
        - UniversityCollege
        - Farm
        - Mine
        - Retail
        - Hospitality
        - Medical
        - Airport
        - SelfStorage
        - Port
        - Prison
        - OtherLimitedAccess
        - ReligiousSite
        - Military
        - Utility
    CountryCode:
      type: string
      title: CountryCode
      enum:
        - UnitedStates
        - Canada
    LocationInfo:
      title: LocationInfo
      type: object
      properties:
        locationType:
          $ref: '#/components/schemas/LocationType'
        distributionWarehouseBrand:
          type: string
          description: Only useful when locationType is DistributionWarehouse. This is the brand of the distribution warehouse. Please contact your account manager for the list
        businessName:
          type: string
          description: Name of the business at this location
        city:
          type: string
          example: San Francisco
        stateOrProvinceCode:
          type: string
          example: CA
          description: State or province code for the location
        postalCode:
          type: string
          example: '94105'
          description: Postal code for the location
        countryCode:
          $ref: '#/components/schemas/CountryCode'
          example: UnitedStates
        addressLine:
          type: string
          example: 123 Main Street
          description: First line of the address
        addressLine2:
          type: string
          example: Suite 100
          description: Second line of the address
        openFrom:
          type: string
          description: Time the earliest time the carrier can visit the location for pickup or delivery
          example: '09:00'
        openUntil:
          type: string
          description: Time the latest time the carrier can visit the location for pickup or delivery
          example: '17:00'
        notes:
          type: string
          description: Any notes that would help the carrier with this location
        contactName:
          type: string
          description: Name of the best contact for this location
        contactPhoneNumber:
          type: string
          description: Phone number for the best contact for this location
        contactPhoneNumberExtension:
          type: string
          description: Extension for the contact phone number
        contactEmail:
          type: string
          description: Email address for the best contact for this location
        referenceNumber:
          type: string
          description: Reference number for this pickup or delivery
        deadline:
          type: string
          format: date
          description: Only useful when locationType is DistributionWarehouse. For pickups this would indicate the last free day for the shipment. For deliveries this would be the MABD (Must Arrive By Date)
        boothNumber:
          type: string
          description: Booth number are required for trade shows
        specialServiceInsideRequired:
          type: boolean
          description: If true, the carrier will need to drive inside the facility to pick up or deliver the shipment
        specialServiceLiftGateRequired:
          type: boolean
          description: If true, the carrier will need to use a lift gate for the pickup or delivery
      required:
        - locationType
        - businessName
        - addressLine
        - addressLine2
        - city
        - stateOrProvinceCode
        - postalCode
        - countryCode
        - openFrom
        - openUntil
        - notes
        - contactName
        - contactPhoneNumber
        - specialServiceInsideRequired
        - specialServiceLiftGateRequired
    DeliveryLocationInfo:
      title: DeliveryLocationInfo
      type: object
      allOf:
        - $ref: '#/components/schemas/LocationInfo'
        - type: object
          properties:
            specialServiceAppointmentRequired:
              type: boolean
              description: If true, the carrier will need to schedule an appointment for the delivery
          required:
            - specialServiceAppointmentRequired
    HandlingUnitType:
      type: string
      title: HandlingUnitType
      enum:
        - Skid
        - Pallet
        - Bin
        - Box
        - Bundle
        - Cage
        - Crate
        - Drum
        - Reel
        - Roll
        - Loose
        - Other
    TemperatureHandling:
      type: string
      title: TemperatureHandling
      enum:
        - KeepFrozen
        - KeepRefrigerated
        - ProtectFromFreezing
        - NoSpecialHandling
    LineItemInfo:
      title: LineItemInfo
      type: object
      description: Represents a line item
      properties:
        handlingUnitType:
          $ref: '#/components/schemas/HandlingUnitType'
        numberHandlingUnits:
          type: integer
        description:
          type: string
        lengthInches:
          type: integer
        widthInches:
          type: integer
        heightInches:
          type: integer
        weightPerHandlingUnitPounds:
          type: integer
        temperatureHandling:
          $ref: '#/components/schemas/TemperatureHandling'
        isDangerous:
          type: boolean
        nmfcItemNumber:
          description: 'The National Motor Freight Classification (NMFC) item number for this shipment. For more details, see: [NMFC Code Guide](https://help.freightsimple.com/using-freightsimple/quoting/shipment-contents#nmfc-code).'
          type: string
        isStackable:
          type: boolean
        dangerousUnNumber:
          type: string
          description: The UN number assigned to the hazardous material, as defined by the United Nations classification system. (e.g. UN0234)</br>Required if the line item is dangerous
        dangerousClassification:
          type: string
          description: The hazard classification of the material, indicating the type of danger it presents (e.g. 1).</br>Required if the line item is dangerous
        dangerousPackingGroup:
          type: string
          description: The packing group (I, II, or III) that indicates the level of danger, where I is the most hazardous and III is the least.</br>Required if the line item is dangerous
        dangerousProperShippingName:
          type: string
          description: The official shipping name for the hazardous material, as specified in the regulations.</br>Required if the line item is dangerous
        dangerousTechnicalName:
          type: string
          description: The precise technical name of the material, providing more detailed identification beyond the proper shipping name.</br>Required if the line item is dangerous
        dangerousNumberPackages:
          type: integer
          description: Number of packages within each handling unit eg. If you are shipping a crate containing 50 bags, enter 50. If you are shipping a drum that is not further subdivided, enter 1</br>Required if the line item is dangerous
        dangerousPackagingType:
          type: string
          description: Number of packages within each handling unit eg. If you are shipping a crate containing 50 bags, enter bag. If you are shipping a drum that is not further subdivided, enter drum</br>Required if the line item is dangerous
      required:
        - handlingUnitType
        - numberHandlingUnits
        - description
        - lengthInches
        - widthInches
        - heightInches
        - weightPerHandlingUnitPounds
        - temperatureHandling
        - isDangerous
        - isStackable
    Address:
      title: Address
      type: object
      description: ''
      properties:
        city:
          type: string
          example: San Francisco
        stateOrProvinceCode:
          type: string
          example: CA
        postalCode:
          type: string
          example: '94105'
        countryCode:
          $ref: '#/components/schemas/CountryCode'
          example: UnitedStates
        addressLine:
          type: string
          example: 123 Main Street
        addressLine2:
          type: string
          example: Suite 100
      required:
        - city
        - stateOrProvinceCode
        - postalCode
        - countryCode
        - addressLine
    Contact:
      title: ContactDetails
      type: object
      properties:
        emailAddress:
          type: string
        contactName:
          type: string
        phoneNumber:
          type: string
        phoneNumberExtension:
          type: string
      required:
        - contactName
        - phoneNumber
    Broker:
      title: Broker
      type: object
      description: Broker information. Required if cross border.
      properties:
        businessName:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        contact:
          $ref: '#/components/schemas/Contact'
      required:
        - businessName
        - address
        - contact
    EmergencyContactDetails:
      title: EmergencyContactDetails
      type: object
      description: Emergency contact information. Required if anything is dangerous.
      properties:
        contactName:
          type: string
        contactPhoneNumber:
          type: string
        contactPhoneNumberExtension:
          type: string
      required:
        - contactName
        - contactPhoneNumber
    StartShipmentQuotingRequest:
      type: object
      description: |
        Represents a shipment request.

        ## Usage Notes
        * All weights should be in pounds
        * Addresses should include city and state
      required:
        - pickupDate
        - pickup
        - delivery
        - lineItems
      properties:
        pickupDate:
          type: string
          format: date
          description: Pickup date for the shipment (ISO 8601 format, e.g. 2020-01-31)
        pickup:
          $ref: '#/components/schemas/LocationInfo'
        delivery:
          $ref: '#/components/schemas/DeliveryLocationInfo'
        lineItems:
          type: array
          items:
            $ref: '#/components/schemas/LineItemInfo'
          description: Line items in the shipment. TODO - Enforce maximums
        broker:
          $ref: '#/components/schemas/Broker'
        emergencyContact:
          $ref: '#/components/schemas/EmergencyContactDetails'
        branchId:
          type: string
          format: uuid
          description: ID of the branch from your addressbook. Only required if you want to code shipments to particular branch locations. Please contact your account manager to set this up if required
          example: b667ad2e-6a61-4fe9-bb42-c5fe8f229076
    StartShipmentQuotingResponse:
      type: object
      required:
        - shipmentId
      properties:
        shipmentId:
          type: string
          format: uuid
          example: b667ad2e-6a61-4fe9-bb42-c5fe8f229076
    StartShipmentQuotingErrorCode:
      type: string
      title: StartShipmentQuotingErrorCode
      enum:
        - InternalServerError
        - InputValidationError
        - UnsupportedEquipmentType
        - LocationTypeCombinationNotSupported
        - PickupAppointmentsNotSupported
        - PickupDateInThePast
        - PickupDateTooFarInTheFuture
        - PickupLocationMissingDistributionWarehouseBrand
        - PickupLocationMissingBusinessName
        - PickupLocationMissingCity
        - PickupLocationMissingStateOrProvinceCode
        - PickupLocationMissingPostalCode
        - PickupLocationMissingCountryCode
        - PickupLocationMissingAddressLine
        - PickupLocationMissingOpenFrom
        - PickupLocationMissingOpenUntil
        - PickupContactMissingName
        - PickupContactMissingPhoneNumber
        - DeliveryLocationMissingDistributionWarehouseBrand
        - DeliveryLocationMissingBusinessName
        - DeliveryLocationMissingCity
        - DeliveryLocationMissingStateOrProvinceCode
        - DeliveryLocationMissingPostalCode
        - DeliveryLocationMissingCountryCode
        - DeliveryLocationMissingAddressLine
        - DeliveryLocationMissingOpenFrom
        - DeliveryLocationMissingOpenUntil
        - DeliveryContactMissingName
        - DeliveryContactMissingPhoneNumber
        - LineItemNumberHandlingUnitsTooHigh
        - LineItemNumberHandlingUnitsTooLow
        - LineItemWeightTooHigh
        - LineItemWeightTooLow
        - LineItemVolumeTooHigh
        - LineItemVolumeTooLow
        - LineItemLengthTooHigh
        - LineItemLengthTooLow
        - LineItemWidthTooHigh
        - LineItemWidthTooLow
        - LineItemHeightTooHigh
        - LineItemHeightTooLow
        - LineItemMissingDangerousUnNumber
        - LineItemMissingDangerousClassification
        - LineItemMissingDangerousPackingGroup
        - LineItemMissingDangerousProperShippingName
        - LineItemMissingDangerousTechnicalName
        - LineItemMissingDangerousNumberPackages
        - LineItemMissingDangerousPackagingType
    StartShipmentQuotingError:
      type: object
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          $ref: '#/components/schemas/StartShipmentQuotingErrorCode'
        errorMessage:
          type: string
    ApiAuthenticationErrorCode:
      type: string
      title: ApiAuthenticationErrorCode
      enum:
        - MissingAuthorizationHeader
        - MissingBearerToken
        - ApiAccessNotEnabled
        - ApiKeyNotFound
        - ApiKeyInvalid
        - ApiKeyExpired
        - ApiKeyRevoked
        - ApiKeyDisabled
        - ApiKeyUnknownError
    ApiAuthenticationError:
      type: object
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          $ref: '#/components/schemas/ApiAuthenticationErrorCode'
        errorMessage:
          type: string
    ShipmentStatus:
      type: string
      title: ShipmentStatus
      enum:
        - QuoteRequested
        - Quoted
        - BookingRequested
        - BookingConfirmed
        - BookingFailed
        - InTransit
        - Delivered
        - Cancelled
        - Lost
        - OnHold
    Currency:
      type: string
      title: Currency
      enum:
        - USD
        - CAD
    EquipmentType:
      type: string
      title: EquipmentType
      enum:
        - dry_van
        - flat_bed
        - reefer
    Quote:
      title: Quote
      type: object
      description: Represents a response from a carrier to a particular quote
      properties:
        createdAt:
          type: string
          format: date-time
        quoteId:
          type: string
          format: uuid
        price:
          type: number
        currency:
          $ref: '#/components/schemas/Currency'
        carrierLogoUrl:
          type: string
        carrierDisplayName:
          type: string
        carrierIdentifier:
          type: string
        transitBusinessDays:
          type: integer
        serviceDisplayName:
          type: string
        expectedDeliveryDate:
          type: string
        quotedPickupDate:
          type: string
        taxAmount:
          type: number
        latestTransitBusinessDays:
          type: integer
        latestEstimatedDeliveryDate:
          type: string
        equipmentType:
          $ref: '#/components/schemas/EquipmentType'
        notes:
          type: string
      required:
        - quoteId
        - price
        - currency
        - carrierLogoUrl
        - carrierIdentifier
        - carrierDisplayName
        - serviceDisplayName
        - quotedPickupDate
        - taxAmount
    Hours:
      title: Hours
      type: object
      properties:
        openFrom:
          type: string
        openUntil:
          type: string
      required:
        - openFrom
        - openUntil
    LatitudeLongitude:
      title: LatitudeLongitude
      type: object
      properties:
        longitude:
          type: number
        latitude:
          type: number
    Accessorials:
      title: Accessorials
      type: array
      items:
        type: string
    Location:
      title: Location
      type: object
      properties:
        locationType:
          $ref: '#/components/schemas/LocationType'
        distributionWarehouseBrand:
          type: string
        businessName:
          type: string
        address:
          $ref: '#/components/schemas/Address'
        hours:
          $ref: '#/components/schemas/Hours'
        latitudeLongitude:
          $ref: '#/components/schemas/LatitudeLongitude'
        accessorials:
          $ref: '#/components/schemas/Accessorials'
        notes:
          type: string
      required:
        - locationType
        - businessName
        - address
        - hours
        - latitudeLongitude
        - accessorials
        - notes
    LineItem:
      title: LineItem
      type: object
      description: Represents a line item
      properties:
        handlingUnitType:
          $ref: '#/components/schemas/HandlingUnitType'
        numberHandlingUnits:
          type: integer
        description:
          type: string
        length:
          type: integer
        width:
          type: integer
        height:
          type: integer
        weightPerHandlingUnit:
          type: integer
        freightClass:
          type: string
        temperatureHandling:
          $ref: '#/components/schemas/TemperatureHandling'
        isDangerous:
          type: boolean
        nmfcItemNumber:
          type: string
        isStackable:
          type: boolean
        lineItemId:
          type: string
          format: uuid
        dangerousUnNumber:
          type: string
        dangerousClassification:
          type: string
        dangerousPackingGroup:
          type: string
        dangerousProperShippingName:
          type: string
        dangerousTechnicalName:
          type: string
        dangerousNumberPackages:
          type: integer
        dangerousPackagingType:
          type: string
      required:
        - numberHandlingUnits
        - description
        - length
        - width
        - height
        - freightClass
        - weightPerHandlingUnit
        - handlingUnitType
        - temperatureHandling
        - isDangerous
        - isStackable
        - lineItemId
    GetShipmentResponse:
      type: object
      required:
        - shipmentId
        - shipmentStatus
        - pickupDate
        - pickupLocation
        - pickupContact
        - deliveryLocation
        - deliveryContact
        - lineItems
        - quotes
        - needsCustomsDocs
      properties:
        shipmentId:
          type: string
          format: uuid
          example: b667ad2e-6a61-4fe9-bb42-c5fe8f229076
        shipmentStatus:
          $ref: '#/components/schemas/ShipmentStatus'
        pickupDate:
          type: string
        bookedAt:
          type: string
        bookedByEmail:
          type: string
        selectedQuote:
          $ref: '#/components/schemas/Quote'
        pickupLocation:
          $ref: '#/components/schemas/Location'
        pickupContact:
          $ref: '#/components/schemas/Contact'
        pickupReferenceNumber:
          type: string
        pickupDeadline:
          type: string
        pickupBoothNumber:
          type: string
        deliveryLocation:
          $ref: '#/components/schemas/Location'
        deliveryContact:
          $ref: '#/components/schemas/Contact'
        deliveryReferenceNumber:
          type: string
        deliveryDeadline:
          type: string
        deliveryBoothNumber:
          type: string
        lineItems:
          type: array
          items:
            $ref: '#/components/schemas/LineItem'
        quotes:
          description: An array of quotes associated with this shipment, sorted by the `createdAt` timestamp in ascending order.
          type: array
          items:
            $ref: '#/components/schemas/Quote'
        broker:
          $ref: '#/components/schemas/Broker'
        needsCustomsDocs:
          type: boolean
        emergencyContactDetails:
          $ref: '#/components/schemas/EmergencyContactDetails'
        expectedDeliveryDate:
          type: string
        actualDeliveryDate:
          type: string
        actualDeliveryTime:
          type: string
        addInsuranceToShipment:
          type: boolean
        insuranceAmount:
          type: number
    ApiForbiddenErrorCode:
      type: string
      title: ApiForbiddenErrorCode
      enum:
        - AccessDenied
    ApiForbiddenError:
      type: object
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          $ref: '#/components/schemas/ApiForbiddenErrorCode'
        errorMessage:
          type: string
    StartShipmentBookingRequest:
      type: object
      required:
        - quoteId
        - pickupDate
      properties:
        quoteId:
          type: string
          format: uuid
          example: d4eeeb7a-5f6d-4424-bb6b-e275237d1dde
          description: The quoteId of the quote to book
        pickupDate:
          type: string
          format: date
          description: Pickup date for the shipment (ISO 8601 format, e.g. 2020-01-31)
          example: '2025-01-31'
    StartShipmentBookingErrorCode:
      type: string
      title: StartShipmentBookingErrorCode
      enum:
        - ShipmentAlreadyBooked
        - InternalServerError
        - InputValidationError
        - UnsupportedEquipmentType
    StartShipmentBookingError:
      type: object
      required:
        - errorCode
        - errorMessage
      properties:
        errorCode:
          $ref: '#/components/schemas/StartShipmentBookingErrorCode'
        errorMessage:
          type: string
  responses:
    '401':
      description: Unauthorized
      headers:
        X-Trace-Id:
          $ref: '#/components/headers/TraceId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiAuthenticationError'
    '403':
      description: Forbidden
      headers:
        X-Trace-Id:
          $ref: '#/components/headers/TraceId'
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiForbiddenError'
    '404':
      description: Shipment not found
      headers:
        X-Trace-Id:
          $ref: '#/components/headers/TraceId'
    '429':
      description: Rate limit exceeded
      headers:
        X-Trace-Id:
          $ref: '#/components/headers/TraceId'
    '500':
      description: Internal server error. <NAME_EMAIL> and provide the value of the X-Trace-Id header
      headers:
        X-Trace-Id:
          $ref: '#/components/headers/TraceId'
x-tagGroups:
  - name: Quoting & Booking
    tags:
      - shipmentsApi
