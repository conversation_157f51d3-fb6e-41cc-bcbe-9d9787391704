<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold mb-6">Modal Close Button Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <button id="openModalBtn" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Open Test Modal
            </button>
        </div>
    </div>

    <script>
        document.getElementById('openModalBtn').addEventListener('click', () => {
            showTestModal();
        });

        function showTestModal() {
            const escapeHtml = (text) => {
                if (typeof text !== 'string') return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            };

            const formatCurrency = (amount) => {
                if (typeof amount !== 'number') return '$0.00';
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(amount);
            };

            // Create test order data
            const order = {
                id: "test-order",
                OrderNbr: "TEST001",
                Status: "Open",
                OrderType: "SO",
                OrderTotal: 27700.00,
                OrderedQty: 2.0,
                LineItemCount: 2,
                TotalBOMItems: 1,
                CreatedDate: { year: 2023, month: 7, day: 7 },
                ShippingDate: { year: 2023, month: 7, day: 15 },
                LineItems: [
                    {
                        id: "line1",
                        InventoryID: "331SDS",
                        IsFromBOM: true,
                        BOMInventoryID: "331SDS",
                        LineDescription: "H2S Analyzer, Dual Sensing, Class 1, Div. 2, Second Gen",
                        Quantity: 1.0,
                        UnitPrice: 22540.0,
                        UOM: "EACH",
                        ExtAmount: 22540.0,
                        BOMItems: [
                            {
                                id: "bom1",
                                InventoryID: "331SDS",
                                ProductionNbr: "SYS000652"
                            }
                        ],
                        BOMCount: 1
                    },
                    {
                        id: "line2",
                        InventoryID: "1100399",
                        IsFromBOM: false,
                        OriginalInventoryID: "1100399",
                        LineDescription: "Probe, direct drive, A+ Genie 760, SS, 1\" NPT process connection",
                        Quantity: 1.0,
                        UnitPrice: 5160.0,
                        UOM: "EACH",
                        ExtAmount: 5160.0,
                        BOMItems: [],
                        BOMCount: 0
                    }
                ]
            };

            const modalHtml = `
                <div id="orderDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div class="relative top-10 mx-auto p-6 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
                        <div class="flex items-center justify-between pb-3 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">
                                Sales Order Details - ${escapeHtml(order.OrderNbr)}
                            </h3>
                            <button id="closeOrderModal" type="button" class="text-gray-400 hover:text-gray-600 p-2 rounded-md hover:bg-gray-100" onclick="if(window.closeOrderModal) window.closeOrderModal(); else document.getElementById('orderDetailsModal').remove();">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>

                        <div class="mt-4">
                            <!-- Order Summary -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-500 mb-2">Order Information</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Order #:</span>
                                            <span class="text-sm font-medium text-gray-900">${escapeHtml(order.OrderNbr)}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Status:</span>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                                ${escapeHtml(order.Status)}
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Type:</span>
                                            <span class="text-sm font-medium text-gray-900">${escapeHtml(order.OrderType)}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-500 mb-2">Quantities & Totals</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Total Qty:</span>
                                            <span class="text-sm font-medium text-gray-900">${order.OrderedQty.toFixed(2)}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Line Items:</span>
                                            <span class="text-sm font-medium text-gray-900">${order.LineItemCount}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Order Total:</span>
                                            <span class="text-sm font-bold text-gray-900">${formatCurrency(order.OrderTotal)}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-500 mb-2">Additional Info</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">BOM Items:</span>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                <i class="fas fa-cogs mr-1"></i>
                                                ${order.TotalBOMItems}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Line Items Table -->
                            <div class="mb-4">
                                <h4 class="text-lg font-medium text-gray-900 mb-3">Line Items</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Price</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Extended</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Production</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            ${order.LineItems.map(item => `
                                                <tr class="hover:bg-gray-50">
                                                    <td class="px-4 py-3 text-sm font-medium text-gray-900">
                                                        <div class="flex items-center space-x-2">
                                                            <span>${escapeHtml(item.InventoryID)}</span>
                                                            ${item.IsFromBOM ? `
                                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">
                                                                    <i class="fas fa-cogs mr-1"></i>
                                                                    BOM ID
                                                                </span>
                                                            ` : item.OriginalInventoryID ? `
                                                                <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                                    <i class="fas fa-box mr-1"></i>
                                                                    Inventory
                                                                </span>
                                                            ` : ''}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        <div class="max-w-xs truncate" title="${escapeHtml(item.LineDescription)}">
                                                            ${escapeHtml(item.LineDescription)}
                                                        </div>
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${item.Quantity.toFixed(2)} ${escapeHtml(item.UOM)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${formatCurrency(item.UnitPrice)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${formatCurrency(item.ExtAmount)}
                                                    </td>
                                                    <td class="px-4 py-3 text-sm text-gray-900">
                                                        ${item.BOMCount > 0 ? `
                                                            <div class="space-y-1">
                                                                ${item.BOMItems.map(bom => `
                                                                    <div class="flex items-center space-x-2">
                                                                        ${bom.ProductionNbr ? `
                                                                            <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                                                <i class="fas fa-industry mr-1"></i>
                                                                                ${escapeHtml(bom.ProductionNbr)}
                                                                            </span>
                                                                        ` : `
                                                                            <span class="text-xs text-gray-500">No Production #</span>
                                                                        `}
                                                                    </div>
                                                                `).join('')}
                                                            </div>
                                                        ` : `
                                                            <span class="text-xs text-gray-500">No BOM</span>
                                                        `}
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end pt-4 border-t border-gray-200">
                            <button id="closeOrderModalBtn" type="button" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500" onclick="if(window.closeOrderModal) window.closeOrderModal(); else document.getElementById('orderDetailsModal').remove();">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to DOM
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Simple and reliable close function
            window.closeOrderModal = () => {
                const modal = document.getElementById('orderDetailsModal');
                if (modal) {
                    modal.remove();
                    console.log('Modal closed and removed from DOM');
                    // Clean up the global function
                    delete window.closeOrderModal;
                    // Remove escape listener
                    document.removeEventListener('keydown', window.orderModalEscapeHandler);
                    delete window.orderModalEscapeHandler;
                }
            };

            // Escape key handler
            window.orderModalEscapeHandler = (e) => {
                if (e.key === 'Escape') {
                    console.log('Escape key pressed');
                    window.closeOrderModal();
                }
            };

            // Set up event listeners immediately
            const modal = document.getElementById('orderDetailsModal');
            const closeHeaderBtn = document.getElementById('closeOrderModal');
            const closeFooterBtn = document.getElementById('closeOrderModalBtn');

            console.log('Modal elements found:', {
                modal: !!modal,
                closeHeaderBtn: !!closeHeaderBtn,
                closeFooterBtn: !!closeFooterBtn
            });

            // Header close button
            if (closeHeaderBtn) {
                closeHeaderBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Header close button clicked');
                    window.closeOrderModal();
                });
            }

            // Footer close button - multiple approaches
            if (closeFooterBtn) {
                // Method 1: addEventListener
                closeFooterBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Footer close button clicked (addEventListener)');
                    window.closeOrderModal();
                });

                // Method 2: onclick property
                closeFooterBtn.onclick = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Footer close button clicked (onclick)');
                    window.closeOrderModal();
                };

                console.log('Footer close button listeners added');
            }

            // Backdrop click
            if (modal) {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        console.log('Backdrop clicked');
                        window.closeOrderModal();
                    }
                });
            }

            // Escape key
            document.addEventListener('keydown', window.orderModalEscapeHandler);

            console.log('All modal event listeners set up');
        }
    </script>
</body>
</html>
