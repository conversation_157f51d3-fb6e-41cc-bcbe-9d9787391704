/* Customer map markers */
.customer-marker {
  width: 30px;
  height: 30px;
  cursor: pointer;
}

.marker-inner {
  width: 20px;
  height: 20px;
  background: #3182CE;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
  position: relative;
}

.marker-inner::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
}

.marker-green {
  background: #48BB78;
}

.marker-red {
  background: #F56565;
}

.marker-yellow {
  background: #ECC94B;
}

.marker-gray {
  background: #A0AEC0;
}

/* Customer popup */
.customer-popup {
  max-width: 300px;
}

.customer-popup .mapboxgl-popup-content {
  padding: 15px;
  border-radius: 8px;
}

.customer-popup-content h3 {
  margin-top: 0;
  margin-bottom: 4px;
}

.dark .mapboxgl-popup-content {
  background-color: #2D3748;
  color: #E2E8F0;
}

.dark .mapboxgl-popup-tip {
  border-top-color: #2D3748;
  border-bottom-color: #2D3748;
}

.dark .mapboxgl-popup-close-button {
  color: #E2E8F0;
}

/* Geocoder control */
.mapboxgl-ctrl-geocoder {
  min-width: 250px;
  font-size: 14px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.mapboxgl-ctrl-geocoder--input {
  height: 36px;
  width: 100%;
  padding: 6px 35px 6px 10px;
  font-size: 14px;
  border: none;
  border-radius: 4px;
  box-shadow: none;
  background-color: white;
}

.dark .mapboxgl-ctrl-geocoder--input {
  background-color: #4A5568;
  color: white;
}

/* Map container adjustments */
#customerMapContainer {
  width: 100%;
  height: 500px;
  position: relative;
}

/* For when the map is loading */
#mapLoadingIndicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
} 