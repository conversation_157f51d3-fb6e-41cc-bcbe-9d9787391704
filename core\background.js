// Background script for Envent Bridge
console.log('Background script loaded');

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Background script received message:', request);
  
  // Handle request to load master parts data
  if (request.action === 'loadMasterPartsData') {
    loadMasterPartsJSON()
      .then(data => {
        console.log('Background: Successfully loaded parts data, found', data.length, 'parts');
        sendResponse({ success: true, data: data });
      })
      .catch(error => {
        console.error('Background: Error loading parts data:', error);
        sendResponse({ success: false, error: error.message });
      });
    
    // Return true to indicate we'll respond asynchronously
    return true;
  }
});

// Function to load the master parts JSON
async function loadMasterPartsJSON() {
  try {
    // Try multiple locations
    const possiblePaths = [
      chrome.runtime.getURL('masterparts.json'),
      chrome.runtime.getURL('json/masterparts.json')
    ];
    
    // Try each path in order
    for (const path of possiblePaths) {
      try {
        console.log('Background: Attempting to load from', path);
        const response = await fetch(path);
        
        if (response.ok) {
          const data = await response.json();
          console.log(`Background: Successfully loaded ${data.length} parts from ${path}`);
          
          // Cache in chrome.storage for future use
          chrome.storage.local.set({ 'masterparts': data }, function() {
            console.log('Background: Cached parts data in storage');
          });
          
          return data;
        }
      } catch (err) {
        console.warn(`Background: Error fetching from ${path}:`, err);
      }
    }
    
    // Check if data is in chrome.storage
    console.log('Background: Checking storage for cached parts data');
    return new Promise((resolve, reject) => {
      chrome.storage.local.get('masterparts', (result) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
          return;
        }
        
        if (result && result.masterparts && result.masterparts.length > 0) {
          console.log(`Background: Found ${result.masterparts.length} parts in storage cache`);
          resolve(result.masterparts);
        } else {
          reject(new Error('Parts data not found in any location'));
        }
      });
    });
  } catch (error) {
    console.error('Background: Error in loadMasterPartsJSON:', error);
    throw error;
  }
}