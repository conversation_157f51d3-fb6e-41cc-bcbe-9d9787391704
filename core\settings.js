// Merged Settings Module - Contains both dark mode functionality and settings panel

// Section 1: Dark Mode Functions
function toggleDarkMode() {
  console.log("Toggle dark mode called")
  const body = document.body
  body.classList.toggle("dark-mode")

  // Update dark mode icon
  const darkModeIcon = document.querySelector("#darkModeButton i")
  if (darkModeIcon) {
    if (body.classList.contains("dark-mode")) {
      darkModeIcon.classList.remove("fa-moon")
      darkModeIcon.classList.add("fa-sun")
    } else {
      darkModeIcon.classList.remove("fa-sun")
      darkModeIcon.classList.add("fa-moon")
    }
  }

  // Toggle the active class on the button
  const darkModeButton = document.getElementById("darkModeButton")
  if (darkModeButton) {
    darkModeButton.classList.toggle("active")
  }

  // Save dark mode preference
  saveDarkModePreference()
}

function saveDarkModePreference() {
  const isDarkMode = document.body.classList.contains("dark-mode")
  if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
    try {
      if (chrome && chrome.storage && chrome.storage.local) {
        chrome.storage.local.set({ darkMode: isDarkMode })
      } else {
        console.warn("Chrome storage API not available. Using localStorage instead.")
        localStorage.setItem("darkMode", isDarkMode)
      }
    } catch (e) {
      console.warn("Chrome storage API not available. Using localStorage instead.", e)
      localStorage.setItem("darkMode", isDarkMode)
    }
  } else {
    localStorage.setItem("darkMode", isDarkMode)
  }
}

function loadDarkModePreference() {
  if (typeof chrome !== "undefined" && typeof chrome.storage !== "undefined") {
    try {
      if (chrome && chrome.storage && chrome.storage.local) {
        chrome.storage.local.get(["darkMode"], (result) => {
          if (result.darkMode) {
            document.body.classList.add("dark-mode")
            updateDarkModeIcon(true)
          }
        })
      } else {
        console.warn("Chrome storage API not available. Using localStorage instead.")
        const isDarkMode = localStorage.getItem("darkMode") === "true"
        if (isDarkMode) {
          document.body.classList.add("dark-mode")
          updateDarkModeIcon(true)
        }
      }
    } catch (e) {
      console.warn("Chrome storage API not available. Using localStorage instead.", e)
      const isDarkMode = localStorage.getItem("darkMode") === "true"
      if (isDarkMode) {
        document.body.classList.add("dark-mode")
        updateDarkModeIcon(true)
      }
    }
  } else {
    const isDarkMode = localStorage.getItem("darkMode") === "true"
    if (isDarkMode) {
      document.body.classList.add("dark-mode")
      updateDarkModeIcon(true)
    }
  }
}

function updateDarkModeIcon(isDarkMode) {
  const darkModeIcon = document.querySelector("#darkModeButton i")
  if (darkModeIcon) {
    if (isDarkMode) {
      darkModeIcon.classList.remove("fa-moon")
      darkModeIcon.classList.add("fa-sun")
    } else {
      darkModeIcon.classList.remove("fa-sun")
      darkModeIcon.classList.add("fa-moon")
    }
  } else {
    console.warn("Dark mode icon not found")
  }
}

// Section 2: Main Settings Component
class MainSettingsComponent {
  constructor(container) {
    this.container = container;
    this.settings = {
      // Existing settings
      theme: 'light',
      fontSize: 'medium',
      language: 'en',
      notifications: true,
      autoSave: true,
      compactView: false,
      defaultView: 'dashboard',
      exportFormat: 'csv',
      dateFormat: 'MM/DD/YYYY',
      refreshInterval: 5,
      advancedMode: false,
      developerOptions: false,
      
      // Dashboard Customization
      startPage: 'dashboard',
      widgetArrangement: 'fixed',
      visibleWidgets: {
        recentActivity: true,
        notifications: true,
        quickStats: true,
        charts: true
      },
      defaultChartType: 'bar',
      refreshIntervals: {
        dashboard: 5,
        analytics: 10,
        inventory: 15
      },
      
      // Notifications & Alerts
      soundAlerts: false,
      browserNotifications: true,
      alertThresholds: {
        inventory: 10,
        partStock: 5
      },
      emailNotifications: false,
      emailAddress: '',
      customAlertCategories: {
        system: true,
        inventory: true,
        orders: true,
        reports: true
      },
      
      // Accessibility Options
      highContrast: false,
      textScaling: 1,
      keyboardShortcuts: true,
      reducedMotion: false,
      screenReader: false
    };
    this.defaultSettings = { ...this.settings };
    this.initialized = false;
  }

  async init() {
    if (!this.initialized) {
      await this.loadSettings();
      this.initialized = true;
    }
    this.render();
    this.setupEventListeners();
    this.applySettings();
  }

  async loadSettings() {
    try {
      console.log('Loading settings...');
      // Try to load settings from localStorage
      const savedSettings = localStorage.getItem('enventBridgeSettings');
      
      if (savedSettings) {
        console.log('Settings found in localStorage');
        try {
          const parsedSettings = JSON.parse(savedSettings);
          this.settings = { ...this.settings, ...parsedSettings };
          console.log('Settings loaded successfully:', this.settings);
        } catch (parseError) {
          console.error('Error parsing settings JSON:', parseError);
          localStorage.removeItem('enventBridgeSettings');
        }
      } else if (typeof chrome !== 'undefined' && chrome.storage) {
        // If chrome.storage is available (extension environment)
        console.log('Attempting to load settings from chrome.storage...');
        return new Promise((resolve) => {
          chrome.storage.local.get(['enventBridgeSettings'], (result) => {
            if (result.enventBridgeSettings) {
              this.settings = { ...this.settings, ...result.enventBridgeSettings };
              console.log('Settings loaded from chrome.storage:', this.settings);
            } else {
              console.log('No settings found in chrome.storage');
            }
            resolve();
          });
        });
      } else {
        console.log('No saved settings found, using defaults');
      }
      
      // Load dark mode separately (since it's managed differently)
      if (typeof chrome !== 'undefined' && chrome.storage) {
        chrome.storage.local.get(['darkMode'], (result) => {
          if (result.darkMode !== undefined) {
            this.settings.theme = result.darkMode ? 'dark' : 'light';
          }
        });
      } else {
        const isDarkMode = localStorage.getItem('darkMode') === 'true';
        if (isDarkMode !== null) {
          this.settings.theme = isDarkMode ? 'dark' : 'light';
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      // If there's an error, use default settings
      this.settings = { ...this.defaultSettings };
    }
  }

  async saveSettings() {
    try {
      console.log('Saving settings to localStorage:', this.settings);
      // Save to localStorage
      localStorage.setItem('enventBridgeSettings', JSON.stringify(this.settings));
      
      // If in extension environment, also save to chrome.storage
      if (typeof chrome !== 'undefined' && chrome.storage) {
        console.log('Also saving to chrome.storage');
        return new Promise((resolve) => {
          chrome.storage.local.set({ enventBridgeSettings: this.settings }, () => {
            console.log('Settings saved to chrome.storage');
            resolve(true);
          });
        });
      }
      
      // Update dark mode if it changed
      const isDarkMode = this.settings.theme === 'dark';
      if (document.body.classList.contains('dark-mode') !== isDarkMode) {
        if (isDarkMode) {
          document.body.classList.add('dark-mode');
          updateDarkModeIcon(true);
        } else {
          document.body.classList.remove('dark-mode');
          updateDarkModeIcon(false);
        }
        saveDarkModePreference();
      }
      
      this.showNotification('Settings saved successfully', 'success');
      return true;
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showNotification('Failed to save settings', 'error');
      return false;
    }
  }

  render() {
    this.container.innerHTML = `
      <div class="p-4 max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-800">Settings</h1>
          <div class="flex space-x-2">
            <button id="resetSettingsBtn" class="px-4 py-2 border border-gray-300 rounded shadow hover:bg-gray-100">
              Reset to Default
            </button>
            <button id="saveSettingsBtn" class="px-4 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700">
              Save Changes
            </button>
          </div>
        </div>

        <!-- Appearance Settings Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
            </svg>
            Appearance
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Theme</label>
              <div class="flex space-x-4">
                <label class="inline-flex items-center">
                  <input type="radio" name="theme" value="light" class="form-radio" ${this.settings.theme === 'light' ? 'checked' : ''}>
                  <span class="ml-2">Light</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="radio" name="theme" value="dark" class="form-radio" ${this.settings.theme === 'dark' ? 'checked' : ''}>
                  <span class="ml-2">Dark</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="radio" name="theme" value="system" class="form-radio" ${this.settings.theme === 'system' ? 'checked' : ''}>
                  <span class="ml-2">System Default</span>
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
              <select name="fontSize" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="small" ${this.settings.fontSize === 'small' ? 'selected' : ''}>Small</option>
                <option value="medium" ${this.settings.fontSize === 'medium' ? 'selected' : ''}>Medium</option>
                <option value="large" ${this.settings.fontSize === 'large' ? 'selected' : ''}>Large</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Compact View</label>
              <label class="inline-flex items-center">
                <input type="checkbox" name="compactView" class="form-checkbox" ${this.settings.compactView ? 'checked' : ''}>
                <span class="ml-2">Enable compact view for tables and lists</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Date Format</label>
              <select name="dateFormat" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="MM/DD/YYYY" ${this.settings.dateFormat === 'MM/DD/YYYY' ? 'selected' : ''}>MM/DD/YYYY</option>
                <option value="DD/MM/YYYY" ${this.settings.dateFormat === 'DD/MM/YYYY' ? 'selected' : ''}>DD/MM/YYYY</option>
                <option value="YYYY-MM-DD" ${this.settings.dateFormat === 'YYYY-MM-DD' ? 'selected' : ''}>YYYY-MM-DD</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Dashboard Customization Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
            </svg>
            Dashboard Customization
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Default Start Page</label>
              <select name="startPage" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="dashboard" ${this.settings.startPage === 'dashboard' ? 'selected' : ''}>Dashboard</option>
                <option value="analytics" ${this.settings.startPage === 'analytics' ? 'selected' : ''}>Analytics</option>
                <option value="partmaster" ${this.settings.startPage === 'partmaster' ? 'selected' : ''}>Part Master</option>
                <option value="assistant" ${this.settings.startPage === 'assistant' ? 'selected' : ''}>Assistant</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Widget Arrangement</label>
              <select name="widgetArrangement" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="fixed" ${this.settings.widgetArrangement === 'fixed' ? 'selected' : ''}>Fixed</option>
                <option value="customizable" ${this.settings.widgetArrangement === 'customizable' ? 'selected' : ''}>Customizable</option>
              </select>
            </div>
            
            <div class="form-group col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Widget Visibility</label>
              <div class="grid grid-cols-2 gap-2">
                <label class="inline-flex items-center">
                  <input type="checkbox" name="visibleWidgets.recentActivity" class="form-checkbox" ${this.settings.visibleWidgets.recentActivity ? 'checked' : ''}>
                  <span class="ml-2">Recent Activity</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="visibleWidgets.notifications" class="form-checkbox" ${this.settings.visibleWidgets.notifications ? 'checked' : ''}>
                  <span class="ml-2">Notifications</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="visibleWidgets.quickStats" class="form-checkbox" ${this.settings.visibleWidgets.quickStats ? 'checked' : ''}>
                  <span class="ml-2">Quick Stats</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="visibleWidgets.charts" class="form-checkbox" ${this.settings.visibleWidgets.charts ? 'checked' : ''}>
                  <span class="ml-2">Charts</span>
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Default Chart Type</label>
              <select name="defaultChartType" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="bar" ${this.settings.defaultChartType === 'bar' ? 'selected' : ''}>Bar Chart</option>
                <option value="line" ${this.settings.defaultChartType === 'line' ? 'selected' : ''}>Line Chart</option>
                <option value="pie" ${this.settings.defaultChartType === 'pie' ? 'selected' : ''}>Pie Chart</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Data Refresh Intervals (Minutes)</label>
              <div class="grid grid-cols-1 gap-2">
                <div class="flex items-center">
                  <span class="w-24 text-sm">Dashboard:</span>
                  <select name="refreshIntervals.dashboard" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="1" ${this.settings.refreshIntervals.dashboard === 1 ? 'selected' : ''}>1 minute</option>
                    <option value="5" ${this.settings.refreshIntervals.dashboard === 5 ? 'selected' : ''}>5 minutes</option>
                    <option value="15" ${this.settings.refreshIntervals.dashboard === 15 ? 'selected' : ''}>15 minutes</option>
                  </select>
                </div>
                <div class="flex items-center">
                  <span class="w-24 text-sm">Analytics:</span>
                  <select name="refreshIntervals.analytics" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="5" ${this.settings.refreshIntervals.analytics === 5 ? 'selected' : ''}>5 minutes</option>
                    <option value="10" ${this.settings.refreshIntervals.analytics === 10 ? 'selected' : ''}>10 minutes</option>
                    <option value="30" ${this.settings.refreshIntervals.analytics === 30 ? 'selected' : ''}>30 minutes</option>
                  </select>
                </div>
                <div class="flex items-center">
                  <span class="w-24 text-sm">Inventory:</span>
                  <select name="refreshIntervals.inventory" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="5" ${this.settings.refreshIntervals.inventory === 5 ? 'selected' : ''}>5 minutes</option>
                    <option value="15" ${this.settings.refreshIntervals.inventory === 15 ? 'selected' : ''}>15 minutes</option>
                    <option value="60" ${this.settings.refreshIntervals.inventory === 60 ? 'selected' : ''}>60 minutes</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Notifications & Alerts Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            Notifications & Alerts
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="soundAlerts" class="form-checkbox" ${this.settings.soundAlerts ? 'checked' : ''}>
                <span class="ml-2">Sound Alerts for Critical Notifications</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="browserNotifications" class="form-checkbox" ${this.settings.browserNotifications ? 'checked' : ''}>
                <span class="ml-2">Browser Notifications</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Alert Thresholds</label>
              <div class="flex items-center mb-2">
                <span class="w-28 text-sm">Inventory Level:</span>
                <input type="number" name="alertThresholds.inventory" min="0" max="100" class="w-20 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="${this.settings.alertThresholds.inventory}">
                <span class="ml-2 text-sm text-gray-600">items</span>
              </div>
              <div class="flex items-center">
                <span class="w-28 text-sm">Part Stock:</span>
                <input type="number" name="alertThresholds.partStock" min="0" max="100" class="w-20 border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="${this.settings.alertThresholds.partStock}">
                <span class="ml-2 text-sm text-gray-600">items</span>
              </div>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center mb-2">
                <input type="checkbox" name="emailNotifications" class="form-checkbox" ${this.settings.emailNotifications ? 'checked' : ''}>
                <span class="ml-2">Email Notifications</span>
              </label>
              <input type="email" name="emailAddress" placeholder="<EMAIL>" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" value="${this.settings.emailAddress}">
            </div>
            
            <div class="form-group col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Alert Categories</label>
              <div class="grid grid-cols-2 gap-2">
                <label class="inline-flex items-center">
                  <input type="checkbox" name="customAlertCategories.system" class="form-checkbox" ${this.settings.customAlertCategories.system ? 'checked' : ''}>
                  <span class="ml-2">System Alerts</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="customAlertCategories.inventory" class="form-checkbox" ${this.settings.customAlertCategories.inventory ? 'checked' : ''}>
                  <span class="ml-2">Inventory Alerts</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="customAlertCategories.orders" class="form-checkbox" ${this.settings.customAlertCategories.orders ? 'checked' : ''}>
                  <span class="ml-2">Order Alerts</span>
                </label>
                <label class="inline-flex items-center">
                  <input type="checkbox" name="customAlertCategories.reports" class="form-checkbox" ${this.settings.customAlertCategories.reports ? 'checked' : ''}>
                  <span class="ml-2">Report Alerts</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Accessibility Options Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
            Accessibility Options
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="highContrast" class="form-checkbox" ${this.settings.highContrast ? 'checked' : ''}>
                <span class="ml-2">High Contrast Mode</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Text Scaling</label>
              <div class="flex items-center">
                <input type="range" name="textScaling" min="0.8" max="1.5" step="0.1" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" value="${this.settings.textScaling}">
                <span class="ml-2 text-sm" id="textScalingValue">${this.settings.textScaling}x</span>
              </div>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="keyboardShortcuts" class="form-checkbox" ${this.settings.keyboardShortcuts ? 'checked' : ''}>
                <span class="ml-2">Enable Keyboard Shortcuts</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="reducedMotion" class="form-checkbox" ${this.settings.reducedMotion ? 'checked' : ''}>
                <span class="ml-2">Reduced Motion</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="screenReader" class="form-checkbox" ${this.settings.screenReader ? 'checked' : ''}>
                <span class="ml-2">Screen Reader Optimizations</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Language & Region Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
            </svg>
            Language & Region
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Language</label>
              <select name="language" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="en" ${this.settings.language === 'en' ? 'selected' : ''}>English</option>
                <option value="es" ${this.settings.language === 'es' ? 'selected' : ''}>Spanish</option>
                <option value="fr" ${this.settings.language === 'fr' ? 'selected' : ''}>French</option>
                <option value="de" ${this.settings.language === 'de' ? 'selected' : ''}>German</option>
                <option value="zh" ${this.settings.language === 'zh' ? 'selected' : ''}>Chinese</option>
              </select>
            </div>
          </div>
        </div>

        <!-- General Settings Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            General Settings
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Default View</label>
              <select name="defaultView" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="dashboard" ${this.settings.defaultView === 'dashboard' ? 'selected' : ''}>Dashboard</option>
                <option value="analytics" ${this.settings.defaultView === 'analytics' ? 'selected' : ''}>Analytics</option>
                <option value="masterpartss" ${this.settings.defaultView === 'masterpartss' ? 'selected' : ''}>Master Parts</option>
                <option value="assistant" ${this.settings.defaultView === 'assistant' ? 'selected' : ''}>Assistant</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Default Export Format</label>
              <select name="exportFormat" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="csv" ${this.settings.exportFormat === 'csv' ? 'selected' : ''}>CSV</option>
                <option value="json" ${this.settings.exportFormat === 'json' ? 'selected' : ''}>JSON</option>
                <option value="excel" ${this.settings.exportFormat === 'excel' ? 'selected' : ''}>Excel</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Auto-Save Changes</label>
              <label class="inline-flex items-center">
                <input type="checkbox" name="autoSave" class="form-checkbox" ${this.settings.autoSave ? 'checked' : ''}>
                <span class="ml-2">Automatically save changes</span>
              </label>
            </div>
            
            <div class="form-group">
              <label class="block text-sm font-medium text-gray-700 mb-1">Dashboard Refresh Interval (minutes)</label>
              <select name="refreshInterval" class="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="1" ${this.settings.refreshInterval === 1 ? 'selected' : ''}>1 minute</option>
                <option value="5" ${this.settings.refreshInterval === 5 ? 'selected' : ''}>5 minutes</option>
                <option value="15" ${this.settings.refreshInterval === 15 ? 'selected' : ''}>15 minutes</option>
                <option value="30" ${this.settings.refreshInterval === 30 ? 'selected' : ''}>30 minutes</option>
                <option value="60" ${this.settings.refreshInterval === 60 ? 'selected' : ''}>60 minutes</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Advanced Options Section -->
        <div class="bg-white rounded-lg shadow p-6 mb-6">
          <h2 class="text-lg font-semibold mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
            </svg>
            Advanced Options
          </h2>
          <div class="grid grid-cols-1 gap-4">
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="advancedMode" class="form-checkbox" ${this.settings.advancedMode ? 'checked' : ''}>
                <span class="ml-2">Enable Advanced Mode</span>
              </label>
              <p class="text-sm text-gray-500 ml-6">Unlocks additional features and customization options</p>
            </div>
            
            <div class="form-group">
              <label class="inline-flex items-center">
                <input type="checkbox" name="developerOptions" class="form-checkbox" ${this.settings.developerOptions ? 'checked' : ''}>
                <span class="ml-2">Developer Options</span>
              </label>
              <p class="text-sm text-gray-500 ml-6">Show developer tools and debugging information</p>
            </div>
            
            <div class="mt-4">
              <button id="exportSettingsBtn" class="text-blue-600 hover:text-blue-800 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export Settings
              </button>
              <button id="importSettingsBtn" class="text-blue-600 hover:text-blue-800 flex items-center mt-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
                Import Settings
              </button>
            </div>
          </div>
        </div>

        <div class="mt-8 text-center text-sm text-gray-500">
          <p>Envent Bridge Dashboard - Version 1.0.0</p>
          <p class="mt-1">© 2025 Envent Engineering Ltd. All rights reserved.</p>
        </div>
      </div>

      <!-- Notification Container -->
      <div id="settingsNotificationContainer" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>
    `;
    
    // Add text scaling value update
    const textScalingSlider = this.container.querySelector('input[name="textScaling"]');
    const textScalingValue = this.container.querySelector('#textScalingValue');
    if (textScalingSlider && textScalingValue) {
      textScalingSlider.addEventListener('input', () => {
        textScalingValue.textContent = `${textScalingSlider.value}x`;
      });
    }
  }

  setupEventListeners() {
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');
    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    const exportSettingsBtn = document.getElementById('exportSettingsBtn');
    const importSettingsBtn = document.getElementById('importSettingsBtn');
    const themeRadios = document.querySelectorAll('input[name="theme"]');
    
    // Add event delegation for the form to capture all inputs
    this.container.addEventListener('change', (e) => {
      const target = e.target;
      if (target.tagName === 'INPUT' || target.tagName === 'SELECT') {
        if (this.settings.autoSave) {
          this.updateSettingsFromForm();
          this.saveSettings();
          this.applySettings();
        }
      }
    });

    if (saveSettingsBtn) {
      saveSettingsBtn.addEventListener('click', () => this.handleSaveSettings());
    }

    if (resetSettingsBtn) {
      resetSettingsBtn.addEventListener('click', () => this.handleResetSettings());
    }

    if (exportSettingsBtn) {
      exportSettingsBtn.addEventListener('click', () => this.exportSettings());
    }

    if (importSettingsBtn) {
      importSettingsBtn.addEventListener('click', () => this.importSettings());
    }

    // Apply theme immediately when changed
    themeRadios.forEach(radio => {
      radio.addEventListener('change', (e) => {
        if (e.target.checked) {
          this.applyTheme(e.target.value);
        }
      });
    });
  }

  updateSettingsFromForm() {
    // Get all relevant inputs and update settings for original settings
    const themeRadio = document.querySelector('input[name="theme"]:checked');
    const fontSizeSelect = document.querySelector('select[name="fontSize"]');
    const languageSelect = document.querySelector('select[name="language"]');
    const notificationsCheckbox = document.querySelector('input[name="notifications"]');
    const autoSaveCheckbox = document.querySelector('input[name="autoSave"]');
    const compactViewCheckbox = document.querySelector('input[name="compactView"]');
    const defaultViewSelect = document.querySelector('select[name="defaultView"]');
    const exportFormatSelect = document.querySelector('select[name="exportFormat"]');
    const dateFormatSelect = document.querySelector('select[name="dateFormat"]');
    const refreshIntervalSelect = document.querySelector('select[name="refreshInterval"]');
    const advancedModeCheckbox = document.querySelector('input[name="advancedMode"]');
    const developerOptionsCheckbox = document.querySelector('input[name="developerOptions"]');

    // Update settings from form values, with proper type conversion
    if (themeRadio) this.settings.theme = themeRadio.value;
    if (fontSizeSelect) this.settings.fontSize = fontSizeSelect.value;
    if (languageSelect) this.settings.language = languageSelect.value;
    if (notificationsCheckbox) this.settings.notifications = notificationsCheckbox.checked;
    if (autoSaveCheckbox) this.settings.autoSave = autoSaveCheckbox.checked;
    if (compactViewCheckbox) this.settings.compactView = compactViewCheckbox.checked;
    if (defaultViewSelect) this.settings.defaultView = defaultViewSelect.value;
    if (exportFormatSelect) this.settings.exportFormat = exportFormatSelect.value;
    if (dateFormatSelect) this.settings.dateFormat = dateFormatSelect.value;
    
    if (refreshIntervalSelect) {
      const intervalValue = parseInt(refreshIntervalSelect.value);
      this.settings.refreshInterval = isNaN(intervalValue) ? 5 : intervalValue;
    }
    
    if (advancedModeCheckbox) this.settings.advancedMode = advancedModeCheckbox.checked;
    if (developerOptionsCheckbox) this.settings.developerOptions = developerOptionsCheckbox.checked;

    // Dashboard customization
    const startPageSelect = document.querySelector('select[name="startPage"]');
    const widgetArrangementSelect = document.querySelector('select[name="widgetArrangement"]');
    const defaultChartTypeSelect = document.querySelector('select[name="defaultChartType"]');
    
    if (startPageSelect) this.settings.startPage = startPageSelect.value;
    if (widgetArrangementSelect) this.settings.widgetArrangement = widgetArrangementSelect.value;
    if (defaultChartTypeSelect) this.settings.defaultChartType = defaultChartTypeSelect.value;
    
    // Widget visibility
    document.querySelectorAll('input[name^="visibleWidgets."]').forEach(input => {
      const key = input.name.split('.')[1];
      this.settings.visibleWidgets[key] = input.checked;
    });
    
    // Refresh intervals
    document.querySelectorAll('select[name^="refreshIntervals."]').forEach(select => {
      const key = select.name.split('.')[1];
      this.settings.refreshIntervals[key] = parseInt(select.value);
    });
    
    // Notifications & Alerts
    const soundAlertsCheckbox = document.querySelector('input[name="soundAlerts"]');
    const browserNotificationsCheckbox = document.querySelector('input[name="browserNotifications"]');
    const emailNotificationsCheckbox = document.querySelector('input[name="emailNotifications"]');
    const emailAddressInput = document.querySelector('input[name="emailAddress"]');
    
    if (soundAlertsCheckbox) this.settings.soundAlerts = soundAlertsCheckbox.checked;
    if (browserNotificationsCheckbox) this.settings.browserNotifications = browserNotificationsCheckbox.checked;
    if (emailNotificationsCheckbox) this.settings.emailNotifications = emailNotificationsCheckbox.checked;
    if (emailAddressInput) this.settings.emailAddress = emailAddressInput.value;
    
    // Alert thresholds
    document.querySelectorAll('input[name^="alertThresholds."]').forEach(input => {
      const key = input.name.split('.')[1];
      this.settings.alertThresholds[key] = parseInt(input.value);
    });
    
    // Alert categories
    document.querySelectorAll('input[name^="customAlertCategories."]').forEach(input => {
      const key = input.name.split('.')[1];
      this.settings.customAlertCategories[key] = input.checked;
    });
    
    // Accessibility Options
    const highContrastCheckbox = document.querySelector('input[name="highContrast"]');
    const textScalingSlider = document.querySelector('input[name="textScaling"]');
    const keyboardShortcutsCheckbox = document.querySelector('input[name="keyboardShortcuts"]');
    const reducedMotionCheckbox = document.querySelector('input[name="reducedMotion"]');
    const screenReaderCheckbox = document.querySelector('input[name="screenReader"]');
    
    if (highContrastCheckbox) this.settings.highContrast = highContrastCheckbox.checked;
    if (textScalingSlider) this.settings.textScaling = parseFloat(textScalingSlider.value);
    if (keyboardShortcutsCheckbox) this.settings.keyboardShortcuts = keyboardShortcutsCheckbox.checked;
    if (reducedMotionCheckbox) this.settings.reducedMotion = reducedMotionCheckbox.checked;
    if (screenReaderCheckbox) this.settings.screenReader = screenReaderCheckbox.checked;
    
    console.log('Settings updated from form:', this.settings);
  }

  async handleSaveSettings() {
    this.updateSettingsFromForm();
    const success = await this.saveSettings();
    
    if (success) {
      this.showNotification('Settings saved successfully', 'success');
      // Apply settings (e.g., theme, font size)
      this.applySettings();
    } else {
      this.showNotification('Failed to save settings', 'error');
    }
  }

  async handleResetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
      this.settings = { ...this.defaultSettings };
      await this.saveSettings();
      this.render();
      this.setupEventListeners();
      this.applySettings();
      this.showNotification('Settings reset to default values', 'info');
    }
  }

  applySettings() {
    // Apply theme
    this.applyTheme(this.settings.theme);
    
    // Apply font size
    this.applyFontSize(this.settings.fontSize);
    
    // Apply language and compact view
    document.documentElement.setAttribute('data-language', this.settings.language);
    document.documentElement.setAttribute('data-compact', this.settings.compactView ? 'true' : 'false');
    
    // Apply accessibility settings
    if (this.settings.highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    
    if (this.settings.textScaling) {
      document.documentElement.style.setProperty('--text-scale-ratio', this.settings.textScaling);
    }
    
    if (this.settings.reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
    
    if (this.settings.screenReader) {
      document.documentElement.setAttribute('role', 'application');
      document.documentElement.setAttribute('aria-live', 'polite');
    } else {
      document.documentElement.removeAttribute('role');
      document.documentElement.removeAttribute('aria-live');
    }
  }

  applyTheme(theme) {
    const body = document.body;
    
    if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      body.classList.add('dark-mode');
      updateDarkModeIcon(true);
    } else {
      body.classList.remove('dark-mode');
      updateDarkModeIcon(false);
    }

    // Sync dark mode preference
    saveDarkModePreference();
  }

  applyFontSize(size) {
    const html = document.documentElement;
    
    switch (size) {
      case 'small':
        html.style.fontSize = '14px';
        break;
      case 'medium':
        html.style.fontSize = '16px';
        break;
      case 'large':
        html.style.fontSize = '18px';
        break;
      default:
        html.style.fontSize = '16px';
    }
  }

  exportSettings() {
    const dataStr = JSON.stringify(this.settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const a = document.createElement('a');
    a.setAttribute('hidden', '');
    a.setAttribute('href', url);
    a.setAttribute('download', 'envent_bridge_settings.json');
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    this.showNotification('Settings exported successfully', 'success');
  }

  importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'application/json';
    
    input.onchange = e => {
      const file = e.target.files[0];
      
      if (file) {
        const reader = new FileReader();
        
        reader.onload = event => {
          try {
            const importedSettings = JSON.parse(event.target.result);
            this.settings = { ...this.settings, ...importedSettings };
            this.saveSettings();
            this.render();
            this.setupEventListeners();
            this.applySettings();
            this.showNotification('Settings imported successfully', 'success');
          } catch (error) {
            console.error('Error importing settings:', error);
            this.showNotification('Failed to import settings. Invalid file format.', 'error');
          }
        };
        
        reader.readAsText(file);
      }
    };
    
    input.click();
  }

  showNotification(message, type = 'info') {
    // Create notification container if it doesn't exist
    let container = document.getElementById('settingsNotificationContainer');
    
    if (!container) {
      container = document.createElement('div');
      container.id = 'settingsNotificationContainer';
      container.className = 'fixed top-4 right-4 z-50 flex flex-col items-end space-y-2';
      document.body.appendChild(container);
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `px-4 py-3 rounded shadow-lg flex items-center ${this.getNotificationClass(type)}`;
    notification.innerHTML = `
      <div class="mr-3">${this.getNotificationIcon(type)}</div>
      <div>${message}</div>
      <button class="ml-4 text-white opacity-75 hover:opacity-100">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    `;
    
    // Add to container
    container.appendChild(notification);
    
    // Add close functionality
    const closeButton = notification.querySelector('button');
    closeButton.addEventListener('click', () => {
      notification.remove();
    });
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode === container) {
        notification.remove();
      }
      
      // Remove container if empty
      if (container.children.length === 0) {
        container.remove();
      }
    }, 3000);
  }

  getNotificationClass(type) {
    switch (type) {
      case 'success':
        return 'bg-green-600 text-white';
      case 'error':
        return 'bg-red-600 text-white';
      case 'warning':
        return 'bg-yellow-600 text-white';
      default:
        return 'bg-blue-600 text-white';
    }
  }

  getNotificationIcon(type) {
    switch (type) {
      case 'success':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        `;
      case 'error':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        `;
      case 'warning':
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        `;
      default:
        return `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        `;
    }
  }
}

// Initialize dark mode on page load
document.addEventListener("DOMContentLoaded", () => {
  loadDarkModePreference();
});

// Export all necessary functions and classes
export {
  toggleDarkMode,
  loadDarkModePreference,
  updateDarkModeIcon,
  MainSettingsComponent
};