// Map component for displaying and managing project locations
import { MapStorage } from "./map-storage.js";

export class MapComponent {
  constructor(container) {
    this.container = container;
    this.mapboxToken = 'pk.eyJ1IjoibWFoZGktZWJhZGkiLCJhIjoiY204YjViYW0xMHAwZTJqcHlxbnp0N2EzbiJ9.SSUsIDq3g5JMy9GYyKQTYg'; // User's Mapbox token
    this.map = null;
    this.markers = [];
    this.currentInfoWindow = null;
    this.projectData = [];
    this.storage = new MapStorage();
  }

  init() {
    this.render();
    this.loadLocalMapboxDependencies();
    this.setupEventListeners();
  }

  loadLocalMapboxDependencies() {
    try {
      console.log("Loading local Mapbox files...");
      
      // Load CSS file
      const existingCss = document.getElementById('mapbox-gl-css');
      if (!existingCss) {
        const mapboxCss = document.createElement('link');
        mapboxCss.id = 'mapbox-gl-css';
        mapboxCss.rel = 'stylesheet';
        mapboxCss.href = 'map/mapbox-gl.css';
        document.head.appendChild(mapboxCss);
      }
      
      // Check if mapboxgl is already defined
      if (typeof window.mapboxgl !== 'undefined') {
        console.log("Mapbox already loaded, initializing map");
        this.initializeMap();
        this.loadProjectData();
        return;
      }
      
      // Load the local JS file
      const mapboxScript = document.createElement('script');
      mapboxScript.src = 'map/mapbox-gl.js';
      mapboxScript.onload = () => {
        console.log("Mapbox loaded successfully from local file");
        // Set CSP worker URL if needed
        // window.mapboxgl.workerUrl = 'map/mapbox-gl-csp-worker.js';
        this.initializeMap();
        this.loadProjectData();
      };
      
      mapboxScript.onerror = (error) => {
        console.error("Error loading Mapbox from local file:", error);
        this.handleMapboxLoadError();
      };
      
      document.head.appendChild(mapboxScript);
    } catch (error) {
      console.error("Error setting up Mapbox dependencies:", error);
      this.handleMapboxLoadError();
    }
  }

  handleMapboxLoadError() {
    // Display error message in the container
    this.container.innerHTML = `
      <div class="p-4">
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong class="font-bold">Map Loading Error!</strong>
          <span class="block sm:inline">Failed to load Mapbox GL JS. Please check your internet connection and try again.</span>
          <button id="retryMapLoad" class="mt-3 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
            Retry Loading
          </button>
        </div>
      </div>
    `;
    
    // Add retry button functionality
    const retryButton = document.getElementById('retryMapLoad');
    if (retryButton) {
      retryButton.addEventListener('click', () => {
        this.init();
      });
    }
  }

  render() {
    this.container.innerHTML = `
      <style>
        .fullscreen-mode {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
          background: white;
          padding: 20px;
          margin: 0;
          width: 100%;
          height: 100vh;
        }
        .dark .fullscreen-mode {
          background: #1a1a1a;
        }
        
        /* Analyzer container styles */
        #analyzerContainer {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
          background: white;
          margin: 0;
          width: 100%;
          height: 100vh;
          display: flex;
          flex-direction: column;
        }
        
        .dark #analyzerContainer {
          background: #1a1a1a;
        }
        
        #analyzerFrame {
          width: 100%;
          height: 100%;
          border: none;
          flex: 1;
        }
        
        #analyzerAuthOverlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10;
        }
        
        .dark #analyzerAuthOverlay {
          background: rgba(26, 26, 26, 0.9);
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        .animate-spin {
          animation: spin 1s linear infinite;
        }
        
        /* Scrollable modal body with max height */
        .modal-scrollable {
          max-height: 70vh;
          overflow-y: auto;
          overflow-x: hidden;
          padding: 0.5rem;
        }
        
        /* Modal positioning for better fit */
        .modal-container {
          margin: 2rem auto;
          max-height: calc(100vh - 4rem);
          overflow: hidden;
          box-sizing: border-box;
        }
        
        /* Ultra compact sidebar styling */
        .compact-sidebar {
          font-size: 0.8rem;
        }
        
        .compact-sidebar h2 {
          font-size: 1rem;
          margin-bottom: 0.4rem;
        }
        
        .compact-sidebar .location-item {
          padding: 0.35rem;
          margin-bottom: 0.2rem;
          border-width: 1px;
        }
        
        .compact-sidebar .location-item .font-medium {
          font-size: 0.8rem;
        }
        
        .compact-sidebar .location-item .text-xs {
          font-size: 0.7rem;
        }
        
        /* Fix for the delete button size */
        .delete-project {
          padding: 0.15rem;
          line-height: 1;
        }
        
        .delete-project i {
          font-size: 0.7rem;
        }
      </style>
      <div class="p-4">
        <div class="flex justify-between items-center mb-4">
          <h1 class="text-2xl font-bold">Project Map</h1>
          <div class="flex space-x-2">
            <button id="addLocationBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
              <i class="fas fa-plus mr-2"></i>Add Location
            </button>
            <button id="fullscreenMapBtn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-md">
              <i class="fas fa-expand mr-2"></i>Fullscreen
            </button>
            <button id="exportMapBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md">
              <i class="fas fa-file-export mr-2"></i>Export
            </button>
          </div>
        </div>
        
        <div class="grid grid-cols-4 gap-4" id="mapGrid">
          <div class="col-span-3">
            <div id="mapContainer" style="height: 500px; border-radius: 8px; overflow: hidden;"></div>
          </div>
          <div class="col-span-1">
            <div class="bg-white dark:bg-gray-800 p-2 rounded-lg shadow compact-sidebar">
              <h2 class="text-lg font-semibold mb-2">Project Locations</h2>
              <div class="mb-2">
                <input type="text" id="searchLocation" placeholder="Search locations..." 
                  class="w-full px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              <div id="locationsList" class="space-y-0">
                <!-- Locations will be populated here -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Add Location Modal -->
      <div id="addLocationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-4 max-w-md w-full modal-container">
          <h3 class="text-xl font-bold mb-3">Add New Project Location</h3>
          <div class="modal-scrollable">
            <form id="addLocationForm">
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Project Name</label>
                <input type="text" id="projectName" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" required>
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Customer Name</label>
                <input type="text" id="customerName" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" required>
              </div>
              <div class="grid grid-cols-2 gap-3 mb-3">
                <div>
                  <label class="block text-sm font-medium mb-1">Latitude</label>
                  <input type="number" id="latitude" step="0.000001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" required>
                </div>
                <div>
                  <label class="block text-sm font-medium mb-1">Longitude</label>
                  <input type="number" id="longitude" step="0.000001" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" required>
                </div>
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Gas Type</label>
                <select id="gasType" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
                  <option value="Natural Gas">Natural Gas</option>
                  <option value="LPG">LPG</option>
                  <option value="CNG">CNG</option>
                  <option value="Biogas">Biogas</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Installation Date</label>
                <input type="date" id="installDate" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Photo URL</label>
                <input type="url" id="photoUrl" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" placeholder="https://example.com/image.jpg">
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Purchase Order (PO) Number</label>
                <input type="text" id="poNumber" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" placeholder="PO-12345">
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Analyzer Model</label>
                <input type="text" id="analyzerModel" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" placeholder="Model-XYZ">
              </div>
              <div class="mb-3">
                <label class="block text-sm font-medium mb-1">Notes</label>
                <textarea id="notes" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white" rows="3"></textarea>
              </div>
              <div class="flex justify-end space-x-3">
                <button type="button" id="cancelAddLocation" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md">Cancel</button>
                <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">Save Location</button>
              </div>
            </form>
          </div>
        </div>
      </div>
      
      <!-- Confirmation Modal -->
      <div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full">
          <h3 class="text-xl font-bold mb-4">Confirm Deletion</h3>
          <p class="mb-6">Are you sure you want to delete this project location? This action cannot be undone.</p>
          <div class="flex justify-end space-x-3">
            <button id="cancelDelete" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md">Cancel</button>
            <button id="confirmDelete" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md">Delete</button>
          </div>
        </div>
      </div>
      
      <!-- Detailed View Modal -->
      <div id="detailViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl w-full">
          <div class="flex justify-between items-start mb-4">
            <h3 class="text-xl font-bold" id="detailTitle">Project Details</h3>
            <button id="closeDetailView" class="text-gray-500 hover:text-gray-700">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div id="detailPhoto" class="rounded-lg overflow-hidden">
              <!-- Photo will be displayed here -->
            </div>
            <div id="detailInfo">
              <!-- Project details will be displayed here -->
            </div>
          </div>
        </div>
      </div>
    `;
  }

  initializeMap() {
    try {
      console.log("Initializing Mapbox with token:", this.mapboxToken);
      
      if (typeof window.mapboxgl === 'undefined') {
        throw new Error("mapboxgl is not defined, cannot initialize map");
      }
      
      // Set access token
      window.mapboxgl.accessToken = this.mapboxToken;
      
      console.log("Creating map instance");
      
      // Create a new map instance with user's custom configuration
      this.map = new window.mapboxgl.Map({
        container: 'mapContainer',
        style: 'mapbox://styles/mahdi-ebadi/cm8vkgjaz00yd01snc85y0bra', // Custom style URL
        center: [-95.7129, 37.0902], // Default center: USA (will update when projects load)
        zoom: 3,
        pitch: 45, // 3D tilt
        bearing: -17.6, // Rotate the map
        antialias: true // Better quality rendering
      });
      
      // Add navigation controls
      this.map.addControl(new window.mapboxgl.NavigationControl());
      
      // Add geolocation control
      this.map.addControl(new window.mapboxgl.GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true
      }));
      
      // Add error handler
      this.map.on('error', (e) => {
        console.error("Mapbox error:", e);
      });
      
      // Add load handler
      this.map.on('load', () => {
        console.log("Map fully loaded");
      });

      // Add 3D features when the style is loaded
      this.map.on('style.load', () => {
        console.log("Map style loaded, adding 3D terrain and buildings");
        
        // Add terrain
        try {
          this.map.addSource('mapbox-dem', {
            'type': 'raster-dem',
            'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
            'tileSize': 512,
            'maxzoom': 14
          });
          
          // Add the DEM source as a terrain layer with exaggerated height
          this.map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': 1.5 });
          
          // Find the first symbol layer in the map style
          const layers = this.map.getStyle().layers;
          const labelLayerId = layers.find(
            (layer) => layer.type === 'symbol' && layer.layout && layer.layout['text-field']
          )?.id;

          // Add 3D building extrusions
          this.map.addLayer(
            {
              'id': 'add-3d-buildings',
              'source': 'composite',
              'source-layer': 'building',
              'filter': ['==', 'extrude', 'true'],
              'type': 'fill-extrusion',
              'minzoom': 15,
              'paint': {
                'fill-extrusion-color': '#aaa',
                'fill-extrusion-height': [
                  'interpolate',
                  ['linear'],
                  ['zoom'],
                  15,
                  0,
                  15.05,
                  ['get', 'height']
                ],
                'fill-extrusion-base': [
                  'interpolate',
                  ['linear'],
                  ['zoom'],
                  15,
                  0,
                  15.05,
                  ['get', 'min_height']
                ],
                'fill-extrusion-opacity': 0.6
              }
            },
            labelLayerId // Place buildings under labels
          );
        } catch (terrainError) {
          console.warn("Error adding 3D terrain:", terrainError);
          // Continue without terrain - not critical
        }
      });
      
    } catch (error) {
      console.error("Error initializing map:", error);
      this.handleMapboxLoadError();
    }
  }

  async loadProjectData() {
    try {
      // Load projects from storage
      this.projectData = await this.storage.loadProjects();
      
      // Validate that we have project data
      if (!this.projectData || !Array.isArray(this.projectData) || this.projectData.length === 0) {
        console.warn("No project data loaded or empty array returned");
      } else {
        console.log(`Successfully loaded ${this.projectData.length} projects`);
      }
      
      // Display the projects on the map and list
      this.displayProjects();
    } catch (error) {
      console.error("Error loading project data:", error);
      // Show error message
      const locationsList = document.getElementById('locationsList');
      if (locationsList) {
        locationsList.innerHTML = `<div class="text-red-500">Error loading project data. Please try again later.</div>`;
      }
    }
  }

  displayProjects() {
    try {
      if (!this.map) {
        console.error("Map is not initialized, cannot display projects");
        return;
      }

      // Clear existing markers
      this.markers.forEach(marker => marker.remove());
      this.markers = [];
      
      // Clear locations list
      const locationsList = document.getElementById('locationsList');
      if (!locationsList) {
        console.error("locationsList element not found");
        return;
      }
      
      locationsList.innerHTML = '';
      
      // Add new markers and list items
      this.projectData.forEach(project => {
        try {
          // Create marker
          const marker = new window.mapboxgl.Marker({ color: this.getMarkerColorByGasType(project.gasType) })
            .setLngLat([project.longitude, project.latitude])
            .addTo(this.map);
          
          // Store reference to marker
          this.markers.push(marker);
          
          // Create popup content
          const popupContent = `
            <div class="popup-content">
              <h3 class="text-lg font-bold">${project.projectName}</h3>
              <p class="text-sm mb-2">Customer: ${project.customerName}</p>
              ${project.photoUrl ? `<img src="${project.photoUrl}" class="w-full h-32 object-cover mb-2 rounded">` : ''}
              <div class="grid grid-cols-2 gap-2 text-xs">
                <div><span class="font-semibold">Gas Type:</span> ${project.gasType}</div>
                <div><span class="font-semibold">Install Date:</span> ${this.formatDate(project.installDate)}</div>
                ${project.poNumber ? `<div><span class="font-semibold">PO Number:</span> ${project.poNumber}</div>` : ''}
                ${project.analyzerModel ? `<div><span class="font-semibold">Analyzer:</span> ${project.analyzerModel}</div>` : ''}
              </div>
              ${project.notes ? `<p class="text-xs mt-2">${project.notes}</p>` : ''}
              <button class="view-details-btn bg-blue-500 text-white text-xs px-2 py-1 rounded mt-2" data-project-id="${project.id}">View Details</button>
            </div>
          `;
          
          // Create popup
          const popup = new window.mapboxgl.Popup({ 
            offset: 25,
            maxWidth: '300px',
            closeButton: true,
            closeOnClick: false
          }).setHTML(popupContent);
          
          // Add event listener for the view details button
          popup.on('open', () => {
            setTimeout(() => {
              const viewDetailsBtn = document.querySelector(`.view-details-btn[data-project-id="${project.id}"]`);
              if (viewDetailsBtn) {
                viewDetailsBtn.addEventListener('click', () => {
                  this.showDetailView(project);
                });
              }
            }, 100);
          });
          
          // Attach popup to marker
          marker.setPopup(popup);
          
          // Add to list
          const listItem = document.createElement('div');
          listItem.className = 'border dark:border-gray-700 rounded-md p-1 mb-1 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 location-item';
          listItem.innerHTML = `
            <div class="flex justify-between items-center">
              <div class="truncate" style="max-width: 85%;">
                <div class="font-medium truncate text-xs">${project.projectName}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400 truncate">${project.customerName}</div>
              </div>
              <button class="delete-project text-red-500 hover:text-red-700 ml-1" data-project-id="${project.id}">
                <i class="fas fa-trash text-xs"></i>
              </button>
            </div>
          `;
          
          // Add click event to list item to fly to location
          listItem.addEventListener('click', (e) => {
            // Don't trigger if the delete button was clicked
            if (e.target.closest('.delete-project')) {
              return;
            }
            
            this.map.flyTo({
              center: [project.longitude, project.latitude],
              zoom: 14,
              essential: true
            });
            marker.togglePopup();
          });
          
          // Add delete button functionality
          listItem.querySelector('.delete-project').addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent flying to the location
            this.showDeleteConfirmation(project.id);
          });
          
          locationsList.appendChild(listItem);
        } catch (projectError) {
          console.error("Error displaying project:", project.id, projectError);
        }
      });
    } catch (error) {
      console.error("Error displaying projects:", error);
    }
  }

  getMarkerColorByGasType(gasType) {
    const colors = {
      'Natural Gas': '#4285F4', // Blue
      'LPG': '#EA4335',        // Red
      'CNG': '#FBBC05',        // Yellow
      'Biogas': '#34A853',     // Green
      'Other': '#9334E6'       // Purple
    };
    
    return colors[gasType] || '#000000';
  }

  formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  showDetailView(project) {
    const detailViewModal = document.getElementById('detailViewModal');
    const detailTitle = document.getElementById('detailTitle');
    const detailPhoto = document.getElementById('detailPhoto');
    const detailInfo = document.getElementById('detailInfo');
    
    if (!detailViewModal || !detailTitle || !detailPhoto || !detailInfo) {
      console.error("Detail view elements not found");
      return;
    }
    
    // Set project title
    detailTitle.textContent = project.projectName;
    
    // Set photo if available
    if (project.photoUrl) {
      detailPhoto.innerHTML = `
        <img src="${project.photoUrl}" class="w-full h-auto object-cover rounded" alt="${project.projectName}">
      `;
    } else {
      detailPhoto.innerHTML = `
        <div class="bg-gray-200 dark:bg-gray-700 w-full h-64 flex items-center justify-center rounded">
          <span class="text-gray-500 dark:text-gray-400">No photo available</span>
        </div>
      `;
    }
    
    // Set project details
    detailInfo.innerHTML = `
      <div class="grid grid-cols-1 gap-4">
        <div>
          <h4 class="text-lg font-semibold mb-2">Customer Information</h4>
          <p class="mb-1"><span class="font-medium">Name:</span> ${project.customerName}</p>
          <p class="mb-1"><span class="font-medium">Location:</span> ${project.latitude.toFixed(6)}, ${project.longitude.toFixed(6)}</p>
        </div>
        
        <div>
          <h4 class="text-lg font-semibold mb-2">Installation Details</h4>
          <p class="mb-1"><span class="font-medium">Gas Type:</span> ${project.gasType}</p>
          <p class="mb-1"><span class="font-medium">Install Date:</span> ${this.formatDate(project.installDate)}</p>
          ${project.poNumber ? `<p class="mb-1"><span class="font-medium">PO Number:</span> ${project.poNumber}</p>` : ''}
          ${project.analyzerModel ? `<p class="mb-1"><span class="font-medium">Analyzer Model:</span> ${project.analyzerModel}</p>` : ''}
        </div>
        
        <div>
          <h4 class="text-lg font-semibold mb-2">Notes</h4>
          <p>${project.notes || 'No notes available'}</p>
        </div>
        
        <div class="mt-4">
          <div class="flex flex-col space-y-4">
            <button id="openAnalyzerBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md flex items-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
              Open Analyzer
            </button>
            
            <div class="flex items-center space-x-2">
              <input type="text" id="customIpAddress" placeholder="Custom IP (e.g. *************:8080)" 
                class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md dark:bg-gray-700 dark:text-white">
              <button id="openCustomIpBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Connect
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Show the modal
    detailViewModal.classList.remove('hidden');
    
    // Add event listener for the analyzer button
    document.getElementById('openAnalyzerBtn').addEventListener('click', () => {
      this.openAnalyzerInterface(project);
    });

    // Add event listener for the custom IP button
    document.getElementById('openCustomIpBtn').addEventListener('click', () => {
      const customIp = document.getElementById('customIpAddress').value.trim();
      if (customIp) {
        this.openAnalyzerInterface(project, customIp);
      } else {
        alert('Please enter a valid IP address');
      }
    });
  }

  // New method to open the analyzer interface with optional custom IP
  async openAnalyzerInterface(project, customIp = null) {
    try {
      // Use custom IP if provided, otherwise use default
      const analyzerUrl = customIp ? 
        (customIp.startsWith('http') ? customIp : `http://${customIp}`) : 
        'http://*************:8080';
      
      // Check if user credentials are available
      let email = '';
      let password = '';
      
      if (typeof chrome !== "undefined" && chrome.storage) {
        // Get user credentials from Chrome storage
        const result = await new Promise((resolve) => {
          chrome.storage.local.get(["user"], (data) => {
            resolve(data);
          });
        });
        
        if (result.user) {
          email = result.user.Email || '';
          password = result.user.Password || '';
        }
      } else {
        // Get from localStorage as fallback
        try {
          const userStr = localStorage.getItem("user");
          if (userStr) {
            const user = JSON.parse(userStr);
            email = user.Email || '';
            password = user.Password || '';
          }
        } catch (e) {
          console.warn("Error retrieving user from localStorage:", e);
        }
      }
      
      if (!email || !password) {
        alert("User credentials not found. Please log in again.");
        return;
      }
      
      // Create fullscreen container for analyzer iframe
      const analyzerContainer = document.createElement('div');
      analyzerContainer.id = 'analyzerContainer';
      analyzerContainer.className = 'fixed inset-0 z-50 bg-white dark:bg-gray-900 flex flex-col';
      analyzerContainer.innerHTML = `
        <div class="bg-gray-200 dark:bg-gray-800 p-2 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
            Analyzer: ${project.projectName} (${project.analyzerModel || 'Unknown Model'})
          </h3>
          <div class="flex space-x-2">
            <button id="toggleFullscreenBtn" class="bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1 rounded">
              <i class="fas fa-expand"></i> Fullscreen
            </button>
            <button id="closeAnalyzerBtn" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded">
              Close
            </button>
          </div>
        </div>
        <div class="flex-1 relative">
          <div id="analyzerAuthOverlay" class="absolute inset-0 bg-gray-100 dark:bg-gray-800 flex items-center justify-center z-10">
            <div class="text-center">
              <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500 mb-3"></div>
              <p class="text-gray-700 dark:text-gray-300">Authenticating to analyzer...</p>
            </div>
          </div>
          <iframe id="analyzerFrame" src="${analyzerUrl}" class="w-full h-full border-0"></iframe>
        </div>
      `;
      
      // Add the container to the body
      document.body.appendChild(analyzerContainer);
      
      // Add close button handler
      document.getElementById('closeAnalyzerBtn').addEventListener('click', () => {
        document.body.removeChild(analyzerContainer);
      });
      
      // Add fullscreen toggle handler
      document.getElementById('toggleFullscreenBtn').addEventListener('click', () => {
        const analyzerUrl = document.getElementById('analyzerFrame').src;
        
        // Create a new HTML page with an iframe that loads the analyzer
        const iframeHtml = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Analyzer: ${project.projectName}</title>
            <style>
              body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
              iframe { width: 100%; height: 100%; border: none; }
            </style>
          </head>
          <body>
            <iframe src="${analyzerUrl}" allowfullscreen></iframe>
          </body>
          </html>
        `;
        
        // Create a blob URL for the HTML content
        const blob = new Blob([iframeHtml], {type: 'text/html'});
        const blobUrl = URL.createObjectURL(blob);
        
        // Open the blob URL in a new tab
        window.open(blobUrl, '_blank');
      });
      
      // Get the iframe and set up a load event handler
      const analyzerFrame = document.getElementById('analyzerFrame');
      analyzerFrame.addEventListener('load', async () => {
        try {
          console.log('Analyzer frame loaded, attempting authentication');
          
          // Attempt to authenticate using the form inside the iframe
          const frameDocument = analyzerFrame.contentDocument || analyzerFrame.contentWindow.document;
          
          // Check if there's a login form
          const usernameField = frameDocument.querySelector('input[type="text"], input[type="email"], input[name="username"]');
          const passwordField = frameDocument.querySelector('input[type="password"]');
          const loginButton = frameDocument.querySelector('button[type="submit"], input[type="submit"]');
          
          if (usernameField && passwordField && loginButton) {
            console.log('Login form found, attempting to fill credentials');
            
            // Fill in credentials
            usernameField.value = email;
            passwordField.value = password;
            
            // Submit the form
            loginButton.click();
            
            // Hide the auth overlay after a short delay to allow login to process
            setTimeout(() => {
              const authOverlay = document.getElementById('analyzerAuthOverlay');
              if (authOverlay) {
                authOverlay.style.display = 'none';
              }
            }, 2000);
          } else {
            console.log('No login form found, analyzer might be already logged in');
            // Hide the auth overlay
            const authOverlay = document.getElementById('analyzerAuthOverlay');
            if (authOverlay) {
              authOverlay.style.display = 'none';
            }
          }
        } catch (error) {
          console.error('Error authenticating to analyzer:', error);
          // Hide the auth overlay
          const authOverlay = document.getElementById('analyzerAuthOverlay');
          if (authOverlay) {
            authOverlay.style.display = 'none';
          }
        }
      });
    } catch (error) {
      console.error('Error opening analyzer interface:', error);
      alert('Failed to open analyzer interface. Please try again.');
    }
  }

  showDeleteConfirmation(projectId) {
    const confirmationModal = document.getElementById('confirmationModal');
    const confirmDelete = document.getElementById('confirmDelete');
    const cancelDelete = document.getElementById('cancelDelete');
    
    if (!confirmationModal || !confirmDelete || !cancelDelete) {
      console.error("Confirmation modal elements not found");
      return;
    }
    
    // Show the modal
    confirmationModal.classList.remove('hidden');
    
    // Set up delete confirmation
    const handleConfirmDelete = async () => {
      try {
        // Delete the project
        this.projectData = await this.storage.deleteProject(projectId);
        
        // Refresh the display
        this.displayProjects();
        
        // Hide the modal
        confirmationModal.classList.add('hidden');
      } catch (error) {
        console.error("Error deleting project:", error);
        alert("Error deleting project. Please try again.");
      }
    };
    
    const handleCancelDelete = () => {
      confirmationModal.classList.add('hidden');
    };
    
    // Remove existing event listeners to prevent duplicates
    const newConfirmDelete = confirmDelete.cloneNode(true);
    const newCancelDelete = cancelDelete.cloneNode(true);
    
    confirmDelete.parentNode.replaceChild(newConfirmDelete, confirmDelete);
    cancelDelete.parentNode.replaceChild(newCancelDelete, cancelDelete);
    
    // Add new event listeners
    newConfirmDelete.addEventListener('click', handleConfirmDelete);
    newCancelDelete.addEventListener('click', handleCancelDelete);
  }

  setupEventListeners() {
    // Add location button
    const addLocationBtn = document.getElementById('addLocationBtn');
    const addLocationModal = document.getElementById('addLocationModal');
    const cancelAddLocation = document.getElementById('cancelAddLocation');
    const addLocationForm = document.getElementById('addLocationForm');
    const searchInput = document.getElementById('searchLocation');
    const fullscreenMapBtn = document.getElementById('fullscreenMapBtn');
    const mapGrid = document.getElementById('mapGrid');
    const mapContainer = document.getElementById('mapContainer');
    const detailViewModal = document.getElementById('detailViewModal');
    const closeDetailView = document.getElementById('closeDetailView');
    
    // Setup fullscreen button to open in browser tab
    if (fullscreenMapBtn) {
      fullscreenMapBtn.addEventListener('click', () => {
        this.openMapInBrowserTab();
      });
    }
    
    // Close detail view
    if (closeDetailView && detailViewModal) {
      closeDetailView.addEventListener('click', () => {
        detailViewModal.classList.add('hidden');
      });
    }
    
    if (addLocationBtn) {
      addLocationBtn.addEventListener('click', () => {
        addLocationModal.classList.remove('hidden');
      });
    }
    
    if (cancelAddLocation) {
      cancelAddLocation.addEventListener('click', () => {
        addLocationModal.classList.add('hidden');
        addLocationForm.reset();
      });
    }
    
    if (addLocationForm) {
      addLocationForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Get form data
        const newProject = {
          projectName: document.getElementById('projectName').value,
          customerName: document.getElementById('customerName').value,
          latitude: parseFloat(document.getElementById('latitude').value),
          longitude: parseFloat(document.getElementById('longitude').value),
          gasType: document.getElementById('gasType').value,
          installDate: document.getElementById('installDate').value,
          photoUrl: document.getElementById('photoUrl').value,
          poNumber: document.getElementById('poNumber').value,
          analyzerModel: document.getElementById('analyzerModel').value,
          notes: document.getElementById('notes').value
        };
        
        try {
          // Use storage to add the project
          this.projectData = await this.storage.addProject(newProject);
          
          // Refresh display
          this.displayProjects();
          
          // Close modal and reset form
          addLocationModal.classList.add('hidden');
          addLocationForm.reset();
          
          // Fly to new location
          this.map.flyTo({
            center: [newProject.longitude, newProject.latitude],
            zoom: 14,
            essential: true
          });
        } catch (error) {
          console.error("Error adding location:", error);
          alert("Error adding location. Please try again.");
        }
      });
    }
    
    // Search functionality
    if (searchInput) {
      searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const locationsList = document.getElementById('locationsList');
        const listItems = locationsList.getElementsByTagName('div');
        
        for (let i = 0; i < listItems.length; i++) {
          const item = listItems[i];
          const text = item.textContent.toLowerCase();
          
          if (text.includes(searchTerm)) {
            item.style.display = '';
          } else {
            item.style.display = 'none';
          }
        }
      });
    }
    
    // Export button
    const exportMapBtn = document.getElementById('exportMapBtn');
    if (exportMapBtn) {
      exportMapBtn.addEventListener('click', () => {
        // Export project data as JSON
        const dataStr = JSON.stringify(this.projectData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
        
        const exportFileDefaultName = 'project_locations.json';
        
        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
        linkElement.remove();
      });
    }

    // Handle map click for adding a location
    if (this.map) {
      this.map.on('click', (e) => {
        // If modal is open, fill in lat/lng
        if (addLocationModal && !addLocationModal.classList.contains('hidden')) {
          const latElement = document.getElementById('latitude');
          const lngElement = document.getElementById('longitude');
          
          if (latElement && lngElement) {
            latElement.value = e.lngLat.lat.toFixed(6);
            lngElement.value = e.lngLat.lng.toFixed(6);
          }
        }
      });
      
      // Add a keyboard event listener to exit fullscreen mode with Escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && mapGrid && mapGrid.classList.contains('fullscreen-mode') && fullscreenMapBtn) {
          // Exit fullscreen
          mapGrid.classList.remove('fullscreen-mode');
          mapContainer.style.height = '500px';
          mapGrid.classList.remove('grid-cols-1');
          mapGrid.classList.add('grid-cols-4');
          fullscreenMapBtn.innerHTML = '<i class="fas fa-expand mr-2"></i>Fullscreen';
          
          // Resize the map
          setTimeout(() => {
            this.map.resize();
          }, 100);
        }
      });
    }
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
      // Close detail view modal when clicking outside
      if (detailViewModal && !detailViewModal.querySelector('.bg-white').contains(e.target) && !e.target.closest('.view-details-btn')) {
        detailViewModal.classList.add('hidden');
      }
      
      // Close confirmation modal when clicking outside
      const confirmationModal = document.getElementById('confirmationModal');
      if (confirmationModal && !confirmationModal.querySelector('.bg-white').contains(e.target) && !e.target.closest('.delete-project')) {
        confirmationModal.classList.add('hidden');
      }
    });
  }

  // Method to open map in a browser tab
  openMapInBrowserTab() {
    try {
      // Store project data in localStorage so fullscreen.html can access it
      console.log(`Saving ${this.projectData.length} projects to localStorage for fullscreen view`);
      
      // Make sure data is properly stringified
      const jsonData = JSON.stringify(this.projectData);
      
      // Check if data is valid
      if (!jsonData || jsonData === '[]' || jsonData === 'null') {
        console.warn('No project data to save for fullscreen view');
      }
      
      // Save the data with a clear key name
      localStorage.setItem('enventbridge_projectData', jsonData);
      
      // Also save to alternative key for backward compatibility
      localStorage.setItem('enventbridge_project_locations', jsonData);
      
      localStorage.setItem('enventbridge_mapboxToken', this.mapboxToken);
      
      // Store dark mode preference
      const isDarkMode = document.body.classList.contains('dark-mode');
      localStorage.setItem('enventbridge_darkMode', isDarkMode);
      
      // Add debug info to localStorage to help troubleshoot
      localStorage.setItem('map_debug_info', JSON.stringify({
        timestamp: new Date().toISOString(),
        projectCount: this.projectData.length,
        dataSize: jsonData.length,
        hasToken: !!this.mapboxToken
      }));
      
      // Open the fullscreen HTML file in a new tab
      window.open('map/fullscreen.html', '_blank');
    } catch (error) {
      console.error("Error opening map in fullscreen:", error);
      alert("Could not open map in fullscreen. Please try again.");
    }
  }
} 