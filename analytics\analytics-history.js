// Improved History Table Component - integrated with centralized data refresh
// Shows newest data first and better looking interface

export class HistoryTable {
  constructor(container, data = [], lastUpdate = null) {
    this.container = container;
    this.data = data; // Data should already be sorted by date (newest first)
    this.itemsPerPage = 5;
    this.currentPage = 1;
    this.filterValue = '';
    this.lastUpdate = lastUpdate || new Date();
  }

  updateData(newData, updateTime = null) {
    this.data = newData; // Data should be pre-sorted
    if (updateTime) {
      this.lastUpdate = updateTime;
    } else {
      this.lastUpdate = new Date(); 
    }
    this.currentPage = 1;
    this.render();
  }

  // Improved render method for HistoryTable - fully responsive with better styling
  render() {
    if (!this.container) return;

    // Filter data if filter is set
    const filteredData = this.filterValue ? 
      this.data.filter(item => 
        (item["Reference Number"] && item["Reference Number"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
        (item["Customer Name"] && item["Customer Name"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
        (item["User Name"] && item["User Name"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
        (item["Carrier"] && item["Carrier"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
        (item["Inventory ID"] && item["Inventory ID"].toLowerCase().includes(this.filterValue.toLowerCase()))
      ) : 
      this.data;

    // Calculate pagination
    const totalPages = Math.ceil(filteredData.length / this.itemsPerPage);
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const visibleData = filteredData.slice(startIndex, startIndex + this.itemsPerPage);

    // Main container - FULLY RESPONSIVE with no fixed width
    let html = `
      <div class="w-full">
        <div class="mb-2 flex items-center justify-between">
          <div class="text-xs text-gray-500">
            ${filteredData.length} shipments found
            ${this.lastUpdate ? `<span class="ml-1">(newest first)</span>` : ''}
          </div>
          <div>
            <input type="text" id="history-filter" placeholder="Search..." value="${this.filterValue}" 
              class="text-xs px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 w-32">
          </div>
        </div>
        
        <div class="overflow-x-auto border border-gray-200 rounded-md w-full">
          <table class="min-w-full divide-y divide-gray-200 text-xs">
            <thead class="bg-gray-50">
              <tr>
                <!-- PERCENTAGE-BASED WIDTHS for responsiveness -->
                <th scope="col" class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider w-[19%]">User</th>
                <th scope="col" class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider w-[14%]">Ref</th>
                <th scope="col" class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider w-[26%]">Customer</th>
                <th scope="col" class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider w-[17%]">Shipping</th>
                <th scope="col" class="px-2 py-2 text-left font-medium text-gray-500 uppercase tracking-wider w-[10%]">Type</th>
                <th scope="col" class="px-2 py-2 text-center font-medium text-gray-500 uppercase tracking-wider w-[14%]">Action</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
    `;

    if (visibleData.length === 0) {
      html += `
        <tr>
          <td colspan="6" class="px-2 py-4 text-center text-gray-500">No shipping history data available</td>
        </tr>
      `;
    } else {
      visibleData.forEach((item, index) => {
        // Format the avatar image
        const avatarSrc = item.Avatar && item.Avatar !== 'N/A' 
          ? `images/avatars/avatar_${item.Avatar}.jpg` 
          : 'images/default-avatar.svg';
        
        // Format shipping info
        const carrier = item.Carrier || 'N/A';
        const trackingNumber = item["Tracking Number"] || 'N/A';
        const trackingLink = item["Tracking Link"] || '';
        
        // Format date (for tooltip and detail view)
        const dateObj = item.Date ? new Date(item.Date) : null;
        const formattedDate = dateObj 
          ? dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: '2-digit' }) 
          : item.Date || 'N/A';
        
        // Format time - NEW (show exact time if available)
        const formattedTime = dateObj 
          ? dateObj.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) 
          : '';
        
        // Get Order Type
        const orderType = item["Order Type"] || 'N/A';
            
        // Create row with alternating background
        html += `
          <tr class="${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'} hover:bg-blue-50 transition-colors duration-150" data-row-id="${index}">
            <!-- USER COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="flex items-center">
                <div class="flex-shrink-0 h-6 w-6">
                  <img class="h-6 w-6 rounded-full border border-gray-200" src="${avatarSrc}" alt="${item["User Name"] || 'User'}" title="${item["User Name"] || 'Unknown'}">
                </div>
                <div class="ml-1 overflow-hidden max-w-[70px]">
                  <div class="font-medium text-gray-900 text-xs truncate" title="${item["User Name"] || 'Unknown'}">${item["User Name"] || 'Unknown'}</div>
                  <div class="text-gray-500 text-xs truncate" title="${formattedDate} ${formattedTime}">
                    ${formattedDate} ${formattedTime ? `<span class="text-gray-400">${formattedTime}</span>` : ''}
                  </div>
                </div>
              </div>
            </td>
            
            <!-- REFERENCE COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="text-gray-900 text-xs truncate max-w-full" title="${item["Reference Number"] || 'N/A'}">${item["Reference Number"] || 'N/A'}</div>
              <div class="text-gray-500 text-xs truncate max-w-full" title="${item["Order Number"] || 'N/A'}">${item["Order Number"] || ''}</div>
            </td>
            
            <!-- CUSTOMER COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="text-gray-900 text-xs truncate max-w-full" title="${item["Customer Name"] || 'N/A'}">${item["Customer Name"] || 'N/A'}</div>
              <div class="text-gray-500 text-xs truncate max-w-full" title="${item["Company Name"] || 'N/A'}">${item["Company Name"] || 'N/A'}</div>
            </td>
            
            <!-- SHIPPING COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="text-gray-900 text-xs truncate max-w-full" title="${carrier}">${carrier}</div>
              <div class="text-gray-500 text-xs truncate max-w-full" title="${trackingNumber}">${this.formatTrackingNumber(trackingNumber)}</div>
            </td>
            
            <!-- ORDER TYPE COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap">
              <div class="text-gray-900 text-xs truncate max-w-full" title="${orderType}">
                ${this.getOrderTypeWithBadge(orderType)}
              </div>
            </td>
            
            <!-- ACTION COLUMN -->
            <td class="px-2 py-2 whitespace-nowrap text-center">
        `;
        
        // Add tracking button if tracking link is available
        if (trackingLink && trackingLink !== 'N/A') {
          html += `
            <a href="${trackingLink}" target="_blank" class="inline-flex items-center px-1.5 py-1 text-xs font-medium rounded-md text-white bg-blue-500 hover:bg-blue-600 focus:outline-none transition-colors duration-150">
              <svg class="w-3 h-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              Track
            </a>
          `;
        } else {
          html += `
            <span class="inline-flex items-center px-1.5 py-1 text-xs font-medium rounded-md bg-gray-100 text-gray-400 cursor-not-allowed">
              <svg class="w-3 h-3 mr-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              N/A
            </span>
          `;
        }
        
        html += `
            </td>
          </tr>
          
          <!-- Expandable row for parts (hidden by default) -->
          <tr class="detail-row hidden bg-blue-50" data-detail-for="${index}">
            <td colspan="6" class="px-2 py-2">
              <div class="grid grid-cols-2 gap-2">
                <div class="col-span-2 md:col-span-1">
                  <div class="text-xs text-gray-700 font-medium mb-1">Inventory IDs:</div>
                  <div class="text-xs text-gray-600 mb-2 break-words">${this.formatInventoryID(item["Inventory ID"] || 'N/A')}</div>
                </div>
                
                <div class="col-span-2 md:col-span-1">
                  <div class="text-xs text-gray-700 font-medium">Package Info:</div>
                  <div class="text-xs text-gray-600">
                    <span>${item["Package Type"] || 'N/A'}</span>, 
                    <span>${item["Package Weight"] || 'N/A'}</span>
                  </div>
                  
                  <div class="text-xs text-gray-700 font-medium mt-1">Shipping Cost:</div>
                  <div class="text-xs text-gray-600">$${this.formatCost(item["Freight Cost"])}</div>
                </div>
              </div>
            </td>
          </tr>
        `;
      });
    }

    html += `
          </tbody>
        </table>
      </div>
    `;

    // Add pagination if needed
    if (totalPages > 1) {
      html += `
        <div class="border-t border-gray-200 px-3 py-2 flex items-center justify-between">
          <div class="flex-1 flex justify-between items-center">
            <button id="prevPage" class="relative inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${this.currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''} border border-gray-200 transition-colors duration-150">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
              Previous
            </button>
            <span class="text-xs text-gray-700">
              Page ${this.currentPage} of ${totalPages}
            </span>
            <button id="nextPage" class="relative inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 ${this.currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''} border border-gray-200 transition-colors duration-150">
              Next
              <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </div>
      `;
    }

    // Render and add event listeners
    this.container.innerHTML = html;
    this.addEventListeners();
  }

  addEventListeners() {
    const prevButton = this.container.querySelector('#prevPage');
    const nextButton = this.container.querySelector('#nextPage');
    const filterInput = this.container.querySelector('#history-filter');

    if (prevButton) {
      prevButton.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.render();
        }
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => {
        const filteredData = this.filterValue ? 
          this.data.filter(item => 
            (item["Reference Number"] && item["Reference Number"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
            (item["Customer Name"] && item["Customer Name"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
            (item["User Name"] && item["User Name"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
            (item["Carrier"] && item["Carrier"].toLowerCase().includes(this.filterValue.toLowerCase())) ||
            (item["Inventory ID"] && item["Inventory ID"].toLowerCase().includes(this.filterValue.toLowerCase()))
          ) : 
          this.data;
        
        const totalPages = Math.ceil(filteredData.length / this.itemsPerPage);
        if (this.currentPage < totalPages) {
          this.currentPage++;
          this.render();
        }
      });
    }
    
    // Add filter input event listener
    if (filterInput) {
      filterInput.addEventListener('input', (e) => {
        this.filterValue = e.target.value;
        this.currentPage = 1; // Reset to first page when filter changes
        this.render();
      });
    }
    
    // Add row click event listeners to expand/collapse part details
    const tableRows = this.container.querySelectorAll('tr[data-row-id]');
    tableRows.forEach(row => {
      row.addEventListener('click', () => {
        const rowId = row.getAttribute('data-row-id');
        const detailRow = this.container.querySelector(`tr[data-detail-for="${rowId}"]`);
        
        if (detailRow) {
          detailRow.classList.toggle('hidden');
          
          // Toggle highlighting on the main row
          if (detailRow.classList.contains('hidden')) {
            row.classList.remove('bg-blue-100');
          } else {
            row.classList.add('bg-blue-100');
          }
        }
      });
    });
  }

  // Helper methods for formatting
  formatTrackingNumber(tracking) {
    if (!tracking || tracking === 'N/A') return 'N/A';
    // If tracking number is too long, truncate it
    return tracking.length > 12 ? tracking.substring(0, 9) + '...' : tracking;
  }

  formatInventoryID(inventoryID) {
    if (!inventoryID || inventoryID === 'N/A') return 'N/A';
    
    // Split comma-separated values and format them
    if (inventoryID.includes(',')) {
      return inventoryID.split(',')
        .map(id => id.trim())
        .filter(id => id)
        .join(', ');
    }
    return inventoryID;
  }

  formatCost(cost) {
    if (!cost) return '0.00';
    const parsedCost = parseFloat(cost);
    return isNaN(parsedCost) ? '0.00' : parsedCost.toFixed(2);
  }

  // Method to display order type with color-coded badge
  getOrderTypeWithBadge(type) {
    if (!type || type === 'N/A') return 'N/A';
    
    let badgeColor = '';
    switch(type.toLowerCase()) {
      case 'standard':
        badgeColor = 'bg-blue-100 text-blue-800';
        break;
      case 'rush':
      case 'expedited':
        badgeColor = 'bg-red-100 text-red-800';
        break;
      case 'international':
        badgeColor = 'bg-purple-100 text-purple-800';
        break;
      case 'return':
        badgeColor = 'bg-yellow-100 text-yellow-800';
        break;
      default:
        badgeColor = 'bg-gray-100 text-gray-800';
    }
    
    return `<span class="px-1.5 py-0.5 rounded-full text-xs ${badgeColor}">${type}</span>`;
  }
}